# 專案設計與維護指南 (初稿 V1.3)

## 1. 專案概覽

本專案是一個線上金融配資平台，旨在為用戶提供股票和可能的期貨配資服務。平台支持用戶進行線上充值、提現、申請不同類型（如按天、按月、VIP）的配資方案、管理其配資帳戶（如追加保證金、提取盈利、續期、終止操盤）等。

系統包含以下主要用戶端：

*   **PC 前台 (`application/index`)**: 面向桌面用戶。
*   **WAP 手機端 (`application/wap`)**: 面向移動用戶。
*   **後台管理系統 (`application/admin`)**: 供管理員進行用戶管理、訂單審核、系統配置等操作。

核心價值在於提供便捷的線上配資渠道和相應的資金管理功能。

## 2. 技術棧

*   **後端框架**: ThinkPHP 5.0.x (專案 `.roorules` 指定版本為 5.0.25)
*   **主要程式語言**: PHP (最低要求 PHP 5.4+，建議 PHP 7.x+)
*   **依賴管理**: Composer (存在 [`composer.json`](composer.json:0) 和 [`composer.lock`](composer.lock:0) 檔案)
*   **HTTP 客戶端**: GuzzleHttp (用於與第三方支付/代付服務通訊)
*   **二維碼生成**: PHP QR Code (在 [`extend/phpqrcode.php`](extend/phpqrcode.php:0) 中引入，用於生成邀請二維碼)
*   **Excel處理**: PHPExcel (在 [`application/admin/controller/Money.php`](application/admin/controller/Money.php:0) 和 [`Stock.php`](application/admin/controller/Stock.php:0) 中通過 `Vendor('Mydemo.PHPExcel')` 引入，用於數據導出)
*   **前端技術**: (未深入分析，但從視圖文件結構看，應為 HTML, CSS, JavaScript，可能未使用現代前端框架)

## 3. 專案結構

專案遵循典型的 ThinkPHP 5.0 目錄結構。

*   **`application/`**: 應用程式目錄
    *   **`admin/`**: 後台管理模組
        *   `controller/`: 後台控制器 (如 [`Money.php`](application/admin/controller/Money.php:0), [`Stock.php`](application/admin/controller/Stock.php:0))
        *   `model/`: 後台業務模型 (如 [`Stock.php`](application/admin/model/Stock.php:0), [`Addfinancing.php`](application/admin/model/Addfinancing.php:0) 等)
        *   `view/`: 後台視圖文件
    *   **`common/`**: 公共模組
        *   `controller/`: 公共控制器基類 (如 [`Wap.php`](application/common/controller/Wap.php:0), [`Admin.php`](application/common/controller/Admin.php:0), [`Member.php`](application/common/controller/Member.php:0))
        *   `model/`: 公共模型 (如 [`RechargeOrder.php`](application/common/model/RechargeOrder.php:0), [`MemberWithdraw.php`](application/common/model/MemberWithdraw.php:0))
        *   `Services/`: 封裝第三方服務的類別 (如 [`Recharge/CheckoutService.php`](application/common/Services/Recharge/CheckoutService.php:0), [`Withdraw/GuantianService.php`](application/common/Services/Withdraw/GuantianService.php:0))
    *   **`index/`**: PC 前台模組
        *   `controller/`: 前台控制器 (如 [`Members.php`](application/index/controller/Members.php:0), [`Stock.php`](application/index/controller/Stock.php:0))
        *   `view/`: 前台視圖文件
    *   **`wap/`**: WAP (手機) 端模組
        *   `controller/`: WAP 控制器 (如 [`Member.php`](application/wap/controller/Member.php:0), [`Recharge.php`](application/wap/controller/Recharge.php:0))
        *   `view/`: WAP視圖文件
    *   **`extra/`**: 額外設定檔目錄 (如 `peizi.php`, `bank.php`)
    *   [`command.php`](application/command.php:0): 控制台命令定義。
    *   [`common.php`](application/common.php:0): 應用公共文件 (當前為空)。
    *   [`function.php`](application/function.php:0): **核心輔助函數庫**，包含大量業務邏輯和工具函數。
    *   [`config.php`](application/config.php:0), [`database.php`](application/database.php:0), [`route.php`](application/route.php:0): 應用主要設定檔。
*   **`public/`**: 對外公開存取目錄，Web 伺服器指向此處。
*   **`runtime/`**: 執行階段目錄 (日誌、快取等)。
*   **`thinkphp/`**: ThinkPHP 框架核心目錄。
*   **`extend/`**: 擴展類庫目錄 (如 `phpqrcode`, `GoogleAuthenticator`, `Moduyun` SMS, `Mydemo/PHPExcel`)。
*   **`temp_doc/`**: 用於存放本文檔。
*   [`.roorules`](.roorules:0): 專案特定的 Roo 指令。
*   [`helpers.php`](helpers.php:0): 全局自定義輔助函數文件，定義了如 `environment()` 和 `randomStr()` 等函數。

## 4. 核心業務邏輯

### 4.1. 用戶系統

*   **註冊**:
    *   PC/WAP 端入口: `index/common/register`, `wap/common/register` (均指向 `CommonController` 的 `register` 方法)。
    *   涉及手機驗證碼 ([`sendsms`](application/function.php:288), [`verify_code`](application/function.php:395) 輔助函數)。
    *   成功後創建 `members` 和 `member_info` 記錄。
    *   可能觸發新手任務 ([`myXsrw`](application/function.php:977) - 註冊獎勵)。
*   **登入**:
    *   PC/WAP 端入口: `index/common/login`, `wap/common/login`。
    *   驗證用戶名密碼 ([`v_pass`](application/function.php:136) 輔助函數)。
    *   成功後設置 session (`uid`, `uname`)。
*   **用戶資訊**:
    *   通過 [`memberInfo($uid)`](application/function.php:773) 輔助函數從 `members`, `member_info`, `member_money` 三表聯查獲取。控制器基類 [`common/controller/Member.php`](application/common/controller/Member.php:0) 和 [`common/controller/Wap.php`](application/common/controller/Wap.php:0) (Wap繼承自Member) 在 `_initialize` 中加載此信息。
*   **實名認證**:
    *   PC 端: [`Index/MembersController::real()`](application/index/controller/Members.php:34)。
    *   WAP 端: [`Wap/MemberController::real()`](application/wap/controller/Member.php:298)。
    *   兩端邏輯相同，對接第三方實名認證接口 ([`realname_request`](application/function.php:238) 輔助函數)。
    *   成功後更新 `member_info` 表，觸發新手任務 ([`myXsrw`](application/function.php:977) - 實名獎勵)。
*   **銀行卡管理**:
    *   PC 端: [`Index/MembersController::bank()`](application/index/controller/Members.php:94)。
    *   WAP 端: [`Wap/MemberController::bank()`](application/wap/controller/Member.php:358)。
    *   兩端邏輯相同，添加銀行卡到 `member_bank` 表。
    *   觸發新手任務 ([`myXsrw`](application/function.php:977) - 綁卡獎勵)。
*   **密碼管理**:
    *   登入密碼修改: PC 端 [`Index/MembersController::pass()`](application/index/controller/Members.php:152)，WAP 端 [`Wap/MemberController::loginPass()`](application/wap/controller/Member.php:171)。兩端邏輯相同。
    *   支付密碼設定/修改: PC 端 [`Index/MembersController::paypass()`](application/index/controller/Members.php:180)。WAP 端 [`Wap/MemberController::paypass()`](application/wap/controller/Member.php:204) 直接調用 PC 端邏輯。

### 4.2. 資金管理

#### 4.2.1. 充值流程

*   **線上充值 (主要體現在 WAP 端)**:
    1.  **用戶發起 (WAP 端 [`MemberController::recharge`](application/wap/controller/Member.php:430))**:
        *   創建 [`RechargeOrder`](application/common/model/RechargeOrder.php:0) 記錄，訂單號由 [`RechargeOrder::generateOrderNo()`](application/common/model/RechargeOrder.php:28) (使用 [`randomStr(6)`](helpers.php:18)) 生成。
        *   調用 `handleCheckoutPayment` 方法。
    2.  **與第三方支付交互 ([`CheckoutService::pay`](application/common/Services/Recharge/CheckoutService.php:27))**:
        *   `handleCheckoutPayment` 調用 `CheckoutService` 的 `pay` 方法。環境判斷使用 [`environment('production')`](helpers.php:3)。
        *   向第三方支付網關發起請求。
    3.  **前端支付**。
    4.  **異步回調處理 ([`Wap/RechargeController::checkoutNotify`](application/wap/controller/Recharge.php:23))**:
        *   驗證簽名，更新訂單狀態和用戶資金 (調用 [`memberMoneyLog`](application/function.php:1228))，觸發新手任務和代理返利，發送通知。
*   **線下充值/提交憑證 (PC 端和 WAP 端均有入口)**:
    1.  **用戶發起**:
        *   PC 端: [`Index/MembersController::recharge()`](application/index/controller/Members.php:375) (AJAX POST)。
        *   WAP 端: (WAP 端 `MemberController::recharge` AJAX 部分似乎主要處理線上，但其 GET 請求也展示了線下信息，線下提交可能通過獨立頁面或不同邏輯)。
        *   用戶提交充值金額、方式、付款帳戶、交易單號等信息。
        *   後端創建 `zh_member_recharge` 表記錄，狀態為待審核。
    2.  **後台審核 ([`Admin/MoneyController::rechargeDeal`](application/admin/controller/Money.php:340))**:
        *   管理員審核。
        *   若通過，更新 `zh_member_recharge` 狀態，調用 [`memberMoneyLog`](application/function.php:1228) 為用戶上帳，處理線下充值返利 ([`sendRebateForDL`](application/function.php:1140)) 和新手任務 ([`myXsrw`](application/function.php:977))。
        *   若拒絕，僅更新狀態。

#### 4.2.2. 提現流程

1.  **用戶發起 (PC/WAP 端)**:
    *   PC 端: [`Index/MembersController::withdraw()`](application/index/controller/Members.php:297)。
    *   WAP 端: [`Wap/MemberController::withdraw()`](application/wap/controller/Member.php:491)。
    *   兩端邏輯相同：驗證支付密碼、金額限制等。
    *   **開啟事務**，調用 [`memberMoneyLog`](application/function.php:1228) **凍結用戶可用餘額**，創建 `MemberWithdraw` 記錄 (status=0)。
    *   通知管理員。
2.  **後台審核 ([`Admin/MoneyController::withdrawDeal`](application/admin/controller/Money.php:48))**:
    *   **審核通過**: 更新 `MemberWithdraw` 狀態為處理中 (status=2)，調用 [`memberMoneyLog`](application/function.php:1228) **扣除凍結資金**，創建 `WithdrawalOrder`，調用 [`GuantianService`](application/common/Services/Withdraw/GuantianService.php:0) 發起代付。若代付請求失敗則回滾。
    *   **審核拒絕**: 更新 `MemberWithdraw` 狀態為失敗 (status=-1)，調用 [`memberMoneyLog`](application/function.php:1228) **返還凍結資金**。
3.  **異步回調處理 ([`Wap/WithdrawController::guantianNotify`](application/wap/controller/Withdraw.php:24))**:
    *   第三方代付平台通知。
    *   驗證簽名。
    *   若成功: 更新 `WithdrawalOrder` 和 `MemberWithdraw` 狀態為成功 (status=1)。
    *   若失敗: 更新 `WithdrawalOrder` 狀態為失敗。**注意文檔V1.2中指出的此處可能存在的資金解凍邏輯缺失問題。**

#### 4.2.3. 管理員手動轉帳 ([`Admin/MoneyController::transfer`](application/admin/controller/Money.php:722))
*   允許管理員直接向用戶的可用餘額或管理費餘額進行加款或扣款。
*   記錄到 `zh_transfer` 表，並通過 [`memberMoneyLog`](application/function.php:1228) (類型 15) 更新用戶資金。

### 4.3. 配資系統 (股票為主，期貨待確認)

*   **核心模型**: [`Stock`](application/admin/model/Stock.php:0) (對應 `zh_stock_borrow` 表)
*   **子業務模型**: [`Addfinancing`](application/admin/model/Addfinancing.php:0), [`Drawprofit`](application/admin/model/Drawprofit.php:0), [`Fill`](application/admin/model/Fill.php:0), [`Renewal`](application/admin/model/Renewal.php:0), [`Stopfinancing`](application/admin/model/Stopfinancing.php:0)。
*   **方案展示 (PC 端 [`Index/StockController`](application/index/controller/Stock.php:0))**: `trial`, `free`, `day`, `month`, `vip` 方法負責展示不同配資方案頁面，從配置加載參數。
*   **訂單確認彈窗 (PC 端 [`Index/StockController::order`](application/index/controller/Stock.php:108))**: 計算並展示費用、風控線、用戶資金是否充足。
*   **首次申請/體驗申請 (PC 端 [`Index/StockController`](application/index/controller/Stock.php:0))**:
    *   `createOrder()`: 處理普通配資方案的首次申請。
    *   `tryFinancingApply()`: 處理體驗配資申請。
    *   核心邏輯在 `applyFinancing()` 內部方法：驗證參數、檢查用戶限制、計算費用和風控線、**開啟事務**、插入 `zh_stock_borrow` 記錄 (status=0)、調用 [`updateMoneyLogWithInterest`](application/function.php:1313) **凍結保證金和利息**。
*   **配資子操作申請 (PC/WAP 端)**:
    *   用戶端 (PC [`Index/StockController`](application/index/controller/Stock.php:0) 或 WAP [`Wap/MemberController`](application/wap/controller/Member.php:0) 間接調用 PC 端) 的 `drawprofitApply`, `fillApply`, `addfinancingApply`, `renewalApply`, `stopfinancingApply` 方法負責接收用戶請求、校驗、**凍結相應資金** (如適用，例如補虧、擴大配資、續期)，並創建對應的申請記錄 (存入 `zh_stock_drawprofit` 等表，status=0)。
*   **審核處理 (後台 [`Admin/StockController`](application/admin/controller/Stock.php:0))**:
    *   各 `*Edit` 方法 (如 `sendFinancingUser`, `addfinancingEdit`, `renewalEdit`, `fillEdit`, `drawprofitEdit`, `stopfinancingEdit`) 負責處理各類申請的審核。
    *   **審核通過**: 更新申請單狀態，**扣除/轉移之前凍結的資金**，更新主配資訂單 (`zh_stock_borrow`) 的相關信息（如金額、期限、狀態），分配交易子帳戶（首次申請），觸發返利和新手任務，發送通知。
    *   **審核拒絕**: 更新申請單狀態，**返還之前凍結的資金**，發送通知。
    *   **手動結算/延期**: 提供管理員直接操作配資訂單的功能。
*   **費用計算與資金處理**: 依賴 [`application/function.php`](application/function.php:0) 中的 [`calculate_rate`](application/function.php:692), [`calculate_renewal`](application/function.php:743), [`addFinancingCount`](application/function.php:1603), [`memberMoneyLog`](application/function.php:1228), [`updateMoneyLogWithInterest`](application/function.php:1313)。
*   **自動續期**: [`api_auto_renewal`](application/function.php:597) 輔助函數。
*   **結算**: [`settlementFinancing`](application/function.php:1658) 輔助函數。

### 4.4. 新手任務與獎勵

*   通過 [`myXsrw($uid, $rw_id)`](application/function.php:977) 輔助函數實現。
*   在用戶完成特定操作後觸發，獎勵管理費到 `interest_money`。

### 4.5. 代理返利

*   通過 [`sendRebateForDL($recommender, $investor, $id, $type)`](application/function.php:1140) 和 [`Admin/StockController::sendRebate()`](application/admin/controller/Stock.php:1412) 實現。
*   返利到代理的 `account_money`。

## 5. API 設計 (初步)

*   **內部 AJAX API**: PC/WAP/Admin 各控制器中大量使用 AJAX 處理表單提交和局部刷新。
*   **第三方回調 API**:
    *   充值: `wap/recharge/checkoutNotify`
    *   代付: `wap/withdraw/guantianNotify`
*   **業務 API (供前端調用)**:
    *   PC 端 [`Index/StockController`](application/index/controller/Stock.php:0): `createOrder`, `tryFinancingApply`, `drawprofitApply`, `fillApply`, `addfinancingApply`, `renewalApply`, `stopfinancingApply`, `calculate_rate`, `calculate_renewal`。
*   **API 設計規範**: 參考 `.roorules`。

## 6. 資料庫設計概覽 (待補充詳細字段)

*   **用戶相關表**: `zh_members`, `zh_member_info`, `zh_member_money`, `zh_member_bank`, `zh_member_qd`, `zh_member_login`。
*   **資金流水與訂單**: `zh_member_moneylog`, `zh_recharge_orders`, `zh_member_recharge`, `zh_member_withdraw`, `zh_withdrawal_orders`。
*   **配資相關表**: `zh_stock_borrow`, `zh_stock_addmoney`, `zh_stock_drawprofit`, `zh_stock_addfinancing`, `zh_stock_renewal`, `zh_stock_stopfinancing`, `zh_stock_account`。
*   **系統配置與內容**: `zh_global`, `zh_stock_global`, `zh_future_global`, `zh_stock_rateset`, `zh_future_rateset`, `zh_navigation`, `zh_article`, `zh_category`, `zh_slide`, `zh_ad`, `zh_realname_set`, `zh_sms_set`, `zh_kefu`, `zh_hetong`, `zh_recharge_set`。
*   **其他**: `zh_send_log`, `zh_xsrw`, `zh_inner_msg`, `zh_tip`, `zh_verifyip`, `zh_acl`, `zh_admin_log`, `zh_transfer`。

## 7. 程式碼風格與規範

*   遵循 `.roorules`。
*   **命名**: 混合使用蛇形和駝峰。
*   **註釋**: 較少。
*   **邏輯分層**: 控制器、服務、模型、輔助函數。**輔助函數承載大量業務邏輯**。
*   **安全性**: MD5 密碼加密 (建議升級)，依賴框架防SQL注入，部分使用表單令牌。

## 8. 維護與開發建議

*   **核心理解**: [`application/function.php`](application/function.php:0), [`memberMoneyLog`](application/function.php:1228), [`updateMoneyLogWithInterest`](application/function.php:1313)。
*   **資金安全**: 嚴格審查資金操作，保證事務。
*   **配置管理**: 注意數據庫配置表的影響。
*   **第三方依賴**: 注意支付/代付接口。
*   **日誌**: 統一日誌管理。
*   **安全加固**: 升級加密，審查 XSS/CSRF，嚴格輸入驗證。
*   **重構**: 考慮將 `function.php` 中的業務邏輯按領域重構到服務類或模型。
*   **測試**: 引入單元/整合測試。

## 9. 待釐清或進一步研究的事項

*   **期貨業務邏輯**: [`Admin/FutureController.php`](application/admin/controller/Future.php:0), [`Index/FutureController.php`](application/index/controller/Future.php:0) 及相關配置和模型。
*   **用戶資金對帳機制**: 是否存在後台對帳功能。
*   **風控細節**: 警戒線、平倉線的觸發機制和處理流程，自動化風控任務。
*   **`sendsms_hx()` 函數來源**: 在後台控制器中調用，但未找到定義。
