{__NOLAYOUT__}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <title>跳转提示</title>
    <style type="text/css">
        *{ padding: 0; margin: 0; }
        body{
            font-family: "Microsoft Yahei","Helvetica Neue",Helvetica,Arial,sans-serif;
            background: #f4f0f1;
        }
        em{ font-style: normal; font-weight: 700; }
        a,a:hover{ text-decoration: none; }
        .msgbox{
            width: 625px;
            height: 205px;
            margin: 0 auto;
            margin-top: 240px;
            background: #fff;
        }
        .msgbox .msgtit{
            height: 32px;
            line-height: 32px;
            color: #fff;
            background: #009788;
            font-size: 20px;
            text-align: center;
        }
        .float_l{
            float: left;
        }
        .msgmain .msgimg{
            margin-left: 20px;
        }
        .msgmain .title,.msgmain em,.msgmain em a{
            color: #fb4848;
        }
        .msgmain em a {
            padding: 0px 5px;
        }
        .msgmain .title{
            margin-top: 32px;
            font-size: 23px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .n_b_tn{
            margin-top: 18px;
        }
        .n_b_tn a{
            background: #009788;
            padding: 6px 18px;
            color: #fff;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            border:1px solid #34a263;
        }
        .t_lv{
            color: #34a263 !important;
        }
        .zq_x{
            width: 89px;
            height: 142px;
            margin:12px 30px 0px 20px;
        }
        .zq_x i{
            display: inline-block;
            width: 89px;
            height: 142px;
        }
        .zq_x .i_x{
            background: url(/static/img/A/x.png) no-repeat;
            background-size: 100% 100%;
        }
        .zq_x .i_y{
            background: url(/static/img/A/y.png) no-repeat;
            background-size: 100% 100%;
        }
    </style>
</head>
<body>
    {php}
        /*dump( $code );
        dump( $msg );
        dump( $url );
        dump( $wait );*/
    {/php}

    <div class="msgbox">
        <div class="msgtit">提示信息</div>
        <div class="msgmain">
            <div class="msgimg float_l">
                <div class="zq_x">
                    {if condition="$code eq 0"}
                    <i class="i_x"></i>
                    {else/}
                    <i class="i_y"></i>
                    {/if}
                </div>
            </div>
            <div class="msgtxt float_l">
                {if condition="$code eq 0"}
                <div class="title">{$msg}</div>
                {else/}
                <div class="title t_lv">{$msg}</div>
                {/if}

                <div class="content">页面自动跳转-等待时间：<em id="wait">{$wait}</em>秒</div>
                <div class="n_b_tn">
                    <a href="{$url}" id="href">立即跳转</a>
                </div>
            </div>
        </div>
   </div>

<script type="text/javascript">
    (function(){
        var wait = document.getElementById('wait'),
            href = document.getElementById('href').href;
        var interval = setInterval(function(){
            var time = --wait.innerHTML;
            if(time <= 0) {
                location.href = href;
                clearInterval(interval);
            };
        }, 1000);
    })();
</script>
</body>
</html>