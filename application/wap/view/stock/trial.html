{include file="public/header" /}
<title>免费体验 - {$global.web_name}</title>
<link href="/static/wap/css/finance.css" rel="stylesheet" type="text/css">

<style type="text/css">
.layui-m-layerchild.layui-m-layer-msg {
    position: fixed !important; /* 确保相对于视窗固定定位 */
    top: 50% !important;        /* 垂直居中 */
    left: 50% !important;       /* 水平居中 */
    transform: translate(-50%, -50%) !important; /* 精确调整到中心点 */
    bottom: auto !important;    /* 取消任何可能存在的底部定位 */
    margin: 0 !important;       /* 重置可能影响定位的外边距 */
    width: 80% !important;     /* 設定為螢幕寬度的80% */
    box-sizing: border-box !important; /* 確保 padding 和 border 不影響寬度 */
}
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">免费体验</h1>
    </div>
    
    <div class="peizi_nav clearfix">
        <ul>
            <!-- <li class="active"><a>免费体验</a></li>
            <li><a href="{:url('stock/free')}">免息配资</a></li>
            <li><a href="{:url('stock/day')}">按天配资</a></li>
            <li><a href="{:url('stock/month')}">按月配资</a></li>
            <li><a href="{:url('stock/vip')}">VIP配资</a></li> -->
            <br>
        </ul>
    </div>

    <div class="weui-tab mod-products"> 
        <div class="borBox" id="experienceBox">
            <dl id="textBox">
                <dt>每个新用户只有一次体验机会<em></em></dt>
                <dd>平台出<b class="font1">{$try_set.2}</b>元<span>（完全免费）</span><em></em></dd>
                <dd>您交<b class="font1">{$try_set.0}</b>元<span>（体验费，体验结束全额退还）</span><em></em></dd>
                <dd>总计<b class="font1">{$try_set.3}</b>元<span>（由您操盘）</span><em></em></dd>
                <dd>交易<b class="font1">{$try_set.1}</b>天<span>（第二个交易日14:30前必须卖出股票）</span><em></em></dd>
                <dd>盈利全归你，超额亏损算我们<em></em></dd>
            </dl>
            <div class="tryText">
                <p>只需支付{$try_set.0}元就可以立刻获得{$try_set.3}元体验操盘帐号</p>
                <a class="btn" onclick="apply()">免费体验</a>
            </div>
        </div>  
    </div>
</div>

<script type="text/javascript">
function apply(){
    var uid = parseInt('{$uid}');
    if( !uid ){
        return layer.open({
            content: '您还未登录'
            ,btn: ['去登录', '取消']
            ,yes: function(index){
                window.location.href = '{:url("common/login")}';
            }
        });
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });

    $.ajax({
        url: '{:url("stock/trial")}',
        type: "post",
        dataType: "json",
        // data: {},
        success: function(d,e,x){
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
                ,offset: 'auto'
            });
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("member/index")}';
                        // location.reload();
                    },1500);
            }else{
                layer.close(l_index);
            }
        }
    });

}
</script>
{include file="public/footer" /}