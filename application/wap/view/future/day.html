{include file="public/header" /}
<title>按天配资 - {$global.web_name}</title>
<link href="/static/wap/css/finance.css" rel="stylesheet" type="text/css">

<style type="text/css">
.peizi_nav li{ width: 29%; }
</style>
<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">按天配资</h1>
    </div>
    
    <div class="peizi_nav clearfix">
        <ul>
            <li><a href="{:url('future/free')}">免息配资</a></li>
            <li class="active"><a >按天配资</a></li>
            <li><a href="{:url('future/month')}">按月配资</a></li>
        </ul>
    </div>

    <div class="page-box">
        <p class="wap_ddw_l">1.输入保证金</p> 
        <div class="default-width"> 
            <input id="deposit_money" class="money" type="number" placeholder="{$moneyRange.0} - {:getMoneyFormt($moneyRange.1)}" v-model="depositMoney" /> 
        </div>
        <p class="wap_ddw_l">2.选择杠杆，资金放大{$max_multiple}倍</p> 
        <div id="div_risk">
            <ul id="pz_sel" class="clearfix">
                {volist name="rateArr" id="vo"}
                <li data-rate="{$vo.rate}" data-multiple="{$vo.multiple}" class=" {eq name='key' value='0'}active{/eq} ">
                    <strong>{$vo.multiple}</strong>倍
                    <p>日利率{$vo.rate}%</p>
                </li>
                {/volist}
            </ul>
        </div>
        <p class="wap_ddw_l">3.选择操作期限</p> 
        <div class="weui-cell__bd">
            <span>期限：</span>
            <select class="mselect" name="duration" id="tradecycle" v-model="duration">
                {volist name="use_day" id="vo"}
                <option value="{$vo}">{$vo}个交易日</option>
                {/volist}
            </select>
        </div>
        <p class="wap_ddw_l">4.选择开始交易时间</p>
        <div id="div_trade_time">
            <ul class="clearfix">
                {eq name="couldApplyToday" value="2"}
                <li type="2" class="active">下个交易日</li>
                {else/}
                <li type="1" class="active">立即生效</li>
                <li type="2" >下个交易日</li>
                {/eq}
            </ul>
        </div>
        <p class="wap_ddw_l">5.确认操盘信息</p>
        <div class="wap_table" style="padding:0 10px">
            <table class="sure" width="100%" cellpadding="0" cellspacing="0">
                <tbody>
                    <tr>
                        <td class="title">总操盘资金</td>
                        <td><em id="totalAmount">0</em>元</td>
                    </tr>
                    <tr>
                        <td class="title">风险保证金</td>
                        <td><em id="bzjOK">0</em>元</td>
                    </tr>
                    <tr>
                        <td class="title">亏损警戒线</td>
                        <td><em id="warnLine">0</em>元</td>
                    </tr>
                    <tr>
                        <td class="title">亏损平仓线</td>
                        <td><em id="outLine" style="color:#FF0000;">0</em>元</td>
                    </tr>
                    <tr>
                        <td class="title">操盘周期</td>
                        <td><em id="cycle" style="color:#FF0000;">0</em>个交易日</td>
                    </tr>
                    <tr>
                        <td class="title">账户管理费</td>
                        <td><i id="accountManageFees" class="fs20">0元/日 共0元</i> </td>
                    </tr>
                </tbody>
            </table>
            <p id="demo" style="font-size:14px;color:#ff3b3b;line-height: 20px;padding: 8px 0 0 0">
                备注：您需支付的金额为保证金0元+0元（利息）= 0元
            </p>
            <div style="padding:0px 0px;">
                <div class="tc mtb10">
                    <input id="agreement" type="checkbox" checked="checked" style="position: relative;top: 2px;">
                    <label class="fs14" for="agreement">我已阅读并同意 </label>
                    <a href="{:url('common/hetong')}" class="link-blue blue fs14">《配资合作协议》</a>
                </div>
                <div style="color:#777;line-height: 26px;font-size: 14px">
                    如您不清楚规则，或有其它疑问，请联系客服
                </div>
                <div class="pdd_20" style="margin-top:10px">
                    <input type="button" class="btn btn-l" value="我要操盘" id="subBtn" onclick="apply()" @click="apply"style="width:100%;">
                </div>
            </div>
        </div> 
    </div> 

</div>

<script type="text/javascript">
var dayStockSet = {
        warnLine: {$day_loss[0]},
        closeLine: {$day_loss[1]}
    },
    type = 1,
    duration = {$use_day[0]},
    multiple = {$rateArr[0]['multiple']},
    rate = {$rateArr[0]['rate']},
    depositMoney = {$moneyRange[0]},
    gainMoney = 0,
    warnMoney = 0,
    totalRate = 0,
    closeMoney = 0,
    beishu = {$moneyRange[2]},
    borrow_money_list;
    borrow_money_list = $("#pz_sel").find("li");
// 第一步
$("#deposit_money").on('keyup',function(){
    depositMoney = isNaN(parseInt($(this).val())) ? 0 : parseInt($(this).val());

    // calc_borrow_money(depositMoney);
    updateText( depositMoney, multiple, duration, rate );
}).on('blur', function(event) {
    // console.log( $(this).val() , {$moneyRange[0]} );
    if ( $(this).val() > {$moneyRange[1]} ){
        $(this).val( {$moneyRange[1]} );    
    }
    if( $(this).val() < {$moneyRange[0]} ){
        $(this).val( {$moneyRange[0]} );    
        // calc_borrow_money( {$moneyRange[0]} );
    }

    depositMoney = parseInt( $(this).val() );
    updateText( depositMoney, multiple, duration, rate );
});
// 第二步
$("#pz_sel li").on("click",function(){
    $(this).addClass("active").siblings().removeClass('active');
    //倍率
    multiple = $(this).data("multiple");
    //天利率
    rate = parseFloat( $(this).data("rate") );
    updateText(depositMoney, multiple, duration, rate);
})
// 第三步
$("#tradecycle").on("change",function(){
    duration = parseInt( $(this).val() );
    updateText(depositMoney, multiple, duration, rate);
})
// 第四步
$("#div_trade_time li").click(function(){
    $("#div_trade_time li").removeClass("active");
    $(this).addClass("active");            
})
// 1.保证金 2.倍数 3.期限 4.利率
function updateText(depositMoney, multiple, duration, rate) {
    // console.log(depositMoney, multiple, duration, rate);
    if( !$("#deposit_money").val() ){ return; }
    // 配资金额计算
    gainMoney = depositMoney * multiple;
    // 警戒线 = 配资金额 + 保证金 * 警告线率
    warnMoney = gainMoney + (depositMoney * dayStockSet.warnLine / 100);
    // 平仓线 = 配资金额 + 保证金 * 平仓线率
    closeMoney = gainMoney + (depositMoney * dayStockSet.closeLine / 100);

    // 利息计算
    // 总利息 = 配资金额 * 利率(转成百分比) * 天数
    totalRate = (gainMoney * (rate / 100) * duration).toFixed(2);
    // 每天利息 = 配资金额 * 利率(转成百分比)
    oneRate = (gainMoney * (rate / 100)).toFixed(2);

    var zhglf = oneRate+'元/日 共'+totalRate+'元';// 账户管理费/日
    $("#accountManageFees").text(zhglf);

    $("#totalAmount").text( depositMoney+gainMoney );//总配资资金

    $('#bzjOK').text(depositMoney);//保证金
    $("#warnLine").text(warnMoney);
    $("#outLine").text(closeMoney);
    $("#cycle").text(duration);//周期

    var beizhu = '备注：您需支付的金额为保证金'+depositMoney+'元+'+totalRate+'元（利息）= '+(parseFloat(depositMoney)+parseFloat(totalRate))+'元';
    $("#demo").text(beizhu);
}

function apply(){
    var uid = parseInt('{$uid}');
    if( !uid ){
        return layer.open({
            content: '您还未登录'
            ,btn: ['去登录', '取消']
            ,yes: function(index){
                window.location.href = '{:url("common/login")}';
            }
        });
    }
    if( !$('#deposit_money').val() ){
        return layer.open({
                content: '请输入保证金'
                ,skin: 'msg'
                ,time: 3
            });
    }
    if( (depositMoney%beishu) != 0 ){
        return layer.open({
                content: '您的保证金金额必须是'+beishu+'的整数倍'
                ,skin: 'msg'
                ,time: 3
            });
    }
    if( !$("#agreement").is(":checked") ){
        return layer.open({
                content: '请阅读并同意配资协议'
                ,skin: 'msg'
                ,time: 3
            });
    }
    $('#div_trade_time ul li').each(function(index, val) {
        if( $(this).hasClass('active') ){
            startDate = parseInt($(this).attr('type'));
        }
    });

    var confirm = layer.open({
            content: '已确认操盘信息，提交申请？'
            ,btn: ['确定提交', '返回修改']
            ,yes: function(index){
                layer.close(confirm);
                var l_index = layer.open({
                    type: 2,
                    shadeClose: false
                    // ,content: '提交中'
                });
                $.ajax({
                    url: '{:url("future/createOrder")}',
                    type: "post",
                    dataType: "json",
                    data: {
                        'money': gainMoney,//配资金额
                        'depositMoney': depositMoney,
                        'multiple': multiple,
                        'duration': duration,
                        "type": type,
                        "startDate": startDate
                    },
                    success: function(d,e,x){
                        layer.open({
                            content: d.message
                            ,skin: 'msg'
                            ,time: 3
                        });
                        if(d.status==1){
                            var t1 = setTimeout(function(){
                                    window.location.href = '{:url("member/index")}';
                                    // location.reload();
                                },1500);
                        }else{
                            layer.close(l_index);
                        }
                    }
                });
            }
        });
}


/*function calc_borrow_money( deposit_money ){
    borrow_money_list.each(function(item){
        // console.log(item);return
        var itemMultiple = parseInt( $(this).data("multiple") );
        $(this).find(".times").text( itemMultiple*deposit_money );
  
        if((itemMultiple * deposit_money) < 10){
            $(this).find(".times").html(0);
        }
    })
}*/
</script>
{include file="public/footer" /}