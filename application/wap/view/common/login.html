<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>登录 - {$global.web_name}</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">

    <link rel="stylesheet" href="/static/wap/weui/weui.css"/>
    <link rel="stylesheet" href="/static/wap/css/common.css"/>
    <link rel="stylesheet" href="/static/wap/css/log-reg.css"/>

    <script type="text/javascript" src="/static/wap/js/neizhi.js"></script>
    <script type="text/javascript" src="/static/js/jquery183.js"></script>   
    <script type="text/javascript" src="/static/wap/js/common.js"></script>
    <script type="text/javascript" src="/static/wap/weui/weui.min.js"></script>

    <script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
</head>
<body class="page-with-footbar">
    <div id="app">
        <div class="v-header">
            <div class="v-header-left">
                <div class="left-arrow">返回</div>
            </div>
            <h1 class="v-header-title">登录</h1>
            <div class="v-header-right">
                <div class="right-arrow">
                    <a href="{:url('/index')}">首页</a>
                </div>
            </div>
        </div>

        <div class="l-header">
            {:get_ad(30)}
        </div>

        <div class="wecell_login">
            <div class="weui-cell">
                <div class="weui-cell__bd">
                    <input type="text" placeholder="请输入用户名" v-model="username" class="weui-input username s1" maxlength="16">
                </div>
            </div>

            <div class="weui-cell">
                <div class="weui-cell__bd">
                    <input type="password" placeholder="请输入6-16位密码" v-model="password" class="weui-input password s2" maxlength="16">
                </div>
            </div>

            <div class="weui-flex log-botton">
                <div class="weui-flex__item">
                    <div class="btn-wrapper">
                        <a href="javascript:;" onclick="login()" class="weui-btn weui-btn_primary" @click="login">登录</a>
                    </div>
                </div>
            </div>

            <div class="weui-cells__title cell-links">
                <a href="{:url('common/register')}" class="fl">没有账号？立即注册</a>
                <a href="{:url('common/findpwd')}" class="fr">忘记密码？</a>
            </div>
        </div>

    </div>
</body>
<script type="text/javascript">
function login(){
    var username = $('.username').val();
    var password = $('.password').val();
    /*var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(username)) ){
        return layer.open({
            content: "手机号码有误，请重新输入"
            ,skin: 'msg'
            ,time: 2
        });
    }*/

    if( username.length<2 || username.length>16 ){
        return layer.open({
            content: "用户名不得小于2位或者大于16位"
            ,skin: 'msg'
            ,time: 2
        });
    }
    if( password.length<6 || password.length>16 ){
        return layer.open({
            content: "密码不得小于6位或者大于16位"
            ,skin: 'msg'
            ,time: 2
        });
    }
    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });

    $.ajax({
        url: '{:url("common/login")}',
        type: "post",
        dataType: "json",
        data: {username:username,password:password},
        success: function(d,e,x){
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 2
            });
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("member/index")}';
                        // location.reload();
                    },1500);
                return;
            }else{
                layer.close(l_index);
            }
        }
    });
}

</script>
</html>