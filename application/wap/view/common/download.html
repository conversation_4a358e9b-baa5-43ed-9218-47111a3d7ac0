{include file="public/header" /}
<title>APP下载 - {$global.web_name}</title>

<style type="text/css">
body{ background-color: #fff; }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">APP下载</h1>
    </div>

    <div class="app_hd">
        <h2 >下载配资APP</h2>
        
        <p>第一种方式：（如果在浏览器中打开此页面，点击下面按钮下载）</p>
    
        <a href="{$global.wapapp}" class="adown_btn">点击下载APP</a>

        <p>第二种方式：（如果在微信打开此页面，复制链接在浏览器中打开下载）</p>
        
        <div class="inputBox df" style="margin: 10px auto;">
            <input type="" name="" id="inp_url" value="{$global.wapapp}">                
            <button type="button" id="btn_down" class="df_1">复制链接</button>
        </div>

        <p>第三种方式：（扫描二维码或截图保存下载网站APP）</p>

        <div class="app_down_rwm" style="margin: 10px auto;">
            {:get_ad(18)}
        </div>

    </div>

    <div class="app_hd">
        <h2 >下载交易APP</h2>
        <!-- <p style="text-align: center;"><img src="/static/wap/img/app_text.png" width="70%"></p> -->
        <p>第一种方式：（如果在浏览器中打开此页面，点击下面按钮下载）</p>
        
        <a href="{$global.jyrjapp}" class="adown_btn">点击下载APP</a>

        <p>第二种方式：（如果在微信打开此页面，复制链接在浏览器中打开下载）</p>
        
        <div class="inputBox df" style="margin: 10px auto;">
            <input type="" name="" id="inp_url1" value="{$global.jyrjapp}">                
            <button type="button" id="btn_down1" class="df_1">复制链接</button>
        </div>

        <p>第三种方式：（扫描二维码或截图保存下载交易APP）</p>

        <div class="app_down_rwm" style="margin: 10px auto;">
            {:get_ad(19)}
        </div>     
        
        
    </div>


   


</div>


<script type="text/javascript" src="/static/js/clipboard.min.js"></script>
<script type="text/javascript">
$(function(){
    var text = $("#inp_url").val();
    var clipboard = new Clipboard('#btn_down', {
        text: function() {
            return text;
        }
    });
    clipboard.on('success', function(e) {
        layer.open({
            content: '复制成功'
            ,skin: 'msg'
            ,time: 3
        });
    });
})
$(function(){
    var text = $("#inp_url1").val();
    var clipboard = new Clipboard('#btn_down1', {
        text: function() {
            return text;
        }
    });
    clipboard.on('success', function(e) {
        layer.open({
            content: '复制成功'
            ,skin: 'msg'
            ,time: 3
        });
    });
})
</script>
{include file="public/footer" /}