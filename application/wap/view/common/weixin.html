{include file="public/header" /}
<title>微信客服 - {$global.web_name}</title>


<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">微信客服</h1>
    </div>

    <div class="app_hd">
        <h2 >微信客服</h2>
        
        <p>第一种方式：（如果在微信打开此页面，复制账号添加微信客服）</p>
        <div class="inputBox df" style="margin: 10px auto;">
            <input type="" name="" id="inp_url" value="{$global.wx_chat}">                
            <button type="button" id="btn_down" class="df_1">复制链接</button>
        </div>
        <p>第二种方式：（扫描二维码或截图保存添加微信客服）</p>

        <div class="app_down_rwm" style="margin: 10px auto;">
            {:get_ad(24)}
        </div>

    </div>

   

</div>


<script type="text/javascript" src="/static/js/clipboard.min.js"></script>
<script type="text/javascript">
$(function(){
    var text = $("#inp_url").val();
    var clipboard = new Clipboard('#btn_down', {
        text: function() {
            return text;
        }
    });
    clipboard.on('success', function(e) {
        layer.open({
            content: '复制成功'
            ,skin: 'msg'
            ,time: 3
        });
    });
})

</script>
{include file="public/footer" /}