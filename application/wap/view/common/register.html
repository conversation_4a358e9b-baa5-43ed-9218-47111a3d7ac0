<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>注册 - {$global.web_name}</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">

    <link rel="stylesheet" href="/static/wap/weui/weui.css"/>
    <link rel="stylesheet" href="/static/wap/css/common.css"/>
    <link rel="stylesheet" href="/static/wap/css/log-reg.css"/>

    <script type="text/javascript" src="/static/wap/js/neizhi.js"></script>
    <script type="text/javascript" src="/static/js/jquery183.js"></script>   
    <script type="text/javascript" src="/static/wap/js/common.js"></script>
    <script type="text/javascript" src="/static/wap/weui/weui.min.js"></script>

    <script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
</head>
<body class="page-with-footbar">
    <div id="app">
        <div class="v-header">
            <div class="v-header-left">
                <div class="left-arrow">返回</div>
            </div>
            <h1 class="v-header-title">注册</h1>
            <div class="v-header-right">
                <div class="right-arrow">
                    <a href="{:url('/index')}">首页</a>
                </div>
            </div>
        </div>

        <div class="l-header">
            {:get_ad(30)}
        </div>
        
        <div class="wecell_login wecell_reg">
            <div class="weui-cell">
                <div class="weui-cell__bd">
                    <input id='username' maxlength="11" type="text" placeholder="请输入11位中国大陆手机号" v-model="cellphone" class="weui-input s3">
                </div>
            </div>       
            <div class="weui-cell weui-cell_vcode">          
                <div class="weui-cell__bd">
                    <input id='vcode' class="weui-input s4" type="text" v-model="vcode" maxlength="6" placeholder="请输入验证码">
                </div>
                <div class="weui-cell__ft">
                    <button class="weui-vcode-btn" id='sendSMS' @click="sendCode" v-html="vCodeTxt">短信验证码</button>
                </div>
            </div>
            <div class="weui-cell">
                <div class="weui-cell__bd">
                    <input id='pass' type="password" maxlength="16" placeholder="请输入6-16位密码" v-model="password" class="weui-input s2">
                </div>
            </div>
            <div class="weui-cell">
                <div class="weui-cell__bd">
                    <input id='pass2' type="password" maxlength="16" placeholder="确认密码" v-model="repassword" class="weui-input s2">
                </div>
            </div>
            <div class="weui-cell">
                <div class="weui-cell__bd">
                    {if condition='$invite'}
                    <input id='recommend' value="{$invite}" readonly="" maxlength="11" type="text" placeholder="推荐人，没有可以不填" v-model="invite" class="weui-input s5">
                    {else /}
                    <input id='recommend' maxlength="11" type="text" placeholder="推荐人，没有可以不填" v-model="invite" class="weui-input s5">
                    {/if}
                </div>
            </div> 
            <div class="weui-flex log-botton">
                <div class="weui-flex__item">
                    <div class="btn-wrapper">
                        <a class="weui-btn weui-btn_primary" onclick="reg()" @click="register">立即注册</a>
                    </div>
                </div>
            </div> 
            <div class="weui-cells__title cell-links">        
                <a href="{:url('common/login')}" class="fr">已有账号，马上登录！</a>
            </div>
        </div>


    </div>
</body>
<script type="text/javascript">
function reg(){
    var username = $('#username').val();
    var vcode = $('#vcode').val();
    var pass = $('#pass').val();
    var pass2 = $('#pass2').val();
    var recommend = $('#recommend').val();

    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(username)) ){
        return layer.open({
            content: "手机号码有误，请重新输入"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( !vcode ){
        return layer.open({
            content: "短信验证码不能为空"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( pass.length<6 || pass.length>16 ){
        return layer.open({
            content: "密码不得小于6位或者大于16位"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( pass != pass2 ){
        return layer.open({
            content: "两次密码输入不一致"
            ,skin: 'msg'
            ,time: 3
        });
    }
    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });

    // console.log(username,vcode,recommend);
    $.ajax({
        url: '{:url("common/register")}',
        type: "post",
        dataType: "json",
        data: {uname:username,vcode:vcode,pass:pass,recommend:recommend},
        success: function(d,e,x){  
            if(d.status==1){
                layer.open({
                    content: d.message
                    ,skin: 'msg'
                    ,time: 3
                });
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("member/index")}';
                    },1500);
            }else{
                layer.close(l_index);
                layer.open({
                    content: d.message
                    ,skin: 'msg'
                    ,time: 3
                });
            }
        }
    });
}



var txtObj = $('#sendSMS');//文字DOM Obj
var InterValObj; //timer变量，控制时间 
var count = 90; //间隔函数，1秒执行 
var curCount;//当前剩余秒数
function myInterval() {
    curCount = count;
    //设置button效果，开始计时
    txtObj.text(curCount + "秒后重新发送"); 
    InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次 
} 
function SetRemainTime() {
    if (curCount == 0) {         
        window.clearInterval(InterValObj);//停止计时器 
        //启用按钮
        txtObj.removeAttr("disabled");
        txtObj.text("重新发送"); 
    }else{
        curCount--;
        txtObj.text(curCount + "秒后重新发送"); 
    } 
}
$('#sendSMS').on('click', function(event) {
    var phone = $('#username').val();
    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(phone)) ){
        return layer.open({
            content: "手机号码有误，请重新输入"
            ,skin: 'msg'
            ,time: 3
        }); 
    } 
    // console.log(phone);
    var l_index = layer.open({
        type: 2,
        shadeClose: false
        ,content: '发送中'
    });
    $.ajax({
        url: '{:url("common/sendSMS")}',
        type: "post",
        dataType: "json",
        data: {phone:phone,source:'register'},
        beforeSend:function(){
            txtObj.text('验证码发送中').attr("disabled",true);
        },
        success: function(d,e,x){  
            layer.close(l_index);
            if(d.status==1){
                myInterval();

                layer.open({
                    content: d.message
                    ,skin: 'msg'
                    ,time: 3
                }); 
            }else{
                //启用按钮
                txtObj.text('重新发送').removeAttr("disabled");
                
                layer.open({
                    content: d.message
                    ,skin: 'msg'
                    ,time: 3
                }); 
            }
        }
    });
});
</script>
</html>