{include file="public/header" /}
<title>重置密码 - {$global.web_name}</title>
<link rel="stylesheet" href="/static/wap/css/log-reg.css"/>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">重置密码</h1>
    </div>

    <div class="wecell_login wecell_reg" style="margin-top:40px;">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input type="password" id="pass" placeholder="新密码" v-model="password" class="weui-input s2">
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input type="password" id="pass2" placeholder="确认新密码" v-model="repassword" class="weui-input s2">
            </div>
        </div>      
        <div class="weui-flex log-botton">
            <div class="weui-flex__item">
                <div class="btn-wrapper">
                    <a href="javascript:;" onclick="sub()" class="weui-btn weui-btn_primary">重置密码</a>
                </div>
            </div>
        </div>       
    </div>

</div>

<script type="text/javascript">
function sub(){
    var pass = $('#pass').val();
    var pass2 = $('#pass2').val();

    if( pass.length<6 || pass.length>16 ){
        return layer.open({
            content: "密码不得小于6位或者大于16位"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( pass != pass2 ){
        return layer.open({
            content: "两次密码输入不一致"
            ,skin: 'msg'
            ,time: 3
        });
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });
    $.ajax({
        url: '{:url("common/findpwd2")}',
        type: "post",
        dataType: "json",
        data: {pass:pass,pass2:pass2},
        success: function(d,e,x){
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
            });
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("common/login")}';
                    },1500);
            }else if(d.status==-1){
                
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("common/findpwd")}';
                    },1500);
            }else{
                layer.close(l_index);
            }
        }
    });
}

</script>