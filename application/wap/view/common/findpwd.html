{include file="public/header" /}
<title>找回密码 - {$global.web_name}</title>
<link rel="stylesheet" href="/static/wap/css/log-reg.css"/>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">找回密码</h1>
    </div>

    <div class="wecell_login wecell_reg" style="margin-top:40px;">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <input type="text" id="phone" placeholder="请输入绑定的手机号码" maxlength="11" v-model="cellphone" class="weui-input s3">
            </div>
        </div>       
        <div class="weui-cell weui-cell_vcode">          
            <div class="weui-cell__bd">
                <input class="weui-input s4" type="text" id="vcode" maxlength="6" v-model="vcode" minlength="6" maxlength="6" placeholder="短信验证码" autocomplete="off">
            </div>
            <div class="weui-cell__ft">
                <button class="weui-vcode-btn" id='sendSMS' @click="sendCode" v-html="vCodeTxt">短信验证码</button>
            </div>
        </div>      
        <div class="weui-flex log-botton">
            <div class="weui-flex__item">
                <div class="btn-wrapper">
                    <a class="weui-btn weui-btn_primary" onclick="next()" @click="">下一步</a>
                </div>
            </div>
        </div>       
    </div>

</div>

<script type="text/javascript">
function next(){
    var phone = $('#phone').val();
    var vcode = $('#vcode').val();

    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(phone)) ){
        return layer.open({
            content: "手机号码有误，请重新输入"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( !vcode ){
        return layer.open({
            content: "短信验证码不能为空"
            ,skin: 'msg'
            ,time: 3
        });
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });
    $.ajax({
        url: '{:url("common/findpwd")}',
        type: "post",
        dataType: "json",
        data: {phone:phone,vcode:vcode},
        success: function(d,e,x){
            if(d.status==1){
                window.location.href = '{:url("common/findpwd2")}';
                
                // var t1 = setTimeout(function(){
                //         window.location.href = '{:url("member/index")}';
                //     },1500);
            }else{
                layer.close(l_index);
                layer.open({
                    content: d.message
                    ,skin: 'msg'
                    ,time: 3
                });
            }
        }
    });
}


var txtObj = $('#sendSMS');//文字DOM Obj
var InterValObj; //timer变量，控制时间 
var count = 90; //间隔函数，1秒执行 
var curCount;//当前剩余秒数
function myInterval() {
    curCount = count;
    //设置button效果，开始计时
    txtObj.text(curCount + "秒后重新发送"); 
    InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次 
} 
function SetRemainTime() {
    if (curCount == 0) {         
        window.clearInterval(InterValObj);//停止计时器 
        //启用按钮
        txtObj.removeAttr("disabled");
        txtObj.text("重新发送"); 
    }else{
        curCount--;
        txtObj.text(curCount + "秒后重新发送"); 
    } 
}
$('#sendSMS').on('click', function(event) {
    var phone = $('#phone').val();
    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(phone)) ){
        return layer.open({
            content: "手机号码有误，请重新输入"
            ,skin: 'msg'
            ,time: 3
        }); 
    } 
    // console.log(phone);
    var l_index = layer.open({
        type: 2,
        shadeClose: false
        ,content: '发送中'
    });
    $.ajax({
        url: '{:url("common/sendSMS")}',
        type: "post",
        dataType: "json",
        data: {phone:phone,source:'findpwd'},
        beforeSend:function(){
            txtObj.text('验证码发送中').attr("disabled",true);
        },
        success: function(d,e,x){  
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
            }); 
            layer.close(l_index);
            if(d.status==1){
                
                myInterval();
            }else{
                //启用按钮
                txtObj.text('重新发送').removeAttr("disabled");
            }
        }
    });
});
</script>