{include file="public/header" /}
<title>{$global.index_title} - {$global.web_name}</title>
<link href="/static/wap/css/index.css" rel="stylesheet" type="text/css">

<link href="/static/wap/weui/swiper.min.css" rel="stylesheet">
<script src="/static/wap/weui/swiper.min.js"></script>

<!-- 股市數據API暫時禁用，避免載入錯誤 -->
<!-- <script type="text/javascript" src="//hq.sinajs.cn/rn=1461855885572&list=s_sh000001,s_sz399001,s_sz399006"></script> -->
<script src="/static/wap/js/vue.min.js"></script>

<style type="text/css">
.layui-m-layercont{ padding: 12px;text-align: unset; }
.layui-m-layerbtn{ height: 38px; line-height: 38px; }
</style>

<div class="header">
      <img src="{$global.logo}" alt="{$global.web_name}logo">

    {if condition="$uid GT 0"}

    {else /}
    <div class="login"><a class="white" href="{:url('common/login')}">登录</a></div>
    <div class="register"><a class="white" href="{:url('common/register')}">注册</a></div>
    {/if}
</div>

<div class="header-height"></div>

<div class="swiper-container swiper-container-banner">
    <div class="swiper-wrapper">
        {volist name="banner" id="vo"}
        <div class="swiper-slide">
          <a href="{$vo.link}"><img src="{$vo.image}"></a>
        </div>
        {/volist}
    </div>

    <div class="swiper-pagination"></div>
</div>

<div class="notice clearfix">
    <div class="fl notice-icon">
          <img src="/static/wap/img/laba.png" width="18"/>
    </div>
    <div class="ggScrolltop">
        <ul class="ggmain" style="">
            {volist name="noticeList" id="vo"}
            <li>
                <a href="{:url('cms/detial',['aid'=>$vo.id])}" class="fl primary">
                    {$vo.title}
                </a>
                <span class="fr primary">{:date('Y-m-d',$vo.add_time)}</span>
            </li>
            {/volist}
        </ul>
    </div>
</div>

<!-- menu -->
<div class="weui-flex project-con"> 
    <div class="weui-flex__item">
        <div class="item-wrapper">
            <a href="{:url('xsrw/index')}">
                <img src="/static/wap/img/project-1.png">
                <p>新手任务</p> 
            </a>
        </div>
    </div> 
    <div class="weui-flex__item">
        <div class="item-wrapper">
            <a href="{:url('cms/help')}">
                <img src="/static/wap/img/project-2.png">
                <p>帮助中心</p> 
            </a>
        </div>
    </div>
    <div class="weui-flex__item">
        <div class="item-wrapper">
            <a href="{:url('cms/about')}">
                <img src="/static/wap/img/project-3.png">
                <p>关于我们</p> 
            </a>
        </div>
    </div>
    <div class="weui-flex__item">
        <div class="item-wrapper">
            <a href="{:url('member/invite')}">
                <img src="/static/wap/img/project-4.png">
                <p>我要邀请</p> 
            </a>
        </div>
    </div>
    <div class="weui-flex__item">
        <div class="item-wrapper">
            <a href="{:url('common/download')}">
                <img src="/static/wap/img/project-5.png">
                <p>APP下载</p> 
            </a>
        </div>
    </div>      
</div>

<!-- 产品 -->
<div class="products">
    <div class="weui-tab mod-products" id="product-tab">
        <div class="weui-navbar">
            <div class="weui-navbar__item">免费体验</div>
            <div class="weui-navbar__item">免息配资</div>
            <div class="weui-navbar__item">按天配资</div>
            <div class="weui-navbar__item">按月配资</div>
            <!-- <div class="weui-navbar__item">VIP配资</div> -->
        </div>
        <div class="weui-tab__panel">
            <div class="weui-tab__content mod-product-item">
                <div class="item">
                    <!-- <i class="tj">推荐</i> -->
                    <div class="item-top"> 
                        <p class="top-l">您出<span>{$try_set.0}</span>元</p>
                        <p class="top-r">平台出<span>{$try_set.2}</span>元</p> 
                    </div>
                    <div class="mod"></div>
                    <div class="item-bot">
                        <p>免费操盘<span>{$try_set.1}</span>天</p>
                        <p>盈利全归你，亏损算我们</p>
                    </div>
                </div>
                <div class="item-btn">
                    <a href="{:url('stock/trial')}" class="weui-btn weui-btn_primary" href="">立即申请</a>
                </div> 
            </div>
            <div class="weui-tab__content mod-product-item">
                <div class="item">
                    <div class="item-top"> 
                        <p class="top-l">资金<span>{$free_money_range.0}</span>元起</p>
                        <p class="top-r">倍数：最高<span>{$free_set.0}</span>倍</p> 
                    </div>
                    <div class="mod"></div>
                    <div class="item-bot">
                        <p>极速开户，最高操盘<span>{:getMoneyFormt($free_money_range.1)}</span>，操盘20天</p>
                        <p>盈利<span>{$free_set.2}%</span>全归您，平台不收取利息费用</p>
                    </div>
                </div>
                <div class="item-btn">
                    <a href="{:url('stock/free')}" class="weui-btn weui-btn_primary" href="">立即申请</a>
                </div>
            </div>
            <div class="weui-tab__content mod-product-item">
                <div class="item">
                    <div class="item-top"> 
                        <p class="top-l">资金<span>{$day_money_range.0}</span>元起</p>
                        <p class="top-r">倍数：最高<span>{$day_max_multiple}</span>倍</p> 
                    </div>
                    <div class="mod"></div>
                    <div class="item-bot">
                        <p>利息用几天算几天，短线投资更便捷</p>
                        <p><span>{$day_money_range.0}</span>元起配，最高<span>{$day_max_multiple}</span>倍配资杠杆</p>
                    </div>
                </div>
                <div class="item-btn">
                    <a href="{:url('stock/day')}" class="weui-btn weui-btn_primary" href="">立即申请</a>
                </div>
            </div>
            <div class="weui-tab__content mod-product-item">
                <div class="item">
                    <i class="tj">推荐</i>
                    <div class="item-top"> 
                        <p class="top-l">资金<span>{$month_money_range.0}</span>元起</p>
                        <p class="top-r">倍数：最高<span>{$month_max_multiple}</span>倍</p> 
                    </div>
                    <div class="mod"></div>
                    <div class="item-bot">
                        <p>长线配资选择按月</p>
                        <p>利息低至<span>0.6%</span>，操盘金额 <span>{$month_money_range.0} - {:getMoneyFormt($month_money_range.1)}</span></p>
                    </div>
                </div>
                <div class="item-btn">
                    <a href="{:url('stock/month')}" class="weui-btn weui-btn_primary" href="">立即申请</a>
                </div>
            </div>
           <!--  <div class="weui-tab__content mod-product-item">
                <div class="item">
                    <i class="tj">推荐</i>
                    <div class="item-top"> 
                        <p class="top-l">资金<span>{$vip_money_range.0}</span>元起</p>
                        <p class="top-r">倍数：最高<span>{$vip_max_multiple}</span>倍</p> 
                    </div>
                    <div class="mod"></div>
                    <div class="item-bot">
                        <p>大额配资，长线配资更划算</p>
                        <p>利息低至<span>0.5%</span>，操盘金额 <span>{$vip_money_range.0} - {:getMoneyFormt($vip_money_range.1)}</span></p>
                    </div>
                </div>
                <div class="item-btn">
                    <a href="{:url('stock/vip')}" class="weui-btn weui-btn_primary" href="">立即申请</a>
                </div>
            </div> -->

        </div>
    </div>
</div>


<div class="stock-data" id="stock-data" v-cloak>
    <div class="hd">
        <i class="icon-prefixer"></i>
        <span>国内指数</span>
    </div>
    <div class="weui-flex bd">
        <div class="weui-flex__item stock-data__item">
            <div class="flex-item-wrapper">
                <div class="title">{{sh[0]}}</div>
                <div class="stock-number " :class="sh[2] > 0 ? 'number-red' : 'number-green'">
                    {{sh[1] | doubleval}}
                </div>
                <div class="meta">
                    <span class="point" :class="sh[2] > 0 ? 'number-red' : 'number-green'">{{sh[2] | doubleval}}</span>
                    &nbsp;
                    <span class="percent-red" :class="sh[2] > 0 ? 'number-red' : 'number-green'">{{sh[3]}}%</span>
                </div>
            </div>
        </div>
        <div class="weui-flex__item stock-data__item">
            <div class="flex-item-wrapper">
                <div class="title">{{sz[0]}}</div>
                <div class="stock-number number-xxl" :class="sz[2] > 0 ? 'number-red' : 'number-green'">
                    {{sz[1] | doubleval}}
                </div>
                <div class="meta">
                    <span class="point" :class="sz[2] > 0 ? 'number-red' : 'number-green'">{{sz[2] | doubleval}}</span>
                    &nbsp;
                    <span class="percent" :class="sz[2] > 0 ? 'number-red' : 'number-green'">{{sz[3]}}%</span>
                </div>
            </div>
        </div>
        <div class="weui-flex__item stock-data__item">
            <div class="flex-item-wrapper">
                <div class="title">{{cy[0]}}</div>
                <div class="stock-number number-xxl" :class="cy[2] > 0 ? 'number-red' : 'number-green'">
                    {{cy[1] | doubleval}}
                </div>
                <div class="meta">
                    <span class="point" :class="cy[2] > 0 ? 'number-red' : 'number-green'">{{cy[2] | doubleval}}</span>
                    &nbsp;
                    <span class="percent" :class="cy[2] > 0 ? 'number-red' : 'number-green'">{{cy[3]}}%</span>
                </div>
            </div>
        </div>
    </div>
</div>



<style type="text/css">
.layui-m-layer0 .layui-m-layerchild{
    width: 90%;
}
.layui-m-layer-tc_pic .layui-m-layercont{
    padding: 0;
}
.layui-m-layer-tc_pic .layui-m-layercont img{
    width: 100%;
}
.layui-m-layer-tc_pic .closed{
    position: absolute;
    right: -20px;
    top: -20px;
}
.layui-m-layer-tc_pic .closed img{
    width: 34px;
    height: 29px;
}
</style>
<!-- 文字弹窗模式 -->
<div id="web_show_txt" style="display:none;">{:get_ad(16)}</div>
<!-- 图片弹窗模式 -->
<div id="web_show_pic" style="display:none;"><div class="closed"  onclick='t()'><img src="/static/img/H/extend/closed.png"></div>{:get_ad(16)}</div>
<script type="text/javascript">
//banner
$(function(){
    var swiper = new Swiper('.swiper-container-banner', {
        pagination: '.swiper-pagination',
        paginationClickable: true,
        speed: 2000,
        loop: true,
        observer:true,
        observeParents:true,
        autoplayDisableOnInteraction : false,
        autoplay: 3000
    });

    // 首页文字弹框
    var web_show = "{$global.web_show}";
    var web_show_txt = $('#web_show_txt').html();
    if( web_show==1 ){
        layer.open({
            content: web_show_txt
            ,btn: '我知道了'
        });
    }
    //首页图片弹框
    var web_show_pic = $('#web_show_pic').html();
    if( web_show==2 ){
        layer.open({
            skin: 'tc_pic'
            ,btn: '我知道了'
            ,content: web_show_pic
        });
    }

    setInterval('AutoScroll(".ggScrolltop")', 3000);
})
function t(){
    $('#layui-m-layer0').hide(288);
}
//banner page
new Swiper('.swiper-container-banner', {
    pagination: '.swiper-pagination',
    paginationClickable: true
});
function AutoScroll(obj) { 
    $(obj).find("ul:first").animate({ 
        marginTop: "-33px"
    }, 800, function () { 
        $(this).css({ marginTop: "0px" }).find("li:first").appendTo(this); 
    });
}

// product   
weui.tab('#product-tab',{
    defaultIndex: 0
});

// 股市數據暫時使用模擬數據，避免API載入錯誤
new Vue({
   el : "#stock-data",
   data: {
       // 模擬股市數據，避免API載入失敗
       mockData: {
           sh: ['上證指數', '3000.00', '10.50', '0.35'],
           sz: ['深證成指', '11000.00', '50.20', '0.46'],
           cy: ['創業板指', '2200.00', '15.30', '0.70']
       }
   },
   computed : {
       sh : function(){
           // 檢查API數據是否可用，否則使用模擬數據
           if (typeof hq_str_s_sh000001 !== 'undefined') {
               return hq_str_s_sh000001.split(',');
           }
           return this.mockData.sh;
       },
       sz : function(){
           if (typeof hq_str_s_sz399001 !== 'undefined') {
               return hq_str_s_sz399001.split(',');
           }
           return this.mockData.sz;
       },
       cy : function(){
           if (typeof hq_str_s_sz399006 !== 'undefined') {
               return hq_str_s_sz399006.split(',');
           }
           return this.mockData.cy;
       }
   },
   filters : {
       doubleval : function (val) {
           return parseFloat(val).toFixed(2);
       }
   }
})
</script>
{include file="public/footer" /}