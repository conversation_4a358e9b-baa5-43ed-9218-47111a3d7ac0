{include file="public/header" /}
<title>微信扫码 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<style type="text/css">
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">微信扫码</h1>
    </div>

    <!-- 实名认证？ -->
    {eq name="$minfo.id_status" value="1"}

        <div id="panel">
            <p class="tx_ts">请先成功付款后再“填写付款信息”确认提交</p>

            <ul class="account">
                {if condition="$weixin.status EQ 1"}
                <li>
                  <p style="text-align:center;text-indent: 0px">
                    <img src="{$weixin.qrcode}" width="160">
                  </p>
                  <p style="text-align:center;text-indent: 0px;font-size:12px;color:#999">截屏保存二维码到本地相册</p>
                </li>
                {/if}
                {if condition="$weixin.payee EQ 1"}
                <li>
                    <p>微信账号</p>
                    <input id="wxacccode" value="{$weixin.number}" type="text" readonly>
                    <button id="copy_holder" class="btncopy">复制</button>
                </li>
                {/if}
                <li>
                  <i class="c_9">充值说明：</i>
                  <div class="content">
                        {:htmlspecialchars_decode($weixin.content)}
                    </div>
                </li>
            </ul>

            <p class="tx_ts">填写付款信息</p>
            <ul class="list_k">
                <li>
                    <p>转账金额</p>
                    <input id="money" type="text" placeholder="请输入转账金额"/>
                </li>
                <li>
                    <p>微信昵称</p>
                    <input id="account" type="text" placeholder="请输入微信昵称"/>
                </li>
                <!--<li>
                    <p>订单号</p>
                    <input id="tran_id" type="text" placeholder="请输入订单号"/>
                </li>-->
            </ul>

            <div id="checksubmit" class="btn_primary mt20" style="width:90%;" onclick="sub()">确认提交</div>

            <div class="clear"></div>
            <div class="tishi">
                <p style="color:#f53d52;font-size: 14px">温馨提示：</p>
                {:get_ad(22)}
            </div>

        </div>

    {else/}

        <div class="fs18 red" style="padding: 150px 0px;text-align: center;">
            您的身份信息未绑定，
            <a href="{:url('member/real')}" style="color:blue">去绑定</a>
        </div>

    {/eq}

</div>


<script type="text/javascript" src="/static/js/clipboard.min.js"></script>
<script type="text/javascript">
var way = 'wx';
function sub(){
    var money = parseFloat($('#money').val());
    var account = $('#account').val();
    var tran_id = $('#tran_id').val();
    if( isNaN(money) || money<=0 ){
        return layer.open({ content: "充值金额不能小于0",skin: 'msg',time: 3 });
    }
    if( !account ){
        return layer.open({ content: "微信昵称不能为空",skin: 'msg',time: 3 });
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
    });
    $.ajax({
        url: '{:url("member/recharge")}',
        type: "post",
        dataType: "json",
        data: {money:money,account:account,tran_id:tran_id,way:way},
        success: function(d,e,x){
            layer.open({ content: d.message,skin: 'msg',time: 3 });

            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("member/index")}';
                    },999);
                return;
            }else{
                layer.close(l_index);
            }
        }
    });

}

$(function(){
    var text1 = $("#wxacccode").val();
    var clipboard1 = new Clipboard('#copy_holder', {
        text: function() {
            return text1;
        }
    });
    clipboard1.on('success', function(e) {
        layer.open({ content: '复制成功',skin: 'msg',time: 3 });
    });
})


</script>
{include file="public/footer" /}
