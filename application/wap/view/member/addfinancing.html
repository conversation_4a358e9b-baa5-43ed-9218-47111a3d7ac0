<script type="text/javascript" src="/static/js/jquery183.js"></script>
<script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>

<style type="text/css">
.layui-m-layercont{ padding: 10px;font-size: 12px; }
.layui-m-layer0 .layui-m-layerchild{ width: 69%; }
</style>
<div class="stock-deal">

    <div class="box_prompt">
        1. 扩大配资产生的利息费，需要一次性付清。 <br>
        2. 按月配资以30天为一个月计算，不满一个月的部分，按照占30天的百分比计算。
    </div>
    
    <p style="margin-top:20px;">
        当前账户余额：<span>{$minfo.account_money/100}</span>元
    </p>

    <div class="box_word" style="margin-top: 15px;">
        扩大金额：<input type="text" id="money" class="inp" value="{$kd_set.0}" autocomplete="off">&nbsp;元
    </div>

    <p style="margin: 12px 0 0;">手续费：<span id='j-fee'>&nbsp;</span></p>

    <div class="operate-group" style="margin-top: 15px;">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消扩大</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认扩大</a>
    </div>
</div>


{load href="/static/js/underscore-min.js" /}
<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
var min = parseInt('{$kd_set.0}');
var max = parseInt('{$kd_set.1}');
var yu = parseInt('{$kd_set.2}');
function apply(){
    if( !id ){
        return msg('数据不能为空，请按照平台规则重试！');
    }
    var money = parseInt( $('#money').val() );    
    if( isNaN(money) || money<=0 ){
        return msg('扩大金额错误，请按照平台规则重试！');
    }
    if( money<min ){
        return msg('扩大金额不得小于'+min+'元');
    }
    if( money>max ){
        return msg('扩大金额不得大于'+max+'元');
    }
    if( money%yu != 0 ){
        return msg('扩大金额必须是'+yu+'的整倍数');
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
    });
    $.ajax({
        url: "{:url('member/addfinancing')}",
        type: "post",
        dataType: "json",
        data: {id:id,money:money,c:category},
        success: function(d,e,x){  
            // console.log(d,e,x);
            msg( d.message );
            layer.close(l_index);
            if(d.status==1){
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){
                    // parent.layer.close(index);
                    parent.location.reload();
                }, 999);
            }else{
                return msg( d.message );
            }
        }
    });
    
}
$(function(){
    $("#money")
        .on("keyup", _.debounce(function (e) {
            $("#j-fee").text("计算中...");
            var money = parseInt( $(this).val() );
            $.ajax({
                url: "{:url('member/calculate_rate')}",
                type: "post",
                dataType: "json",
                data: {id:id,money:money,action:'kuoda'},
                success: function(d,e,x){
                    console.log(d);
                    $("#j-fee").text(d.fee);
                }
            });
        }, 300)).trigger('keyup');
})
function msg(msg){
    layer.open({
            content: msg,
            shadeClose: false,
            anim: 'up',
            time: 2
        });
}
function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}
</script>