{include file="public/header" /}
<title>修改密码 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<link href="/static/wap/css/log-reg.css" rel="stylesheet" type="text/css">

<style type="text/css">
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">修改密码</h1>
    </div>

    <div class="wecell_login wecell_reg" style="margin-top:40px;">
        <div class="weui-cell">
            <div class="weui-cell__bd">
              <input type="password" id="old" placeholder="旧登录密码" v-model="password" class="weui-input s2">
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
              <input type="password" id="pass" placeholder="新登录密码" v-model="repassword" class="weui-input s2">
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
              <input type="password" id="pass2" placeholder="新登录密码确认" v-model="repassword" class="weui-input s2">
            </div>
        </div> 
        <div class="weui-flex log-botton">
            <div class="weui-flex__item">
              <div class="btn-wrapper">
                <a class="weui-btn weui-btn_primary" onclick="sub()" @click="register">修改</a>
              </div>
            </div>
        </div>       
    </div>

</div>



<script type="text/javascript">
function sub(){
    var old = $('#old').val();
    var pass = $('#pass').val();
    var pass2 = $('#pass2').val();

    if( !old ){
        return layer.open({
            content: "旧登录密码不能为空"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( pass.length<6 || pass.length>16 ){
        return layer.open({
            content: "新密码不得小于6位或者大于16位"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( old==pass ){
        return layer.open({
            content: "旧密码不能和新密码相同"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if( pass != pass2 ){
        return layer.open({
            content: "两次密码输入不一致"
            ,skin: 'msg'
            ,time: 3
        });
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });
    $.ajax({
        url: '{:url("member/loginPass")}',
        type: "post",
        dataType: "json",
        data: {old:old,pass:pass,pass2:pass2},
        success: function(d,e,x){
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
            });
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("common/login")}';
                    },1500);
            }else{
                layer.close(l_index);
            }
        }
    });
}
</script>
{include file="public/footer" /}