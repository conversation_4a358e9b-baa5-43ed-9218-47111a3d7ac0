<script type="text/javascript" src="/static/js/jquery183.js"></script>
<script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>

<style type="text/css">
.layui-m-layercont{ padding: 10px;font-size: 12px; }
.layui-m-layer0 .layui-m-layerchild{ width: 69%; }
</style>
<div class="stock-deal">

    <div class="box_prompt">
        温馨提示：补充亏损最小额度{$fill_set.0}，金额是{$fill_set.1}的整倍数，注：追加保证金没有放大资金的效果。 
        如需放大资金，请申请扩大配资。
    </div>
    
    <p style="margin-top:20px;">
        当前账户余额：<span>{$minfo.account_money/100}</span>元
    </p>

    <div class="box_word" style="margin-top: 15px;">
        补亏金额：<input type="text" id="money" class="inp" value="{$fill_set.0}" autocomplete="off">&nbsp;元
    </div>


    <div class="operate-group" style="margin-top: 22px;">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消补亏</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认补亏</a>
    </div>
</div>



<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
var min = parseFloat("{$fill_set.0}");
var yu = parseFloat("{$fill_set.1}");
var account_money = parseFloat("{$minfo.account_money/100}");
function apply(){
    if( !id ){
        return msg('数据不能为空，请按照平台规则重试！');
    }
    var money = parseInt( $('#money').val() );    
    if( !money || money<min ){
        return msg( '补亏金额不能小于'+min+'元' );
    }
    if( money%yu != 0 ){
        return msg('补亏金额必须是'+yu+'的整倍数' );
    }
    if( account_money<money ){
        return msg( '余额不足，请先充值' );
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
    });
    $.ajax({
        url: "{:url('member/fill')}",
        type: "post",
        dataType: "json",
        data: {id:id,money:money,c:category},
        success: function(d,e,x){  
            // console.log(d,e,x);
            msg( d.message );
            layer.close(l_index);
            if(d.status==1){
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){
                    // parent.layer.close(index);
                    parent.location.reload();
                }, 999);
            }else{
                return msg( d.message );
            }
        }
    });
    
}
$(function(){
    
})
function msg(msg){
    layer.open({
            content: msg,
            shadeClose: false,
            anim: 'up',
            time: 2
        });
}
function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}
</script>