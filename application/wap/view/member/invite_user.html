{include file="public/header" /}
<title>推广链接 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/js/clipboard.min.js"></script>

<style type="text/css">
.weui-media-box{
    padding: 8px 15px;
    border-bottom: 1px solid #cecece;
}
.weui-media-box__desc{ color: #666; }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">推广用户</h1>
    </div>
    
    
    <div class="weui-panel">
        <div class="weui-panel__bd">

            {volist name="data" id="vo" empty="$empty_data"}
            <div class="weui-media-box weui-media-box_text">
                <p class="weui-media-box__desc">用户名：{$vo.user_name_display}</p>
                <p class="weui-media-box__desc">真实姓名：{$vo.real_name_display}</p>
                <p class="weui-media-box__desc">注册时间：{:date('Y-m-d H:i', $vo.last_log_time)}</p>
                <p class="weui-media-box__desc">上次登录时间：{:date('Y-m-d H:i', $vo.reg_time)}</p>
            </div>
            {/volist}
        </div>
    </div>
    
    <div class="pager2">
        {$page}
    </div>

</div>



<script type="text/javascript">
$(function(){
    var text = $("#div_url").text();
    var clipboard = new Clipboard('#btn_down', {
        text: function() {
            return text;
        }
    });
    clipboard.on('success', function(e) {
        layer.open({
            content: '复制成功'
            ,skin: 'msg'
            ,time: 3
        });
    });
})
</script>
{include file="public/footer" /}