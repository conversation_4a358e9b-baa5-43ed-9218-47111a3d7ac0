{include file="public/header" /}
<title>银行卡管理 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<style type="text/css">
    body {
        {eq name="$action" value="add"}
            background-color: #ffffff;
        {else}
            background-color: #153473;
        {/eq}
    } 
    
    .weui-cells {
        background-color: #383c3c;
        line-height: 10px;
        margin-top: 40px;
    }

    a.weui-cell {
        color: #B1B1B1;
    }

    table tr td .inp {
        border: 1px solid #AAAAAA;
        border-radius: 3px;
        height: 27px;
        width: 80%;
        padding: 0 5px;
    }

    .tjyhk {
        display: block;
        margin: 20px 15px 0;
        color: #E5EDFF;
        padding: 10px 0;
        border-bottom: 1px solid currentColor;
    }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">银行卡管理</h1>
    </div>

    <!-- 实名认证？ -->
    {eq name="$id_status" value="1"}

    {eq name="$action" value="add"}
    <!-- 添加银行卡 -->
    <div class="my-space">
        <div class="ms-c6">
            <div style="padding: 5% 5% 0">添加银行卡</div>
            <div class="formbox">
                <table>
                    <tbody>
                    <tr>
                        <td style="width:100px">帐户名：</td>
                        <td>
                            <input type="text" maxlength="20" id="account_name" class="inp input">
                        </td>
                    </tr>
                    <tr>
                        <td>开户银行：</td>
                        <td>
                            <select name="bank" style="height: 33px;width: 75%;border-radius: 3px">
                                <option value="">选择银行</option>
                                {volist name="bank_list" id="vo"}
                                <option value="{$key}">{$vo}</option>
                                {/volist}
                            </select>

                            <font color="red">*</font>
                        </td>
                    </tr>
                    <tr id="otherbanktr" style="display:none;">
                        <td>银行名称</td>
                        <td>
                            <input type="text" class="inp input" id="otherbank" placeholder="请输入其他银行名称"
                                   style="width:75%;">
                            <font color="red"></font>
                        </td>
                    </tr>
                    <tr>
                        <td>开户行所在地：</td>
                        <td>
                            <select class="sel" id="selProvince" onchange="changeCity()"
                                    style="height:33px;width: 40%;border-radius: 3px;margin-right: 3px;">
                                <option value="">请选择</option>
                            </select>
                            <select class="sel" id="selCity" style="height:33px;width: 40%;border-radius: 3px">
                                <option value="">请选择</option>
                            </select>

                            <font color="red">*</font>
                        </td>
                    </tr>

                    <tr>
                        <td>支行名称：</td>
                        <td>
                            <input type="text" maxlength="20" id="address" class="inp input">
                            <font color="red">*</font>
                        </td>
                    </tr>
                    <tr>
                        <td>银行卡号：</td>
                        <td>
                            <input type="text" maxlength="19" id="number" class="inp input">
                            <font color="red">*</font>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div style="padding: 0 5%;">
                    <span style="color:red;font-size:14px;line-height: 20px">
                    注意：仅限同名添加{$global.band_bank}张银行卡
                    <br>
                    温馨提示：为了您的账户资金安全，银行卡绑定仅限实名认证本人的银行帐号，否则将无法提现
                    </span>
            </div>

            <div class="btnbox mt20" style="padding: 0 5%;">
                <a class="btn_primary" onclick="sub()" href="javascript:;">确定</a>
            </div>

        </div>
    </div>

    {else/}
    <!-- 银行卡记录？ -->
    {empty name="bank"}
    <br>
    <div class="card">
        <p style="line-height: 75px;">您还未绑定银行卡</p>
    </div>
    {else /}
    <div class="padding15" style="color: #fff;">您已认证的银行卡：</div>
    {volist name="data" id="vo"}
    <div class="card">
        <div class="bank-logo">
            {$vo.bank_name}
        </div>
        <span>已绑定</span>
        <div class="clearfix"></div>
        <div class="inpbox">
            <i>{$vo.number_display}</i>
        </div>
    </div>
    {/volist}
    <p class="bank-p">该银行卡仅用户账户余额提现</p>
    {/empty}

    <a href="{:url('member/bank','action=add')}" class="tjyhk">
        <img src="/static/wap/img/jiahao.png" alt="" style="margin-right: 3px;">
        添加银行卡
    </a>

    {/eq}

    {else/}

    <div class="fs18 white" style="padding: 150px 0px;text-align: center;">
        您的身份信息未绑定，
        <a href="{:url('member/real')}" style="color:red">去绑定</a>
    </div>

    {/eq}

</div>


{load href="/static/js/area2.js" /}
<script type="text/javascript">
    function sub() {
        var bankCode = $('select[name=bank]').val();
        var otherbank = $.trim($("#otherbank").val());
        var province = $('#selProvince').val();
        var city = $('#selCity').val();
        var address = $('#address').val();
        var number = $('#number').val();
        var account_name = $('#account_name').val();

        if (!account_name) {
            return layer.open({content: '帐户名不能为空', skin: 'msg', time: 3});
        }
        if (!bankCode) {
            return layer.open({content: '请选择开户银行', skin: 'msg', time: 3});
        }
        if (bankCode == "other" && !otherbank) {
            return layer.open({content: '请输入其他银行名称', skin: 'msg', time: 3});
        }
        if (!province || !city) {
            return layer.open({content: '请选择开户行所在地', skin: 'msg', time: 3});
        }
        if (!(/^[\u4e00-\u9fa5]+$/.test(address))) {
            return layer.open({content: '支行名称必须是中文', skin: 'msg', time: 3});
        }
        if (!number) {
            return layer.open({content: '银行卡号不能为空', skin: 'msg', time: 3});
        }
        if (!(/^\d{16}|\d{19}$/.test(number))) {
            return layer.open({content: '银行卡号一般为16位或19位数字', skin: 'msg', time: 3});
        }

        var l_index = layer.open({
            type: 2,
            shadeClose: false
        });
        $.ajax({
            url: '{:url("member/bank")}',
            type: "post",
            dataType: "json",
            data: {
                bankCode: bankCode,
                otherbank: otherbank,
                province: province,
                city: city,
                address: address,
                number: number,
                account_name: account_name,
            },
            success: function (d, e, x) {
                layer.open({content: d.message, skin: 'msg', time: 3});
                if (d.status == 1) {
                    var t1 = setTimeout(function () {
                        window.location.href = '{:url("member/bank")}';
                    }, 999);
                } else {
                    layer.close(l_index);
                }
            }
        });


    }

    $('select[name=bank]').on('change', function (event) {
        if ($(this).val() == 'other') {
            $('#otherbanktr').show();
        } else {
            $('#otherbanktr').hide();
        }
    });
    $(function () {
        //加载页面时候添加省份下拉框里面的信息
        addProvince();
    });
</script>
{include file="public/footer" /}