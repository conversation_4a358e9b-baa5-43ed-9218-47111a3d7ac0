<script type="text/javascript" src="/static/js/jquery183.js"></script>
<script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>

<style type="text/css">
.layui-m-layercont{ padding: 10px;font-size: 12px; }
.layui-m-layer0 .layui-m-layerchild{ width: 69%; }
</style>
<div class="stock-deal">

    <div class="box_prompt">
        温馨提示：请妥善保管您的账户密码，切勿告诉他人。
    </div>

    <div class="sqyanqi" style="margin: 15px 0 20px;">
        <table>
            <tbody>
                <tr>
                    <th><b>子账号：</b></th>
                    <td>{$data.home_user}</td>
                </tr>
                <tr>
                    <th><b>交易密码：</b></th>
                    <td>{$data.home_pws}</td>
                </tr>
            </tbody>
        </table>
    </div>    

    <div class="operate-group" style="">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">返回</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">下载交易软件</a>
    </div>
</div>


<script type="text/javascript">
function apply(){
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.href = '{:url("common/download")}';
}
$(function(){
})
function msg(msg){
    layer.open({
            content: msg,
            shadeClose: false,
            anim: 'up',
            time: 2
        });
}
function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}
</script>