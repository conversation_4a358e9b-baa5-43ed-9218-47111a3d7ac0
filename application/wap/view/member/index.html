{include file="public/header" /}
<title>我的账户 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">我的账户</h1>
        <div class="v-header-right">
            <div class="right-arrow">{:substr_replace($uname,'****',3,4)}</div>
        </div>
    </div>

    <div class="wap_photo">
        {if condition="$qiandao"}
        <div class="qiandao" onclick="qd()">签到</div>
        {else /}
        <div class="qiandao" >已签到</div>
        {/if}

        <div class="head_text white">
            <p class="account-money-title">账户余额</p>
            <p class="account-money">
                {:number_format($minfo.account_money/100,2)}
            </p>
        </div>

        <div class="text-center">
            <div class="btn_cz" onclick="window.location.href='{:url("member/recharge")}' ">充值</div>
            <div class="btn_cz" onclick="window.location.href='{:url("member/withdraw")}' ">提现</div>
        </div>
    </div>
    
    <div class="user_box">
        <ul>
            <li>
                <a href="{:url('member/assets')}">
                <div class="icon"><img src="/static/wap/img/1_icon.png"/></div>
                <div class="title">帐户总资产</div>
                <div class="tips">
                    <span>
                        {php}
                            echo number_format( ($minfo['account_money']+$minfo['money_freeze']+$minfo['interest_money'])/100 , 2 )
                        {/php}
                    </span>
                    元
                </div>
                <div class="more"></div>
                </a>
            </li>
            <li>
                <a href="{:url('member/stockList')}">
                    <div class="icon"><img src="/static/wap/img/2_icon.png"/></div>
                    <div class="title">配资订单</div>
                    <div class="tips">
                        <span>{$use_stock}</span>单
                    </div>
                    <div class="more"></div>
                </a>
            </li>
            <li>
                <a href="{:url('member/mrecord')}">
                    <div class="icon"><img src="/static/wap/img/3_icon.png"/></div>
                    <div class="title">资金流水</div>
                    <div class="tips"></div>
                    <div class="more"></div>
                </a>
            </li>
        </ul>
    </div>

    <div class="height10"></div>

    <div class="user_box">
        <ul>
            <li>
                <a href="{:url('member/real')}">
                    <div class="icon"><img src="/static/wap/img/4_icon.png"/></div>
                    <div class="title">实名认证</div>
                    <div class="tips">
                        {if condition='$minfo.id_status eq 1'}
                        已认证
                        {else /}
                        未认证
                        {/if}
                    </div>
                    <div class="more"></div>
                </a>
            </li>
            <li>
                <a href="{:url('member/bank')}">
                    <div class="icon"><img src="/static/wap/img/5_icon.png"/></div>
                    <div class="title">银行卡管理</div>
                    <div class="tips">
                        {if condition='$bank'}
                        已绑定
                        {else /}
                        未绑定
                        {/if}
                    </div>
                    <div class="more"></div>
                </a>
            </li>
            <li>
                <a href="{:url('member/paypass')}">
                    <div class="icon"><img src="/static/wap/img/6_icon.png"/></div>
                    <div class="title">支付密码</div>
                    <div class="tips">
                        {if condition='$minfo.pin_pass'}
                        修改
                        {else /}
                        未设置
                        {/if}
                    </div>
                    <div class="more"></div>
                </a>
            </li>
            <li>
                <a href="{:url('member/loginPass')}">
                    <div class="icon"><img src="/static/wap/img/6_icon.png"/></div>
                    <div class="title">修改密码</div>
                    <div class="tips"></div>
                    <div class="more"></div>
                </a>
            </li>
            
        </ul>
    </div>

    <div class="height10"></div>

    <div class="user_box">
        <ul>
            <li>
                <a href="{:url('member/msg')}">
                    <div class="icon"><img src="/static/wap/img/10_icon.png"/></div>
                    <div class="title">站内信息</div>
                    {gt name="inner_count" value="0"}
                    <div class="tishi">{$inner_count}</div>
                    {/gt}
                    <div class="more"></div>
                </a>
            </li>
            
            <!-- <li>
                <a href="">
                    <div class="icon"><img src="/static/wap/img/9_icon.png"/></div>
                    <div class="title">帮助中心</div>
                    <div class="tips"></div>
                    <div class="more"></div>
                </a>
            </li> -->
            <li>
                <a href="{:url('member/invite')}">
                    <div class="icon"><img src="/static/wap/img/7_icon.png"/></div>
                    <div class="title">推广链接</div>
                    <div class="tips"></div>
                    <div class="more"></div>
                </a>
            </li>
            <li>
                <a href="{:url('member/inviteUser')}">
                    <div class="icon"><img src="/static/wap/img/7_icon.png"/></div>
                    <div class="title">推广用户</div>
                    <div class="tips"></div>
                    <div class="more"></div>
                </a>
            </li>
            <li>
                <a href="{:url('cms/about')}">
                    <div class="icon"><img src="/static/wap/img/8_icon.png"/></div>
                    <div class="title">关于我们</div>
                    <div class="tips"></div>
                    <div class="more"></div>
                </a>
            </li>

        </ul>
    </div>
    <div class="height10"></div>

    <div class="tuichu" onclick="logout()"><a class="white">退出登录</a></div>

</div>

<script type="text/javascript">
var qiandao = "{$qiandao}";
function qd(){
    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });

    $.ajax({
        url: '{:url("member/qiandao")}',
        type: "post",
        dataType: "json",
        // data: {},
        success: function(d,e,x){
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
            });
            if(d.status==1){
                var t1 = setTimeout(function(){
                        // window.location.href = '{:url("members/index")}';
                        location.reload();
                    },1500);
                return;
            }else{
                layer.close(l_index);
            }
        }
    });
}


function logout(){
    var confirm = layer.open({
        content: '确定退出账号吗？'
        ,btn: ['取消', '确认退出']
        ,skin: 'footer'
        ,yes: function(index){
            layer.close(confirm);
        }
        ,no: function(){
            var l_index = layer.open({
                type: 2,
                shadeClose: false
                // ,content: '加载中'
            });

            $.ajax({
                url: '{:url("common/logout")}',
                type: "post",
                dataType: "json",
                // data: {},
                success: function(d,e,x){
                    layer.open({
                        content: d.message
                        ,skin: 'msg'
                        // ,time: 3
                    });
                    if(d.status==1){
                        var t1 = setTimeout(function(){
                                window.location.href = '{:url("index/index")}';
                            },1200);
                        return;
                    }else{
                        layer.close(l_index);
                    }
                }
            });
        }
    });
}
</script>
{include file="public/footer" /}