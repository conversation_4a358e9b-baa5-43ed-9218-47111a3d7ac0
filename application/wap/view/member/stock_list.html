{include file="public/header" /}
<title>我的配资 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<style type="text/css">
body{ background-color: #fff; }
.layui-layer-setwin{ top: 10px; }
.layui-layer-title{ line-height: 36px;height: 35px; }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">我的配资</h1>
    </div>

    <div class="" style="padding:15px 10px;">
        <div id="record">
            {volist name="data" id="vo" empty="$empty_data"}
            <div class="ms-c9">
                <table>
                    <tbody>
                        <tr>
                            <td colspan="4" style="padding: 8px 8px 5px;">
                                {eq name="vo.category" value="1"}股票{else/}期货{/eq}配资(编号：{$vo.id})
                                <span class='fr'>
                                {:date('Y/m/d',$vo.add_time)} ~ {:date('Y/m/d',$vo.end_time)}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th style="width:130px;">类型：</th>
                            <td style="width:100px;">
                                {:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $vo.type)}
                            </td>

                            <th style="width:108px;">状态：</th>
                            <td style="width:100px;" class="{$vo.status_class}">
                                {$vo.status_display}
                                <!-- <strong class="yellow"></strong> -->
                            </td>
                        </tr>
                        <tr>
                            <th>保证金：</th>
                            <td>{$vo.deposit_money/100}</td>

                            <th>总利息：</th>
                            <td>{$vo.totalinterest/100}</td>
                        </tr>
                        <tr>
                            <th>借入资金：</th>
                            <td>{php}echo ($vo['borrow_money']-$vo['deposit_money'])/100;{/php}</td>

                            <th>警戒线：</th>
                            <td>{$vo.loss_warn/100}</td>
                        </tr>
                        <tr>
                            <th>总操盘资金：</th>
                            <td>
                                {$vo.borrow_money/100}
                            </td>

                            <th>平仓线：</th>
                            <td>{$vo.loss_close/100}</td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                {if condition="$vo.status==2"}
                                    <!-- <div id="btn_view" class="btn" onclick="view()">详情</div> -->

                                    <div class="btn" onclick="addIframe('扩大配资','{:url("member/stock_account",["id"=>$vo.id])}');">查看交易密码</div>
                                    {if condition="($vo.type==1) OR ($vo.type==2) OR ($vo.type==8)"}
                                    <div class="btn" onclick="addIframe('扩大配资','{:url("member/addfinancing",["id"=>$vo.id, "c"=>$vo.category])}');">扩大配资</div>
                                    <div class="btn" onclick="addIframe('申请延期','{:url("member/renewal",["id"=>$vo.id,"type"=>$vo.type, "c"=>$vo.category])}');">申请延期</div>
                                    {/if}

                                    {if condition="$vo.type NEQ 4"}
                                    <div class="btn" onclick="addIframe('补充亏损','{:url("member/fill",["id"=>$vo.id, "c"=>$vo.category])}');">补充亏损</div>
                                    <div class="btn" onclick="addIframe('提取盈利','{:url("member/drawprofit",["id"=>$vo.id, "c"=>$vo.category])}');">提取盈利</div>
                                    {/if}
                                    
                                    <div class="btn" onclick="addIframe('终止操盘','{:url("member/stopfinancing",["id"=>$vo.id, "c"=>$vo.category])}');">终止操盘</div>
                                    <div class="btn" onclick=" window.location.href='{:url("common/hetong",["id"=>$vo.id, "c"=>$vo.category])}' ">查看网签合同</div>

                                    {if condition="$vo.sub_account"}
                                    <div class="btn btn-trading" onclick="window.open('{$trading_login_url}?account={$vo.sub_account.name}&password={$vo.sub_account.password}', '_blank');">前往操盤</div>
                                    {/if}

                                {/if}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            {/volist}
        </div>

        <div class="pager2">
            {$page}
        </div>
    </div> 

</div>


<script type="text/javascript" src="/static/layer-v3.1.1/layer/layer.js"></script>
<script type="text/javascript">
function addIframe(title,url){
    layer.open({
        type: 2,
        title: title,
        maxmin: false,
        resize: false,
        closeBtn: false,
        scrollbar: false,
        shade: [0.6, '#000'],
        area : ['85%' , '55%'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
</script>
{include file="public/footer" /}