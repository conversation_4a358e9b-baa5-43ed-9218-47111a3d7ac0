{include file="public/header" /}
<title>提现 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<style type="text/css">
body{ background-color: #fff; }
.formbox{ margin: 5% 0; }
.formbox td{ padding-left: 8px; }
.formbox .inp{ height: 27px;padding: 0 5px; }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">提现</h1>
    </div>

    <!-- 实名认证？ -->
    {eq name="$minfo.id_status" value="1"}

        {empty name="bank"}
    
            <div class="fs18 red" style="padding: 150px 0px;text-align: center;">
                您的银行卡未绑定，
                <a href="{:url('member/bank')}" style="color:blue">去绑定</a>
            </div>

        {else /}

            <div class="ms-c6">
                <div class="ms-c6-m pt10">
                    <div class="formbox bg withdraw-css">
                        <table>
                            <tbody>
                                <tr>
                                    <td style="width:33%;">可提款金额：</td>
                                    <td>
                                        <strong style="color:#F00;font-weight: bold">{:number_format($minfo.account_money/100,2)}</strong> 元
                                    </td>
                                </tr>
                                <tr>
                                    <td>提款金额：</td>
                                    <td>
                                        <input id="money" class="inp" style="width:60%" type="text" placeholder='请输入整数'> 元
                                    </td>
                                </tr>
                                <tr>
                                    <td>提款银行卡：</td>
                                    <td>
                                        <select name="bank" id="bank" style="height:30px;width:77%; border: 1px solid #ccc; border-radius: 3px; text-indent: 5px;">
                                            <option value="">请选择</option>
                                            {volist name="bank" id="vo"}
                                            <option value="{$vo.id}" >{$vo.bank_name}&nbsp;({$vo.number_display})</option>
                                            {/volist}
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td>支付密码：</td>
                                    <td>
                                        {eq name="is_set_pass" value="1"}
                                            <input type="password" id="pin" class="inp" style="width:60%" placeholder='请输入支付密码'>
                                        {else /}
                                            <a href="{:url('member/paypass')}" style="color: #F00;">设置支付密码</a>后才可以进行提现~
                                        {/eq}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="width:90%;margin: 20px auto 0;">
                        <div onclick="sub()" class="btn_primary">确认提交</div>
                    </div>
                    <div class="ms-c6-dl clearfix">                    
                        <p>温馨提示：</p>
                        <p>1、每天最多申请提现{$withdraw_set.1}次；</p>
                        <p>2、禁止洗钱、信用卡套现、虚假交易等行为；</p>
                    </div>
                </div>
            </div>
        
        {/empty}


    {else/}
        
        <div class="fs18 red" style="padding: 150px 0px;text-align: center;">
            您的身份信息未绑定，
            <a href="{:url('member/real')}" style="color:blue">去绑定</a>
        </div>

    {/eq}
</div>



<script type="text/javascript">
var withdraw_min = parseFloat("{$withdraw_set.0}");
var is_set_pass = parseInt("{$is_set_pass}");
function sub(){
    var balance = parseInt("{$minfo.account_money}")/100;
    var money = parseFloat($('#money').val());
    var bank = $('#bank').val();
    var pin = $('#pin').val();
    if( is_set_pass!=1 ){
        return layer.open({ content: '设置支付密码后才可以进行提现',skin: 'msg',time: 3 });
    }
    if ( !money ) {
        return layer.open({ content: '请输入提现的金额',skin: 'msg',time: 3 });
    }
    if ( money < withdraw_min ) {
        return layer.open({ content: '提现金额不能小于'+withdraw_min+'元',skin: 'msg',time: 3 });
    }
    if ( money > balance) {
        return layer.open({ content: '提现金额不能大于可提款金额',skin: 'msg',time: 3 });
    }
    if( !/^\d+$/.test(money) ){
        return layer.open({ content: '提现金额必须是整数',skin: 'msg',time: 3 });
    }
    if ( !bank ) {
        return layer.open({ content: '请选择提现银行卡',skin: 'msg',time: 3 });
    }
    if ( !pin ) {
        return layer.open({ content: '请输入支付密码',skin: 'msg',time: 3 });
    }

    var l_index = layer.open({
                type: 2,
                shadeClose: false
            });
    $.ajax({
        url: '{:url("member/withdraw")}',
        type: "post",
        dataType: "json",
        data: {money:money,bank:bank,pin:pin},
        success: function(d,e,x){
            layer.open({ content: d.message,skin: 'msg',time: 3 });
            
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("member/index")}';
                    },999);
                return;
            }else{
                layer.close(l_index);
            }
        }
    });

}
</script>
{include file="public/footer" /}