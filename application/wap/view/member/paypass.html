{include file="public/header" /}
<title>支付密码 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<link href="/static/wap/css/log-reg.css" rel="stylesheet" type="text/css">

<style type="text/css">
body{ background-color: #fff; }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">
            {if condition='$minfo.pin_pass'}
            修改支付密码
            {else /}
            设置支付密码
            {/if}
        </h1>
    </div>
    
    <!-- 实名认证？ -->
    {eq name="$minfo.id_status" value="1"}

        {if condition='$minfo.pin_pass'}
            <div class="wecell_login wecell_reg" style="margin-top:40px;">
                <div class="weui-cell">
                    <div class="weui-cell__bd">
                        <input type="password" id="pass" placeholder="新密码" v-model="password" class="weui-input s2">
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__bd">
                        <input type="password" id="pass2" placeholder="确认新密码" v-model="repassword" class="weui-input s2">
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__bd">
                        <input type="text" id="phone" value="{:substr_replace($minfo.phone,'****',3,4)}" readonly="" maxlength="11" v-model="cellphone" class="weui-input s3">
                    </div>
                </div> 
                <div class="weui-cell weui-cell_vcode">          
                    <div class="weui-cell__bd">
                        <input class="weui-input s4" type="text" id="scode" maxlength="6" v-model="vcode" minlength="6" maxlength="6" placeholder="短信验证码" autocomplete="off">
                    </div>
                    <div class="weui-cell__ft">
                        <button class="weui-vcode-btn" id='sendSMS' @click="sendCode" v-html="vCodeTxt">短信验证码</button>
                    </div>
                </div> 

                <div class="weui-flex log-botton">
                    <div class="weui-flex__item">
                      <div class="btn-wrapper">
                        <a class="weui-btn weui-btn_primary" onclick="updatePin()" @click="register">确定</a>
                      </div>
                    </div>
                </div>       
            </div>
        {else /}
            <div class="wecell_login wecell_reg" style="margin-top:40px;">
                <div class="weui-cell">
                    <div class="weui-cell__bd">
                        <input type="password" id="pass" placeholder="输入密码" v-model="password" class="weui-input s2">
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__bd">
                        <input type="password" id="pass2" placeholder="确认密码" v-model="repassword" class="weui-input s2">
                    </div>
                </div>
                <div class="weui-cell">
                    <div class="weui-cell__bd">
                        <input type="text" id="vcode" maxlength="4" placeholder="图形验证码" v-model="repassword" class="weui-input s4">
                        <img src='/captcha.html' style="width: 40%;
                                                        position: absolute;
                                                        top: 0;
                                                        right: 0;" class="yzm jym"  onclick="this.src='/captcha.html?cd='+Math.random()" />
                    </div>
                </div> 

                <div class="weui-flex log-botton">
                    <div class="weui-flex__item">
                      <div class="btn-wrapper">
                        <a class="weui-btn weui-btn_primary" onclick="setPin()" @click="register">确定</a>
                      </div>
                    </div>
                </div>       
            </div>
        {/if}
    {else /}

        <div class="fs18 red" style="padding: 150px 0px;text-align: center;">
            您的身份信息未绑定，
            <a href="{:url('member/real')}" style="color:blue">去绑定</a>
        </div>

    {/eq}
</div>



<script type="text/javascript">
function setPin(){
    var pin = $('#pass').val();
    var pinConfirm = $('#pass2').val();
    var vcode = $('#vcode').val();

    if ( !pin || !pinConfirm ) {
        return layer.open({ content: "请输入密码",skin: 'msg',time: 3 });
    }
    if ( pin.length<6 || pin.length>16 ) {
        return layer.open({ content: "密码必须为6-16位的英文或数字组合",skin: 'msg',time: 3 });
    }
    if ( pin != pinConfirm ) {
        return layer.open({ content: "两次密码输入不一致，请重新输入",skin: 'msg',time: 3 });
    }
    if ( !vcode ) {
        return layer.open({ content: "请输入图形验证码",skin: 'msg',time: 3 });
    }

    var l_index = layer.open({
                type: 2,
                shadeClose: false
            });
    $.ajax({
        url: '{:url("member/paypass")}',
        type: "post",
        dataType: "json",
        data: {pin:pin,pinConfirm:pinConfirm,vcode:vcode,action:'set'},
        success: function(d,e,x){
            layer.open({ content: d.message,skin: 'msg',time: 3 });

            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("member/index")}';
                    },1000);
                return;
            }else{
                $('.jym').trigger('click');
                layer.close(l_index);
            }
        }
    });

}


function updatePin(){
    var pin = $('#pass').val();
    var pinConfirm = $('#pass2').val();
    var scode = $('#scode').val();
    if ( !pin || !pinConfirm ) {
        return layer.open({ content: "请输入密码",skin: 'msg',time: 3 });
    }
    if ( pin.length<6 || pin.length>16 ) {
        return layer.open({ content: "密码必须为6-16位的英文或数字组合",skin: 'msg',time: 3 });
    }
    if ( pin != pinConfirm ) {
        return layer.open({ content: "两次密码输入不一致，请重新输入",skin: 'msg',time: 3 });
    }
    if ( !scode ) {
        return layer.open({ content: "请输入短信验证码",skin: 'msg',time: 3 });
    }

    var l_index = layer.open({
                type: 2,
                shadeClose: false
            });
    $.ajax({
        url: '{:url("member/paypass")}',
        type: "post",
        dataType: "json",
        data: {pin:pin,pinConfirm:pinConfirm,scode:scode,action:'update'},
        success: function(d,e,x){
            layer.open({ content: d.message,skin: 'msg',time: 3 });
            
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("member/index")}';
                    },1000);
                return;
            }else{
                layer.close(l_index);
            }
        }
    });
}
var txtObj = $('#sendSMS');//文字DOM Obj
var InterValObj; //timer变量，控制时间 
var count = 90; //间隔函数，1秒执行 
var curCount;//当前剩余秒数
function myInterval() {
    curCount = count;
    //设置button效果，开始计时
    txtObj.text(curCount + "秒后重新发送"); 
    InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次 
} 
function SetRemainTime() {
    if (curCount == 0) {         
        window.clearInterval(InterValObj);//停止计时器 
        //启用按钮
        txtObj.removeAttr("disabled");
        txtObj.text("重新发送"); 
    }else{
        curCount--;
        txtObj.text(curCount + "秒后重新发送"); 
    } 
}
$('#sendSMS').on('click', function(event) {
    var pin = $('#pass').val();
    var pinConfirm = $('#pass2').val();
    if ( !pin || !pinConfirm ) {
        return layer.open({ content: "请输入密码",skin: 'msg',time: 3 });
    }
    if ( pin.length<6 || pin.length>16 ) {
        return layer.open({ content: "密码必须为6-16位的英文或数字组合",skin: 'msg',time: 3 });
    }
    if ( pin != pinConfirm ) {
        return layer.open({ content: "两次密码输入不一致，请重新输入",skin: 'msg',time: 3 });
    }

    var phone = "{$minfo.phone}";
    
    var l_index = layer.open({
        type: 2,
        shadeClose: false
        ,content: '发送中'
    });
    $.ajax({
        url: '{:url("common/sendSMS")}',
        type: "post",
        dataType: "json",
        data: {phone:phone,source:'findpwd'},
        beforeSend:function(){
            txtObj.text('验证码发送中').attr("disabled",true);
        },
        success: function(d,e,x){  
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
            }); 
            layer.close(l_index);
            if(d.status==1){
                
                myInterval();
            }else{
                //启用按钮
                txtObj.text('重新发送').removeAttr("disabled");
            }
        }
    });
});
</script>
{include file="public/footer" /}