<script type="text/javascript" src="/static/js/jquery183.js"></script>
<script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>

<style type="text/css">
.layui-m-layercont{ padding: 10px;font-size: 12px; }
.layui-m-layer0 .layui-m-layerchild{ width: 69%; }
</style>
<div class="stock-deal">

    <div class="box_prompt">
        1. 配资用户的可用资金大于总操盘资金，且只能提取盈利部分，提取盈利最少为{$sy_set.0}元，<br>
        2. 以{$sy_set.1}元单位为递增，即可申请提取盈利。
    </div>
    
    <div class="box_word" style="margin-top: 23px;">
        提取金额：<input type="text" value="{$sy_set.0}" class="inp" id="money" autocomplete="off">&nbsp;元
    </div>

    <div class="operate-group" style="margin-top: 22px;">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消提盈</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认提盈</a>
    </div>

</div>


<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
var min = parseFloat('{$sy_set.0}');
var yu = parseFloat('{$sy_set.1}');
function apply(){
    if( !id ){
        return msg('数据不能为空，请按照平台规则重试！');
    }
    var money = parseFloat($('#money').val());
    if( isNaN(money) || money<=0 ){
        return msg('提盈金额错误，请按照平台规则重试！');
    }
    if( money<min ){
        return msg('提盈金额不得小于'+min+'元');
    }
    if( money%yu != 0 ){
        return msg('提盈金额必须是'+yu+'的整倍数');
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
    });
    $.ajax({
        url: "{:url('member/drawprofit')}",
        type: "post",
        dataType: "json",
        data: {id:id,money:money,c:category},
        success: function(d,e,x){  
            // console.log(d,e,x);
            msg( d.message );
            layer.close(l_index);
            if(d.status==1){
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){
                    // parent.layer.close(index);
                    parent.location.reload();
                }, 999);
            }else{
                return msg( d.message );
            }
        }
    });
    
}
$(function(){
    
})
function msg(msg){
    layer.open({
            content: msg,
            shadeClose: false,
            anim: 'up',
            time: 2
        });
}
function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}
</script>