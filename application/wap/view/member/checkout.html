{include file="public/header" /}
<title>支付宝扫码 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<style type="text/css">
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">支付宝扫码</h1>
    </div>

    <!-- 实名认证？ -->
    {eq name="$minfo.id_status" value="1"}

    <div id="panel">
        <p class="tx_ts">请先成功付款后再“填写付款信息”确认提交</p>

        <p class="tx_ts">填写付款信息</p>
        <ul class="list_k">
            <li>
                <p>转账金额</p>
                <input class="input" id="amount" type="text" placeholder="请输入转账金额"/>
            </li>
        </ul>

        <div id="checksubmit" class="btn_primary mt20" style="width:90%;" onclick="sub()">确认提交</div>

        <div class="clear"></div>
        <div class="tishi">
            <p style="color:#f53d52;font-size: 14px">温馨提示：</p>
            {:get_ad(22)}
        </div>

    </div>

    {else/}

    <div class="fs18 red" style="padding: 150px 0px;text-align: center;">
        您的身份信息未绑定，
        <a href="{:url('member/real')}" style="color:blue">去绑定</a>
    </div>

    {/eq}

</div>


<script type="text/javascript" src="/static/js/clipboard.min.js"></script>
<script type="text/javascript">
    var way = 'checkout';

    function sub() {
        var amount = parseFloat($('#amount').val());
        if (isNaN(amount) || amount <= 0) {
            return layer.open({content: "充值金额不能小于0", skin: 'msg', time: 3});
        }
        var l_index = layer.open({
            type: 2,
            shadeClose: false
        });
        $.ajax({
            url: '{:url("member/recharge")}',
            type: "post",
            dataType: "json",
            data: {amount, way: way},
            success: function (d, e, x) {
                layer.open({content: d.message, skin: 'msg', time: 3});

                window.location.href = d.url;
            },
            error: function (x, e, t) {
                const data = JSON.parse(x.responseText);
                layer.open({content: data.message, skin: 'msg', time: 3});
                layer.close(l_index);
            },
        });

    }

    $(function () {
        var text1 = $("#zfbacccode").val();
        var clipboard1 = new Clipboard('#copy_holder', {
            text: function () {
                return text1;
            }
        });
        clipboard1.on('success', function (e) {
            layer.open({content: '复制成功', skin: 'msg', time: 3});
        });
    })

</script>
{include file="public/footer" /}
