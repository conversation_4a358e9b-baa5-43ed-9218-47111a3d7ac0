<script type="text/javascript" src="/static/js/jquery183.js"></script>
<script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>


<style type="text/css">
.layui-m-layercont{ padding: 10px;font-size: 12px; }
.layui-m-layer0 .layui-m-layerchild{ width: 69%; }
</style>
<div class="stock-deal">

    <div class="box_prompt">
        利息根据当前平台设置的利率重新算， 收取费用的公式和申请配资一样
    </div>
    
    <p style="margin-top:20px;">
        当前账户余额：<span>{$minfo.account_money/100}</span>元
    </p>

    <div class="box_word" style="margin-top: 15px;">
        续期时间：
        <select id="use_time">
        {volist name="use_time" id="vo"}
        <option value="{$vo}">{$vo}{$unit}</option>
        {/volist}
        </select>
    </div>

    <p style="margin: 12px 0 0;">手续费：<span id='j-fee'>&nbsp;</span></p>

    <div class="operate-group" style="margin-top: 15px;">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消续期</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认续期</a>
    </div>
</div>


{load href="/static/js/underscore-min.js" /}
<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
function apply(){
    if( !id ){
        return msg('数据不能为空，请按照平台规则重试！');
    }
    var duration = $("#use_time").val();
    if( !id || !duration ){
        return msg('数据不能为空，请按照平台规则重试！');
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
    });
    $.ajax({
        url: "{:url('member/renewal')}",
        type: "post",
        dataType: "json",
        data: {id:id,duration:duration,c:category},
        success: function(d,e,x){  
            // console.log(d,e,x);
            msg( d.message );
            layer.close(l_index);
            if(d.status==1){
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){
                    // parent.layer.close(index);
                    parent.location.reload();
                }, 999);
            }else{
                return msg( d.message );
            }
        }
    });
    
}
$(function(){
    $("#use_time")
        .on("change", _.debounce(function (e) {
            $("#j-fee").text("计算中...")
            var duration = parseInt( $(this).val() );
            $.ajax({
                url: "{:url('member/calculate_rate')}",
                type: "post",
                dataType: "json",
                data: {id:id,duration:duration,action:'yanqi'},
                success: function(d,e,x){
                    // console.log(d);
                    $("#j-fee").text(d.fee);
                }
            });
        }, 300)).trigger('change');
})
function msg(msg){
    layer.open({
            content: msg,
            shadeClose: false,
            anim: 'up',
            time: 2
        });
}
function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}
</script>