<script type="text/javascript" src="/static/js/jquery183.js"></script>
<script type="text/javascript" src="/static/layer_mobile-v2.0/layer.js"></script>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>

<style type="text/css">
.layui-m-layercont{ padding: 10px;font-size: 12px; }
.layui-m-layer0 .layui-m-layerchild{ width: 69%; }
</style>
<div class="stock-deal">

    <div class="box_prompt">
        1. 请确保您的交易账户已经卖出，否则我们有权把您持有进行强制处理（不保证平仓价）<br>
        提前申请终止操盘，剩余配资利息不予退回。
    </div>
    
    <br>

    <div class="operate-group" style="margin-top: 10px;">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消终止</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认终止</a>
    </div>

</div>


<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
function apply(){
    if( !id ){
        return msg('数据不能为空，请按照平台规则重试！');
    }

    var l_index = layer.open({
        type: 2,
        shadeClose: false
    });
    $.ajax({
        url: "{:url('member/stopfinancing')}",
        type: "post",
        dataType: "json",
        data: {id:id,c:category},
        success: function(d,e,x){  
            // console.log(d,e,x);
            msg( d.message );
            layer.close(l_index);
            if(d.status==1){
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){
                    // parent.layer.close(index);
                    parent.location.reload();
                }, 999);
            }else{
                return msg( d.message );
            }
        }
    });
    
}
$(function(){
    
})
function msg(msg){
    layer.open({
            content: msg,
            shadeClose: false,
            anim: 'up',
            time: 2
        });
}
function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}
</script>