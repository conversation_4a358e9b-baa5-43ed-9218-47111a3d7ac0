{include file="public/header" /}
<title>实名认证 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">
<link href="/static/wap/css/log-reg.css" rel="stylesheet" type="text/css">

<style type="text/css">
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">实名认证</h1>
    </div>

    {eq name="minfo.id_status" value="1"}
    
    <div class="space-main clearfix">
        <div class="space-right">
            <div class="real-div">
                <p class="pass-p">您已通过实名认证！</p>
                <p class="pass-bank">
                    如果还没有完成银行卡绑定，您可以选择
                    <a class="primary" href="{:url('member/bank')}">绑定银行卡</a>
                </p>
            </div>
            <div class="formbox">
                <table>
                    <tbody>
                        <tr>
                            <th>真实姓名：</th>
                            <td>{$minfo.realname_display}</td>
                        </tr>
                        <tr>
                            <th>证件类型：</th>
                            <td>身份证</td>
                        </tr>
                        <tr>
                            <th>证件号码：</th>
                            <td>{$minfo.idcard_display}</td>
                        </tr>
                        <tr>
                            <th>认证状态：</th>
                            <td>
                            <strong style="color:gray">已认证</strong></td>
                        </tr>              
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {else/}
    
    <div class="wecell_login wecell_reg" style="margin-top:40px;">
        <div class="weui-cell">
            <div class="weui-cell__bd">
              <input type="text" id="realName" placeholder="请输入真实姓名" v-model="password" class="weui-input s1">
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
              <input type="text" id="idCard" placeholder="请输入身份证号码" v-model="repassword" class="weui-input s5">
            </div>
        </div>

        <input value="" id="province" type="hidden"/>
        <input value="" id="birthday" type="hidden"/>
        <input value="" id="age" type="hidden"/>
        <input value="" id="sex" type="hidden"/>

        <div class="weui-flex log-botton" style="margin-top:20px;">
            <div class="weui-flex__item">
              <div class="btn-wrapper">
                <a class="weui-btn weui-btn_primary" onclick="sub()" @click="register">确定</a>
              </div>
            </div>
        </div>

        <div style="padding: 25px 0 15px;">
            <dl>
                <dt>
                    <font color="red">
                        温馨提示：认证后身份信息不可修改；且之后仅限添加本人的银行卡！
                    </font>
                </dt>
            </dl>
        </div>
    </div>

    {/eq}

</div>


{load href="/static/js/M/idvalidate.js" /}
<script type="text/javascript">
function sub(){
    var name = $('#realName').val();
    var number = $('#idCard').val();
    if ( name == '') {
        return layer.open({
            content: "请输入您的真实姓名！"
            ,skin: 'msg'
            ,time: 3
        });
    }
    var reg=/^[\u2E80-\u9FFF]+$/;//Unicode编码中的汉字范围
    if( !reg.test(name) ){
        return layer.open({
            content: "真实姓名输入错误"
            ,skin: 'msg'
            ,time: 3
        });
    }
    if (!IdCardValidate( number )) {
        return layer.open({
            content: "身份证号码不正确！"
            ,skin: 'msg'
            ,time: 3
        });
    } else {
        setInfo( number , 'sex', 'birthday', 'province', 'age');
    }
    var sex = $('#sex').val();
    var province = $('#province').val();

    var l_index = layer.open({
        type: 2,
        shadeClose: false
        // ,content: '加载中'
    });
    $.ajax({
        url: '{:url("member/real")}',
        type: "post",
        dataType: "json",
        data: {name:name,number:number,sex:sex,province:province},
        success: function(d,e,x){
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
            });
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.reload();
                        // window.location.href = '{:url("member/index")}';
                    },1500);
            }else{
                layer.close(l_index);
            }
        }
    });
}
</script>
{include file="public/footer" /}