{include file="public/header" /}
<title>站内信息 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<style type="text/css">
.layui-m-layerchild h3{
    height: 35px;
    line-height: 35px;
    font-size: 15px;
}
.layui-m-layercont{ padding: 10px 20px 20px; }
.layui-m-layerbtn{ height: 40px;line-height: 40px; background-color: #fff; }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">站内信息</h1>
    </div>
    
    <div class="user_box">
        <div class="yidu" onclick="setAllRead()">全部标记已读</div>
        <ul>
            {volist name="data" id="vo" empty="$empty_data"}
            <li class="df" id="this_{$vo.id}"  innerid="{$vo.id}" status="{$vo.status}">
                <div class="title text_con pl15">
                    {eq name="vo.status" value="0"}
                    <span class="status dian"></span>
                    {else/}
                    <span class="status dian2"></span>
                    {/eq}
                    <span class="content" title="{$vo.title}" info="{$vo.msg}">{:cnsubstr($vo.msg,20)}!</span>
                </div>
                <div class="time">{:date('m-d H:i',$vo.send_time)}</div>
            </li>
            {/volist}
        </ul>
    </div>

</div>






<script type="text/javascript">
$('.user_box ul li').each(function(index, el) {

    $(el).on('click', function(event) {
        // console.log(index, el)
        readThis(el);
    });
});
function readThis(obj){

    var id = $(obj).attr('innerid');
    var status = $(obj).attr('status');
    var title = $(obj).find('.content').attr('title');
    
    layer.open({
        title: title
        ,content: $(obj).find('.content').attr('info')
        ,btn: '关闭'
    });

    if( status==0 ){
        $.ajax({
            url: '{:url("member/msg")}',
            type: "post",
            dataType: "json",
            data: {id:id,action:'one'},
            success: function(d,e,x){
                if(d.status==1){
                    $(obj).attr('status',1).find('.status').addClass('dian2');
                }else{
                    layer.close(la_load);
                    return layer.open({
                        content: d.message
                        ,skin: 'msg'
                        ,time: 3
                    });
                }
            }
        });
    }

}

function setAllRead(){

    var l_index = layer.open({
                type: 2,
                shadeClose: false
                // ,content: '加载中'
            });
    $.ajax({
        url: '{:url("member/msg")}',
        type: "post",
        dataType: "json",
        data: {action:'all'},
        success: function(d,e,x){
            layer.open({
                content: d.message
                ,skin: 'msg'
                ,time: 3
            });
            if(d.status==1){
                var t1 = setTimeout(function(){
                        window.location.reload();
                    },1000);
            }else{
                layer.close(l_index);
            }
        }
    });

}
</script>
{include file="public/footer" /}