{include file="public/header" /}
<title>账户总资产 - {$global.web_name}</title>
<link href="/static/wap/css/member.css" rel="stylesheet" type="text/css">

<style type="text/css">
body{ background-color: #fff; }
</style>

<div id="app">
    <div class="v-header">
        <div class="v-header-left">
            <div class="left-arrow">返回</div>
        </div>
        <h1 class="v-header-title">账户总资产</h1>
    </div>

    <div class="m15">
        <div class="wrap-a">
            <div class="formbox m_a_none">   
                <table>
                    <tbody>
                        <tr>
                            <th></th>
                            <td class="r-e">
                            <h6>账户余额</h6>
                            <span class="c-red">{:number_format($minfo.account_money/100,2)}</span>元
                            </td>
                        </tr>
                        <tr>
                            <th></th>
                            <td class="r-p">
                            <h6>冻结资金</h6>
                            <span class="c-red">{:number_format($minfo.money_freeze/100,2)}</span>元
                            </td>
                        </tr>
                        <tr>
                            <th></th>
                            <td class="r-p">
                            <h6>赠送管理费余额</h6>
                            <span class="c-red">{:number_format($minfo.interest_money/100,2)}</span>元
                            </td>
                        </tr>

                        <tr class="bgc1">
                            <th>总资产：</th>
                            <td class="r">
                                <strong>
                                    {php}
                                        echo number_format( ($minfo['account_money']+$minfo['money_freeze']+$minfo['interest_money'])/100 , 2 )
                                    {/php}
                                </strong>元
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="btnbox mt30">
                <a class="btn_primary" href ="{:url('member/mrecord')}">查询资金流水</a>
            </div>
        </div>  
    </div>

</div>



<script type="text/javascript">
    
</script>
{include file="public/footer" /}