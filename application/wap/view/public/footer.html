
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>
<div class="wap_footer">
	<ul>
		<li class=" {eq name="controllerName" value="index" }active{/eq} " >
            <a class=" home" href="{:url('/index')}">首页</a>
		</li>
		<li>
			<a class="trading" href="{:url('member/stocklist')}">操盘</a>
		</li>
		<li class=" {eq name="controllerName" value="stock" }active{/eq} " >
            <a class=" stock" href="{:url('stock/month')}">股票配资</a>
		</li>
		<!-- <li class=" {eq name="controllerName" value="future"}active{/eq} " style="width: 21%;">
            <a class="stock" href="{:url('future/month')}">期货配资</a>
        </li> -->
		<li>
			<a class="kefu" href="javascript:;" id='kefukuang'>客服</a>
		</li>
		<li class=" {eq name="controllerName" value="member" }active{/eq} " >
            <a class=" user" href="{:url('member/index')}">我的</a>
			{gt name="inner_count" value="0"}
			<div class="red_d">{$inner_count}</div>
			{/gt}
		</li>
	</ul>
	<div class="am-share">
		<ul class="am-share-sns">
			<li><a href="{$global.kefu_l}">
					<span>在线客服</span> </a> </li>
			<li><a href="/wap.php/common/weixin.html"> <span>微信客服</span> </a> </li>
			<!-- <li><a href="tencent://Message/?Uin=192266666"> <span>QQ客服</span> </a> </li> -->
		</ul>
		<div class="am-share-footer"><button class="share_btn">取消</button></div>
	</div>
</div>


<script type="text/javascript">
$('#kefukuang').on('click', function(event) {
	console.log('客服按鈕被點擊');
	$(".am-share").addClass("am-modal-active");
		if ($(".sharebg").length > 0) {
			console.log('背景元素已存在，添加active類');
			$(".sharebg").addClass("sharebg-active");
		} else {
			console.log('創建新的背景元素');
			$("body").append('<div class="sharebg"></div>');
			$(".sharebg").addClass("sharebg-active");
		}
		console.log('背景元素數量:', $(".sharebg").length);
		console.log('背景元素類:', $(".sharebg").attr('class'));
});

// 處理背景點擊關閉視窗
$(document).on('click', '.sharebg', function(e) {
	console.log('背景被點擊了！');
	console.log('點擊目標:', e.target);
	console.log('當前元素:', this);
	closeModal();
});

// 處理取消按鈕點擊
$(document).on('click', '.share_btn', function() {
	console.log('取消按鈕被點擊');
	closeModal();
});

// 防止點擊視窗內容時關閉
$(document).on('click', '.am-share', function(e) {
	console.log('視窗內容被點擊，阻止冒泡');
	e.stopPropagation();
});

// 關閉模態視窗的函數
function closeModal() {
	console.log('執行關閉模態視窗');
	$(".am-share").removeClass("am-modal-active");
	setTimeout(function() {
		$(".sharebg-active").removeClass("sharebg-active");
		$(".sharebg").remove();
		console.log('背景元素已移除');
	}, 300);
}
	function toshare() {

	}


</script>


<!-- 统计代码 -->
{:htmlspecialchars_decode($global.count_code)}
<!-- 统计代码 -->

</body>
</html>
