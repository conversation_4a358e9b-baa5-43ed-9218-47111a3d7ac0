
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>
<div class="wap_footer">
	<ul>
		<li class=" {eq name="controllerName" value="index" }active{/eq} " >
            <a class=" home" href="{:url('/index')}">首页</a>
		</li>
		<li>
			<a class="trading" href="{:url('member/stocklist')}">操盘</a>
		</li>
		<li class=" {eq name="controllerName" value="stock" }active{/eq} " >
            <a class=" stock" href="{:url('stock/month')}">股票配资</a>
		</li>
		<!-- <li class=" {eq name="controllerName" value="future"}active{/eq} " style="width: 21%;">
            <a class="stock" href="{:url('future/month')}">期货配资</a>
        </li> -->
		<li>
			<a class="kefu" href="javascript:;" id='kefukuang'>客服</a>
		</li>
		<li class=" {eq name="controllerName" value="member" }active{/eq} " >
            <a class=" user" href="{:url('member/index')}">我的</a>
			{gt name="inner_count" value="0"}
			<div class="red_d">{$inner_count}</div>
			{/gt}
		</li>
	</ul>
	<div class="am-share">
		<ul class="am-share-sns">
			<li><a href="{$global.kefu_l}">
					<span>在线客服</span> </a> </li>
			<li><a href="/wap.php/common/weixin.html"> <span>微信客服</span> </a> </li>
			<!-- <li><a href="tencent://Message/?Uin=192266666"> <span>QQ客服</span> </a> </li> -->
		</ul>
		<div class="am-share-footer"><button class="share_btn">取消</button></div>
	</div>
</div>


<script type="text/javascript">
$('#kefukuang').on('click', function(event) {
	console.log('客服按鈕被點擊');
	$(".am-share").addClass("am-modal-active");
		if ($(".sharebg").length > 0) {
			console.log('背景元素已存在，添加active類');
			$(".sharebg").addClass("sharebg-active");
		} else {
			console.log('創建新的背景元素');
			$("body").append('<div class="sharebg"></div>');
			$(".sharebg").addClass("sharebg-active");
		}
		console.log('背景元素數量:', $(".sharebg").length);
		console.log('背景元素類:', $(".sharebg").attr('class'));
});

// 處理背景點擊關閉視窗 - 使用更精確的事件檢查
$(document).on('click', '.sharebg', function(e) {
	console.log('背景被點擊了！');
	console.log('點擊目標:', e.target);
	console.log('當前元素:', this);
	// 確保點擊的是背景本身，不是其子元素
	if (e.target === this || $(e.target).hasClass('sharebg')) {
		console.log('確認是背景點擊，執行關閉');
		closeModal();
	} else {
		console.log('點擊的不是背景，忽略');
	}
});

// 處理取消按鈕點擊
$(document).on('click', '.share_btn', function() {
	console.log('取消按鈕被點擊');
	closeModal();
});

// 處理整個文檔的點擊，檢查是否點擊在視窗外
$(document).on('click', function(e) {
	// 檢查是否點擊在視窗外且視窗是開啟的
	if ($('.am-share').hasClass('am-modal-active')) {
		console.log('視窗開啟中，檢查點擊位置');
		console.log('點擊目標:', e.target);

		var isInContent = $(e.target).closest('.am-share-sns, .am-share-footer').length > 0;
		var isKefuButton = $(e.target).closest('#kefukuang').length > 0;

		console.log('是否在內容區域:', isInContent);
		console.log('是否是客服按鈕:', isKefuButton);

		// 如果點擊不在實際內容區域且不是客服按鈕，則關閉視窗
		if (!isInContent && !isKefuButton) {
			console.log('點擊在內容區域外，關閉視窗');
			closeModal();
		} else {
			console.log('點擊在內容區域內，保持開啟');
		}
	}
});

// 關閉模態視窗的函數
function closeModal() {
	console.log('執行關閉模態視窗');
	$(".am-share").removeClass("am-modal-active");
	setTimeout(function() {
		$(".sharebg-active").removeClass("sharebg-active");
		$(".sharebg").remove();
		console.log('背景元素已移除');
	}, 300);
}
	function toshare() {

	}


</script>


<!-- 统计代码 -->
{:htmlspecialchars_decode($global.count_code)}
<!-- 统计代码 -->

</body>
</html>
