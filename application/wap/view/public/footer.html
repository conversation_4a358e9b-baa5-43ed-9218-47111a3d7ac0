
<script type="text/javascript" src="/static/wap/js/neizhi.js"></script>
<div class="wap_footer">
	<ul>
		<li class=" {eq name="controllerName" value="index" }active{/eq} " >
            <a class=" home" href="{:url('/index')}">首页</a>
		</li>
		<li>
			<a class="trading" href="javascript:;" onclick="goToTrading()">操盘</a>
		</li>
		<li class=" {eq name="controllerName" value="stock" }active{/eq} " >
            <a class=" stock" href="{:url('stock/month')}">股票配资</a>
		</li>
		<!-- <li class=" {eq name="controllerName" value="future"}active{/eq} " style="width: 21%;">
            <a class="stock" href="{:url('future/month')}">期货配资</a>
        </li> -->
		<li>
			<a class="kefu" href="javascript:;" id='kefukuang'>客服</a>
		</li>
		<li class=" {eq name="controllerName" value="member" }active{/eq} " >
            <a class=" user" href="{:url('member/index')}">我的</a>
			{gt name="inner_count" value="0"}
			<div class="red_d">{$inner_count}</div>
			{/gt}
		</li>
	</ul>
	<div class="am-share">
		<ul class="am-share-sns">
			<li><a href="{$global.kefu_l}">
					<span>在线客服</span> </a> </li>
			<li><a href="/wap.php/common/weixin.html"> <span>微信客服</span> </a> </li>
			<!-- <li><a href="tencent://Message/?Uin=192266666"> <span>QQ客服</span> </a> </li> -->
		</ul>
		<div class="am-share-footer"><button class="share_btn">取消</button></div>
	</div>
</div>


<script type="text/javascript">
$('#kefukuang').on('click', function(event) {
	$(".am-share").addClass("am-modal-active");
		if ($(".sharebg").length > 0) {
			$(".sharebg").addClass("sharebg-active");
		} else {
			$("body").append('<div class="sharebg"></div>');
			$(".sharebg").addClass("sharebg-active");
		}
		
});
$(".sharebg-active,.share_btn").click(function() {
			$(".am-share").removeClass("am-modal-active");
			setTimeout(function() {
				$(".sharebg-active").removeClass("sharebg-active");
				$(".sharebg").remove();
			}, 300);
		})
	function toshare() {

	}

	// 前往操盤功能
	function goToTrading() {
		var tradingUrl = '{$trading_login_url}';
		if (tradingUrl) {
			window.open(tradingUrl, '_blank');
		} else {
			alert('交易站點配置錯誤，請聯繫客服');
		}
	}
</script>


<!-- 统计代码 -->
{:htmlspecialchars_decode($global.count_code)}
<!-- 统计代码 -->

</body>
</html>
