<?php 

return [

    // 默认跳转页面对应的模板文件
    'dispatch_success_tmpl'  => APP_PATH . 'wap_jump.tpl',
    'dispatch_error_tmpl'    => APP_PATH . 'wap_jump.tpl',

    // 错误显示信息,非调试模式有效
    'error_message'          => '页面显示错误！请稍后再试或联系客服人员',
    // 异常页面的模板文件
    'exception_tmpl'         => APP_PATH . 'think_exception.tpl',

    /*'template'               => [
        'layout_on'     =>  true,
        'layout_name'   =>  'layout',
    ],*/

    'paginate'               => [
    	'type'      => 'demo2',
        'var_page'  => 'pager',
        'list_rows' => 10,
    ]

];