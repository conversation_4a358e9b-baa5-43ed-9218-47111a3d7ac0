<?php 
namespace app\wap\controller;

use think\Controller;

class Error extends Controller{
    protected function _initialize(){
        parent::_initialize();

        $this->assign('jump_url_404' , url('/') );
    }
    public function index(){
        // echo '空控制器 Admin Error';

        // abort(404,'当前地址不存在');
        $this->error('当前地址不存在');
    }

    public function _empty(){
        // echo '空操作！Admin Error';

        // abort(404,'当前操作地址不存在');
        $this->error('当前操作地址不存在');
    }

}