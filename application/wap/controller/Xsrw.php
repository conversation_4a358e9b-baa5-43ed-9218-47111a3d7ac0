<?php

namespace app\wap\controller;

use think\Controller;
use think\Request;
use app\common\controller\Wap;

class Xsrw extends Wap
{

    protected function _initialize(){
        parent::_initialize();
        
        $this->hola = 'Wap xsrw hola !!!';
    }
    
    public function index(){
        $xsrw_on = $this->global['xsrw_on'];
        $xsrw_money = explode('|', $this->global['xsrw_money']);
        $shiming_1 = $peizi_1 = $chognzhi_1 = $yhk_1 = $kdpz_1 = $bukui_1 = $tiying_1 = false;

        if( $this->uid ){
            $mod = db('xsrw');
            //实名认证
            $shiming = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['SHIMING_JL']])->count('id');
            $shiming2 = db('member_info')->where('uid',$this->uid)->where('id_status',1)->count('id');
            if( $shiming || $shiming2 ){
                $shiming_1 = true;
            }

            //首次配资
            $peizi = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['PEIZI_JL']])->count('id');
            $peizi2 = db('stock_borrow')->where('uid',$this->uid)->where('status','in','2,3')->count('id');
            if( $peizi || $peizi2 ){
                $peizi_1 = true;
            }

            //首次充值
            $chognzhi = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['CHONGZHI_JL']])->count('id');
            $chognzhi2 = db('member_recharge')->where('uid',$this->uid)->where('status',1)->count('id');
            if( $chognzhi || $chognzhi2 ){
                $chognzhi_1 = true;
            }

            //首次绑定银行卡
            $yhk = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['BDYHK_JL']])->count('id');
            $yhk2 = db('member_bank')->where('uid',$this->uid)->count('id');
            if( $yhk || $yhk2 ){
                $yhk_1 = true;
            }

            // 首次扩大配资奖励
            $kdpz = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['KDPEIZI_JL']])->count('id');
            $kdpz2 = db('stock_addfinancing')->where('uid',$this->uid)->where('status',1)->count('id');
            if( $kdpz || $kdpz2 ){
                $kdpz_1 = true;
            }

            // 首次补亏
            $bukui = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['BUKUI_JL']])->count('id');
            $bukui2 = db('stock_addmoney')->where('uid',$this->uid)->where('status',1)->count('id');
            if( $bukui || $bukui2 ){
                $bukui_1 = true;
            }

            // 首次提盈
            $tiying = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['TIYING_JL']])->count('id');
            $tiying2 = db('stock_drawprofit')->where('uid',$this->uid)->where('status',1)->count('id');
            if( $tiying || $tiying2 ){
                $tiying_1 = true;
            }
        }

        $this->assign('shiming_1',$shiming_1);
        $this->assign('peizi_1',$peizi_1);
        $this->assign('chognzhi_1',$chognzhi_1);
        $this->assign('yhk_1',$yhk_1);
        $this->assign('kdpz_1',$kdpz_1);
        $this->assign('bukui_1',$bukui_1);
        $this->assign('tiying_1',$tiying_1);
        $this->assign('money',$xsrw_money);
        return view();
    }



}
