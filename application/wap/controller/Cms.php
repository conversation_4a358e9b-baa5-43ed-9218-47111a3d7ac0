<?php

namespace app\wap\controller;

use think\Controller;
use think\Request;
use app\common\controller\Wap;

class Cms extends Wap
{

    protected function _initialize(){
        parent::_initialize();
        
        $this->hola = 'Wap cms hola !!!';
    }
    
    public function index(){
        $type_nid = input('param.tab/s','');
        if( !$type_nid ){
            $this->error('缺少参数！请按照平台规则重试');
        }
        $category = db('category');
        $data = $category->where('type_nid',$type_nid)->field(true)->find();
        if( !$data ){
            $this->error('该栏目内容未找到，请按照平台规则重试');
        }
        $type_set = $data['type_set'];
        if( $type_set==1 ){
            $web_title = $data['name'];

            $this->assign('data',$data);
            $this->assign('web_title',$web_title);
            return view('column');
        }else if( $type_set==2 ){
            $web_title = $data['name'];

            $list = db('article')
                        ->where('parent_id',$data['id'])
                        ->where('is_hide',1)
                        ->field('id,title,info,add_time')
                        ->order('sort_order DESC,add_time DESC')
                        ->paginate(10, true, ['query'=>request()->param()]);
                        // ->select();

            $page = $list->render();

            $this->assign('list',$list);
            $this->assign('page',$page);
            $this->assign('web_title',$web_title);
            return view('article');
        }else{
            $this->error('该栏目内容未找到，请按照平台规则重试');
        }
    }

    public function detial(){
        $aid = input('param.aid/d',0);
        if( !$aid ){
            $this->error('缺少参数！请按照平台规则重试');
        }
        $mod = db('article');
        $data = $mod->alias('a')
                ->join('category c','a.parent_id = c.id','left')
                ->where('a.id',$aid)
                ->field('a.*,c.name AS category_name,c.type_nid AS nid')
                ->find();
        // SEO元素
        $this->global['web_keywords'] = $data['keyword'];
        $this->global['web_descript'] = $data['info'];
        $this->assign('global',$this->global);

        $this->assign('data',$data);
        return view();
    }

    public function about(){
        $type_id = 71;//自定义typeid
        $type_list = db('category')
                    ->where('parent_id', $type_id)
                    ->where('is_hidden', 1)
                    ->field('id,name,type_nid,type_set')
                    ->order('sort_order desc')
                    ->select();
        foreach ($type_list as $k => &$v) {
            $v['style'] = '';
            if($k==0){
                continue;
            }
            if($k % 3 == 0){
                $v['style'] = 'mt08';
            }
        }
        unset($v);

        $this->assign('list',$type_list);
        return view();
    }

    public function help(){
        $type_id = 114;//自定义typeid
        $type_list = db('category')
                    ->where('parent_id', $type_id)
                    ->where('is_hidden', 1)
                    ->field('id,name,type_nid,type_set')
                    ->order('sort_order desc')
                    ->select();
        foreach ($type_list as $k => &$v) {
            $v['style'] = '';
            if($k==0){
                continue;
            }
            if($k % 3 == 0){
                $v['style'] = 'mt08';
            }
        }
        unset($v);

        $this->assign('list',$type_list);
        return view();
    }

}
