<?php

namespace app\wap\controller;

use app\common\model\MemberWithdraw;
use app\common\model\WithdrawalOrder;
use app\common\Services\Withdraw\GuantianService;
use think\Controller;
use think\Db;
use think\Request;
use think\Response;

class Withdraw extends Controller
{
    protected $guantianService;

    public function __construct(Request $request = null)
    {
        parent::__construct($request);

        $this->guantianService = new GuantianService();
    }

    public function guantianNotify(Request $request)
    {
        $data = $request->post();
        $sign = $data['sign'];
        unset($data['sign']);

        if (!$this->guantianService->verifySign($data, $sign)) {
            return Response::create(['message' => '签名错误'], 'json', 400);
        }

        $withdrawOrder = WithdrawalOrder::where('order_no', $data['user_order_no'])->find();

        if (!$withdrawOrder) {
            return Response::create(['message' => '订单不存在'], 'json', 400);
        }

        if ($withdrawOrder->status !== WithdrawalOrder::STATUS_PENDING) {
            return 'success';
        }

        if (cache("withdraw-{$withdrawOrder->id}")) {
            return Response::create(['status' => -1, 'message' => '该数据正在处理中，请稍后再试', 'json', 400]);
        }
        cache("withdraw-{$withdrawOrder->id}", true, 60 * 3);

        db()->startTrans();

        if ($data['code'] == 1) {
            // 更新订单状态
            $withdrawOrder->status = WithdrawalOrder::STATUS_SUCCESS;
            $withdrawOrder->save();
            cache("withdraw-{$withdrawOrder->id}", null);

            $memberWithdraw = MemberWithdraw::where('id', $withdrawOrder->member_withdraw_id)->find();
            // 站内信
            $title = '提现处理完成';
            $msg = '提现处理完成。手续费' . ($memberWithdraw->fee) / 100 . '元，实际到账' . ($memberWithdraw->account / 100) . '元。';

            $inner_res = addInnerMsg($memberWithdraw->uid, $title, $msg); //站内信

            $memberWithdraw->status = MemberWithdraw::STATUS_SUCCESS;
            $memberWithdraw->save();
        } else {
            $withdrawOrder->status = WithdrawalOrder::STATUS_FAILED;
            $withdrawOrder->save();
        }

        db()->commit();

        cache("withdraw-{$withdrawOrder->id}", null);

        return 'success';
    }
}