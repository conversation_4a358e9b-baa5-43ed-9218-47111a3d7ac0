<?php

namespace app\wap\controller;

use app\common\model\RechargeOrder;
use app\common\Services\Recharge\CheckoutService;
use think\Controller;
use think\Db;
use think\Request;
use think\Response;

class Recharge extends Controller
{
    protected $checkoutService;

    public function __construct(Request $request = null)
    {
        parent::__construct($request);

        $this->checkoutService = new CheckoutService();
    }

    public function checkoutNotify(Request $request)
    {
        $data = $request->post();
        $sign = $data['sign'];
        unset($data['sign']);

        if (!$this->checkoutService->verifySign($data, $sign)) {
            return Response::create(['message' => '签名错误'], 'json', 400);
        }

        $rechargeOrder = RechargeOrder::where('order_no', $data['user_order_no'])->find();

        if (!$rechargeOrder) {
            return Response::create(['message' => '订单不存在'], 'json', 400);
        }

        if ($rechargeOrder->status !== RechargeOrder::STATUS_PENDING) {
            return 'success';
        }

        if ($data['code'] == 1) {
            // Do something with the successful payment
            // 添加该记录锁定
            if (cache("recharge-{$rechargeOrder->id}")) {
                return Response::create(['status' => -1, 'message' => '该数据正在处理中，请稍后再试', 'json', 400]);
            }
            cache("recharge-{$rechargeOrder->id}", true, 60 * 3);
            // 开启事务
            db()->startTrans();
            Db::name('members')->lock(true);
            Db::name('member_info')->lock(true);
            Db::name('member_money')->lock(true);
            Db::name('member_moneylog')->lock(true);
            // 站内信
            $title = '充值订单通过';
            $msg = '充值订单通过，增加至可用余额';
            // 增加资金记录
            $mlog = memberMoneyLog($rechargeOrder->member_id, $data['amount'] * 100, $msg, 1);

//            if ($data['way'] == 'offline') {
//                // 线下充值返利
//                $rebate = (int)$this->global['rebate_num'];//千分比
//                if ($rebate > 0) {
//                    $rebate_money = $data['amount'] * ($rebate / 1000);
//                    $rebate_money = (int)$rebate_money;
//
//                    if ($rebate_money > 0) {
//                        $rebate_msg = '线下充值' . ($data['amount'] / 100) . '元，返利' . ($rebate_money / 100) . '元，已增加至管理费余额';
//                        $rebate_log = memberMoneyLog($rechargeOrder->member_id, $rebate_money, $rebate_msg, 10, 2);
//                    }
//                }
//
//                // 线下充值代理返利
//                sendRebateForDL('', $rechargeOrder->member_id, $rechargeOrder->id, 5);
//            }

            $xsrw = myXsrw($rechargeOrder->member_id, config('MY_XSRW')['CHONGZHI_JL']);
            file_put_contents('./xsrw.txt',
                date("Y-m-d H:i ") . "uid=" . $rechargeOrder->member_id . "&rw_id=" . config('MY_XSRW')['CHONGZHI_JL'] . "&xsrw:" . json_encode($xsrw) . "\r\n",
                FILE_APPEND);
            if ($mlog) {
                $ctip = clearTip($rechargeOrder->member_id, 1);
                $inner_res = addInnerMsg($rechargeOrder->member_id, $title, $msg);//站内信

                // 更新订单状态
                $rechargeOrder->amount = $data['amount'];
                $rechargeOrder->status = RechargeOrder::STATUS_SUCCESS;
                $rechargeOrder->paid_at = date('Y-m-d H:i:s');
                $rechargeOrder->save();

                cache("recharge-{$rechargeOrder->id}", null);//结束运行 释放记录锁
                db()->commit();
                return 'success';
            } else {
                cache("recharge-{$rechargeOrder->id}", null);//结束运行 释放记录锁
                db()->rollback();
                return Response::create(['status' => -5, 'message' => '处理失败，请稍后再试', 'json', 400]);
            }
        } else {
            $rechargeOrder->status = RechargeOrder::STATUS_FAILED;
            $rechargeOrder->save();
        }

        return 'success';
    }
}