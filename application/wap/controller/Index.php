<?php

namespace app\wap\controller;

use think\Controller;
use think\Request;
use app\common\controller\Wap;

class Index extends Wap
{

    protected function _initialize(){
        parent::_initialize();
        
        $this->hola = 'Wap index hola !!!';
    }
    
    public function index(){

        $banner = getBanner()['wap'];//wap轮播
        $noticeList = getArticleList(73, 8);

        $try_set = explode('|', $this->stock['try_set']);
        $free_set = explode('|', $this->stock['free_set']);
        $free_money_range = explode('|', $this->stock['free_money_range']);
        $day_money_range = explode('|', $this->stock['day_money_range']);
        $day_max_multiple = getmax_multiple(1);
        $month_money_range = explode('|', $this->stock['month_money_range']);
        $month_max_multiple = getmax_multiple(2);
        $vip_money_range = explode('|', $this->stock['vip_money_range']);
        $vip_max_multiple = getmax_multiple(8);

        $this->assign('noticeList',$noticeList);
        $this->assign('banner',$banner);

        $this->assign('try_set',$try_set);
        $this->assign('free_set',$free_set);
        $this->assign('free_money_range',$free_money_range);
        $this->assign('day_money_range',$day_money_range);
        $this->assign('day_max_multiple',$day_max_multiple);
        $this->assign('month_money_range',$month_money_range);
        $this->assign('month_max_multiple',$month_max_multiple);
        $this->assign('vip_money_range',$vip_money_range);
        $this->assign('vip_max_multiple',$vip_max_multiple);
        return view();
    }

    public function test(){

        $a = controller('index/stock');
        $res = $a->stopfinancingApply();

        var_dump($res);
    }


}
