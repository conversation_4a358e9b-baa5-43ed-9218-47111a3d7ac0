<?php

namespace app\wap\controller;

use think\Controller;
use think\Request;
use app\common\controller\Wap;

class Common extends Wap
{

    protected function _initialize(){
        parent::_initialize();
        
        $this->hola = 'Wap common hola !!!';
    }

    public function login(){
        if( session('?uid') ){
            $this->redirect('member/index');
        }
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);

            $u_info = db('members')->where('user_name',$data['username'])
                        ->field('id,user_name,user_pass,is_ban')
                        ->find();
            if( !$u_info ){
                return ['status'=>-1,'message'=>'无该用户信息！'];
            }
            if( $u_info['is_ban']==1 ){
                return ['status'=>-1,'message'=>'该账户已冻结！有问题联系在线客服'];
            }
            $v_pass = v_pass($data['password'] , $u_info['user_pass']);
            $a = controller('index/common');
            if( $v_pass ){
                $log = $a->_loginlog($u_info['id'],true);
            }else{
                $stime = strtotime( date("Y-m-d", time()) );
                $etime = strtotime( date("Y-m-d", time()).' 23:59:59' );
                $login_count = db('member_login')
                        ->where(['uid'=>$u_info['id'],'is_success'=>0])
                        ->whereTime('add_time','between',[$stime,$etime])
                        ->count('id');
                if( $login_count>10 ){
                    return ['status'=>-2,'message'=>'今天登陆错误次数过多，请明天再试！'];
                }
                $log = $a->_loginlog($u_info['id'],false);

                return ['status'=>-3,'message'=>'密码输入错误！请重试'];
            }

            session('uid',$u_info['id']);
            session('uname',$u_info['user_name']);
            return ['status'=>'1','message'=>'登录成功'];
        }else{

            // session('hola','123123');
            // dump( session('hola') );

            return view();
        }
    }
    public function logout(){
        session('uid',null);
        session('uname',null);
        
        return ['status'=>1,'message'=>'退出成功'];
    }

    public function register(){
        if( session('?uid') ){
            $this->redirect('member/index');
        }
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $a = controller('index/common');
            $res = $a->register();

            return $res;
        }else{
            $invite = input('param.invite/s','');

            $this->assign('invite',$invite);
            return view();
        }
    }
    public function sendSMS(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $source = input('post.source/s','');
            if( $source=='findpwd' ){
                // 来源于找回密码
                $uid = db('member_info')->where('phone',$data['phone'])->value('uid');
                if( !$uid ){
                    return ['status'=>-1,'message'=>'该手机号未绑定用户信息！'];
                }
            }

            $a = controller('index/common');
            $res = $a->user_verify();

            return $res;
        }else{
            abort(404,'当前页面不存在');
        }
    }




    public function findpwd(){
        if( session('?uid') ){
            $this->redirect('member/index');
        }

        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $uid = db('member_info')->where('phone',$data['phone'])->value('uid');
            if( !$uid ){
                return ['status'=>-1,'message'=>'该手机号未绑定用户信息！'];
            }
            if( verify_code($data['phone'],$data['vcode']) ){
                cookie('temp_findpwd_verify', $uid , 60*5);

                return ['status'=>1,'message'=>'验证成功'];
            }else{
                return ['status'=>-3,'message'=>'短信验证码错误'];
            }

        }else{

            return view();
        }
    }
    public function findpwd2(){
        if( session('?uid') ){
            $this->redirect('member/index');
        }
        
        $request = request();
        if( $request->isAjax() ){
            $uid = cookie('temp_findpwd_verify');
            if( !$uid ){
                return ['status'=>-1,'message'=>'身份验证超时，请返回重新验证'];
            }
            $pass = input('post.pass/s','');
            $pass2 = input('post.pass2/s','');
            if( $pass!=$pass2 ){
                return ['status'=>-2,'message'=>'两次密码输入不一致！'];
            }
            $newp = set_pass($pass);
            $res = db('members')->where('id',$uid)->update(['user_pass'=>$newp]);
            if( $res ){
                cookie('temp_findpwd_verify',null);

                return ['status'=>1,'message'=>'修改成功，请重新登录'];
            }else{
                return ['status'=>-3,'message'=>'修改失败，请重试'];
            }
        }else{
            if( cookie('temp_findpwd_verify') ){

                return view();
            }else{
                abort(404,'验证超时,请返回重试');
            }
        }

    }

    public function hetong(){
        $category = input('param.c/d',1);
        $borrow_id = input('param.id/d',0);

        $stock = array();
        if( $borrow_id ){
            $stock = model('admin/Stock')->getStock($borrow_id);

            $w = array('borrow_id'=>$stock['id'], 'uid'=>$stock['uid'], 'status'=>1);
            $sum1 = db('stock_addfinancing')->where($w)->sum('borrow_fee');
            $sum2 = db('stock_renewal')->where($w)->sum('borrow_fee');
            $stock['sumInterest_display'] = $stock['borrow_interest'] + $sum1 + $sum2;
        }

        $this->assign('stock',$stock);
        if( $category==1 ){
            $this->assign('data',hetong(1));
            return view();
        }else{
            $this->assign('data',hetong(2));
            return view('qihuohetong');
        }
    }

    public function wapapp(){

        return view();
    }
    public function download(){

        return view();
    }
	public function weixin(){
	
	    return view();
	}


}
