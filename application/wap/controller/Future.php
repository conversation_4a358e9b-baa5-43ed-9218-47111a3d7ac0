<?php

namespace app\wap\controller;

use think\Controller;
use think\Request;
use app\common\controller\Wap;

class Future extends Wap
{

    protected function _initialize(){
        parent::_initialize();

        // 当前时间是否可申请
        $couldApplyToday = couldApplyToday();
        $this->assign('couldApplyToday',$couldApplyToday);

        $this->hola = 'Wap future hola !!!';
    }

    public function trial(){
        $request = request();
        if( $request->isAjax() ){
            $a = controller('index/future');
            $res = $a->tryFinancingApply();

            return $res;
        }else{

            $try_set = explode('|', $this->future['try_set']);

            $this->assign('try_set',$try_set);
            return view();
        }
    }

    public function free(){
        $money_range = explode( "|", $this->future['free_money_range']);//资金范围
        $free_loss = explode( "|", $this->future['free_loss']);//亏损比例
        $free_set = explode( "|", $this->future['free_set']);//

        $this->assign('moneyRange',$money_range);
        $this->assign('free_loss',$free_loss);
        $this->assign('free_set',$free_set);
        return view();
    }

    public function day(){
        $money_range = explode('|', $this->future['day_money_range']);
        $max_multiple = getmax_multiple(1);
        $day_loss = explode( "|", $this->future['day_loss']);
        $use_day = explode( "|", $this->future['day_use_time']);

        $rateInfo = $this->future_rate[1];
        $i = 0;
        $rateArr = array();
        foreach($rateInfo as $k=>$v){
            $rateArr[$i]['multiple'] = $k;
            $rateArr[$i]['rate'] = $v;
            if($i % 3 == 0){
                $rateArr[$i]['ml0'] = 1;
            }
            $i++;
        }

        $this->assign('moneyRange',$money_range);
        $this->assign('day_loss',$day_loss);
        $this->assign('use_day',$use_day);
        $this->assign('rateArr',$rateArr);
        $this->assign('max_multiple',$max_multiple);
        return view();
    }

    public function month(){
        $money_range = explode( "|", $this->future['month_money_range']);//资金范围
        $max_multiple = getmax_multiple(2);
        $use_month = explode( "|", $this->future['month_use_time']);//使用期限
        $month_loss = explode( "|", $this->future['month_loss']);//亏损比例
        $rateInfo = $this->future_rate[2];

        $i = 0;
        $rateArr = array();
        foreach($rateInfo as $k=>$v){
            $rateArr[$i]['multiple'] = $k;
            $rateArr[$i]['rate'] = $v;
            if($i % 3 == 0){
                $rateArr[$i]['ml0'] = 1;
            }
            $i++;
        }

        $this->assign('moneyRange',$money_range);
        $this->assign('max_multiple',$max_multiple);
        $this->assign('month_loss',$month_loss);
        $this->assign('use_month',$use_month);
        $this->assign('rateArr',$rateArr);
        return view();
    }
    public function vip(){
        $vip_range = explode( "|", $this->future['vip_money_range']);//资金范围
        $max_multiple = getmax_multiple(8);
        $use_vip = explode( "|", $this->future['vip_use_time']);//使用期限
        $vip_loss = explode( "|", $this->future['vip_loss']);//亏损比例
        $rateInfo = $this->future_rate[8];
        
        $i = 0;
        $rateArr = array();
        foreach($rateInfo as $k=>$v){
            $rateArr[$i]['multiple'] = $k;
            $rateArr[$i]['rate'] = $v;
            if($i % 3 == 0){
                $rateArr[$i]['ml0'] = 1;
            }
            $i++;
        }
        
        $this->assign('moneyRange',$vip_range);
        $this->assign('max_multiple',$max_multiple);
        $this->assign('vip_loss',$vip_loss);
        $this->assign('use_vip',$use_vip);
        $this->assign('rateArr',$rateArr);
        return view();
    }

    public function createOrder(){
        $request = request();
        if( $request->isAjax() ){
            $a = controller('index/future');
            $res = $a->createOrder();

            return $res;
            // var_dump(input('post.'));die;
        }else{
            abort(404,'页面不存在');
        }
    }


}
