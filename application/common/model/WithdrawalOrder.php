<?php

namespace app\common\model;

use think\Model;

class WithdrawalOrder extends Model
{
    protected $table = 'zh_withdrawal_orders';

    const PAYMENT_PROVIDER_GUANTIAN = 'guantian';

    public static $paymentProviderMap = [
        self::PAYMENT_PROVIDER_GUANTIAN => '冠天',
    ];

    const PAYMENT_METHOD_BANK = 'bank';

    public static $paymentMethodMap = [
        self::PAYMENT_METHOD_BANK => '银行卡',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';

    public static $statusMap = [
        self::STATUS_PENDING => '处理中',
        self::STATUS_SUCCESS => '提现成功',
        self::STATUS_FAILED => '提现失败',
    ];


    public static function generateOrderNo()
    {
        do {
            $orderNo = date('YmdHis') . randomStr(6);
        } while (self::where('order_no', $orderNo)->find());

        return $orderNo;
    }
}
