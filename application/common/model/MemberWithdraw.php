<?php

namespace app\common\model;

use think\Model;

class MemberWithdraw extends Model
{
    protected $table = 'zh_member_withdraw';

    const STATUS_PENDING = 0;
    const STATUS_SUCCESS = 1;
    const STATUS_PROCESSING = 2;
    const STATUS_FAILED = -1;

    public static $statusMap = [
        self::STATUS_PENDING => 'Pending',
        self::STATUS_SUCCESS => 'Success',
        self::STATUS_PROCESSING => 'Processing',
        self::STATUS_FAILED => 'Failed',
    ];
}
