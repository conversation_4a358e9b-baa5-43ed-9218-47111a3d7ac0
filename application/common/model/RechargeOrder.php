<?php

namespace app\common\model;

use think\Model;

class RechargeOrder extends Model
{
    protected $table = 'zh_recharge_orders';

    const PAYMENT_PROVIDER_CHECKOUT = 'checkout';

    public static $paymentProviderMap = [
        self::PAYMENT_PROVIDER_CHECKOUT => '收银台',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';

    public static $statusMap = [
        self::STATUS_PENDING => '待支付',
        self::STATUS_SUCCESS => '支付成功',
        self::STATUS_FAILED => '支付失败',
    ];


    public static function generateOrderNo()
    {
        do {
            $orderNo = date('YmdHis') . randomStr(6);
        } while (self::where('order_no', $orderNo)->find());

        return $orderNo;
    }
}
