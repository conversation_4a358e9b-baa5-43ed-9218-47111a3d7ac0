<?php

namespace app\common\controller;

use think\Controller;
use think\Request;

class Member extends Controller
{
    protected $hola = '';
    protected $uid = null;
    protected $uname = null;
    protected $global = null;
    protected $stock = null;
    protected $future = null;
    protected $kefu = null;
    protected $minfo = null;
    protected $navigation = null;
    protected $inner_count = 0;

    public function __construct(Request $request = null)
    {
        parent::__construct($request);

        Header("HTTP/1.1 301 Moved Permanently");
        $red = request()->domain() . '/wap.php';
        $this->redirect($red, 301);
    }

    protected function _initialize(){
        parent::_initialize();

        // echo 'Common Member _initialize<hr/>';

        if( !session('?uid') ){
            $this->error('请进行登录~','Common/login');
        }else{
            $this->uid = session('uid');
            $this->uname = session('uname');
        }
        $this->assign('uid',$this->uid);
        $this->assign('uname',$this->uname);
        
        $this->hola = 'Common Member hola !!!';
        $this->stock = get_stock_setting();
        $this->future = get_future_setting();
        $this->global = get_global_setting();
        $this->kefu = get_kefu();
        $this->minfo = memberInfo($this->uid);
        $this->navigation = getNav();
        $this->inner_count = getInnerCount($this->uid);
        $last_login = last_login($this->uid);

        $this->assign('last_login',$last_login);
        $this->assign( 'hola',$this->hola );
        $this->assign( 'global',$this->global );
        $this->assign( 'stock',$this->stock );
        $this->assign( 'kefu',$this->kefu );
        $this->assign( 'minfo',$this->minfo );
        $this->assign( 'navigation',$this->navigation );
        $this->assign( 'inner_count',$this->inner_count );
    }

    public function _empty(){
        // echo '空操作！Member Controller ';
        // echo 'At Member Controller <hr>';die();

        $this->error('页面未找到','/members');
    }
    
}
