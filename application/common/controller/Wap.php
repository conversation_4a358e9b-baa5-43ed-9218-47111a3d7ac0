<?php

namespace app\common\controller;

use think\Controller;
use think\Request;

class Wap extends Controller
{
    protected $hola = '';
    protected $uid = 0;
    protected $uname = null;
    protected $global = null;
    protected $stock = null;
    protected $future = null;
    protected $rateset = null;
    protected $future_rate = null;

    protected $kefu = null;
    protected $inner_count = 0;
    protected $empty_data = null;
    protected $controllerName = null;
    protected function _initialize(){
        parent::_initialize();

        // echo 'Common Member _initialize<hr/>';

        $this->uid = session('uid');
        $this->uname = session('uname');
        $this->assign('uid',$this->uid);
        $this->assign('uname',$this->uname);
        
        $this->hola = 'Common Member hola !!!';
        $this->global = get_global_setting();
        $this->stock = get_stock_setting();
        $this->future = get_future_setting();
        $this->rateset = get_stock_rate();
        $this->future_rate = get_future_rate();
        
        $this->kefu = get_kefu();
        $this->inner_count = getInnerCount($this->uid);
        $this->controllerName = strtolower(request()->controller());

        // 讀取交易站點登入URL配置
        // 由於環境變量加載問題，暫時直接從 .env 文件讀取
        $envFile = dirname(dirname(dirname(__DIR__))) . '/.env';
        $tradingLoginUrl = 'https://trade.35211m.com/login/'; // 默認值

        if (is_file($envFile)) {
            $env = parse_ini_file($envFile, true);
            if (isset($env['trading_site']['login_url'])) {
                $tradingLoginUrl = $env['trading_site']['login_url'];
            }
        }

        $this->empty_data = '<div class="emptydata"><p>呃...没有该记录!</p></div>';
        $this->assign( 'empty_data',$this->empty_data );

        $this->assign( 'hola',$this->hola );
        $this->assign( 'global',$this->global );
        $this->assign( 'stock',$this->stock );
        $this->assign( 'kefu',$this->kefu );
        $this->assign( 'controllerName',$this->controllerName );
        $this->assign( 'trading_login_url',$tradingLoginUrl );

        $this->assign( 'inner_count',$this->inner_count );
    }

    public function _empty(){
        // echo '空操作！Member Controller ';
        // echo 'At Member Controller <hr>';die();

        $this->error('页面未找到');
    }
    
}
