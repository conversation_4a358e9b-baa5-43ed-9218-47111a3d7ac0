<?php 
namespace app\common\controller;

use think\Controller;

class Admin extends Controller{

    protected $empty_data = '';
    protected $controllerName = '';
    protected $actionName = '';
    protected $global = null;
    protected $stock = null;
    protected $future = null;
    protected $admin_id = null;
    protected $admin_name = null;
    protected $admin_group = null;
    protected function _initialize(){
        parent::_initialize();
        // echo 'Admin init<hr/>';
        $this->hola = 'Admin hola !!!';
        
        $this->controllerName = strtolower(request()->controller());
        $this->actionName = request()->action();
        
        // 检测登录
        $this->ckLogin();
        // 检测ip
        //$this->checkIp();
        // 检测权限
        $this->rabc();

        $this->admin_id = session('admin_id');
        $this->admin_name = session('admin_name');
        $this->admin_group = session('admin_level');

        $this->assign('admin_id' , session('admin_id'));
        $this->assign('admin_name' , session('admin_name'));
        $this->assign('admin_level' , session('admin_level'));
        $this->assign('admin_group' , session('admin_level'));

        $this->global = get_global_setting();
        $this->assign( 'global',$this->global );
        
        $this->stock = get_stock_setting();
        $this->future = get_future_setting();

        $this->empty_data = admin_empty_data();
        $this->assign('empty_data',$this->empty_data);

        // 选择菜单
        $this->setMenu();
    }

    protected function ckLogin(){
        if( !session('?admin_id') ){
            $this->redirect('Common/index');
        }
        return false;
    }
    protected function checkIp(){
        $controller = strtolower($this->controllerName);
        $action = strtolower($this->actionName);
        $allow_action = ['index','setiptip','clearall','verifyip1','verifyip2'];

        if( $controller!='index' || !in_array($action, $allow_action) ){
            $allow = admin_verifyip( request()->ip() );

            if( !$allow ){
                $this->redirect('Index/setipTip');
            }
        }
        return true;
    }

    public function _empty(){
        // echo '空操作！AdminController';
        $this->error('当前地址不存在，请在网站中手动点击链接');
    }

    protected function rabc(){
        $gid = session('admin_level');
        $controllerName = $this->controllerName;
        $actionName = $this->actionName;

        $result = admin_acl($gid, $controllerName, $actionName);
        if( $result===false ){
            $this->error('对不起，权限不足', url('/'));
        }

    }

    private function setMenu(){
        $menu = config('menu');
        $bind = $this->controllerName;
        $actionName = $this->actionName;

        // 解决菜单高亮
        foreach ($menu as $v) {
            if($v['bind'] == $bind){
                $bindMenu = $v['info'];
                break;
            }
        }
        unset($v);
        // 解决栏目是否展开
        foreach ($bindMenu as &$v) {
            $v['is_open'] = false;
            foreach ($v['list'] as $v2) {
                if(strtolower($v2[2]) == $actionName){
                    $v['is_open'] = true;
                    break;
                }
            }
        }
        unset($v);

        $this->assign('menu',$menu);
        $this->assign('bindMenu',$bindMenu);
        $this->assign('controllerName',$bind);
        $this->assign('actionName',$actionName);
        $this->assign('menuJson', json_encode($menu));

        // dump($actionName);
        // dump($bindMenu);die;
        // dump($menu);die;
    }




}