<?php

namespace app\common\controller;

use think\Controller;
use think\Request;

class Common extends Controller
{
    protected $uid = null;
    protected $uname = null;
    protected $hola = null;
    protected $global = null;
    protected $stock = null;
    protected $future = null;
    protected $rateset = null;
    protected $future_rate = null;
    
    protected $kefu = null;
    protected $navigation = null;
    protected $inner_count = 0;

    public function __construct(Request $request = null)
    {
        parent::__construct($request);

//        Header("HTTP/1.1 301 Moved Permanently");
//        $red = request()->domain() . '/wap.php';
//        $this->redirect($red, 301);
    }

    protected function _initialize(){
        parent::_initialize();

        // echo 'Common _initialize<hr/>';
        $this->hola = 'Common hola !!!';

        if( session('?uid') ){
            $this->uid = session('uid');
            $this->uname = session('uname');
        }
        $this->assign('uid',$this->uid);
        $this->assign('uname',$this->uname);

        $this->stock = get_stock_setting();
        $this->future = get_future_setting();
        $this->global = get_global_setting();
        $this->rateset = get_stock_rate();
        $this->future_rate = get_future_rate();
        $this->kefu = get_kefu();
        $this->navigation = getNav();
        $this->inner_count = getInnerCount($this->uid);

        $this->assign( 'hola',$this->hola );
        $this->assign( 'stock',$this->stock );
        $this->assign( 'global',$this->global );
        $this->assign( 'kefu',$this->kefu );
        $this->assign( 'navigation',$this->navigation );
        $this->assign( 'inner_count',$this->inner_count );
    }

    public function _empty(){
        // echo '空操作！IndexController ';
        // echo 'At CommonController _empty <hr>';
        
        $this->error('页面未找到','/');
    }
    
}
