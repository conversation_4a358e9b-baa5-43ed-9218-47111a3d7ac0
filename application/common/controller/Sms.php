<?php
/**
 * Create by PhpStorm
 * User:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date:2020/5/9
 * Time:19:18
 */
namespace app\common\controller;

use think\Controller;
use think\Request;
use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;

class Sms extends Controller
{
    public function sendcode($phone,$code)
    {
        AlibabaCloud::accessKeyClient('LTAI4G4WriYNKJspY7BG6Fik', '******************************')
            ->regionId('cn-hangzhou')
            ->asDefaultClient();

        try {
            $code = [
                'code' => $code
            ];
            $result = AlibabaCloud::rpc()
                ->product('Dysmsapi')
                // ->scheme('https') // https | http
                ->version('2017-05-25')
                ->action('SendSms')
                ->method('POST')
                ->host('dysmsapi.aliyuncs.com')
                ->options([
                    'query' => [
                        'RegionId' => "cn-hangzhou",
                        'PhoneNumbers' => $phone,
                        'SignName' => "荣耀网站",
                        'TemplateCode' => "SMS_189761410",
                        'TemplateParam' => json_encode($code),
                    ],
                ])
                ->request();
            if ($result['Code'] == 'OK')
            {
                return true;
            } else{
                return false;
            }
        } catch (ClientException $e) {
            echo $e->getErrorMessage() . PHP_EOL;
        } catch (ServerException $e) {
            echo $e->getErrorMessage() . PHP_EOL;
        }
    }
}