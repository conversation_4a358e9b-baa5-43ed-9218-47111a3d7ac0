<?php

namespace app\common\Services;

use think\Log;

class TransactionApiService
{
    protected $baseUrl;
    protected $defaultHeaders;

    public function __construct()
    {
        $this->baseUrl = config('peizi.transaction_api')['base_url'];
        if (empty($this->baseUrl)) {
            Log::error('交易站 API 基础 URL (peizi.transaction_api.base_url) 未在 application/extra/peizi.php 或对应的 .env (transaction_api.base_url) 中设定。');
        }

        $this->defaultHeaders = [
            'User-Agent: Apifox/1.0.0 (https://apifox.com)',
            'Accept: */*',
            'Connection: keep-alive'
        ];
    }

    /**
     * 注册客户 (调用 /customer/registcustomer)
     *
     * @param string $u_loginname 登录名 (API: u_loginname)
     * @param string $u_cnname 中文名 (API: u_cnname)
     * @param string $u_password 密码 (API: u_password)
     * @param string $u_password_real 密码 (API: u_password_real)
     * @param string|null $u_phone 手机号码 (API: u_phone, 可选)
     * @param int $a_loginid 关联登录ID (API: a_loginid, 范例为 0, 通常指代理或介绍人ID)
     * @param string|null $u_addtime 用户添加时间 (API: u_addtime, 格式如 '2024/01/01 10:10:10', 可选)
     * @param float $u_initcreditvalue 初始信用额度/本金 (API: u_initcreditvalue, 例如保证金)
     * @param float $u_givencredit 授信额度/配资额 (API: u_givencredit, 例如借入的资金)
     * @param float $u_totalcreditvalue 总信用额度/总操盘资金 (API: u_initcreditvalue + u_givencredit)
     * @param float $u_multiple 杠杆倍数 (API: u_multiple)
     * @param bool $u_cordontype 是否启用预警线 (API: u_cordontype)
     * @param float $u_cordon 预警线金额 (API: u_cordon) - 注意：API范例是金额
     * @param bool $u_closinglinetype 是否启用平仓线 (API: u_closinglinetype)
     * @param float $u_closingline 平仓线金额 (API: u_closingline) - 注意：API范例是金额
     * @param string|null $u_endtime 用户账户到期时间 (API: u_endtime, 格式如 '2030/01/01 10:10:10', 可选)
     * @param float $u_commission 佣金率 (API: u_commission, 范例为 0)
     * @param float $u_transferfee 过户费率 (API: u_transferfee, 范例为 0)
     * @param float $u_stampduty 印花税率 (API: u_stampduty, 范例为 0)
     * @return array 成功时返回 API 回应中的客户数据部分 (或标识已存在)，失败时返回包含 error=true 的错误数组
     */
    public function registerCustomer(
        string $u_loginname,
        string $u_cnname,
        string $u_password,
        string $u_password_real,
        ?string $u_phone = null,
        int $a_loginid = 0,
        ?string $u_addtime = null,
        float $u_initcreditvalue = 0.0,
        float $u_givencredit = 0.0,
        float $u_totalcreditvalue = 0.0,
        float $u_multiple = 1.0,
        bool $u_cordontype = false,
        float $u_cordon = 0.0,
        bool $u_closinglinetype = false,
        float $u_closingline = 0.0,
        ?string $u_endtime = null,
        float $u_commission = 0.0,
        float $u_transferfee = 0.0,
        float $u_stampduty = 0.0
    ) {
        $endpoint = '/customer/registcustomer';
        $payload = [
            "u_loginname" => $u_loginname,
            "u_cnname" => $u_cnname,
            "u_password" => $u_password,
            "u_password_real" => $u_password_real,
            "u_phone" => $u_phone,
            "a_loginid" => $a_loginid,
            "u_addtime" => $u_addtime ?: date('Y/m/d H:i:s'),
            "u_initcreditvalue" => $u_initcreditvalue,
            "u_givencredit" => $u_givencredit,
            "u_totalcreditvalue" => $u_totalcreditvalue,
            "u_multiple" => $u_multiple,
            "u_cordontype" => $u_cordontype,
            "u_cordon" => $u_cordon,
            "u_closinglinetype" => $u_closinglinetype,
            "u_closingline" => $u_closingline,
            "u_endtime" => $u_endtime,
            "u_commission" => $u_commission,
            "u_transferfee" => $u_transferfee,
            "u_stampduty" => $u_stampduty,
        ];

        $payload = array_filter($payload, function ($value) {
            return $value !== null;
        });

        $response = $this->request($endpoint, 'POST', $payload, ['Content-Type' => 'application/json']);

        if (isset($response['error']) && $response['error'] === true) {
            return $response;
        }

        if (is_array($response) && count($response) === 2 && isset($response[1]['code'])) {
            $apiCode = $response[1]['code'];
            $apiDescription = $response[1]['description'] ?? '无描述';

            if ($apiCode === '000000') {
                Log::info('TransactionApiService: registerCustomer 成功 - Loginname: ' . $u_loginname);
                return $response[0];
            } elseif ($apiCode === '00006') {
                Log::info('TransactionApiService: registerCustomer 用户已存在 - Loginname: ' . $u_loginname . ' Description: ' . $apiDescription);
                return ['already_exists' => true, 'data' => $response[0], 'description' => $apiDescription];
            } else {
                Log::error('TransactionApiService: registerCustomer 业务逻辑错误 - Loginname: ' . $u_loginname . ' Code: ' . $apiCode . ' Description: ' . $apiDescription);
                return ['error' => true, 'code' => $apiCode, 'description' => $apiDescription, 'message' => '注册客户API返回业务错误: ' . $apiDescription];
            }
        }

        Log::error('TransactionApiService: registerCustomer 响应格式错误 - Loginname: ' . $u_loginname . ' Response: ' . json_encode($response));
        return ['error' => true, 'message' => '注册客户API响应格式不正确', 'response_data' => $response];
    }

    /**
     * 根据登录名获取客户模型 (调用 /customer/GetModelByLoginname)
     *
     * @param string $loginname 登录名
     * @return array|false 成功时返回 API 回应中的客户数据部分，失败时返回 false 或包含错误的数组
     */
    public function getCustomerByLoginname(string $loginname)
    {
        $endpoint = '/customer/GetModelByLoginname';
        $params = ['loginname' => $loginname];

        $response = $this->request($endpoint, 'GET', $params);

        if (isset($response['error']) && $response['error'] === true) {
            return $response;
        }

        if (is_array($response) && count($response) === 2 && isset($response[1]['code'])) {
            $apiCode = $response[1]['code'];
            $apiDescription = $response[1]['description'] ?? '无描述';
            if ($apiCode === '000000') {
                Log::info('TransactionApiService: getCustomerByLoginname 成功 - Loginname: ' . $loginname);
                return $response[0];
            } else {
                Log::warning('TransactionApiService: getCustomerByLoginname 业务逻辑信息 - Loginname: ' . $loginname . ' Code: ' . $apiCode . ' Description: ' . $apiDescription);
                return ['error' => true, 'code' => $apiCode, 'description' => $apiDescription, 'message' => '获取客户信息API返回业务消息: ' . $apiDescription];
            }
        }

        Log::error('TransactionApiService: getCustomerByLoginname 响应格式错误 - Loginname: ' . $loginname . ' Response: ' . json_encode($response));
        return ['error' => true, 'message' => '获取客户信息API响应格式不正确', 'response_data' => $response];
    }

    /**
     * 修改客户的授信额度 (调用 /customer/Modify_AddGivenCredit)
     *
     * @param string $loginname 登录名
     * @param float $creditValue 要增加的授信额度 (可以是负数，单位：元)
     * @return bool|array 成功时返回 true，失败时返回 false 或包含错误的数组
     */
    public function addGivenCredit(string $loginname, float $creditValue)
    {
        $endpoint = '/customer/Modify_AddGivenCredit';
        $payload = [
            "u_loginname" => $loginname,
            "u_addgivencreditvalue" => $creditValue
        ];

        $response = $this->request($endpoint, 'POST', $payload, ['Content-Type' => 'application/json']);

        if (isset($response['error']) && $response['error'] === true) {
            if (isset($response['http_code']) && $response['http_code'] == 500 && isset($response['exceptionMessage']) && strpos($response['exceptionMessage'], '尝试除以零') !== false) {
                Log::error('TransactionApiService: addGivenCredit 交易站API内部错误 (除以零) - Loginname: ' . $loginname . ' Value: ' . $creditValue . ' Response: ' . json_encode($response));
                return ['error' => true, 'message' => '交易站处理此请求时发生内部错误(除以零)，请联系交易站管理员。', 'api_response' => $response];
            }
            return $response;
        }

        if (is_array($response) && count($response) === 2 && isset($response[1]['code'])) {
            $apiCode = $response[1]['code'];
            $apiDescription = $response[1]['description'] ?? '无描述';
            if ($apiCode === '000000') {
                Log::info('TransactionApiService: addGivenCredit 成功 - Loginname: ' . $loginname . ' Value: ' . $creditValue);
                return true;
            } elseif ($apiCode === '000001') {
                Log::error('TransactionApiService: addGivenCredit 参数无效 (可能帐号不存在) - Loginname: ' . $loginname . ' Code: ' . $apiCode . ' Description: ' . $apiDescription);
                return ['error' => true, 'code' => $apiCode, 'description' => $apiDescription, 'message' => '修改授信失败：交易站返回无效参数（可能帐号不存在）。'];
            } else {
                Log::error('TransactionApiService: addGivenCredit 业务逻辑错误 - Loginname: ' . $loginname . ' Code: ' . $apiCode . ' Description: ' . $apiDescription);
                return ['error' => true, 'code' => $apiCode, 'description' => $apiDescription, 'message' => '修改授信API返回业务错误: ' . $apiDescription];
            }
        }

        Log::error('TransactionApiService: addGivenCredit 响应格式错误 - Loginname: ' . $loginname . ' Response: ' . json_encode($response));
        return ['error' => true, 'message' => '修改授信API响应格式不正确', 'response_data' => $response];
    }
    /**
     * 添加账户日志 (调用 /fstock/v2/api/AccountLog/Add)
     *
     * @param string $u_loginname 登录名
     * @param string $u_cnname 中文名
     * @param float $u_initcreditvalue 初始信用额度/本金 (对应 API 的 deal_account)
     * @param float $u_totalcreditvalue 总信用额度/总操盘资金 (对应 API 的 deal_newcreditvalue)
     * @return array 成功时返回 API 回应 (包含 success=true)，失败时返回包含 error=true 的错误数组
     */
    public function addAccountLog(
        string $u_loginname,
        string $u_cnname,
        float $u_initcreditvalue,
        float $u_totalcreditvalue
    ) {
        $endpoint = '/AccountLog/Add'; // 修正路径，baseUrl 已包含 /fstock/v2/api
        
        // 生成类似 C# Guid.NewGuid().ToString().Replace("-","") 的随机码
        // PHP 7+ 可以使用 random_bytes 来生成一个安全的随机十六进制字符串
        // bin2hex(random_bytes(16)) 会产生一个32位的十六进制字符串
        try {
            $generated_id = bin2hex(random_bytes(16));
        } catch (\Exception $e) {
            // 如果 random_bytes 由于某种原因失败 (不太可能在 PHP 7+ 中发生)
            // 使用一个备用方法，尽管它不如 random_bytes 安全或唯一
            Log::warning('TransactionApiService: addAccountLog - random_bytes() failed, using fallback for ID generation. Error: ' . $e->getMessage());
            $generated_id = md5(uniqid(rand() . $u_loginname, true));
        }

        $payload = [
            "_id" => $generated_id,
            "u_loginname" => $u_loginname,
            "u_cnname" => $u_cnname,
            "account_time" => date('Y-m-d H:i:s'), // 必须是现在时间
            "operate" => "注入用户本金", // 预设字串
            "deal_account" => $u_initcreditvalue, // 对应u_initcreditvalue
            "deal_newcreditvalue" => $u_totalcreditvalue, // 对应u_totalcreditvalue
            "stockcode" => "-", // 不可为空预设-即可
            "stockname" => "-", // 不可为空预设-即可
            "deal_billno" => "" // 传空值
        ];

        $response = $this->request($endpoint, 'POST', $payload, ['Content-Type' => 'application/json']);

        // 首先检查是否有 cURL 错误或基础 URL 未设置等由 request 方法直接返回的错误
        if (isset($response['error']) && $response['error'] === true && isset($response['message'])) {
            // 这些是请求级别或配置级别的错误，直接返回
            Log::error('TransactionApiService: addAccountLog 请求准备/执行失败 - Loginname: ' . $u_loginname . ' Message: ' . $response['message'] . ' FullResponse: ' . json_encode($response));
            return $response;
        }

        // 处理 HTTP 500 错误，特别是 "Duplicate entry"
        if (isset($response['http_code']) && $response['http_code'] == 500 && isset($response['exceptionMessage'])) {
            if (strpos($response['exceptionMessage'], "Duplicate entry") !== false && strpos($response['exceptionMessage'], $generated_id) !== false) {
                Log::error('TransactionApiService: addAccountLog 失败 (HTTP 500) - _id 重复: ' . $generated_id . ' Loginname: ' . $u_loginname . ' Response: ' . json_encode($response));
                return ['error' => true, 'code' => 'DUPLICATE_ID_HTTP_500', 'description' => 'ID重复导致服务器错误。', 'message' => '添加账户日志失败：ID 生成重复导致服务器内部错误。', 'api_response' => $response];
            }
            // 其他 HTTP 500 错误
            Log::error('TransactionApiService: addAccountLog HTTP 500 错误 - Loginname: ' . $u_loginname . ' _id: ' . $generated_id . ' Response: ' . json_encode($response));
            return ['error' => true, 'code' => 'HTTP_500_ERROR', 'description' => $response['message'] ?? ($response['exceptionMessage'] ?? '服务器内部错误'), 'message' => '添加账户日志时发生服务器内部错误。', 'api_response' => $response];
        }


        // 预期成功响应: [[], {"code": "000000", "description": "日志添加成功"}]
        if (is_array($response) && count($response) === 2 && isset($response[1]['code'])) {
            $apiCode = $response[1]['code'];
            $apiDescription = $response[1]['description'] ?? '无描述';

            if ($apiCode === '000000') {
                Log::info('TransactionApiService: addAccountLog 成功 - Loginname: ' . $u_loginname . ' _id: ' . $generated_id);
                return ['success' => true, 'code' => $apiCode, 'description' => $apiDescription, 'data' => $response[0]];
            } else {
                // API 返回了业务逻辑错误代码，但 HTTP 状态码可能是 2xx
                Log::error('TransactionApiService: addAccountLog 业务逻辑错误 - Loginname: ' . $u_loginname . ' _id: ' . $generated_id . ' Code: ' . $apiCode . ' Description: ' . $apiDescription . ' Response: ' . json_encode($response));
                return ['error' => true, 'code' => $apiCode, 'description' => $apiDescription, 'message' => '添加账户日志API返回业务错误: ' . $apiDescription, 'api_response' => $response];
            }
        }

        // 如果响应不是预期的格式
        Log::error('TransactionApiService: addAccountLog 响应格式错误 - Loginname: ' . $u_loginname . ' _id: ' . $generated_id . ' Response: ' . json_encode($response));
        return ['error' => true, 'message' => '添加账户日志API响应格式不正确', 'response_data' => $response];
    }


    /**
     * 底层发送 API 请求的方法
     *
     * @param string $endpoint API 端点路径
     * @param string $method HTTP 方法
     * @param array $data 请求数据
     * @param array $extraHeaders 额外的请求标头 (例如 ['Content-Type' => 'application/json'])
     * @return mixed API 响应数据 (通常是数组) 或包含错误信息的数组
     */
    protected function request(string $endpoint, string $method = 'GET', array $data = [], array $extraHeaders = [])
    {
        if (empty($this->baseUrl)) {
            Log::error('TransactionApiService 错误：API 基础 URL (peizi.transaction_api.base_url) 未设定。');
            return ['error' => true, 'message' => 'API 基础 URL 未设定。'];
        }

        $url = rtrim($this->baseUrl, '/') . '/' . ltrim($endpoint, '/');
        $method = strtoupper($method);

        $currentHeaders = $this->defaultHeaders;
        $contentTypeFromExtra = null;
        $finalHeadersInput = [];

        foreach ($extraHeaders as $key => $value) {
            if (strtolower($key) === 'content-type') {
                $contentTypeFromExtra = $value;
            } else {
                $finalHeadersInput[] = $key . ': ' . $value;
            }
        }
        $finalHeadersInput = array_merge($currentHeaders, $finalHeadersInput);

        $isJsonPayload = false;
        if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            if ($contentTypeFromExtra) {
                $finalHeadersInput[] = 'Content-Type: ' . $contentTypeFromExtra;
                if (stripos($contentTypeFromExtra, 'application/json') !== false) {
                    $isJsonPayload = true;
                }
            } else {
                $finalHeadersInput[] = 'Content-Type: application/json';
                $isJsonPayload = true;
            }
        }
        $finalHeadersInput = array_unique($finalHeadersInput);


        $ch = curl_init();

        if ($method === 'GET' && !empty($data)) {
            $url .= '?' . http_build_query($data);
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $finalHeadersInput);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);

        if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            if ($method === 'POST') {
                curl_setopt($ch, CURLOPT_POST, 1);
            } else {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            }
            if ($isJsonPayload) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            }
        } elseif ($method === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            if (!empty($data)) {
                if ($isJsonPayload) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                } else {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                }
            }
        }

        $responseBody = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        $logPayload = ($isJsonPayload && !empty($data)) ? json_encode($data) : http_build_query($data);
        if (empty($data) && in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            $logPayload = ($isJsonPayload) ? '{}' : '';
        }


        if ($curl_error) {
            Log::error('TransactionApiService cURL 错误：' . $curl_error . ' | URL：' . $url . ' | 方法：' . $method . ' | 请求体：' . $logPayload);
            return ['error' => true, 'message' => 'cURL 错误：' . $curl_error, 'http_code' => $http_code];
        }

        $decoded_response = json_decode($responseBody, true);

        if ($http_code >= 200 && $http_code < 300) {
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded_response;
            } else {
                Log::warning('TransactionApiService JSON 解码错误：' . json_last_error_msg() . ' | URL：' . $url . ' | 方法：' . $method . ' | 原始响应：' . $responseBody);
                return ['error' => true, 'message' => 'JSON 响应解码失败。', 'http_code' => $http_code, 'response_body' => $responseBody];
            }
        } else {
            Log::error('TransactionApiService HTTP 错误：' . $http_code . ' | URL：' . $url . ' | 方法：' . $method . ' | 原始响应：' . $responseBody . ' | 请求体：' . $logPayload);
            if ($decoded_response !== null && is_array($decoded_response)) {
                return array_merge(['error' => true, 'message' => 'API HTTP 错误', 'http_code' => $http_code], $decoded_response);
            }
            return ['error' => true, 'message' => 'API HTTP 错误', 'http_code' => $http_code, 'response_body' => $responseBody];
        }
    }
}
