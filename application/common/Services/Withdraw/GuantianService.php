<?php

namespace app\common\Services\Withdraw;

use GuzzleHttp\Client;
use think\Log;

class GuantianService
{
    protected $baseUrl;
    protected $client;

    public function __construct()
    {
        if (environment('production')) {
            $this->baseUrl = 'https://api.kmvdf.xyz';
        } else {
            $this->baseUrl = 'https://api.payment66.com';
        }

        $this->client = new Client([
            // Base URI is used with relative requests
            'base_uri' => $this->baseUrl,
        ]);
    }

    public function withdraw($order)
    {
        $data = [
            'user' => config('peizi.guantian')['user'],
            'user_order_no' => $order['order_no'],
            'amount' => $order['amount'],
            'account_name' => $order['account_name'],
            'account_number' => $order['account_number'],
            'bank_name' => $order['bank_name'],
            'bank_branch' => $order['bank_branch'],
            'notify_url' => str_replace('admin.php', 'wap.php', url('withdraw/guantianNotify', '', '', true)),
        ];

        $data['sign'] = $this->getSign($data);

        Log::info("请求数据:" . json_encode($data));

        $response = $this->client->post('/api/pending-order/sell', [
            'json' => $data
        ]);

        Log::info("请求结果:" . $response->getBody());

        $result = json_decode($response->getBody(), true);

        return $result;
    }

    public function getSign($data)
    {
        ksort($data);
        $sign_str = [];

        foreach ($data as $key => $value) {
            if ($key == 'sign' || $value === '') {
                continue;
            } else {
                if (is_array($value)) {
                    $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                } else {
                    $value = (string)$value;
                }
                $sign_str[] = "{$key}={$value}";
            }
        }

        $sign_str = join('&', $sign_str) . "&key=" . config('peizi.guantian')['key'];
        Log::info("加密字串:$sign_str");
        $result = strtolower(md5($sign_str));
        Log::info("加密结果:$result");
        return $result;
    }

    public function verifySign($data, $sign)
    {
        $mySign = $this->getSign($data);
        return $mySign === $sign;
    }
}