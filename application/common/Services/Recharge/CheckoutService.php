<?php

namespace app\common\Services\Recharge;

use GuzzleHttp\Client;
use think\Log;

class CheckoutService
{
    protected $baseUrl;
    protected $client;

    public function __construct()
    {
        if (environment('production')) {
            $this->baseUrl = 'https://api.ktkdf.xyz';
        } else {
            $this->baseUrl = 'https://unionapi.35211m.com';
        }

        $this->client = new Client([
            // Base URI is used with relative requests
            'base_uri' => $this->baseUrl,
        ]);
    }

    public function pay($order)
    {
        $data = [
            'user' => config('peizi.checkout')['user'],
            'user_uid' => $order['member_id'],
            'user_order_no' => $order['order_no'],
            'amount' => $order['amount'],
            'notify_url' => url('recharge/checkoutNotify', '', '', true),
        ];

        $data['sign'] = $this->getSign($data);
        $data['channel'] = 'uying';

        Log::info("请求数据:" . json_encode($data));

        $response = $this->client->post('/api/pay-order/apply', [
            'json' => $data
        ]);

        Log::info("请求结果:" . $response->getBody());

        $result = json_decode($response->getBody(), true);

        return $result;
    }

    public function getSign($data)
    {
        ksort($data);
        $sign_str = [];

        foreach ($data as $key => $value) {
            if ($key == 'sign' || $value === '') {
                continue;
            } else {
                $sign_str[] = "{$key}={$value}";
            }
        }

        $sign_str = join('&', $sign_str) . "&key=" . config('peizi.checkout')['key'];
        Log::info("加密字串:$sign_str");
        $result = strtolower(md5($sign_str));
        Log::info("加密结果:$result");
        return $result;
    }

    public function verifySign($data, $sign)
    {
        $mySign = $this->getSign($data);

        return $mySign === $sign;
    }
}