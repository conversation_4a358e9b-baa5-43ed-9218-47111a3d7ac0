<?php
//dezend by http://www.yunlu99.com/
function changeNavc($actionName, $nowAction)
{
	return strtolower($actionName) == $nowAction ? true : false;
}

function getNav()
{
	if (cache('navigation')) {
		$data = cache('navigation');
	}
	else {
		$nav = db('navigation')->where('is_hidden', 1)->order('sort_order DESC')->field('id,name,url,parent_id')->select();
		$data = getCategoryTreeArray($nav, 'id', 'parent_id');
		cache('navigation', $data);
	}

	return $data;
}

function clearAll($dir = RUNTIME_PATH)
{
	$dh = @opendir($dir);

	while ($file = @readdir($dh)) {
		if ($file != '.' && $file != '..') {
			$fullpath = $dir . '/' . $file;

			if (!is_dir($fullpath)) {
				unlink($fullpath);
			}
			else {
				clearAll($fullpath);
			}
		}
	}

	@closedir($dh);
	return true;
}

function cnsubstr($str, $length = 20)
{
	if (!$str || !$length) {
		return false;
	}

	$res = mb_substr($str, 0, $length);

	if ($length <= mb_strlen($str)) {
		$res = $res . '...';
	}

	return $res;
}

function getArticleList($parent_id = 0, $limit = 5, $field = 'id,title,parent_id,info,add_time')
{
	if (!$parent_id || !$limit || !$field) {
		return false;
	}

	$data = db('article')->where(array('parent_id' => $parent_id, 'is_hide' => 1))->order('sort_order DESC,add_time DESC')->field($field)->limit($limit)->select();

	foreach ($data as &$v) {
		$v['article_url'] = url('article/' . $v['id']);
	}

	unset($v);
	return $data;
}

function getCategoryTree($type = 'category')
{
	if ($type == 'category') {
		$data = db('category')->field('id,name,parent_id')->order('sort_order DESC')->select();
	}
	else if ($type == 'navigation') {
		$data = db('navigation')->field('id,name,parent_id')->order('sort_order DESC')->select();
	}

	$tree = getCategoryLevel($data);
	$res = array();

	foreach ($tree as $v) {
		$res[] = array('id' => $v['id'], 'name' => $v['name'], 'str' => str_repeat('--- ', $v['level']) . $v['name']);
	}

	return $res;
}

function getCategoryLevel($arr, $pid = 0, $level = 0)
{
	static $list = array();

	foreach ($arr as $k => $v) {
		if ($v['parent_id'] == $pid) {
			$v['level'] = $level;
			$list[] = $v;
			unset($arr[$k]);
			getCategoryLevel($arr, $v['id'], $level + 1);
		}
	}

	return $list;
}

function getCategoryTreeArray($list, $pk = 'id', $pid = 'pid', $child = 'children', $root = 0)
{
	$tree = array();
	$packData = array();

	foreach ($list as $data) {
		$packData[$data[$pk]] = $data;
	}

	foreach ($packData as $key => $val) {
		if ($val[$pid] == $root) {
			$tree[] = &$packData[$key];
		}
		else {
			$packData[$val[$pid]][$child][] = &$packData[$key];
		}
	}

	return $tree;
}

function set_pass($str)
{
	$pass = md5($str);
	return $pass;
}

function v_pass($newstr, $savestr)
{
	$result = md5($newstr) == $savestr ? true : false;
	return $result;
}

function getBanner()
{
	if (cache('banner')) {
		$data = cache('banner');
	}
	else {
		$data = array(
			'pc'  => array(),
			'wap' => array()
		);
		$slide = db('slide')->field(true)->order('order DESC')->select();

		foreach ($slide as $v) {
			if ($v['type'] == 1) {
				$data['pc'][] = $v;
			}
			else if ($v['type'] == 2) {
				$data['wap'][] = $v;
			}
		}

		cache('banner', $data);
	}

	return $data;
}

function get_ad($id = 0)
{
	if (!$id) {
		return false;
	}

	if (cache('ad' . $id)) {
		$data = cache('ad' . $id);
	}
	else {
		$res = db('ad')->where('id', $id)->field('hide,content')->find();

		if ($res) {
			if ($res['hide'] == 1) {
				$data = htmlspecialchars_decode($res['content']);
			}
			else {
				$data = '';
			}
		}
		else {
			$data = '该广告位暂未上传内容...';
		}

		cache('ad' . $id, $data);
	}

	return $data;
}

function isMobile()
{
	$useragent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
	$useragent_commentsblock = 0 < preg_match('|\\(.*?\\)|', $useragent, $matches) ? $matches[0] : '';
	function CheckSubstrs($substrs, $text)
	{
		foreach ($substrs as $substr) {
			if (false !== strpos($text, $substr)) {
				return true;
			}
		}

		return false;
	}
	$mobile_os_list = array('Google Wireless Transcoder', 'Windows CE', 'WindowsCE', 'Symbian', 'Android', 'armv6l', 'armv5', 'Mobile', 'CentOS', 'mowser', 'AvantGo', 'Opera Mobi', 'J2ME/MIDP', 'Smartphone', 'Go.Web', 'Palm', 'iPAQ');
	$mobile_token_list = array('Profile/MIDP', 'Configuration/CLDC-', '160×160', '176×220', '240×240', '240×320', '320×240', 'UP.Browser', 'UP.Link', 'SymbianOS', 'PalmOS', 'PocketPC', 'SonyEricsson', 'Nokia', 'BlackBerry', 'Vodafone', 'BenQ', 'Novarra-Vision', 'Iris', 'NetFront', 'HTC_', 'Xda_', 'SAMSUNG-SGH', 'Wapaka', 'DoCoMo', 'iPhone', 'iPod');
	$found_mobile = CheckSubstrs($mobile_os_list, $useragent_commentsblock) || CheckSubstrs($mobile_token_list, $useragent);

	if ($found_mobile) {
		return true;
	}
	else {
		return false;
	}
}

function get_realname_param()
{
	if (cache('realname_set')) {
		$data = cache('realname_set');
	}
	else {
		$data = db('realname_set')->where('id', 1)->find();
		cache('realname_set', $data);
	}

	return $data;
}

function realname_request($url, $params, $body = '', $isPost = false, $isImage = false)
{
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_URL, $url . '?' . http_build_query($params));

	if ($isPost) {
		if ($isImage) {
			curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: multipart/form-data;', 'Content-Length: ' . strlen($body)));
		}
		else {
			curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: text/plain'));
		}

		curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
	}

	$result = curl_exec($ch);
	curl_close($ch);
	$data = json_decode($result, 1);

	if ($data['code'] == 10000) {
		if ($data['result']['result']['isok']) {
			return true;
		}
		else {
			return false;
		}
	}
	else {
		return false;
	}
}

function get_sms_param()
{
	if (cache('sms_set')) {
		$data = cache('sms_set');
	}
	else {
		$res = db('sms_set')->where('id', 1)->find();
		$data = array('user' => $res['user'], 'password' => $res['password'], 'sign' => $res['sign']);
		cache('sms_set', $data);
	}

	return $data;
}

function sendsms($phone, $content)
{
	if (!$phone || !$content) {
		return false;
	}

	$msgconfig = get_sms_param();
	$data['Account'] = $msgconfig['user'];
	$data['Pwd'] = $msgconfig['password'];
	$data['Content'] = $content;
	$data['Mobile'] = $phone;
	$data['SignId'] = $msgconfig['sign'];
	$url = 'http://api.feige.ee/SmsService/Send';
	$timeout = 20;
	$curl = curl_init();
	curl_setopt($curl, CURLOPT_URL, $url);
	curl_setopt($curl, CURLOPT_HEADER, 0);
	curl_setopt($curl, CURLOPT_POST, true);
	curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
	curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
	curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
	$res = curl_exec($curl);
	curl_close($curl);
	unset($curl);
	file_put_contents('send.txt', print_r($res, 1) . ' | ' . $phone . ' | ' . print_r($content, 1) . PHP_EOL, FILE_APPEND);
	$res = json_decode($res, 1);

	if ($res['Code'] == 0) {
		return true;
	}
	else {
		return false;
	}
}

function sendsms_mandao($phone, $content)
{
	if (!$phone || !$content) {
		return false;
	}

	$msgconfig = get_sms_param();
	$data['sn'] = $msgconfig['user'];
	$data['pwd'] = strtoupper(md5($msgconfig['user'] . $msgconfig['password']));
	$data['mobile'] = $phone;
	$data['content'] = $content;
	$data['ext'] = '';
	$data['stime'] = '';
	$data['rrid'] = '';
	$data['msgfmt'] = '';
	$data = http_build_query($data);
	$url = 'http://sdk.entinfo.cn:8061/webservice.asmx/mdsmssend';
	$timeout = 20;
	$curl = curl_init();
	curl_setopt($curl, CURLOPT_URL, $url);
	curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
	curl_setopt($curl, CURLOPT_HEADER, 0);
	curl_setopt($curl, CURLOPT_POST, true);
	curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
	curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
	curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
	$res = curl_exec($curl);
	curl_close($curl);
	unset($curl);
	$res = str_replace('<?xml version="1.0" encoding="utf-8"?>', '', $res);
	$res = str_replace('<string xmlns="http://entinfo.cn/">', '', $res);
	$res = str_replace('</string>', '', $res);

	if (0 < $res) {
		return true;
	}
	else {
		return false;
	}
}

function sendsms_to_admin($msg)
{
	if (cache('admin_phone')) {
		$admin_phone = cache('admin_phone');
	}
	else {
		$admin_phone = get_global_setting()['admin_phone'];
		cache('admin_phone', $admin_phone);
	}

	if (strlen($admin_phone) == 11) {
		sendsms($admin_phone, $msg);
	}
	else {
		return true;
	}
}

function rand_number($length = 6)
{
	$str = NULL;

	for ($i = 0; $i < $length; $i++) {
		$str .= mt_rand(0, 100);
	}

	return substr($str, 0, 6);
}

function verify_code($phone, $code)
{
	$mod = db('send_log');
	$send_log = $mod->where(array('phone' => $phone, 'is_use' => 0))->whereTime('send_time', 'between', array(time() - 60 * 5, time()))->order('send_time desc')->value('code');
	if ($send_log && $code == $send_log) {
		return true;
	}
	else {
		return false;
	}
}

function get_global_setting()
{
	if (cache('global_set')) {
		$result = cache('global_set');
	}
	else {
		$data = db('global')->field('code,text')->select();
		$result = array();

		foreach ($data as $v) {
			$result[trim($v['code'])] = $v['text'];
		}

		cache('global_set', $result);
	}

	return $result;
}

function get_future_setting()
{
	if (cache('future_set')) {
		$result = cache('future_set');
	}
	else {
		$data = db('future_global')->field('code,text')->select();
		$result = array();

		foreach ($data as $v) {
			$result[trim($v['code'])] = $v['text'];
		}

		cache('future_set', $result);
	}

	return $result;
}

function get_stock_setting()
{
	if (cache('stock_set')) {
		$result = cache('stock_set');
	}
	else {
		$data = db('stock_global')->field('code,text')->select();
		$result = array();

		foreach ($data as $v) {
			$result[trim($v['code'])] = $v['text'];
		}

		cache('stock_set', $result);
	}

	return $result;
}

function getmax_multiple($type)
{
	$arr = get_stock_rate()[$type];
	ksort($arr);
	end($arr);
	return key($arr);
}

function get_stock_rate()
{
	if (cache('stock_rateset')) {
		$result = cache('stock_rateset');
	}
	else {
		$data = db('stock_rateset')->field('type,info')->select();
		$result = array();

		foreach ($data as $v) {
			$result[trim($v['type'])] = json_decode($v['info'], 1);
		}

		cache('stock_rateset', $result);
	}

	return $result;
}

function get_future_rate()
{
	if (cache('future_rateset')) {
		$result = cache('future_rateset');
	}
	else {
		$data = db('future_rateset')->field('type,info')->select();
		$result = array();

		foreach ($data as $v) {
			$result[trim($v['type'])] = json_decode($v['info'], 1);
		}

		cache('future_rateset', $result);
	}

	return $result;
}

function getAddTimeForRenewal($type, $end_time, $borrow_duration)
{
	if ($type == 1) {
		$nowData = date('Y-m-d', $end_time);
		$set_holidays = explode(',', get_stock_setting()['holidays']);
		$endYmd = getEndDay($nowData, $borrow_duration, $set_holidays);
		$addEndTime = $endYmd . ' 14:45:00';
		$addEndTime = strtotime($addEndTime);
	}
	else {
		if ($type == 2 || $type == 8) {
			$addEndTime = strtotime('+' . $borrow_duration . ' month', $end_time);
		}
	}

	return $addEndTime;
}

function api_auto_renewal_old()
{
	file_put_contents('renewal.txt', 'auto=' . date('Y-m-d H-i-s', time()) . PHP_EOL, FILE_APPEND);
	$stock_borrow = db('stock_borrow');
	$nowDate = date('Y-m-d', time());
	$w = array();
	$w['status'] = 2;
	$w['type'] = array(
		'in',
		array(1, 2, 8)
	);
	$beginOfToday = strtotime($nowDate . ' 0:0:0');
	$endOfToday = strtotime($nowDate . ' 23:59:59');
	$w['end_time'] = array(
		'between',
		array($beginOfToday, $endOfToday)
	);
	$stocks = $stock_borrow->where($w)->field('id,uid,order_id,category,type,borrow_money,borrow_duration,deposit_money,multiple,end_time')->select();

	if (empty($stocks)) {
		return NULL;
	}

	$time = date('H:i:s');
	$member_money = db('member_money');

	foreach ($stocks as $stock) {
		$duration = 1;
		$rateInfo = get_stock_rate()[$stock['type']];
		$gainMoney = $stock['borrow_money'] - $stock['deposit_money'];
		$fee = $gainMoney * $duration * ($rateInfo[$stock['multiple']] / 100);
		$minfo = $member_money->where('uid', $stock['uid'])->field('account_money,interest_money')->find();

		if ($minfo['interest_money'] + $minfo['account_money'] < $fee) {
			$warnMsg = '您的配资(编号：' . $stock['id'] . ')已到期，由于您账号内余额不足，自动续期失败，如需继续使用，请联系客服人员。';
			addInnerMsg($stock['uid'], '自动续期失败', $warnMsg);
			continue;
		}

		$endTime = getaddtimeforrenewal($stock['type'], $stock['end_time'], $duration);
		$info = '您的配资订单(编号：' . $stock['id'] . ')已到期，自动续期扣除1' . str_replace(array(1, 2, 8), array('天', '月', '月'), $stock['type']) . '手续费' . $fee / 100 . '元';
		$updateStockData = array();
		$updateStockData['end_time'] = $endTime;
		$updateStockData['borrow_duration'] = $stock['borrow_duration'] + $duration;
		$updateStock = $stock_borrow->where('id', $stock['id'])->update($updateStockData);

		if (!$updateStock) {
			$temp_log = '更新配资' . $stock['id'] . '结束时间失败';
			file_put_contents('renewal.txt', 'result=' . $temp_log . date('Y-m-d H-i-s', time()) . PHP_EOL, FILE_APPEND);
		}

		$deposit_money = 0;
		$member_moneylog = updateMoneyLogWithInterest($stock['uid'], $fee, $deposit_money, $fee, $info, 16, 0, 0);

		if (!$member_moneylog) {
			$temp_log = '配资' . $stock['id'] . '自动续期扣款失败';
			file_put_contents('renewal.txt', 'result=' . $temp_log . date('Y-m-d H-i-s', time()) . PHP_EOL, FILE_APPEND);
		}

		if ($updateStock && $member_moneylog) {
			addInnerMsg($stock['uid'], '自动续期成功', $info);
		}

		unset($temp_log);
	}

	echo 'auto renewal';
}

function api_auto_renewal()
{
	file_put_contents('renewal.txt', 'auto come in (1) =' . date('Y-m-d H:i:s', time()) . PHP_EOL, FILE_APPEND);
	$stock_borrow = db('stock_borrow');
	$stock_renewal = db('stock_renewal');
	$stock_addfinancing = db('stock_addfinancing');
	$nowDate = date('Y-m-d', time());
	$w = array();
	$w['status'] = 2;
	$w['auto_renewal'] = 1;
	$w['type'] = array(
		'in',
		array(1, 2, 8)
	);
	$beginOfToday = strtotime($nowDate . ' 0:0:0');
	$endOfToday = strtotime($nowDate . ' 23:59:59');
	$w['end_time'] = array(
		'between',
		array($beginOfToday, $endOfToday)
	);
	$stocks = $stock_borrow->where($w)->field('id,uid,order_id,category,type,borrow_money,borrow_duration,deposit_money,multiple,end_time')->select();

	if (empty($stocks)) {
		return NULL;
	}

	$time = date('H:i:s');
	$member_money = db('member_money');

	foreach ($stocks as $stock) {
		$duration = 1;

		if ($stock['category'] == 1) {
			$rateInfo = get_stock_rate()[$stock['type']];
		}
		else {
			$rateInfo = get_future_rate()[$stock['type']];
		}

		$peiziTypeTxt = str_replace(array(1, 2, 3, 4, 8), array('按天', '按月', '免息', '体验', 'VIP'), $stock['type']);
		$renewalCount = $stock_renewal->where(array('borrow_id' => $stock['id'], 'status' => 0, 'category' => $stock['category']))->count('id');

		if ($renewalCount) {
			file_put_contents('renewal.txt', 'auto renewalCount (2) =' . date('Y-m-d H:i:s', time()) . ' 配资(编号：' . $stock['id'] . ')已到期，由于当前有未处理的延期申请，自动延期失败' . PHP_EOL, FILE_APPEND);
			$warnMsg = '您的' . $peiziTypeTxt . '配资(编号：' . $stock['id'] . ')已到期，由于当前有未处理的延期申请，自动延期失败，如需继续使用，请联系客服人员。';
			addInnerMsg($stock['uid'], '自动延期失败', $warnMsg);
			continue;
		}

		$expendSearch = array('borrow_id' => $stock['id'], 'status' => 0, 'category' => $stock['category']);
		$expendCount = $stock_addfinancing->where($expendSearch)->count('id');

		if ($expendCount) {
			file_put_contents('renewal.txt', 'auto expendCount (3) =' . date('Y-m-d H:i:s', time()) . ' 配资(编号：' . $stock['id'] . ')已到期，由于当前有未处理的扩大配资申请，自动延期失败' . PHP_EOL, FILE_APPEND);
			$warnMsg = '您的' . $peiziTypeTxt . '配资(编号：' . $stock['id'] . ')已到期，由于当前有未处理的扩大配资申请，自动延期失败，如需继续使用，请联系客服人员。';
			addInnerMsg($stock['uid'], '自动延期失败', $warnMsg);
			continue;
		}

		$fee = 0;
		$gainMoney = $stock['borrow_money'] - $stock['deposit_money'];
		$fee = $gainMoney * $duration * ($rateInfo[$stock['multiple']] / 100);
		$minfo = $member_money->where('uid', $stock['uid'])->field('account_money,interest_money')->find();

		if ($minfo['interest_money'] + $minfo['account_money'] < $fee) {
			$warnMsg = '您的配资(编号：' . $stock['id'] . ')已到期，由于您账号内余额不足，自动延期失败，如需继续使用，请联系客服人员。';
			addInnerMsg($stock['uid'], '自动延期失败', $warnMsg);
			file_put_contents('renewal.txt', 'auto borrow_fee (4) =' . date('Y-m-d H:i:s', time()) . ' 配资(编号：' . $stock['id'] . ')已到期，由于用户资金不足，自动延期失败' . PHP_EOL, FILE_APPEND);
			$warnMsgToAdmin = $peiziTypeTxt . ('方案已到期，编号：' . $stock['id'] . '，由于用户资金不足，自动延期失败');
			sendsms_to_admin($warnMsgToAdmin);
			continue;
		}

		$add = array('uid' => $stock['uid'], 'borrow_id' => $stock['id'], 'borrow_fee' => $fee, 'borrow_duration' => $duration, 'add_time' => time(), 'type' => 1, 'category' => $stock['category']);
		$res = $stock_renewal->insertGetId($add);
		$info = '您的配资订单(编号：' . $stock['id'] . ')已到期，自动申请延期1' . str_replace(array(1, 2, 8), array('天', '月', '月'), $stock['type']) . ' 扣除利息' . $fee / 100 . '元';
		$deposit_money = 0;
		$member_moneylog = updateMoneyLogWithInterest($stock['uid'], $fee, $deposit_money, $fee, $info, 16, 3, $res);

		if (!$member_moneylog) {
			$temp_log = '配资编号：' . $stock['id'] . ' 自动延期扣款失败';
			file_put_contents('renewal.txt', 'auto moneylog (5) = ' . $temp_log . date('Y-m-d H:i:s', time()) . PHP_EOL, FILE_APPEND);
		}

		if ($res && $member_moneylog) {
			addTip($stock['uid'], 10);
			addInnerMsg($stock['uid'], '自动延期申请成功', $info);
		}

		unset($temp_log);
	}

	echo 'auto renewal';
}

function calculate_rate($borrow_id = 0, $deposit_money = 0)
{
	if (!$borrow_id || !$deposit_money) {
		return false;
	}

	$deposit_money = $deposit_money * 100;
	$data = db('stock_borrow')->where('id', $borrow_id)->field(true)->find();
	$now = time();

	switch ($data['type']) {
	case 1:
		$duration = 0;
		$durationTmp = ceil(($data['end_time'] - $now) / 3600 / 24);
		$durationDays = array();

		for ($i = 0; $i < $durationTmp; $i++) {
			$durationDays[$i] = date('Y-m-d', strtotime('+' . $i . ' day'));
		}

		$holidays = explode(',', get_stock_setting()['holidays']);

		foreach ($durationDays as $v) {
			if (getEndDay2($v, $holidays) == 1) {
				$duration++;
			}
		}

		break;

	case 2:
	case 8:
		$temp_days = $data['borrow_duration'] * 30;
		$duration = round(($temp_days - intval(($now - $data['add_time']) / 86400)) / 30, 8);
		break;

	default:
		return false;
		break;
	}

	$fee = round($deposit_money * $data['multiple'] * $duration * ($data['rate'] / 100), 0);

	if (0 < $fee) {
		return $fee;
	}
	else {
		return false;
	}
}

function calculate_renewal($borrow_id = 0, $duration = 0)
{
	if (!$borrow_id || !$duration) {
		return false;
	}

	$binfo = db('stock_borrow')->where('id', $borrow_id)->field(true)->find();

	if (!$binfo) {
		return false;
	}

	if ($binfo['category'] == 1) {
		$rateInfo = get_stock_rate()[$binfo['type']];
	}
	else {
		$rateInfo = get_future_rate()[$binfo['type']];
	}

	$gainMoney = $binfo['borrow_money'] - $binfo['deposit_money'];
	$fee = $gainMoney * $duration * ($rateInfo[$binfo['multiple']] / 100);

	if ($fee) {
		return $fee;
	}
	else {
		return false;
	}
}

function memberInfo($uid = 0, $field = '')
{
	$result = db('members')->alias('m1')->join('member_info m2', 'm1.id = m2.uid', 'left')->join('member_money m3', 'm1.id = m3.uid', 'left')->field('m1.*,m2.phone,m2.phone_status,m2.real_name,m2.id_status,m2.id_card,m3.account_money,m3.interest_money,m3.money_freeze,m3.deposit_money,m3.back_money,m3.deposit_money')->order('id DESC')->where(array('m1.id' => $uid))->find();
	return $result;
}

function dl_yuming($server_name = '')
{
	if (!$server_name) {
		return false;
	}

	$dailiren = db('members')->where('user_type', config('DAILIREN_TYPE'))->field('id,user_name,dl_yuming')->select();

	foreach ($dailiren as $v) {
		if ($v['dl_yuming']) {
			$temp_arr = explode(',', $v['dl_yuming']);

			if (1 <= count($temp_arr)) {
				foreach ($temp_arr as $v2) {
					if (stristr($v2, $server_name)) {
						return $v['id'];
					}
				}
			}

			unset($temp_arr);
		}
	}

	return false;
}

function getFriend($type = 1, $limit = 8)
{
	if (cache('friend' . $type)) {
		$data = cache('friend' . $type);
	}
	else {
		$data = db('friend')->where(array('f_type' => $type, 'is_show' => 1))->field(true)->limit($limit)->select();
		cache('friend' . $type, $data);
	}

	return $data;
}

function get_kefu()
{
	if (cache('kefu')) {
		$result = cache('kefu');
	}
	else {
		$data = db('kefu')->field('number,title,type')->where('is_show=1')->order('order desc')->select();
		$result = array(
			'qq'  => array(),
			'tel' => array()
		);

		foreach ($data as $v) {
			if ($v['type'] == 1) {
				$result['qq'][] = $v;
			}
			else if ($v['type'] == 2) {
				$result['tel'][] = $v;
			}
		}

		cache('kefu', $result);
	}

	return $result;
}

function getMoneyFormt($money = 0)
{
	$result = '';
	$money = (int) $money;
	if (1000 <= $money && $money < 10000) {
		$result = $money / 1000 . '千元';
	}
	else {
		if (10000 <= $money && $money < 100000000) {
			$result = $money / 10000 . '万元';
		}
		else if (100000000 <= $money) {
			$result = $money / 100000000 . '亿';
		}
		else {
			$result = $money . '元';
		}
	}

	return $result;
}

function couldApplyToday()
{
	$startDate = strtotime(date('Y-m-d 06:00:00'));
	$endDate = strtotime(date('Y-m-d 14:50:00'));
	$nowData = date('Y-m-d');
	$nowTime = time();
	$holidays = explode(',', get_stock_setting()['holidays']);
	$holidaysTrue = getEndDay2($nowData, $holidays);
	if (!($startDate <= $nowTime && $nowTime <= $endDate) || $holidaysTrue == 2) {
		$couldApplyToday = 2;
	}
	else {
		$couldApplyToday = 1;
	}

	return $couldApplyToday;
}

function getNextWorkDay(&$now, $holiday)
{
	$now->add(new DateInterval('P1D'));

	if (getEndDay2($now->format('Y-m-d'), $holiday) == 2) {
		return getNextWorkDay($now, $holiday);
	}
	else {
		return $now->format('Y-m-d');
	}
}

function getEndDay2($start = 'now', $exception = '')
{
	$starttime = strtotime($start);
	$weekday = date('N', $starttime);
	$tmpday = date('Y-m-d', $starttime);

	if (is_array($exception)) {
		$bfd = in_array($tmpday, $exception);
	}
	else {
		$bfd = $exception == $tmpday;
	}

	if ($weekday <= 5 && !$bfd) {
		$status = 1;
	}
	else {
		$status = 2;
	}

	return $status;
}

function getEndDay($start = 'now', $offset = 0, $exception = '')
{
	$starttime = strtotime($start);
	$endtime = $starttime + $offset * 24 * 3600;
	$end = date('Y-m-d', $endtime);
	$weekday = date('N', $starttime);
	$newoffset = 0;

	for ($i = 1; $i <= $offset; $i++) {
		$today = date('Y-m-d', $starttime + $i * 24 * 3600);

		switch (($weekday + $i) % 7) {
		case 6:
			$newoffset += 1;
			break;

		case 0:
			$newoffset += 1;
			break;

		default:
			if (is_array($exception)) {
				if (in_array($today, $exception)) {
					$newoffset += 1;
				}
			}
			else if ($today == $exception) {
				$newoffset += 1;
			}

			break;
		}
	}

	if (0 < $newoffset) {
		return getEndDay($end, $newoffset, $exception);
	}
	else {
		return $end;
	}
}

function formatBankCardNo($bankCardNo)
{
	$prefix = substr($bankCardNo, 0, 4);
	$suffix = substr($bankCardNo, -4, 4);
	$maskBankCardNo = $prefix . ' **** **** **** ' . $suffix;
	return $maskBankCardNo;
}

function orderNumber()
{
	$number = substr(date('ymdHis'), 0, 8) . mt_rand(1000000, 9999999);
	return $number;
}

function myXsrw($uid = 0, $rw_id = 0)
{
	if (!$uid || !$rw_id) {
		return false;
	}

	$mod = db('xsrw');
	$add = array('uid' => $uid, 'rw_id' => $rw_id, 'add_time' => time());
	$res = $mod->insert($add);
	$global = get_global_setting();
	$xsrw_on = $global['xsrw_on'];
	$xsrw_money = explode('|', $global['xsrw_money']);

	if ($xsrw_on != 1) {
		return array('status' => '-1', 'message' => '任务未开启');
	}

	file_put_contents('./xsrw.txt', date('Y-m-d H:i ') . ('uid=' . $uid . '&rw_id=' . $rw_id . '
'), FILE_APPEND);
	$rw_arr = config('MY_XSRW');
	$w = array('uid' => $uid, 'rw_id' => $rw_id);

	switch ($rw_id) {
	case $rw_arr['ZHUCE_JL']:
		$money = $xsrw_money[0] * 100;
		$info = '恭喜注册成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 21, 2);
		break;

	case $rw_arr['SHIMING_JL']:
		$shiming = $mod->where($w)->count('id');

		if (1 < $shiming) {
			return array('status' => '-2', 'message' => '已经实名认证1');
		}

		$money = $xsrw_money[1] * 100;
		$info = '恭喜实名认证成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 22, 2);
		break;

	case $rw_arr['PEIZI_JL']:
		$is_first = $mod->where($w)->count('id');

		if (1 < $is_first) {
			return array('status' => '-3', 'message' => '非首次配资');
		}

		$is_first2 = db('stock_borrow')->where('uid', $uid)->where('status', 'in', '2,3')->count('id');

		if (1 < $is_first2) {
			return array('status' => '-3', 'message' => '非首次配资');
		}

		$money = $xsrw_money[2] * 100;
		$info = '恭喜首次配资成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 23, 2);
		break;

	case $rw_arr['CHONGZHI_JL']:
		$is_first = $mod->where($w)->count('id');

		if (1 < $is_first) {
			return array('status' => '-4', 'message' => '非首次充值');
		}

		$is_first2 = db('member_recharge')->where('uid', $uid)->where('status', 1)->count('id');

		if (1 < $is_first2) {
			return array('status' => '-4', 'message' => '非首次充值');
		}

		$money = $xsrw_money[3] * 100;
		$info = '恭喜首次充值成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 24, 2);
		break;

	case $rw_arr['BDYHK_JL']:
		$is_first = $mod->where($w)->count('id');

		if (1 < $is_first) {
			return array('status' => '-5', 'message' => '非首次绑定银行卡');
		}

		$is_first2 = db('member_bank')->where('uid', $uid)->count('id');

		if (1 < $is_first2) {
			return array('status' => '-5', 'message' => '非首次绑定银行卡');
		}

		$money = $xsrw_money[4] * 100;
		$info = '恭喜首次绑定银行卡成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 25, 2);
		break;

	case $rw_arr['KDPEIZI_JL']:
		$is_first = $mod->where($w)->count('id');

		if (1 < $is_first) {
			return array('status' => '-6', 'message' => '非首次扩大配资');
		}

		$is_first2 = db('stock_addfinancing')->where('uid', $uid)->where('status', 1)->count('id');

		if (1 < $is_first2) {
			return array('status' => '-6', 'message' => '非首次追加配资');
		}

		$money = $xsrw_money[5] * 100;
		$info = '恭喜首次扩大配资成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 26, 2);
		break;

	case $rw_arr['BUKUI_JL']:
		$is_first = $mod->where($w)->count('id');

		if (1 < $is_first) {
			return array('status' => '-7', 'message' => '非首次补亏');
		}

		$is_first2 = db('stock_addmoney')->where('uid', $uid)->where('status', 1)->count('id');

		if (1 < $is_first2) {
			return array('status' => '-7', 'message' => '非首次补亏');
		}

		$money = $xsrw_money[6] * 100;
		$info = '恭喜首次补亏成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 27, 2);
		break;

	case $rw_arr['TIYING_JL']:
		$is_first = $mod->where($w)->count('id');

		if (1 < $is_first) {
			return array('status' => '-8', 'message' => '非首次提盈');
		}

		$is_first2 = db('stock_drawprofit')->where('uid', $uid)->where('status', 1)->count('id');

		if (1 < $is_first2) {
			return array('status' => '-8', 'message' => '非首次提盈');
		}

		$money = $xsrw_money[7] * 100;
		$info = '恭喜首次提盈成功，奖励 ' . $money / 100 . ' 元管理费';
		$addMoney = memberMoneyLog($uid, $money, $info, 28, 2);
		break;

	default:
		return false;
		break;
	}

	if ($addMoney) {
		$inner_res = addInnerMsg($uid, '新手任务奖励', $info);
		return 'ok';
	}
	else {
		return 'addHb error';
	}
}

function sendRebateForDL($investor, $id, $recommender = 0, $type = 0)
{
	if (!$investor || !$id || !$type) {
		return false;
	}

	$members = db('members');

	if (!$recommender) {
		$recommender = $members->where('id', $investor)->value('recommend_id');

		if (!$recommender) {
			return false;
		}
	}

	$log = array('recommender' => $recommender, 'investor' => $investor, 'id' => $id, 'type' => $type);
	file_put_contents('./sendRebate.txt', date('Y-m-d H:i ') . 'param:' . json_encode($log) . ' 
', FILE_APPEND);
	$investor_username = $members->where('id', $investor)->value('user_name');
	$investor_username = substr_replace($investor_username, '****', 3, 4);
	$recommender_info = $members->where('id', $recommender)->where('user_type', config('DAILIREN_TYPE'))->value('czpz_bl');
	$bili = explode('|', $recommender_info);

	if (!$recommender_info) {
		return false;
	}

	switch ($type) {
	case 5:
		if (!$bili[0]) {
			return false;
		}

		$money = db('member_recharge')->where('id', $id)->value('money');
		$rebate_money = intval($money * ($bili[0] / 100));
		$info = '您推荐的用户' . $investor_username . '线下充值' . $money / 100 . '元已审核通过，您获得返利：' . $rebate_money / 100 . '元';
		memberMoneyLog($recommender, $rebate_money, $info, 31);
		addInnerMsg($recommender, '代理下线充值返利', $info);
		break;

	case 1:
		if (!$bili[1]) {
			return false;
		}

		$interest = db('stock_borrow')->where('id', $id)->value('borrow_interest');
		$rebate_money = intval($interest * ($bili[1] / 100));
		$info = '您推荐的用户' . $investor_username . '配资成功，配资管理费：' . $interest / 100 . '元 您获得返利：' . $rebate_money / 100 . '元';
		memberMoneyLog($recommender, $rebate_money, $info, 32);
		addInnerMsg($recommender, '代理下线配资返利', $info);
		break;

	case 2:
		if (!$bili[1]) {
			return false;
		}

		$interest = db('stock_addfinancing')->where('id', $id)->value('borrow_fee');
		$rebate_money = intval($interest * ($bili[1] / 100));
		$info = '您推荐的用户' . $investor_username . '扩大配资成功，扩大配资管理费：' . $interest / 100 . '元 您获得返利：' . $rebate_money / 100 . '元';
		memberMoneyLog($recommender, $rebate_money, $info, 32);
		addInnerMsg($recommender, '代理下线配资返利', $info);
		break;

	case 3:
		if (!$bili[1]) {
			return false;
		}

		$interest = db('stock_renewal')->where('id', $id)->value('borrow_fee');
		$rebate_money = intval($interest * ($bili[1] / 100));
		$info = '您推荐的用户' . $investor_username . '申请延期成功，延期配资管理费：' . $interest / 100 . '元 您获得返利：' . $rebate_money / 100 . '元';
		memberMoneyLog($recommender, $rebate_money, $info, 32);
		addInnerMsg($recommender, '代理下线配资返利', $info);
		break;

	default:
		return false;
		break;
	}

	$log = array('bili' => $bili, 'rebate_money' => $rebate_money, 'info' => $info);
	file_put_contents('./sendRebate.txt', date('Y-m-d H:i ') . ' result:' . json_encode($log) . ' 

', FILE_APPEND);
}

function memberMoneyLog($uid, $money, $info = '', $type = 0, $interest = 1)
{
	if (!$uid || !$money) {
		return false;
	}

	$minfo = memberinfo($uid);

	if (!$minfo) {
		return false;
	}

	switch ($type) {
	case 3:
	case 7:
	case 11:
		$account_money = $minfo['account_money'] - $money;
		$interest_money = $minfo['interest_money'];
		$money_freeze = $minfo['money_freeze'] + $money;
		break;

	case 4:
		$account_money = $minfo['account_money'];
		$interest_money = $minfo['interest_money'];
		$money_freeze = $minfo['money_freeze'] - $money;
		$money = 0 - $money;
		break;

	case 5:
		$account_money = $minfo['account_money'] + $money;
		$interest_money = $minfo['interest_money'];
		$money_freeze = $minfo['money_freeze'] - $money;
		break;

	case 1:
	case 6:
	case 8:
	case 10:
	case 12:
	case 13:
	case 15:
	case 21:
	case 22:
	case 23:
	case 24:
	case 25:
	case 26:
	case 27:
	case 28:
	case 31:
	case 32:
		if ($interest == 1) {
			$account_money = $minfo['account_money'] + $money;
			$interest_money = $minfo['interest_money'];
		}
		else if ($interest == 2) {
			$account_money = $minfo['account_money'];
			$interest_money = $minfo['interest_money'] + $money;
		}
		else {
			return false;
		}

		$money_freeze = $minfo['money_freeze'];
		break;

	default:
		return false;
		break;
	}

	$mmoney['account_money'] = $account_money;
	$mmoney['interest_money'] = $interest_money;
	$mmoney['money_freeze'] = $money_freeze;
	$log = array('uid' => $uid, 'user_name' => $minfo['user_name'], 'type' => $type, 'affect_money' => $money, 'account_money' => $account_money, 'interest_money' => $interest_money, 'freeze_money' => $money_freeze, 'info' => $info, 'add_time' => time(), 'add_ip' => empty($ip) ? '' : $ip);
	$res1 = db('member_moneylog')->insert($log);
	$res2 = db('member_money')->where('uid', $uid)->update($mmoney);
	if ($res1 && $res2) {
		return true;
	}
	else {
		return false;
	}
}

function updateMoneyLogWithInterest($uid, $affect_money, $deposit_money, $interest_money, $info, $log_type, $trans_type, $borrow_id)
{
	$minfo = memberinfo($uid);

	if ($interest_money <= $minfo['interest_money']) {
		$money_arr['interest_money'] = $minfo['interest_money'] - $interest_money;
		$money_arr['account_money'] = $minfo['account_money'] - $deposit_money;
		$dikou_interest = $interest_money;
	}
	else {
		$money_arr['interest_money'] = 0;
		$temp_money = $deposit_money + ($interest_money - $minfo['interest_money']);
		$money_arr['account_money'] = $minfo['account_money'] - $temp_money;
		$dikou_interest = $minfo['interest_money'];
	}

	$money_arr['money_freeze'] = $minfo['money_freeze'] + $affect_money;
	$money_alter = db('member_money')->where('uid', $uid)->update($money_arr);

	if (0 < $dikou_interest) {
		switch ($trans_type) {
		case 1:
			$temp_res = db('stock_borrow')->where(array('id' => $borrow_id))->update(array('dikou_interest' => $dikou_interest));
			$info .= '，管理费抵扣' . $dikou_interest / 100 . '元';
			break;

		case 2:
			$temp_res = db('stock_addfinancing')->where(array('id' => $borrow_id))->update(array('dikou_interest' => $dikou_interest));
			$info .= '，管理费抵扣' . $dikou_interest / 100 . '元';
			break;

		case 3:
			$temp_res = db('stock_renewal')->where(array('id' => $borrow_id))->update(array('dikou_interest' => $dikou_interest));
			$info .= '，管理费抵扣' . $dikou_interest / 100 . '元';
			break;

		default:
			break;
		}
	}

	$log = array('uid' => $uid, 'user_name' => $minfo['user_name'], 'type' => $log_type, 'affect_money' => 0 - $affect_money, 'account_money' => $money_arr['account_money'], 'interest_money' => $money_arr['interest_money'], 'freeze_money' => $money_arr['money_freeze'], 'info' => $info, 'add_time' => time(), 'add_ip' => request()->ip());
	$moneyLog = db('member_moneylog')->insert($log);
	if ($money_alter && $moneyLog) {
		return true;
	}
	else {
		return false;
	}
}

function last_login($uid = 0)
{
	if (!$uid) {
		return false;
	}

	$res = db('member_login')->where('uid', $uid)->order('id DESC')->limit('1,1')->select();

	if ($res) {
		return date('Y-m-d H:i', $res[0]['add_time']);
	}
	else {
		return '暂无登录记录';
	}
}

function qiandao($uid = 0)
{
	if (!$uid) {
		return false;
	}

	$stime = strtotime(date('Y-m-d'));
	$etime = strtotime(date('Y-m-d') . ' 23:59:59');
	$count = db('member_qd')->where('uid', $uid)->whereTime('add_time', 'between', array($stime, $etime))->count('id');

	if ($count) {
		return false;
	}
	else {
		return true;
	}
}

function addInnerMsg($uid, $title = '', $msg = '')
{
	if (empty($uid)) {
		return false;
	}

	if (is_array($uid)) {
		$add = array();
		$now = time();

		foreach ($uid as $v) {
			$add[] = array('uid' => $v['id'], 'title' => $title, 'msg' => $msg, 'send_time' => $now);
		}

		$res = db('inner_msg')->insertAll($add);
	}
	else {
		$add = array('uid' => $uid, 'title' => $title, 'msg' => $msg, 'send_time' => time());
		$res = db('inner_msg')->insert($add);
	}

	if ($res) {
		return true;
	}
	else {
		return false;
	}
}

function getInnerCount($uid = 0)
{
	$data = db('inner_msg')->where('uid', $uid)->where('status', 0)->count('id');
	return $data;
}

function hetong($category = 0)
{
	if (!$category) {
		return false;
	}

	if (cache('hetong' . $category)) {
		$data = cache('hetong' . $category);
	}
	else {
		$data = db('hetong')->where('category', $category)->field(true)->find();
		cache('hetong' . $category, $data);
	}

	return $data;
}

function setBackUrl()
{
	if (!empty($_SERVER['HTTP_REFERER'])) {
		$urlArr = parse_url($_SERVER['HTTP_REFERER']);
		$url = empty($urlArr['query']) ? $urlArr['path'] : $urlArr['path'] . '?' . $urlArr['query'];
		session('history_url', $url);
		return true;
	}
	else {
		session('history_url', NULL);
		return false;
	}
}

function addTip($uid, $type, $money = 0, $info = '')
{
	$add = array('uid' => $uid, 'type' => $type, 'money' => $money, 'info' => $info, 'create_time' => date('Y-m-d H:i:s', time()));
	$res = db('tip')->insert($add);

	if ($res) {
		return true;
	}
	else {
		return false;
	}
}

function clearTip($uid, $type)
{
	$w = array('uid' => $uid, 'type' => $type);
	$res = db('tip')->where($w)->update(array('status' => 1));

	if ($res) {
		return true;
	}
	else {
		return false;
	}
}

function admin_verifyip($client_ip = '')
{
	if (!$client_ip) {
		return false;
	}

	if (cache('allow_ip')) {
		$allow_ip = cache('allow_ip');
	}
	else {
		$allow_ip = array();
		$allow = db('verifyip')->field('ip')->select();

		foreach ($allow as $v) {
			$allow_ip[] = $v['ip'];
		}

		cache('allow_ip', $allow_ip);
	}

	if (in_array($client_ip, $allow_ip)) {
		return true;
	}
	else {
		return false;
	}
}

function admin_acl($group_id, $controller, $action)
{
	if (!$group_id || !$controller || !$action) {
		return false;
	}

	$rabc = config('rabc');

	if (cache('admin_acl' . $group_id)) {
		$admin_acl = cache('admin_acl' . $group_id);
	}
	else {
		$admin_acl = db('acl')->where('id', $group_id)->value('value');
		$admin_acl = json_decode($admin_acl, true);
		cache('admin_acl' . $group_id, $admin_acl);
	}

	if (empty($admin_acl[$controller])) {
		return false;
	}
	else {
		$temp_admin_acl = array_map('strtolower', $admin_acl[$controller]);
	}

	if (!empty($rabc[$controller]['acl'])) {
		$temp_acl = array();

		foreach ($rabc[$controller]['acl'] as $v) {
			$temp_acl = array_merge($temp_acl, array_keys($v['list']));
		}

		$temp_acl = array_map('strtolower', $temp_acl);

		if (in_array($action, $temp_acl)) {
			if (!empty($temp_admin_acl)) {
				if (in_array($action, $temp_admin_acl)) {
					return true;
				}
			}

			return false;
		}
	}

	return 'true out';
}

function admin_empty_data()
{
	return '<tr class=\'empty_data\'><td colspan=\'100\'><i></i>暂无数据</td></tr>';
}

function admin_log($admin_name = '', $info = '', $ip = '')
{
	if (!$admin_name) {
		return false;
	}

	$add = array('admin_name' => $admin_name, 'deal_ip' => request()->ip(), 'deal_info' => $info, 'add_time' => time());
	$res = db('admin_log')->insert($add);

	if ($res) {
		return true;
	}
	else {
		return false;
	}
}

function get_user_value($uid = 0, $value = 'user_name')
{
	if (!$uid || !$value) {
		return false;
	}

	$result = db('members')->where('id', $uid)->value($value);

	if ($result) {
		return $result;
	}
	else {
		return false;
	}
}

function addFinancingCount($id)
{
	if (!$id) {
		return false;
	}

	$field = 'a.uid,a.borrow_id,a.money,a.borrow_fee,a.money,a.dikou_interest,a.status,a.category,b.deposit_money,b.type,b.borrow_money,b.home_user,b.multiple,b.repayment_type,m.user_name,m2.real_name';
	$info = db('stock_addfinancing')->alias('a')->join('stock_borrow b', 'a.borrow_id=b.id', 'left')->join('members m', 'b.uid=m.id', 'left')->join('member_info m2', 'b.uid=m2.uid', 'left')->field($field)->where(array('a.id' => $id, 'a.status' => 0))->find();

	if ($info['category'] == 1) {
		$stock_set = get_stock_setting();
	}
	else {
		$stock_set = get_future_setting();
	}

	if ($info) {
		$applyMoney = $info['money'] * $info['multiple'];

		switch ($info['type']) {
		case 1:
			$loss = explode('|', $stock_set['day_loss']);
			break;

		case 2:
			$loss = explode('|', $stock_set['month_loss']);
			break;

		case 8:
			$loss = explode('|', $stock_set['vip_loss']);
			break;

		default:
			return false;
			break;
		}

		$borrowSumMoney = $info['borrow_money'] + $applyMoney + $info['money'];
		$sumDepositMoney = $info['deposit_money'] + $info['money'];
		$sumMoney = $borrowSumMoney - $sumDepositMoney;
		$loss_warn = $sumMoney + $sumDepositMoney * $loss[0] / 100;
		$loss_close = $sumMoney + $sumDepositMoney * $loss[1] / 100;
		$info['sumFee'] = $info['money'] + $info['borrow_fee'];
		$info['sumDepositMoney'] = $sumDepositMoney;
		$info['sumBorrowMoney'] = $borrowSumMoney;
		$info['sumLossWarn'] = $loss_warn;
		$info['sumLossClose'] = $loss_close;
	}
	else {
		return false;
	}

	return $info;
}

function settlementFinancing($money = 0, $borrow_id = 0)
{
	if (!$money || !$borrow_id) {
		return false;
	}

	$binSql = db('stock_borrow');
	$binfo = $binSql->where(array('id' => $borrow_id, 'status' => 2))->field(true)->find();
	$type = $type2 = 0;

	if ($binfo) {
		if ($binfo['borrow_money'] < $money) {
			$profitMoney = $money - $binfo['borrow_money'];

			if ($binfo['category'] == 1) {
				$freeSet = explode('|', get_stock_setting()['free_set']);
			}
			else {
				$freeSet = explode('|', get_future_setting()['free_set']);
			}

			$type = 12;
			$type2 = 13;
			$affect_money = $binfo['deposit_money'];
			$info = '配资使用期限结束，释放保证金：' . $affect_money / 100 . '元';

			if ($binfo['type'] == 3) {
				$affect_money2 = $profitMoney * ($freeSet[2] / 100);
				$info2 = '配资使用期限结束，盈利' . $profitMoney / 100 . ('元，盈利的' . $freeSet[2] . '% ') . $affect_money2 / 100 . '元归您';
			}
			else if ($binfo['type'] == 4) {
				$affect_money2 = $profitMoney;
				$info2 = '免费体验使用期限结束，盈利' . $profitMoney / 100 . '元，盈利金额已转入可用余额';
			}
			else {
				$affect_money2 = $profitMoney;
				$info2 = '配资使用期限结束，盈利' . $profitMoney / 100 . '元';
			}
		}
		else if ($money - $binfo['borrow_money'] == 0) {
			$type = 12;
			$mlog2 = true;
			$affect_money = $binfo['deposit_money'];
			$info = '配资使用期限结束，剩余配资金额与原总操盘金额相等，释放保证金：' . $affect_money / 100 . '元';
		}
		else {
			$lossMoney = $binfo['borrow_money'] - $money;
			if ($binfo['type'] == 4 && $binfo['deposit_money'] <= $lossMoney) {
				$sumMoney = 0 - $binfo['deposit_money'];
			}
			else {
				$sumMoney = 0 - $lossMoney;
			}

			$mlog2 = true;
			$type = 12;
			$type2 = $binfo['type'] == 4 ? 0 : 13;
			$affect_money = $binfo['deposit_money'];
			$affect_money2 = $sumMoney;
			$info = '配资使用期限结束，释放保证金：' . $affect_money / 100 . '元';
			$info2 = '配资使用期限结束，由于您剩余配资资金小于总操盘资金，扣除亏损金额' . $lossMoney / 100 . '元';
		}

		if ($type == 12) {
			$mlog1 = membermoneylog($binfo['uid'], $affect_money, $info, $type);
		}

		if ($type2 == 13) {
			$interest = 1;
			$mlog2 = membermoneylog($binfo['uid'], $affect_money2, $info2, $type2, $interest);
		}

		$totalDrawProfit = db('stock_drawprofit')->where('borrow_id', $borrow_id)->where('status', 1)->sum('money');
		$totalAddMoney = db('stock_addmoney')->where('borrow_id', $borrow_id)->where('status', 1)->sum('money');
		$resu = $totalDrawProfit - $totalAddMoney;
		$data['return_money'] = $money - $binfo['borrow_money'] + $resu;
		$data['return_rate'] = $data['return_money'] / $binfo['deposit_money'];
		$data['status'] = 3;
		$data['stock_money'] = $money + $resu;
		$data['stock_usable_money'] = 0;
		$ret = $binSql->where('id', $borrow_id)->update($data);
		if ($mlog1 && $mlog2) {
			return true;
		}
		else {
			return false;
		}
	}
}

function checkStopApply($borrow_id = 0)
{
	if (!$borrow_id) {
		return array('status' => -1, 'message' => '数据解析错误，请按规则重试！');
	}

	$w = array('status' => 0, 'borrow_id' => $borrow_id);
	$addmoneyres = db('stock_addmoney')->where($w)->count('id');

	if ($addmoneyres) {
		return array('status' => -1, 'message' => '该用户有未审核的补充亏损申请，请到“补充亏损”处理');
	}

	$renewalres = db('stock_renewal')->where($w)->count('id');

	if ($renewalres) {
		return array('status' => -1, 'message' => '该用户有未审核的申请延期，请到“申请延期”处理');
	}

	$addfinancingres = db('stock_addfinancing')->where($w)->count('id');

	if ($addfinancingres) {
		return array('status' => -1, 'message' => '该用户有未审核的扩大配资申请，请到“扩大配资”处理');
	}

	$drawprofits = db('stock_drawprofit')->where($w)->count('id');

	if ($drawprofits) {
		return array('status' => -1, 'message' => '该用户有未审核的提取盈利申请，请到“提取盈利”处理');
	}

	return true;
}
?>
