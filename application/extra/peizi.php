<?php
// 自定义扩展配置文件
use think\Env;

return [
    'checkout' => [
        'user' => Env::get('checkout.user', 'provider'),
        'key' => Env::get('checkout.key')
    ],

    'guantian' => [
        'user' => Env::get('guantian.user', 'provider'),
        'key' => Env::get('guantian.key')
    ],

    'transaction_api' => [
        'base_url' => Env::get('transaction_api.base_url'),
    ],

    'trading_site' => [
        'login_url' => Env::get('trading_site.login_url', 'https://trade.35211m.com/login/'),
    ],
];