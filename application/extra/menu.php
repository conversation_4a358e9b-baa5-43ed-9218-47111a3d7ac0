<?php
use think\Url;
function makeU($url = '', $vars = '', $suffix = true, $domain = false){
	return Url::build($url, $vars, $suffix, $domain);
}
$i=0;
$menu = array();

$menu[$i]['module'] = '全局';
$menu[$i]['bind'] = 'index';
$menu[$i]['info'][] = array(
					'title'=>'全局设置',
					'list'=>array(
						// !格式!： 菜单名,URL,操作方法名
						['首页',makeU('/index'),'index'],
						['网站配置',makeU('/index/webSet'),'webSet'],
						['广告管理',makeU('/index/adv'),'adv'],
						['前台导航',makeU('/index/nav'),'nav'],
						['友情链接',makeU('/index/friendLink'),'friendLink'],
						['后台日志',makeU('/index/adminLog'),'adminLog'],
						['清空缓存',makeU('/index/clearAll'),'clearAll'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'合同设置',
					'list'=>array(
						['注册协议',makeU('/index/zcxy'),'zcxy'],
						['股票合同',makeU('/index/hetong'),'hetong'],
						// ['期货合同',makeU('/index/qihuohetong'),'qihuohetong'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'充值设置',
					'list'=>array(
						['线下充值',makeU('/index/offline'),'offline'],
						['扫码充值',makeU('/index/scanqrcode'),'scanqrcode'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'首页数据',
					'list'=>array(
						['今日注册',makeU('/index/jrzc'),'jrzc'],
						['今日充值',makeU('/index/jrcz'),'jrcz'],
						// ['配资达人',makeU('/index/pzdr'),'pzdr'],
						// ['今日股票配资',makeU('/index/jrgppz'),'jrgppz'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'后台管理员',
					'list'=>array(
						['管理员列表',makeU('/index/adminList'),'adminList'],
						['权限组管理',makeU('/index/adminAcl'),'adminAcl'],
						['谷歌管理',makeU('/index/codeList'),'codeList'],
					)
				);

$i++;
// !格式!： 菜单名,URL,操作方法名
$menu[$i]['module'] = '股票配资';
$menu[$i]['bind'] = 'stock';
$menu[$i]['info'][] = array(
					'title'=>'股票配资',
					'list'=>array(
						['配资设置',makeU('/stock/stockSet'),'stockSet'],
						['利率设置',makeU('/stock/rateSet'),'rateSet'],
						['交易账户',makeU('/stock/subaccount'),'subaccount'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'配资管理',
					'list'=>array(
						['待审核配资',makeU('/stock/stockApply'),'stockApply'],
						['使用中配资',makeU('/stock/stockList'),'stockList'],
						['已到期配资',makeU('/stock/stockend'),'stockend'],
						['已完成配资',makeU('/stock/stockFinish'),'stockFinish'],
						['未通过配资',makeU('/stock/stockPass'),'stockPass'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'扩大配资',
					'list'=>array(
						['待审核扩大',makeU('/stock/addfinancing'),'addfinancing'],
						['已处理扩大',makeU('/stock/addfinancingList'),'addfinancingList'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'申请延期',
					'list'=>array(
						['待审核延期',makeU('/stock/renewalApply'),'renewalApply'],
						['已处理延期',makeU('/stock/renewalList'),'renewalList'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'补充亏损',
					'list'=>array(
						['待审核补亏',makeU('/stock/fillApply'),'fillApply'],
						['已处理补亏',makeU('/stock/fillList'),'fillList'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'提取盈利',
					'list'=>array(
						['待审核提盈',makeU('/stock/drawprofitApply'),'drawprofitApply'],
						['已处理提盈',makeU('/stock/drawprofitList'),'drawprofitList'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'终止操盘',
					'list'=>array(
						['待审核终止',makeU('/stock/stopfinancingApply'),'stopfinancingApply'],
						['已处理终止',makeU('/stock/stopfinancingList'),'stopfinancingList'],
					)
				);

/*$menu[$i]['info-' . $j]['title'] = '扩大配资';
$menu[$i]['info-' . $j][] = ['待审核配资','/stock/addFinancing'];
$menu[$i]['info-' . $j][] = ['使用中配资','/stock/addFinancingList'];
$j++;*/

$i++;
// $menu[$i]['module'] = '期货配资';
// $menu[$i]['bind'] = 'future';
// $menu[$i]['info'][] = array(
// 					'title'=>'期货配资',
// 					'list'=>array(
// 						['配资设置',makeU('/future/stockSet'),'stockSet'],
// 						['利率设置',makeU('/future/rateSet'),'rateSet'],
// 						['交易账户',makeU('/future/subaccount'),'subaccount'],
// 					)
// 				);
// $menu[$i]['info'][] = array(
// 					'title'=>'配资管理',
// 					'list'=>array(
// 						['待审核配资',makeU('/future/stockApply'),'stockApply'],
// 						['使用中配资',makeU('/future/stockList'),'stockList'],
// 						['已到期配资',makeU('/future/stockend'),'stockend'],
// 						['已完成配资',makeU('/future/stockFinish'),'stockFinish'],
// 						['未通过配资',makeU('/future/stockPass'),'stockPass'],
// 					)
// 				);
// $menu[$i]['info'][] = array(
// 					'title'=>'扩大配资',
// 					'list'=>array(
// 						['待审核扩大',makeU('/future/addfinancing'),'addfinancing'],
// 						['已处理扩大',makeU('/future/addfinancingList'),'addfinancingList'],
// 					)
// 				);
// $menu[$i]['info'][] = array(
// 					'title'=>'申请延期',
// 					'list'=>array(
// 						['待审核延期',makeU('/future/renewalApply'),'renewalApply'],
// 						['已处理延期',makeU('/future/renewalList'),'renewalList'],
// 					)
// 				);
// $menu[$i]['info'][] = array(
// 					'title'=>'补充亏损',
// 					'list'=>array(
// 						['待审核补亏',makeU('/future/fillApply'),'fillApply'],
// 						['已处理补亏',makeU('/future/fillList'),'fillList'],
// 					)
// 				);
// $menu[$i]['info'][] = array(
// 					'title'=>'提取盈利',
// 					'list'=>array(
// 						['待审核提盈',makeU('/future/drawprofitApply'),'drawprofitApply'],
// 						['已处理提盈',makeU('/future/drawprofitList'),'drawprofitList'],
// 					)
// 				);
// $menu[$i]['info'][] = array(
// 					'title'=>'终止操盘',
// 					'list'=>array(
// 						['待审核终止',makeU('/future/stopfinancingApply'),'stopfinancingApply'],
// 						['已处理终止',makeU('/future/stopfinancingList'),'stopfinancingList'],
// 					)
// 				);

// $i++;
// !格式!： 菜单名,URL,操作方法名
$menu[$i]['module'] = '会员';
$menu[$i]['bind'] = 'members';
$menu[$i]['info'][] = array(
					'title'=>'会员管理',
					'list'=>array(
						['会员列表',makeU('/members/index'),'index'],
						['登录IP统计',makeU('/members/ipsummary'),'ipsummary']
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'代理管理',
					'list'=>array(
						['代理列表',makeU('/members/daili'),'daili'],
					)
				);

$i++;
// !格式!： 菜单名,URL,操作方法名
$menu[$i]['module'] = '文章';
$menu[$i]['bind'] = 'article';
$menu[$i]['info'][] = array(
					'title'=>'文章管理',
					'list'=>array(
						['文章栏目',makeU('/article/category'),'category'],
						['文章列表',makeU('/article/lists'),'lists'],
					)
				);


$i++;
// !格式!： 菜单名,URL,操作方法名
$menu[$i]['module'] = '资金';
$menu[$i]['bind'] = 'money';
$menu[$i]['info'][] = array(
					'title'=>'资金记录',
					'list'=>array(
						['资金明细',makeU('/money/fund'),'fund'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'后台转账',
					'list'=>array(
						['会员转账',makeU('/money/transfer'),'transfer'],
						['转账记录',makeU('/money/transList'),'transList'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'充值管理',
					'list'=>array(
						['待审核充值',makeU('/money/rechargeApply'),'rechargeApply'],
						['已处理充值',makeU('/money/rechargeList'),'rechargeList'],
						['充值订单',makeU('/money/rechargeOrders'),'rechargeOrders'],
					)
				);
$menu[$i]['info'][] = array(
					'title'=>'提现管理',
					'list'=>array(
						['待审核提现',makeU('/money/withdrawApply'),'withdrawApply'],
						['已处理提现',makeU('/money/withdrawList'),'withdrawList'],
						['提现订单',makeU('/money/withdrawOrders'),'withdrawOrders'],
					)
				);

$i++;
// !格式!： 菜单名,URL,操作方法名
$menu[$i]['module'] = '其他';
$menu[$i]['bind'] = 'other';
$menu[$i]['info'][] = array(
					'title'=>'其他设置',
					'list'=>array(
						['客服管理',makeU('/other/kefu'),'kefu'],
						['短信接口',makeU('/other/duanxin'),'duanxin'],
						['实名接口',makeU('/other/shiming'),'shiming'],
						['PC端轮播',makeU('/other/pclunbo'),'pclunbo'],
						['WAP端轮播',makeU('/other/waplunbo'),'waplunbo'],
					)
				);



return $menu;
