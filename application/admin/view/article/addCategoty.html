{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>文章</a>
		<a>文章管理</a>
		<a>
			<cite>
			{eq name="action" value="edit"}
			修改栏目
			{else/}
			添加栏目
			{/eq}
			</cite>
		</a>
	</span>
</div>

<style type="text/css">
.layui-form-item .layui-input-inline{ width: 266px; }
</style>


<!-- <form class="layui-form" action="{:url('article/addCategoty')}" method="POST" onsubmit="return checkSub()"> -->
<form class="layui-form" action="{:url('article/addCategoty')}" method="POST" onsubmit="return sub();">


<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
	<ul class="layui-tab-title">
		<li class="layui-this">设置</li>
		<li>内容</li>
	</ul>
	<div class="layui-tab-content" style="height: 100px;">
		<div class="layui-tab-item layui-show">
			<div class="layui-form-item" >
			    <label class="layui-form-label layui-width">栏目名称：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="mingcheng" value="{$data.name|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"> <span style='color:red;'>*</span> </div>
			</div>
			
			<div class="layui-form-item" >
				<div class="layui-inline">
					<label class="layui-form-label">上级栏目：</label>
					<div class="layui-input-inline">
						<select name="subcate" lay-verify="required" lay-search="">
							{eq name="action" value="edit"}
								{eq name="data.parent_id" value="0"}
									<option value="0" selected>顶级栏目</option>
								{else/}
									<option value="0">顶级栏目</option>
									{volist name="tree" id="vo"}
									<option value="{$vo.id}" {if condition="$vo.id eq $data.parent_id"}selected{/if} >{$vo.str}</option>
									{/volist}
								{/eq}
							{else/}
								<option value="0" selected>顶级栏目</option>
								{volist name="tree" id="vo"}
								<option value="{$vo.id}">{$vo.str}</option>
								{/volist}
							{/eq}

						</select>
						<!-- <div class="layui-form-select">
							<div class="layui-select-title">
								<input type="text" placeholder="直接选择或搜索选择" value="" class="layui-input">
								<i class="layui-edge"></i>
							</div>
						</div> -->
					</div>
			    	<div class="layui-form-mid layui-word-aux "> <span style='color:red;'>*</span></div>
			    </div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">栏目属性：</label>
				<div class="layui-input-block">
					{eq name="action" value="edit"}
					<input type="radio" name="shuxing" value="1" title="单页面" {neq name="data.type_set" value="2"} checked {/neq} >
					<input type="radio" name="shuxing" value="2" title="列表页" {eq name="data.type_set" value="2"} checked {/eq} >
					{else/}
					<input type="radio" name="shuxing" value="1" title="单页面" checked>
					<input type="radio" name="shuxing" value="2" title="列表页">
					{/eq}
				</div>
		  	</div>

			<div class="layui-form-item" >
			    <label class="layui-form-label layui-width">栏目简称：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="jiancheng" value="{$data.type_nid|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="" maxlength="16" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"><span style='color:red;'>* 该栏目的网址标识(使用英文字母)</span></div>
			</div>

			<div class="layui-form-item" >
			    <label class="layui-form-label layui-width">排序：</label>
			    <div class="layui-input-inline ">
					<input type="number" name="paixu" value="{$data.sort_order|default=0}" lay-verify="number" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">是否显示：</label>
				<div class="layui-input-block">
					{eq name="action" value="edit"}
					<input type="checkbox" {eq name="data.is_hidden" value="1"}checked{/eq} name="is_hidden" lay-skin="switch" lay-filter="switchTest" lay-text="显 示|隐 藏">
					{else/}
					<input type="checkbox" checked="" name="is_hidden" lay-skin="switch" lay-filter="switchTest" lay-text="显 示|隐 藏">
					{/eq}
				</div>
			</div>

			<br>
			<div class="layui-form-item">
				<div class="layui-input-block" style="margin-left: 110px;">
					<button class="layui-btn" lay-submit="">
						{eq name="action" value="edit"}
						确认修改
						{else/}
						确认提交
						{/eq}
					</button>
				</div>
			</div>

		</div>
		<div class="layui-tab-item">
			
			<div class="layui-form-item">
				<label class="layui-form-label">单页内容：</label>
				<div class="layui-input-inline " style="width:865px;padding-top: 8px;">
					<script id="container" name="contents" type="text/plain">{eq name="action" value="edit"}{:htmlspecialchars_decode($data.content)}{/eq}</script>
				</div>
			</div>

			<div class="layui-form-item">
				<div class="layui-input-block" style="margin-left: 110px;">
					<button class="layui-btn" lay-submit="">
						{eq name="action" value="edit"}
						确认修改
						{else/}
						确认提交
						{/eq}
					</button>
				</div>
			</div>

		</div>
	</div>
</div> 

	<input type="hidden" name="id" value="{$id}">
	<input type="hidden" name="action" value="{$action}">
	{:token()}
	
</form>


<script type="text/javascript">
var isSub = false;
function sub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2, anim:6});
		return false;
	}
	var title = $('input[name=mingcheng]').val();
	if(!title){
		layer.msg('栏目名称不能为空！',{icon:2, anim:6});
		return false;
	}
	var jiancheng = $('input[name=jiancheng]').val();
	if( !jiancheng ){
		layer.msg('栏目简称不能为空！',{icon:2, anim:6});
		return false;
	}
	var pat = /^[\da-zA-Z]{2,16}$/;
    if( !pat.test(jiancheng) ){
        layer.msg('栏目简称必须使用英文字母！',{icon:2, anim:6});
        return false;
    }

	isSub = true;
	return true;
}

function delConfirm(id,title){
	layer.confirm('您确定删除？', {
		title:title,
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('other/doKefu')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}
$(function(){
})
</script>
{load href="/ueditor/ueditor.config.js" /}
{load href="/ueditor/ueditor.all.js" /}
<script type="text/javascript">
var ue = UE.getEditor('container',{
    autoHeightEnabled: true,
    autoFloatEnabled:false
});
</script>