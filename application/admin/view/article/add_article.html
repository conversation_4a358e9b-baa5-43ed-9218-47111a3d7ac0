{layout name="layout" /}

{load href="/static/laydate-v5.0.9/laydate.js" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>文章</a>
		<a>文章管理</a>
		<a><cite>{eq name="action" value="edit"}修改文章{else/}添加文章{/eq}</cite></a>
	</span>
</div>

<br><br>

<form class="layui-form" action="{:url('article/addArticle')}" method="post" onsubmit="return sub();">
	<div class="layui-form-item">
	    <label class="layui-form-label label2-title">文章标题：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="title" value="{$data.title|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"><span style='color:red;'>*</span> 标题最大长度50个字</div>
	</div>

	<div class="layui-form-item" >
		<div class="layui-inline">
			<label class="layui-form-label label2-title">上级栏目：</label>
			<div class="layui-input-inline w333px">
				<select name="parent_id" lay-verify="required" lay-search="">
					{eq name="action" value="edit"}
						<option value="0" selected>选择栏目</option>
						{volist name="tree" id="vo"}
						<option value="{$vo.id}" {if condition="$vo.id eq $data.parent_id"}selected{/if} >{$vo.str}</option>
						{/volist}
					{else/}
						<option value="0" selected>选择栏目</option>
						{volist name="tree" id="vo"}
						<option value="{$vo.id}">{$vo.str}</option>
						{/volist}
					{/eq}
				</select>
			</div>
	    	<div class="layui-form-mid layui-word-aux "><span style='color:red;'>* 所属栏目</span></div>
	    </div>
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label label2-title">文章关键字：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="keyword" value="{$data.keyword|default=''}" lay-verify="" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"> SEO元素</div>
	</div>

	 <div class="layui-form-item layui-form-text">
        <label class="layui-form-label label2-title">文章简介：</label>
        <div class="layui-input-inline w333px">
            <textarea name="info" placeholder="请输入内容" class="layui-textarea" style="min-height: 69px;">{$data.info|default=''}</textarea>
        </div>
        <div class="layui-form-mid layui-word-aux"> SEO元素</div>
    </div>
	
	<div class="layui-form-item">
	    <label class="layui-form-label label2-title">文章顺序：</label>
	    <div class="layui-input-inline w333px">
			<input type="number" name="sort_order" value="{$data.sort_order|default=0}" lay-verify="number" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"> 越大越靠前</div>
	</div>
	
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label label2-title">发布时间：</label>
			<div class="layui-input-inline w333px">
				{eq name="action" value="edit"}
				<input type="text" name="art_time" value="{:date('Y-m-d H:i:s',$data.add_time)}" class="layui-input" id="test1" placeholder="可自定义文章发布时间" autocomplete="off">
				{else/}
				<input type="text" name="art_time" class="layui-input" id="test1" placeholder="可自定义文章发布时间" autocomplete="off">
				{/eq}
			</div>
			<div class="layui-form-mid layui-word-aux"> 默认当前时间</div>
		</div>
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label label2-title">是否显示/隐藏：</label>
	    <div class="layui-input-block">
	    	{eq name="action" value="edit"}
	    	<input type="radio" name="is_hide" value="1" title="显示" {eq name="data.is_hide" value="1"}checked{/eq} >
	      	<input type="radio" name="is_hide" value="2" title="隐藏" {eq name="data.is_hide" value="2"}checked{/eq}>
			{else/}
			<input type="radio" name="is_hide" value="1" title="显示" checked="">
	      	<input type="radio" name="is_hide" value="2" title="隐藏">
			{/eq}
	    </div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label label2-title">文章内容：</label>

		<div class="layui-input-inline " style="width:900px;padding-top: 8px;">
			<script id="container" name="contents" type="text/plain">{eq name="action" value="edit"}{:htmlspecialchars_decode($data.content)}{/eq}</script>
		</div>
	</div>

	{:token()}
	<input type="hidden" name="id" value="{$id}">
	<input type="hidden" name="action" value="{$action}">
	<br>
	<div class="layui-form-item">
		<label class="layui-form-label label2-title"></label>
		<button class="layui-btn" lay-submit="sss">
			确认提交
		</button>
	</div>

</form>

{load href="/ueditor/ueditor.config.js" /}
{load href="/ueditor/ueditor.all.js" /}
<script type="text/javascript">
var ue = UE.getEditor('container',{
    // autoHeightEnabled: true,
    autoFloatEnabled:false,
    initialFrameWidth: 850,
    initialFrameHeight: 400
});

//常规用法
laydate.render({
	elem: '#test1',
	type: 'datetime'
});

$(function(){
})

var isSub = false;
function sub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2, anim:6});
		return false;
	}
	var title = $('input[name=title]').val();
	if(!title){
		layer.msg('文章标题不能为空！',{icon:2, anim:6});
		return false;
	}
	var parent_id = parseInt($('select[name=parent_id]').val());
	if( !parent_id ){
		layer.msg('请选择上级栏目！',{icon:2, anim:6});
		return false;
	}

	isSub = true;
	return true;
}

</script>
