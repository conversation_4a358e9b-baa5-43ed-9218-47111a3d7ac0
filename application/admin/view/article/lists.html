{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>文章</a>
		<a>文章管理</a>
		<a><cite>文章列表</cite></a>
	</span>
</div>

<style type="text/css">
.layui-btn{ float: left; }
</style>

<a href="{:url('article/addArticle', 'action=add')}" class="layui-btn">添加文章</a>

<form class="layui-form">
<div class="layui-inline">
	<label class="layui-form-label" style="width:0;"></label>
	<div class="layui-input-inline">
        <select name="status" lay-filter="test" id='cstatus'>
        	<option data-url='{:url("article/lists")}' >所有栏目</option>
        	{volist name="category" id="vo"}
        	<option data-url='{:url("article/lists", ["parent_id"=>$vo.parent_id])}' {if condition="$vo.parent_id EQ $parent_id"}selected{/if} >{$vo.name}</option>
			{/volist}
        </select>
	</div>
</div>
</form>

<br>

<div class="layui-form" style="min-height:470px;">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="">编号</th>
				<th style="width:33%;text-indent:20px;">文章标题</th>
				<th style="">所属栏目</th>
				<th style="">显示顺序</th>
				<th style="">是否显示</th>
				<th style="">发布时间</th>
				<th style="width:12%;">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
			<tr id="list_{$vo.id}" >
				<td>{$vo.id}</td>
				<td>{$vo.title}</td>
				<td>{$vo.category_name}</td>
				<td>{$vo.sort_order}</td>
				<td>{:str_replace([1,2], ['显示','隐藏'], $vo.is_hide)}</td>
				<td>{:date('Y-m-d H:i',$vo.add_time)}</td>
				<td>
					<a href="{:url('article/addArticle', ['action'=>'edit','id'=>$vo.id] )}" class="layui-bg-blue ft15">编辑</a>
					&nbsp;
					<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">

function delConfirm(id,title){
	layer.confirm('您确定删除？', {
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('article/delArticle')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('#list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

$(function(){

	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});


})


</script>