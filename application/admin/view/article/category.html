{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>文章</a>
		<a>文章管理</a>
		<a><cite>文章栏目</cite></a>
	</span>
</div>

<style type="text/css">
.zsyc{ position: relative;text-indent: 20px; }
.zsyc i {
    display: inline-block;cursor: pointer;
    width: 15px;height: 15px;
    position: absolute;top: 11px;left: 15px;
}
.zsyc i.zhanshi{
	background: url(/static/img/A/zhanshi.png) no-repeat;background-size: 100%;
}
.zsyc i.yincang{
	background: url(/static/img/A/yincang.png) no-repeat;background-size: 100%;
}
.leve_2{ background-color: #f2f2f2; }
.leve_2 .zsyc{ padding-left: 35px; }
.leve_2 .zsyc i{ left: 35px; }
.leve_3{ background-color: #DEDEDE; }
.leve_3 .zsyc{ padding-left: 55px; }
.leve_3 .zsyc i{ left: 55px; }
</style>


<a href="{:url('article/addCategoty','action=add')}" class="layui-btn">添加栏目</a>
<br><br>

<div class="layui-form" style="min-height:470px;">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="">编号</th>
				<th style="width:33%;text-indent:20px;">栏目名称</th>
				<th style="">栏目简称</th>
				<th style="">显示顺序</th>
				<th style="">是否显示</th>
				<th style="">添加时间</th>
				<th style="width:12%;">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo"}
			<tr id="list_{$vo.id}" data-id="{$vo.id}" parentid="0" class="leve_1">
				<td>{$vo.id}</td>
				<td class="zsyc">
					{eq name="vo.has" value="true"}
					<i class="zhanshi"></i>
					{/eq}
					{$vo.name}
				</td>
				<td>{$vo.type_nid}</td>
				<td>{$vo.sort_order}</td>
				<td>{:str_replace([1,2], ['显示','隐藏'], $vo.is_hidden)}</td>
				<td>{$vo.create_time}</td>
				<td>
					<a href="{:url('article/addCategoty', ['action'=>'edit','id'=>$vo.id] )}" class="layui-bg-blue ft15">编辑</a>
					&nbsp;
					<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>




<script type="text/javascript">
$(function(){
	$('.layui-form').on('click', '.zhanshi', function(event) {
		var type_id = $(this).parents('tr').data('id');
		var this_tr = $(this).parents('tr');
		var son_tree =  $('.layui-table tr[parentid='+type_id+']');
		if(son_tree.html()==null){
			$.ajax({
		        url: "{:url('article/getCategotyList')}",
		        type: "post",
		        dataType: "json",
		        data: {type_id:type_id,action:''},
		        success: function(d,e,x){
		            // console.log(d,e,x);return;
		            if(d.status==1){
		            	$(this_tr).after(d.data);
		            }else{
		            	return layer.msg( d.message,{icon : 2} );
		            }
		        }
		    });
		}else{
			if( son_tree.css('display')=='none' ){
				son_tree.show();
			}else{
				son_tree.hide();
			}
		}

	});

})

function delConfirm(id,title){
	layer.confirm('您确定删除？', {
		title:title,
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('article/delCategory')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('#list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

$(function(){

	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});


})


</script>