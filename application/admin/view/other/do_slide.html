{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-label{
	width: 100px;
    padding: 9px 6px;
}
#demo2 .img{
	width: 350px;height: 215px;
	text-align: center;border: 1px solid #ccc;
    border-radius: 6px;overflow: hidden;
    margin-bottom: 10px;position: relative;
    margin: 0 auto;
}
#demo2 .img img{
	width: 100%;height: 125px;
}
#demo2 .img input{
	padding: 3px;margin-bottom: 7px;
	border: 1px solid #D2D2D2;border-radius: 5px;
}
</style>

<div class="layui-main" style="padding: 21px 0;">
	<form class="layui-form" onsubmit="return false;">
		<div class="layui-upload">
			<button type="button" class="layui-btn" id="test1">
				{eq name="action" value="edit"}
				点击修改图片
				{else /}
				点击上传轮播
				{/eq}
			</button>
			<div class="layui-upload-list" id="demo2">
				<div class="img">
					<div class="shade"></div>
					{eq name="action" value="edit"}
					<img id="demo1" src="{$data.image}" alt="">
					{else /}
					<img id="demo1" alt="">
					{/eq}
					<div class="mt15">
						<span>排序：</span><input name="shunxu" value="{$data.order|default=''}" type="number" placeholder="数字大，位置靠前"><br><span>地址：</span><input type="text" value="{$data.link|default=''}" name="lianjie" placeholder="图片链接地址">
					</div>
				</div>
				<p id="demoText"></p>
			</div>
		</div>
		<input type="hidden" name="type" value="{$type}">
		<input type="hidden" name="image" value="{$data.image|default=''}">
		<div class="layui-form-item" style="text-align: center;">
			<button class="layui-btn" lay-submit="" onclick="sub()">
				{eq name="action" value="edit"}
				确认修改
				{else /}
				确认添加
				{/eq}
			</button>
		</div>
	</form>
</div>

<script type="text/javascript">
var action = '{$action}';
var id = '{$data.id|default=0}';
function sub(){
	var image = $('input[name=image]').val();
	var shunxu = $('input[name=shunxu]').val();
	var lianjie = $('input[name=lianjie]').val();
	var type = $('input[name=type]').val();
	if( !image ){
		return layer.msg( '请先上传图片！',{icon : 2} );
	}
	// console.log(!image,image,shunxu,lianjie);return;
	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	if(action=='edit'){
		var param = {image:image,shunxu:shunxu,lianjie:lianjie,type:type,id:id,action:action};
	}else{
		var param = {image:image,shunxu:shunxu,lianjie:lianjie,type:type};
	}
	// return;
	$.ajax({
        url: "{:url('other/doSlide')}",
        type: "post",
        dataType: "json",
        data: param,
        success: function(d,e,x){
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );
            	var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){ 
					parent.reloadData();
					// parent.layer.close(index);
				}, 1000);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}




layui.use('upload', function(){
  	var $ = layui.jquery
  	,upload = layui.upload;
  
  	//普通图片上传
  	var uploadInst = upload.render({
  		accept:'images',
  		field: 'img',
	    elem: '#test1'
	    ,url: '{:url("index/upload")}'
	    ,before: function(obj){
	      	//预读本地文件示例，不支持ie8
	      	obj.preview(function(index, file, result){
				$('#demo1').attr('src', result); //图片链接（base64）
	      	});
	    }
	    ,done: function(res){
	    	// console.log(res,1);
	      	if(res.status == 1){
	      		//上传成功
                $('#demoText').html('');
	      		$('input[name=image]').val(res.savename);
	      	}else{
	      		//如果上传失败
	        	return layer.msg( '文件上传失败！ '+res.message , {anim:6});
	      	}
	    }
	    ,error: function(){
	      	//演示失败状态，并实现重传
	      	var demoText = $('#demoText');
	      	demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
	      	demoText.find('.demo-reload').on('click', function(){
	        	uploadInst.upload();
	      	});
	    }
	});
});



</script>