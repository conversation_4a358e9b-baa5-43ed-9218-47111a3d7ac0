{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>其他</a>
		<a>其他设置</a>
		<a><cite>实名接口</cite></a>
	</span>
</div>

<style type="text/css">
.layui-word-aux{
	margin-left: -30px;
}
.layui-form-label{ width: 100px; }
</style>


<br>

<form class="layui-form" action="{:url('other/shiming')}" method="POST" onsubmit="return checkSub()">
	
	<div class="layui-form-item">
	    <label class="layui-form-label">姓名：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="xingming" value="{$data.xingming|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>
	<div class="layui-form-item">
	    <label class="layui-form-label">证件号：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="zhengjian" value="{$data.zhengjian|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label">接口：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="dizhi" value="{$data.dizhi|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>
	<div class="layui-form-item">
	    <label class="layui-form-label">秘钥：</label>
	    <div class="layui-input-inline w333px">
			<input type="password" name="mima" value="{$data.mima|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>
	<!-- <div class="layui-form-item">
	    <label class="layui-form-label">APPKEY：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="key" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div> -->
	
	<div class="layui-form-item">
		<label class="layui-form-label">是否启用：</label>
		<div class="layui-input-inline">
			<input type="radio" name="status" value="1" title="启用" {eq name="data.status" value="1"}checked{/eq} >
			<input type="radio" name="status" value="0" title="关闭" {neq name="data.status" value="1"}checked{/neq} >
		</div>
		<div class="layui-form-mid layui-word-aux">关闭自动认证功能后，可在用户资料中手动认证</div>
	</div>

	{:token()}

	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 169px;">
			<button class="layui-btn" lay-submit="" lay-filter="demo1">确认提交</button>
		</div>
	</div>
</form>



<script type="text/javascript">
$(function(){

	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});


})


</script>