{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>其他</a>
		<a>其他设置</a>
		<a><cite>客服管理</cite></a>
	</span>
</div>

<style type="text/css">
</style>


<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
	<ul class="layui-tab-title">
		<li class="layui-this">QQ客服</li>
		<li>电话客服</li>
	</ul>
	<div class="layui-tab-content" style="height: 100px;">
		<div class="layui-tab-item layui-show">
			
			<button class="layui-btn" onclick="addIframe('添加QQ客服' , '{:url('other/kefu','type=qq')}' )">添加QQ客服</button>
			
			<br><br>

			<div class="layui-form" style="height:470px;">
				<table class="layui-table" lay-skin="line">
					<thead>
						<tr>
							<th style="width:6%;">编号</th>
							<th style="">客服名称</th>
							<th style="">客服号码</th>
							<th style="">显示状态</th>
							<th style="">顺序</th>
							<th style="width:12%;">操作</th>
						</tr> 
					</thead>
					<tbody>
						{volist name="qq_data" id="vo"}
						<tr class="list_{$vo.id}">
							<td>{$vo.id}</td>
							<td>{$vo.title}</td>
							<td>{$vo.number}</td>
							<td>{:str_replace([1,2], ['展示','隐藏'], $vo.is_show)}</td>
							<td>{$vo.order}</td>
							<td>
								<a href="javascript:;" onclick="addIframe('修改QQ客服' , '{:url('other/kefu', ['type'=>'qq','action'=>'edit','id'=>$vo.id] )}' )" class="layui-bg-blue ft15">编辑</a>
								&nbsp;
								<a href="javascript:;" onclick="delConfirm( {$vo.id},'删除QQ客服' )" class="layui-bg-red ft15">删除</a>
							</td>
						</tr>
						{/volist}

					</tbody>
			  	</table>
			</div>
			
		</div>
		<div class="layui-tab-item">

			<button class="layui-btn" onclick="addIframe('添加电话客服' , '{:url('other/kefu','type=tel')}' )">添加电话客服</button>
			
			<br><br>

			<div class="layui-form" style="height:470px;">
				<table class="layui-table" lay-skin="line">
					<thead>
						<tr>
							<th style="width:6%;">编号</th>
							<th style="">客服名称</th>
							<th style="">客服号码</th>
							<th style="">显示状态</th>
							<th style="">顺序</th>
							<th style="width:12%;">操作</th>
						</tr> 
					</thead>
					<tbody>
						{volist name="tel_data" id="vo"}
						<tr class="list_{$vo.id}">
							<td>{$vo.id}</td>
							<td>{$vo.title}</td>
							<td>{$vo.number}</td>
							<td>{:str_replace([1,2], ['展示','隐藏'], $vo.is_show)}</td>
							<td>{$vo.order}</td>
							<td>
								<a href="javascript:;" onclick="addIframe('修改电话客服' , '{:url('other/kefu', ['type'=>'tel','action'=>'edit','id'=>$vo.id] )}' )" class="layui-bg-blue ft15">编辑</a>
								&nbsp;
								<a href="javascript:;" onclick="delConfirm( {$vo.id},'删除电话客服' )" class="layui-bg-red ft15">删除</a>
							</td>
						</tr>
						{/volist}

					</tbody>
			  	</table>
			</div>

		</div>
	</div>
</div> 



<script type="text/javascript">
function addIframe(title,url){
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false,
		area : ['660px' , '417px'],
		content: url,
		end: function(){
			// location.reload();
		}
    });
}

function delConfirm(id,title){
	layer.confirm('您确定删除？', {
		title:title,
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('other/doKefu')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

$(function(){

	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});


})


</script>