{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-label{
	width: 100px;
    padding: 9px 6px;
}
.layui-form-item .layui-input-inline{ width: 260px; }
</style>

<div class="layui-main" style="padding: 21px 0;">

	{eq name="type" value="qq"}
		<form class="layui-form" onsubmit="return false;">
			<div class="layui-form-item" >
			    <label class="layui-form-label ">QQ客服名称：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="mingcheng" value="{$data.title|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
			</div>

			<div class="layui-form-item" >
			    <label class="layui-form-label ">QQ客服号码：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="haoma" value="{$data.number|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
			</div>
		
			<div class="layui-form-item" >
			    <label class="layui-form-label ">显示顺序：</label>
			    <div class="layui-input-inline ">
					<input type="number" name="shunxu" value="{$data.order|default=0}" lay-verify="number" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux">数字大顺序靠前</div>
			</div>
			
			<div class="layui-form-item">
				<label class="layui-form-label">是否展示：</label>
				<div class="layui-input-block">
					{eq name="action" value="edit"}
						<input type="radio" name="is_show" value="1" title="展示" {neq name="data.is_show" value="2"}checked{/neq} >
						<input type="radio" name="is_show" value="2" title="隐藏" {eq name="data.is_show" value="2"}checked{/eq} >
					{else/}
						<input type="radio" name="is_show" value="1" title="展示" checked>
						<input type="radio" name="is_show" value="2" title="隐藏" >
					{/eq}
				</div>
			</div>
			<br>
			<div class="layui-form-item">
				<div class="layui-input-block" style="margin-left: 112px;">
					{eq name="action" value="edit"}
					<button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="qqSub('edit' , {$data.id} )">确认修改</button>
					{else/}
					<button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="qqSub()">确认添加</button>
					{/eq}
				</div>
			</div>
		</form>
	{else/}
		<form class="layui-form" onsubmit="return false;">
			<div class="layui-form-item" >
			    <label class="layui-form-label ">电话客服名称：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="mingcheng" value="{$data.title|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
			</div>

			<div class="layui-form-item" >
			    <label class="layui-form-label ">电话客服号码：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="haoma" value="{$data.number|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
			</div>
		
			<div class="layui-form-item" >
			    <label class="layui-form-label ">显示顺序：</label>
			    <div class="layui-input-inline ">
					<input type="number" name="shunxu" value="{$data.order|default=0}" lay-verify="number" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux">数字大顺序靠前</div>
			</div>
			
			<div class="layui-form-item">
				<label class="layui-form-label">是否展示：</label>
				<div class="layui-input-block">
					{eq name="action" value="edit"}
						<input type="radio" name="is_show" value="1" title="展示" {neq name="data.is_show" value="2"}checked{/neq} >
						<input type="radio" name="is_show" value="2" title="隐藏" {eq name="data.is_show" value="2"}checked{/eq} >
					{else/}
						<input type="radio" name="is_show" value="1" title="展示" checked>
						<input type="radio" name="is_show" value="2" title="隐藏" >
					{/eq}
				</div>
			</div>
			<br>
			<div class="layui-form-item">
				<div class="layui-input-block" style="margin-left: 112px;">
					{eq name="action" value="edit"}
					<button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="telSub('edit' , {$data.id} )">确认修改</button>
					{else/}
					<button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="telSub()">确认添加</button>
					{/eq}
				</div>
			</div>
		</form>
	{/eq}

</div>

<script type="text/javascript">
function telSub(action,id){
	var mingcheng = $('input[name=mingcheng]').val();
	var haoma = $('input[name=haoma]').val();
	var shunxu = $('input[name=shunxu]').val();
	var is_show = $('input[name=is_show]:checked').val();
	if( !mingcheng || !haoma || !shunxu ){
		return false;
	}
	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	if(action=='edit'){
		var param = {type:2,mingcheng:mingcheng,haoma:haoma,shunxu:shunxu,is_show:is_show,id:id,action:'edit'}
	}else{
		var param = {type:2,mingcheng:mingcheng,haoma:haoma,shunxu:shunxu,is_show:is_show,action:'add'}
	}
	// console.log(param);
	// return false;
	$.ajax({
        url: "{:url('other/doKefu')}",
        type: "post",
        dataType: "json",
        data: param,
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );
            	var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){ 
					parent.reloadData();
					// parent.layer.close(index);
				}, 1000);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });
}

function qqSub(action,id){
	var mingcheng = $('input[name=mingcheng]').val();
	var haoma = $('input[name=haoma]').val();
	var shunxu = $('input[name=shunxu]').val();
	var is_show = $('input[name=is_show]:checked').val();
	// console.log(!shunxu);return
	if( !mingcheng || !haoma || !shunxu ){
		return false;
	}
	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	if(action=='edit'){
		var param = {type:1,mingcheng:mingcheng,haoma:haoma,shunxu:shunxu,is_show:is_show,id:id,action:'edit'}
	}else{
		var param = {type:1,mingcheng:mingcheng,haoma:haoma,shunxu:shunxu,is_show:is_show,action:'add'}
	}
	// return false;
	$.ajax({
        url: "{:url('other/doKefu')}",
        type: "post",
        dataType: "json",
        data: param,
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );
            	var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){ 
					parent.reloadData();
					// parent.layer.close(index);
				}, 1000);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });
}



layui.use('form', function(){
	var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
	//但是，如果你的HTML是动态生成的，自动渲染就会失效
	//因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
	form.render();
});

</script>