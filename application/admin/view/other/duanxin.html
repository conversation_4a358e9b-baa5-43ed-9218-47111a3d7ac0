{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>其他</a>
		<a>其他设置</a>
		<a><cite>短信接口</cite></a>
	</span>
</div>

<style type="text/css">
.layui-word-aux{
	color: #000 !important;
    font-weight: bold;
    font-size: 15px;
}
.layui-form-label{ width: 100px; }
</style>


<br>

<form class="layui-form" action="{:url('other/duanxin')}" method="POST" onsubmit="return checkSub()">

	<div class="layui-form-item">
	    <label class="layui-form-label">剩余条数：</label>
	    <!-- <div class="layui-input-inline w333px">
			<input type="text" name="" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div> -->
	    <div class="layui-form-mid layui-word-aux">{$result.Balance}</div>
	</div>
	
	<div class="layui-form-item">
	    <label class="layui-form-label">短信签名：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="sign" value="{$msgconfig.sign}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label">账号：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="user" value="{$msgconfig.user}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>
	
	<div class="layui-form-item">
	    <label class="layui-form-label ">密码：</label>
	    <div class="layui-input-inline w333px">
			<input type="password" name="password" value="{$msgconfig.password}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>

	{:token()}

	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 169px;">
			<button class="layui-btn" lay-submit="" lay-filter="demo1">确认提交</button>
		</div>
	</div>
</form>



<script type="text/javascript">

function delConfirm(id,title){
	layer.confirm('您确定删除？', {
		title:title,
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('other/doKefu')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

$(function(){

	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});


})


</script>