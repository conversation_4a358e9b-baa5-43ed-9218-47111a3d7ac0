{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>其他</a>
		<a>其他设置</a>
		<a><cite>PC端轮播</cite></a>
	</span>
</div>
<style type="text/css">
#demo2{
	width: 1125px;
	margin: 10px 0 0;
}
.layui-form-item{
	text-align: center;width: 1000px;
}
#demo2:after{
	content: '';display: block;clear: both;
}
#demo2 .img{
	width: 323px;height: 183px;margin-right: 27px;
	float: left;text-align: center;border: 1px solid #ccc;
    border-radius: 6px;overflow: hidden;
    margin-bottom: 10px;position: relative;
}
#demo2 .img img{
	width: 100%;height: 99px;
}
#demo2 .img .shade{
	height: 99px;
    position: absolute;
    background: rgba(0,0,0,0.6);
    width: 100%;
    display: none;
}
#demo2 .img .shade div{
	margin-top: 40px;
}
#demo2 .img .shade a{
	color: #fff;
	font-weight: bold;
}
#demo2 .img input{
	padding: 3px;margin-bottom: 7px;
	border: 1px solid #D2D2D2;border-radius: 5px;
}
</style>

<form class="layui-form" onsubmit="return false;">
	<div class="layui-upload">
		<button class="layui-btn" onclick="addIframe('添加轮播图片')">添加轮播图片</button>

		<blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
			预览图：
			<div class="layui-upload-list" id="demo2" >
				{volist name="data" id="vo"}
				<!-- type=1&action=edit& -->
				<div class="img list_{$vo.id}">
					<div class="shade">
						<div>
						<a href="javascript:;" onclick="addIframe('修改轮播信息' , '{:url('other/doSlide' , ['id'=>$vo.id,'type'=>1,'action'=>'edit'] )}' )">修改</a>
						&nbsp;&nbsp;
						<a href="javascript:;" onclick="delConfirm( {$vo.id} )">删除</a>
						</div>
					</div>
					<img src="{$vo.image}" class="onimg" alt="">
					<div class="mt15">
						<span>排序：</span><input name="shunxu[]" value="{$vo.order}" type="number" placeholder="数字大，位置靠前"><br><span>地址：</span><input type="text" value="{$vo.link}" name="lianjie[]" placeholder="图片链接地址">
					</div>
				</div>
				{/volist}

			</div>
			{:token()}
			<div id="hidden">
				{volist name="data" id="vo"}
				<input type="hidden" name="tupian[]" value="{$vo.image}">
				{/volist}
			</div>

			<!-- <div class="layui-form-item" style="margin: 12px 0 5px;">
				<button class="layui-btn" lay-submit="" lay-filter="demo1">立即提交</button>
			</div> -->
		</blockquote>
	</div>
</form>


<script type="text/javascript">
function addIframe(title,url){
	if(url){
		var url = url;
	}else{
		var url = "{:url('other/doSlide' , 'type=1')}";
	}
	// console.log(url);return;
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false,
		area : ['600px' , '417px'],
		content: url,
		end: function(){
			// location.reload();
		}
    });
}
function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除轮播图片',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});

		$.ajax({
	        url: "{:url('other/doSlide')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( '删除成功' ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}



$(function(){


$('#demo2 img').on('mouseover', function(event) {
	$(this).prev('.shade').show();
	$(this).parent().siblings().find('.shade').hide();
	
	// console.log('jin1');
});
// $('#demo2').on('mouseover', '.onimg', function(event) {
// 	$(this).prev('.shade').show();
// 	console.log('jin2');
// });

$('#demo2 .shade').mouseleave(function(event) {
	$(this).hide();

	// console.log('li1');
});
// $('#demo2').on('mouseout','.shade',function(event) {
// 	$(this).hide();
// 	console.log('li2');
// });


})


</script>