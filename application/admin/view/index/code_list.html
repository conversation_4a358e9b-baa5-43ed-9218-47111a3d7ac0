{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>后台管理员</a>
		<a><cite>谷歌管理列表</cite></a>
	</span>
</div>

<style type="text/css">
table thead th{
	width: 14%;
}
</style>
<!--<a href="{:url('index/editCode', ['action'=>'add'])}" class="layui-btn" >添加code</a>

<br><br>-->
<div class="layui-form form-border" style="min-height:550px;">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="width:5%;">编号</th>
				<th style="width:;">账号</th>
				<th style="width:;">秘钥</th>
				<th style="width:;">权限管理组</th>
				<th style="width:;">是否禁用</th>
				<th style="width:;">上次登录</th>
				<!--<th style="width:;">限制ip</th>-->
<!--				<th style="width:;">备注信息</th>-->
				<th>操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
			<tr class="list_{$vo.id}">
				<td>{$vo.id}</td>
				<td>{$vo.user_name}</td>
				<td>{$vo.codeStr}</td>
				<td>{$vo.group_name}</td>
				<td>{:str_replace([0,1], ['启用','禁用'], $vo.is_ban)}</td>
				<td>{:date('Y-m-d H:i',$vo.last_log_time)}</td>
	
				<td>
					{eq name="admin_level" value="1"}
						<a href="{:url('index/editCode', ['admin_id'=>$vo.id,'action'=>'edit'])}" class="layui-bg-blue ft15">编辑</a>
						&nbsp;
						{if condition="$vo.id NEQ 1"}
						<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
						{/if}
					{else/}
						{if condition="$admin_id EQ $vo.id"}
						<a href="{:url('index/editCode', ['admin_id'=>$vo.id,'action'=>'edit'])}" class="layui-bg-blue ft15">编辑</a>
						{else /}
						暂无该权限
						{/if}
					{/eq}
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>



<script type="text/javascript">
function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除友情链接',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/delAdmin')}",
	        type: "post",
	        dataType: "json",
	        data: {admin_id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

function addIframe(title,ext){
	if( ext ){
		var url = '{:url("addfl")}?' + ext;
	}else{
		var url = '{:url("addfl")}';
	}
	// console.log(url);return
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false, //点击遮罩关闭层
		area : ['660px' , '450px'],
		content: url,
		// content: "{:url('addfl')}",
		end: function(){
			// location.reload();
		}
    });
}

$(function(){

})

</script>