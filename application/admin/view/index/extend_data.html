{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<div class="layui-main" style="padding: 21px 0;">
	<form class="layui-form" id="fsub">

		{if condition="$type EQ 'pzdr'"}
			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段1：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field1" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>

			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段2：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field2" value="10000 元" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>

			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段3：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field3" value="30 天" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>
			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段4：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field4" value="150%" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>
			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段5：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field5" value="5000 元" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>

		{elseif condition="$type EQ 'jrgppz'"/}

			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段1：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field1" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>
			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段2：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field2" value="166****7777" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>
			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段3：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field3" value="5102.02元" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>

		{else /}

			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段1：</label>
			    <div class="layui-input-inline ">
					<input type="text" name="field1" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>

			<div class="layui-form-item" >
			    <label class="layui-form-label ">字段2：</label>
			    <div class="layui-input-inline ">
			    	{if condition="$type EQ 'jrzc'"}
					<input type="text" name="field2" lay-verify="required" value="今日已注册成功" placeholder="请输入内容" autocomplete="off" class="layui-input">
					{elseif condition="$type EQ 'jrcz'"/}
					<input type="text" name="field2" lay-verify="required" value="充值5000元" placeholder="请输入内容" autocomplete="off" class="layui-input">
					{else /}
					<input type="text" name="field2" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
					{/if}
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			</div>

		{/if}

		<div class="layui-form-item">
			<div class="layui-input-block" style="margin-left: 110px;">
				<button type="button" class="layui-btn" lay-submit='' onclick="sub()">
					确认添加
				</button>
			</div>
		</div>
	</form>
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}

<script type="text/javascript">
var type = "{$type}";
function sub(){
	var field1 = $('input[name=field1]').val();
	var field2 = $('input[name=field2]').val();
	var field3 = $('input[name=field3]').val();
	var field4 = $('input[name=field4]').val();
	var field5 = $('input[name=field5]').val();
	if( type=='pzdr' ){
		if( !field1 || !field2 || !field3 || !field4 || !field5 ){
			return layer.msg('数据填写不完整！',{anim : 6});
		}
		var param = {field1:field1,field2:field2,field3:field3,field4:field4,field5:field5,type:type};
	}else if( type=='jrgppz' ){
		if( !field1 || !field2 || !field3 ){
			return layer.msg('数据填写不完整！',{anim : 6});
		}
		var param = {field1:field1,field2:field2,field3:field3,type:type};
	}else{
		if( !field1 || !field2 ){
			return layer.msg('数据填写不完整！',{anim : 6});
		}
		var param = {field1:field1,field2:field2,type:type};
	}

	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	// return;
	$.ajax({
        url: '{:url("index/extend_data")}',
        type: "post",
        dataType: "json",
        data: param,
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );

            	var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){ 
					parent.reloadData();
					// parent.layer.close(index);
				}, 1000);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

	

}


layui.use('form', function(){
	var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
	//但是，如果你的HTML是动态生成的，自动渲染就会失效
	//因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
	form.render();
}); 

</script>