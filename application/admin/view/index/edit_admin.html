{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>后台管理员</a>
		<a><cite>
			{eq name="action" value="edit"}
			修改管理员信息
			{else/}
			添加管理员
			{/eq}
			</cite>
		</a>
	</span>
</div>
<style type="text/css">
.layui-form-label{ width: 88px; }
</style>

<br>

<form class="layui-form" action="{:url('index/editAdmin')}" method="post" onsubmit="return sub()">
	
	<div class="layui-form-item" >
		<div class="layui-inline">
			<label class="layui-form-label">权限组：</label>
			<div class="layui-input-inline">
				<select name="group" lay-verify="required" lay-search="">
					<option value="" selected>选择权限组</option>
					{eq name="action" value="edit"}

						{volist name="acl" id="vo"}
						<option value="{$vo.id}" {if condition="$vo.id EQ $data.u_group_id"}selected{/if} >{$vo.name}</option>
						{/volist}

					{else/}

						{volist name="acl" id="vo"}
						<option value="{$vo.id}">{$vo.name}</option>
						{/volist}

					{/eq}
				</select>
			</div>
	    	<div class="layui-form-mid layui-word-aux "> <span style='color:red;'>*</span></div>
	    </div>
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label ">管理员账号：</label>
	    <div class="layui-input-inline w333px">

	    	{eq name="action" value="edit"}
			<input type="text" name="name" value="{$data.user_name|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			{else/}
			<input type="text" name="name" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			{/eq}

	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	
	<div class="layui-form-item">
	    <label class="layui-form-label ">管理员密码：</label>
	    <div class="layui-input-inline w333px">
			<input type="password" name="pass" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label ">确认密码：</label>
	    <div class="layui-input-inline w333px">
			<input type="password" name="pass2" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label">备注信息：</label>
	    <div class="layui-input-inline w333px">
	    	{eq name="action" value="edit"}
			<textarea name="content" placeholder="请输入内容" class="layui-textarea">{$data.content}</textarea>
			{else/}
			<textarea name="content" placeholder="请输入内容" class="layui-textarea"></textarea>
			{/eq}
	    </div>
	    <div class="layui-form-mid layui-word-aux"></div>
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label ">是否启用：</label>
	    <div class="layui-input-block">

	    	{eq name="action" value="edit"}
			<input type="radio" name="status" value="0" title="启用" {if condition="$data.is_ban NEQ 1"}checked{/if}>
	      	<input type="radio" name="status" value="1" title="禁用" {if condition="$data.is_ban EQ 1"}checked{/if}>
			{else/}
			<input type="radio" name="status" value="0" title="启用" checked>
	      	<input type="radio" name="status" value="1" title="禁用" >
			{/eq}
			
	    </div>
	</div>

	{:token()}
	<input type="hidden" name="action" value="{$action}">
	<input type="hidden" name="admin_id" value="{$admin_id}">

	<br>
	<div class="layui-form-item">
		<label class="layui-form-label "></label>
		<button class="layui-btn">
			{eq name="action" value="edit"}
			确认修改
			{else/}
			确认提交
			{/eq}
		</button>
	</div>

</form>

<script type="text/javascript">
var isSub = false;
function sub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2,anim : 6});
		return false;
	}
	var action = "{$action}";
	var group = $('select[name=group]').val();
	var name = $('input[name=name]').val();
	var pass = $('input[name=pass]').val();
	var pass2 = $('input[name=pass2]').val();

	if( !group ){
		layer.msg('请选择一个权限组',{icon:2,anim : 6});
        return false;
	}
	var namepat = /^[\da-zA-Z]{2,16}$/;
    if( !namepat.test(name) ){
        layer.msg('账号格式必须是2~16位字母或者数字',{icon:2,anim : 6});
        return false;
    }

    if( action=='add' ){

		if( !((pass.length>=5)&&(pass.length<=16)) ){
			layer.msg('输入密码不能小于5位数或大于16位数',{icon:2,anim : 6});
			return false;
		}
		if( pass!=pass2 ){
			layer.msg('两次密码输入不一致！',{icon:2,anim : 6});
			return false;
		}
    	
    }else{
    	if( pass ){
    		if( !((pass.length>=5)&&(pass.length<=16)) ){
				layer.msg('输入密码不能小于5位数或大于16位数',{icon:2,anim : 6});
				return false;
			}
			if( pass!=pass2 ){
				layer.msg('两次密码输入不一致！',{icon:2,anim : 6});
				return false;
			}
    	}
    }

	isSub = true;
	return true;
}
</script>
