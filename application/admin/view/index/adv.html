{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>全局设置</a>
		<a><cite>广告管理</cite></a>
	</span>
</div>

<a href="{:url('index/adv','action=add')}" class="layui-btn">添加广告</a>
<br><br>

<div class="layui-form form-border" >
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="">编号</th>
				<th style="width:33%;">分类名称</th>
				<th style="">是否显示</th>
				<th style="">添加时间</th>
				<th style="width:12%;">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
			<tr class="list_{$vo.id}">
				<td>{$vo.id}</td>
				<td>{$vo.title}</td>
				<td>
					{:str_replace([1,2], ['是','否'], $vo.hide)}
				</td>
				<td>{:date('Y-m-d H:s',$vo.add_time)}</td>
				<td>
					<a href="{:url('index/adv' , ['action'=>'edit','id'=>$vo.id])}" class="layui-bg-blue ft15">编辑</a>
					&nbsp;
					<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
				</td>
			</tr>
			{/volist}

		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>


<script type="text/javascript">
function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除该广告',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/delAdv')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}


</script>