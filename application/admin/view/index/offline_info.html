{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>充值设置</a>
		<a><cite>线下充值银行转账</cite></a>
	</span>
</div>
<style type="text/css">
.layui-width{
	width: 100px;padding: 9px 10px;
}
.layui-form-item .layui-input-inline{
	width: 260px;
}
</style>

<form action="{:url('index/offlineInfo')}" method="post" class="layui-form" onsubmit="return checkSub()">

	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">收款人：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="info[shoukuan]" value="{$offline_set.payee|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">开户行：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="info[kaihuhang]" value="{$offline_set.open_bank|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">银行名称：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="info[yinhang]" value="{$offline_set.bank_name|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	
	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">银行账号：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="info[zhanghao]" value="{$offline_set.number|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	<div class="layui-form-item">
        <label class="layui-form-label layui-width">是否启用：</label>
        <div class="layui-input-block">
            <input type="radio" name="info[status]" value="1" title="启用" {eq name="offline_set.status" value="1"}checked{/eq} >
            <input type="radio" name="info[status]" value="0" title="禁止" {neq name="offline_set.status" value="1"}checked{/neq} >
        </div>
    </div>
	<div class="layui-form-item">
		<label class="layui-form-label layui-width">充值说明：</label>
		<div class="layui-input-inline " style="width:865px;padding-top: 8px;">
			<script id="container" name="info[contents]" type="text/plain">{notempty name="offline_set.content"}{:htmlspecialchars_decode($offline_set.content)}{/notempty}</script>
		</div>
	</div>
			<input type="text" name="id" value="{$offline_set.id|default='0'}" style="display:none">
	<input type="hidden" name="way" value="offline">

	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 120px;">
			<button class="layui-btn" lay-submit="" lay-filter="demo1">确认提交</button>
		</div>
	</div>

	{:token()}
</form>
	
{load href="/ueditor/ueditor.config.js" /}
{load href="/ueditor/ueditor.all.js" /}
<script type="text/javascript">
var ue = UE.getEditor('container',{
    // autoHeightEnabled: true,
    autoFloatEnabled: false,
    initialFrameWidth: 666,
    initialFrameHeight: 300
});

$(function(){


})
</script>