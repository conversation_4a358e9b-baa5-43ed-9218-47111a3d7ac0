{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>后台管理员</a>
		<a><cite>
			{eq name="action" value="edit"}
			修改权限组
			{else/}
			添加权限组
			{/eq}
			</cite>
		</a>
	</span>
</div>
<style type="text/css">
.layui-form-label{ width: 98px; }
.layui-form-item{ margin: 0; padding-bottom: 15px; border-bottom: 1px dashed #e6e6e6; }
.acl-block{ line-height: 40px;border-bottom: 1px dashed #e6e6e6; }
.acl-mod{ text-align: right;width: 98px;padding: 12px 15px 2px;font-weight: bold;font-size: 15px; }
.acl-label{ float: left;padding: 0 15px;width: 98px;text-align: right; }
.layui-form-checkbox[lay-skin=primary] i{ width: 14px;height: 14px;line-height: 15px; }
.acl-input-block{ margin-left: 128px; }
.acl-mod .layui-form-checkbox[lay-skin=primary]{ padding-left: 21px; }
.acl-mod .layui-form-checkbox[lay-skin=primary] span{ font-size: 15px;padding-right: 0;color: #111; }
</style>
<br>

<form class="layui-form" action="{:url('index/editAcl')}" method="post" onsubmit="return checkSub()">
	
	<div class="layui-form-item" style="border: none;">
	    <label class="layui-form-label">权限组名称：</label>
	    <div class="layui-input-inline">
			<input type="text" name="name" value="{$acl.name|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	<div class="layui-form-item layui-form-text">
        <label class="layui-form-label">备注信息：</label>
        <div class="layui-input-inline w333px">
            <textarea name="remark" placeholder="请输入内容" class="layui-textarea" style="min-height: 50px;">{$acl.remark|default=''}</textarea>
        </div>
        <!-- <div class="layui-form-mid layui-word-aux"></div> -->
    </div>

	{foreach $data as $k1 => $vo}
		<div class="acl-list" mod="{$k1}">

			<div class="acl-mod">
				<input type="checkbox" title="{$vo.mod}："  data-mod="{$k1}" lay-skin="primary" lay-filter="aclmod" >
			</div>
			{foreach $vo.acl as $k2 => $vo2}
			<div class="acl-block">
			    <label class="acl-label">{$vo2.title}：</label>
			    <div class="acl-input-block">
			    	{foreach $vo2.list as $k3 => $vo3}
			    		{php}
			    			$temp_checked = '';
			    			if( !empty($acl['admin_acl'][$k1]) && $action=='edit' ){
				    			if( in_array($k3, $acl['admin_acl'][$k1]) ){
				    				$temp_checked = 'checked';
				    			}
			    			}
			      		{/php}
			      		<input type="checkbox" name="acl_mod[{$k1}][]" value="{$k3}" title="{$vo3}" {$temp_checked} class="{$k1}_son" lay-skin="primary"  >
			      	{/foreach}
			    </div>
			</div>
			{/foreach}
			
		</div>
	{/foreach}
	
	{:token()}

	<input type="hidden" name="gid" value="{$gid}">
	<input type="hidden" name="action" value="{$action}">

	<br>
	<div class="layui-form-item" style="border:none;">
		<label class="layui-form-label"></label>

		<button class="layui-btn" lay-submit="">
			确认提交
		</button>
	</div>

</form>


<script type="text/javascript">
$(function(){
	layui.use('form', function(){
		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功

		form.on('checkbox(aclmod)', function(data){

			var mod = $(this).data('mod');
			if( data.elem.checked==true ){
				$('.'+mod+'_son').attr('checked',true);
				form.render('checkbox');
			}else{
				$('.'+mod+'_son').attr('checked',false);
				form.render('checkbox');
			}

		});
	});
})

var isSub = false;
function sub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2});
		return false;
	}

	isSub = true;
	return true;
}
</script>
