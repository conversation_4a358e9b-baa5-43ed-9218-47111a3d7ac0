{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<div class="layui-main" style="padding: 21px 0;">
	<!-- <form class="layui-form" action="{:url('test')}" method="POST" id="fsub"> -->
	<form class="layui-form" id="fsub">

		<div class="layui-form-item" >
		    <label class="layui-form-label ">参数名称：</label>
		    <div class="layui-input-inline ">
				<input type="text" name="csname" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
		    </div>
		    <div class="layui-form-mid layui-word-aux">便于自己知道此参数的作用的名称</div>
		</div>

		<div class="layui-form-item" >
		    <label class="layui-form-label ">参数代码：</label>
		    <div class="layui-input-inline ">
				<input type="text" name="csdaima" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
		    </div>
		    <div class="layui-form-mid layui-word-aux">模板中引用的代码，可以加前缀，如ttxf_</div>
		</div>

		<div class="layui-form-item" >
			<div class="layui-inline">
				<label class="layui-form-label">参数类型：</label>
				<div class="layui-input-inline">
					<select name="csleix" lay-verify="required" lay-search="">
						<!-- <option value="">直接选择或搜索选择</option> -->
						<option value="input">单行文本</option>
						<option value="textarea">多行文本</option>
					</select>
					<div class="layui-form-select">
						<div class="layui-select-title">
							<input type="text" placeholder="直接选择或搜索选择" value="" class="layui-input">
							<i class="layui-edge"></i>
						</div>
						<!-- <dl class="layui-anim layui-anim-upbit" style=""><dd lay-value="" class="layui-select-tips layui-this">直接选择或搜索选择</dd><dd lay-value="1" class="">layer</dd><dd lay-value="2" class="">form</dd><dd lay-value="3" class="">layim</dd><dd lay-value="4" class="">element</dd><dd lay-value="5" class="">laytpl</dd><dd lay-value="6" class="">upload</dd><dd lay-value="7" class="">laydate</dd><dd lay-value="8" class="">laypage</dd><dd lay-value="9" class="">flow</dd><dd lay-value="10" class="">util</dd><dd lay-value="11" class="">code</dd><dd lay-value="12" class="">tree</dd><dd lay-value="13" class="">layedit</dd><dd lay-value="14" class="">nav</dd><dd lay-value="15" class="">tab</dd><dd lay-value="16" class="">table</dd><dd lay-value="17" class="">select</dd><dd lay-value="18" class="">checkbox</dd><dd lay-value="19" class="">switch</dd><dd lay-value="20" class="">radio</dd></dl> -->
					</div>
				</div>
		    	<div class="layui-form-mid layui-word-aux extend-inline-text">参数的类型：单行文本所保存内容较小，多行文本可保存较多内容</div>
		    </div>
		    
		</div>

		<div class="layui-form-item" >
		    <label class="layui-form-label ">参数说明：</label>
		    <div class="layui-input-inline ">
				<input type="text" name="csshuom" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
		    </div>
		    <div class="layui-form-mid layui-word-aux">用来更详细的说明此参数的作用</div>
		</div>
		<div class="layui-form-item" >
		    <label class="layui-form-label ">参数排序：</label>
		    <div class="layui-input-inline ">
				<input type="number" name="cspaix" value="0" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
		    </div>
		    <div class="layui-form-mid layui-word-aux">参数在管理列表中的排序，越大越靠前</div>
		</div>
	</form>

	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 110px;">
			<button type="button" class="layui-btn" onclick="sub()">
				确认添加
			</button>
		</div>
	</div>
	
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}

<script type="text/javascript">
function sub(){
	var csname = $('input[name=csname]').val();
	var csdaima = $('input[name=csdaima]').val();
	var csleix = $('select[name=csleix]').val();
	var csshuom = $('input[name=csshuom]').val();
	var cspaix = $('input[name=cspaix]').val();

	if( !csname || !csdaima || !csleix ){
		// console.log(csname,csdaima,csleix,csshuom,cspaix);
		return layer.msg('参数填写不完整！',{anim : 6});
	}
	var daimapat = /^[\da-zA-Z_]{1,16}$/;
	if( !daimapat.test(csdaima) ){
        return layer.msg('参数代码格式填写不正确！',{anim : 6});
    }

	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	// return;
	$.ajax({
        url: '{:url("addpz")}',
        type: "post",
        dataType: "json",
        data: {csname:csname,csdaima:csdaima,csleix:csleix,csshuom:csshuom,cspaix:cspaix},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );

            	var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){ 
					parent.reloadData();
					// parent.layer.close(index);
				}, 1000);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

	

}


layui.use('form', function(){
	var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
	//但是，如果你的HTML是动态生成的，自动渲染就会失效
	//因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
	form.render();
}); 

</script>