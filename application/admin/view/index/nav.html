{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>全局设置</a>
		<a><cite>前台导航</cite></a>
	</span>
</div>
<style type="text/css">
.zsyc{ position: relative;text-indent: 20px; }
.zsyc i {
    display: inline-block;cursor: pointer;
    width: 15px;height: 15px;
    position: absolute;top: 11px;left: 15px;
}
.zsyc i.zhanshi{
	background: url(/static/img/A/zhanshi.png) no-repeat;background-size: 100%;
}
.zsyc i.yincang{
	background: url(/static/img/A/yincang.png) no-repeat;background-size: 100%;
}
.leve_2{ background-color: #f2f2f2; }
.leve_2 .zsyc{ padding-left: 35px; }
.leve_2 .zsyc i{ left: 35px; }
.leve_3{ background-color: #DEDEDE; }
.leve_3 .zsyc{ padding-left: 55px; }
.leve_3 .zsyc i{ left: 55px; }
</style>


<button class="layui-btn" onclick="addIframe('添加导航','{:url('index/addNav',['action'=>'add'])}')">
	添加导航
</button>
<br><br>

<div class="layui-form" style="height:470px;">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="">编号</th>
				<th style="width:18%;text-indent:20px;">分类名称</th>
				<th style="">链接地址</th>
				<th style="">是否显示</th>
				<th style="">显示顺序</th>
				<th style="">添加时间</th>
				<th style="width:12%;">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
			<tr id="list_{$vo.id}" data-id="{$vo.id}" parentid="0" class="leve_1">
				<td>{$vo.id}</td>
				<td class="zsyc">
					{eq name="vo.has" value="true"}
					<i class="zhanshi"></i>
					{/eq}
					{$vo.name}
				</td>
				<td>{$vo.url}</td>
				<td>{:str_replace([1,2], ['显示','隐藏'], $vo.is_hidden)}</td>
				<td>{$vo.sort_order}</td>
				<td>{:date('Y-m-d H:i', $vo.add_time)}</td>
				<td>
					<a href="javascript:;" data-iframe="{:url('index/addNav',['id'=>$vo.id,'action'=>'edit'])}" class="editAdv layui-bg-blue ft15">编辑</a>
					&nbsp;
					<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>



<script type="text/javascript">
function addIframe(title,url){
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false,
		area : ['580px' , '407px'],
		content: url,
		end: function(){
		}
    });
}
$(function(){
	$('.layui-form').on('click', '.editAdv', function(event) {
		var url = $(this).data('iframe')
		layer.open({
			type: 2,
			title: '修改导航',
			maxmin: true,
			shadeClose: false,
			area : ['580px' , '407px'],
			content: url,
			end: function(){
			}
	    });
	});

	$('.layui-form').on('click', '.zhanshi', function(event) {
		var type_id = $(this).parents('tr').data('id');
		var this_tr = $(this).parents('tr');
		var son_tree =  $('.layui-table tr[parentid='+type_id+']');
		if(son_tree.html()==null){
			$.ajax({
		        url: "{:url('index/getNavList')}",
		        type: "post",
		        dataType: "json",
		        data: {type_id:type_id,action:''},
		        success: function(d,e,x){
		            // console.log(d,e,x);return;
		            if(d.status==1){
		            	$(this_tr).after(d.data);
		            }else{
		            	return layer.msg( d.message,{icon : 2} );
		            }
		        }
		    });
		}else{
			if( son_tree.css('display')=='none' ){
				son_tree.show();
			}else{
				son_tree.hide();
			}
		}

	});
})
function delConfirm(id,title){
	layer.confirm('您确定删除？', {
		title:title,
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/addNav')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('#list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

$(function(){
	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});

})


</script>