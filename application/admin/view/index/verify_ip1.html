{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}
<style type="text/css">
.layui-form-label{
    padding: 9px 0;
    text-align: center;
    width: 128px;
}
.tip{
    width: 477px;
    height: auto;
    margin: 0 auto;
    border: 1px solid #e6e6e6;
    /* margin-left: 100px; */
    background: #F9F9F9;
    padding: 8px 8px;
    font-size: 12px;
    line-height: 20px;
    color: #666;
}
.adminphone{
    text-align: left;
    font-size: 16px;
    font-weight: 700;
    color: #666;
    display: inline-block;
    padding: 9px 0;
}
#sendSMS{
    position: absolute;
    top: 0;
    right: 0;
    border: none;
    line-height: 37px;
    width: 123px;
    color: #333;
    cursor: pointer;
}
</style>
<div class="layui-main" style="padding: 21px 0;">
    <form class="layui-form" onsubmit="return false;" >

        <div class="tip">
            <p>设置白名单需要验证超级管理员身份</p>
        </div>
        <br><br>
        <div class="layui-form-item">
            <label class="layui-form-label label2-title">管理员手机号：</label>
            <div class="adminphone">
            &nbsp;&nbsp;{$admin_phone|default='请联系工作人员设置管理员手机号'}
            </div>
            <!-- <div class="layui-form-mid layui-word-aux">奥术大师大所多</div> -->
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label2-title" >输入完整手机号：</label>
            <div class="layui-input-inline " style="width: 260px;">
                <input type="text" name="phone" maxlength="11" lay-verify="" placeholder="请输入手机号" autocomplete="off" class="layui-input">

                <button id='sendSMS' >发送验证码</button>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux">奥术大师大所多</div> -->
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label label2-title" >验证码：</label>
            <div class="layui-input-inline " style="width: 138px;">
                <input type="text" name="vcode" maxlength="6" lay-verify="" placeholder="请输入短信验证码" autocomplete="off" class="layui-input">
            </div>
            <!-- <div class="layui-form-mid layui-word-aux">奥术大师大所多</div> -->
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 128px;">
                <button type="button" class="layui-btn" lay-submit="" onclick="next()">
                    下一步
                </button>
            </div>
        </div>
    </form>
</div>

{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">

var reg = /^1[3456789]\d{9}$/;


function next(){
    var phone = $('input[name=phone]').val();
    var vcode = $('input[name=vcode]').val();
    if( !(reg.test(phone)) ){  
        return layer.msg( '手机号码有误，请重新输入',{icon:2,anim:6} );
    }
    if( !vcode ){
        return layer.msg( '验证码不能为空，请重新输入',{icon:2,anim:6} );
    }
    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url:'{:url("index/verifyIp1")}',
        type: 'post',
        data: {action:'verify',phone:phone,vcode:vcode},
        dataType: "json",
        success:function(d){
            // console.log(d.message);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );
                var t1 = setTimeout(function(){
                        // layer.close(la_load);
                        window.location.href = '{:url("index/verifyIp2")}';
                    },1500);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2,anim:6} );
            }
        }
    });
}



var txtObj = $('#sendSMS');//文字DOM Obj
var InterValObj; //timer变量，控制时间 
var count = 90; //间隔函数，1秒执行 
var curCount;//当前剩余秒数
function myInterval() {
    curCount = count;
    //设置button效果，开始计时
    txtObj.text(curCount + "秒后重新发送"); 
    InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次 
} 
function SetRemainTime() {
    if (curCount == 0) {         
        window.clearInterval(InterValObj);//停止计时器 
        //启用按钮
        txtObj.removeAttr("disabled").removeClass('btn-disabled');
        txtObj.text("重新发送"); 
    }else{
        curCount--; 
        txtObj.text(curCount + "秒后重新发送"); 
    }
} 
$('#sendSMS').on('click', function(event) {
    var phone = $('input[name=phone]').val();
    if( !(reg.test(phone)) ){  
        return layer.msg( '手机号码有误，请重新输入',{icon:2,anim:6} );
    }
    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url:'{:url("index/verifyIp1")}',
        type: 'post',
        data: {action:'sendcode',phone:phone},
        dataType: "json",
        beforeSend:function(){
            txtObj.text('验证码发送中').attr("disabled",true).addClass('btn-disabled');
        },
        success:function(d){
            // console.log(d.message);
            layer.close(la_load);
            if(d.status==1){
                myInterval();
                layer.msg( d.message ,{icon : 1} );
            }else{
                //启用按钮
                txtObj.text('重新发送').removeAttr("disabled").removeClass('btn-disabled');
                return layer.msg( d.message,{icon : 2,anim:6} );
            }
        }

    });
})


layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();
});
</script>