{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>合同设置</a>
		<a><cite>股票合同</cite></a>
	</span>
</div>
<style type="text/css">
.layui-width{
	width: 150px;padding: 9px 10px;
}
.layui-form-item .layui-input-inline{
	width: 380px;
}
</style>

<blockquote class="layui-elem-quote">
	<p>1、该功能用于在借款合同内容中显示平台居间方的相关必要资料</p>
	<p>2、请上传背景透明的图片，允许上传格式：jpg,jpeg,png,gif，建议尺寸：100px*100px
	<p>3、合同内容从<b>第三条</b>开始编辑
</blockquote>

<form action="{:url('index/hetong')}" method="post" class="layui-form" onsubmit="return checkSub()">
	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">合同章上传：</label>
		<div class="layui-upload">
			<button type="button" class="layui-btn layui-btn-normal fl" id="test1">上传图片</button>
			<div class="layui-upload-list">
				<!-- <img class="layui-upload-img" id="demo1"> -->
				<p id="demoText"></p>
			</div>
		</div>
	</div>

	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">合同章缩略图：</label>
	    <div class="layui-input-inline ">
	    	<input type="hidden" name="upimg" value="{$data.hetong_img}">
	    	{if condition="$data.hetong_img"}
				<img class="layui-upload-img" src="{$data.hetong_img}" id="demo1" height="93">
	    	{else /}
				<p style="margin-top: 10px;" id="imgtxt">【暂未上传合同章】</p>
				<img class="layui-upload-img" id="demo1" height="93" style="display:none;">
			{/if}
	    </div>
	</div>

	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">公司名称：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="mingcheng" value="{$data.name}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">公司地址：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="dizhi" value="{$data.dizhi}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>
	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">联系电话：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="dianhua" value="{$data.tel}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>

	
	<div class="layui-form-item">
		<label class="layui-form-label layui-width">合同内容：</label>

		<div class="layui-input-inline " style="width:900px;padding-top: 8px;">
			<script id="container" name="contents" type="text/plain" >{:htmlspecialchars_decode($data.content)}</script>
		</div>
	</div>

	<input type="hidden" name="category" value="1">
	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 170px;">
			<button class="layui-btn" lay-submit="" lay-filter="demo1">确认提交</button>
		</div>
	</div>

	{:token()}
</form>
	
{load href="/ueditor/ueditor.config.js" /}
{load href="/ueditor/ueditor.all.js" /}
<script type="text/javascript">
var ue = UE.getEditor('container',{
    autoHeightEnabled: true,
    autoFloatEnabled: false,
});

$(function(){
	layui.use('upload', function(){
	  	var $ = layui.jquery
	  	,upload = layui.upload;
	  	//普通图片上传
	  	var uploadInst = upload.render({
	  		accept:'images',
	  		field: 'img',
		    elem: '#test1'
		    ,url: '{:url("index/upload")}'
		    ,before: function(obj){
		      	//预读本地文件示例，不支持ie8
		      	obj.preview(function(index, file, result){
		      		$('#imgtxt').hide();
					$('#demo1').show().attr('src', result); //图片链接（base64）
		      	});
		    }
		    ,done: function(res){
		    	// console.log(res,1);
		      	if(res.status == 1){
		      		//上传成功
		      		$('input[name=upimg]').val(res.savename);
		      	}else{
		      		//如果上传失败
		        	return layer.msg( '文件上传失败！ '+res.message , {anim:6});
		      	}
		    }
		    ,error: function(){
		      	//演示失败状态，并实现重传
		      	var demoText = $('#demoText');
		      	demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
		      	demoText.find('.demo-reload').on('click', function(){
		        	uploadInst.upload();
		      	});
		    }
		});
	});


})




</script>