{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>后台管理员</a>
		<a><cite>管理员列表</cite></a>
	</span>
</div>

<style type="text/css">
table thead th{
	width: 14%;
}
</style>

<a href="{:url('index/editAcl', ['action'=>'add'])}" class="layui-btn" >添加权限组</a>

<br><br>

<div class="layui-form form-border" style="min-height:550px;">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="width:5%;">编号</th>
				<th style="width:8%;">组名称</th>
				<th style="width:8%;">添加时间</th>
				<th style="">备注</th>
				<th style="width:5%;">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
			<tr class="list_{$vo.id}">
				<td>{$vo.id}</td>
				<td>{$vo.name}</td>
				<td>{:date('Y-m-d H:i', $vo.add_time)}</td>
				<td>{$vo.remark}</td>
				<td>
					{if condition="$admin_group EQ 1"}
						<a href="{:url('index/editAcl', ['action'=>'edit','gid'=>$vo.id])}" class="layui-bg-blue ft15">编辑</a>
						&nbsp;
						{if condition="$vo.id NEQ 1"}
						<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
						{/if}
					{else /}
						暂无该权限
					{/if}
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>




<script type="text/javascript">
function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除友情链接',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/delAcl')}",
	        type: "post",
	        dataType: "json",
	        data: {gid:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else if( d.code==0 ){
	            	layer.msg( d.msg,{icon : 2} );
	            }else{
	            	layer.msg( d.message,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}


</script>