{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-upload-img {
    height: 38px;
}
.layui-upload-list{
	width: 200px;
    float: left;
    margin: 0 0 0 20px;
}
</style>

<div class="layui-main" style="padding: 21px 0;">
	<form class="layui-form" >

		<div class="layui-form-item" >
			<div class="layui-inline">
				<label class="layui-form-label">类型：</label>
				<div class="layui-input-inline">
					<select name="leixing" lay-verify="required" lay-search="">
						<option value="1" {eq name="data.f_type" value="1"}selected{/eq} >友情链接</option>
						<option value="2" {eq name="data.f_type" value="2"}selected{/eq} >合作伙伴</option>
					</select>
					<div class="layui-form-select">
						<div class="layui-select-title">
							<input type="text" placeholder="直接选择或搜索选择" value="" class="layui-input">
							<i class="layui-edge"></i>
						</div>
						<!-- <dl class="layui-anim layui-anim-upbit" style=""><dd lay-value="" class="layui-select-tips layui-this">直接选择或搜索选择</dd><dd lay-value="1" class="">layer</dd><dd lay-value="2" class="">form</dd><dd lay-value="3" class="">layim</dd><dd lay-value="4" class="">element</dd><dd lay-value="5" class="">laytpl</dd><dd lay-value="6" class="">upload</dd><dd lay-value="7" class="">laydate</dd><dd lay-value="8" class="">laypage</dd><dd lay-value="9" class="">flow</dd><dd lay-value="10" class="">util</dd><dd lay-value="11" class="">code</dd><dd lay-value="12" class="">tree</dd><dd lay-value="13" class="">layedit</dd><dd lay-value="14" class="">nav</dd><dd lay-value="15" class="">tab</dd><dd lay-value="16" class="">table</dd><dd lay-value="17" class="">select</dd><dd lay-value="18" class="">checkbox</dd><dd lay-value="19" class="">switch</dd><dd lay-value="20" class="">radio</dd></dl> -->
					</div>
				</div>
		    	<div class="layui-form-mid layui-word-aux "><span style='color:red;'>友情链接不显示图片，合作伙伴显示图片</span></div>
		    </div>
		</div>

		<div class="layui-form-item" >
		    <label class="layui-form-label ">名称：</label>
		    <div class="layui-input-inline ">
				<input type="text" name="mingcheng" value="{$data.link_txt|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
		    </div>
		    <div class="layui-form-mid layui-word-aux">前台显示的链接文字</div>
		</div>

		<div class="layui-form-item" >
		    <label class="layui-form-label ">链接地址：</label>
		    <div class="layui-input-inline ">
				<input type="text" name="dizhi" value="{$data.link_href|default='http://'}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
		    </div>
		    <div class="layui-form-mid layui-word-aux">链接的网址</div>
		</div>

		<div class="layui-form-item" >
		    <label class="layui-form-label ">显示顺序：</label>
		    <div class="layui-input-inline ">
		    	{eq name="action" value="edit"}
				<input type="number" name="shunxu" value="{$data.link_order}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
				{else/}
				<input type="number" name="shunxu" value="0" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
				{/eq}
		    </div>
		    <div class="layui-form-mid layui-word-aux">数字越大顺序越靠前</div>
		</div>

		<div class="layui-form-item" >
		    <label class="layui-form-label " style="padding: 0 15px;">链接图片Logo：</label>
		  
		    <input type="hidden" name="imglogo" value="{$data.link_img|default=''}">
			<div class="layui-upload">
				<button type="button" class="layui-btn layui-btn-normal fl" id="test1">上传图片</button>
				<div class="layui-upload-list">
					{if condition="$data.link_img"}
					<img class="layui-upload-img" src="{$data.link_img}" id="demo1">
					{else/}
					<img class="layui-upload-img" id="demo1">
					{/if}
					<p id="demoText"></p>
				</div>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label">是否展示：</label>
			<div class="layui-input-block">
				<input type="radio" name="is_show" value="1" title="是" {neq name="data.is_show" value="2"}checked{/neq} >
				<input type="radio" name="is_show" value="2" title="否" {eq name="data.is_show" value="2"}checked{/eq}>
				
			</div>
		</div>
		
	</form>

	
	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 112px;">
			<button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="sub()">
				{eq name="action" value="edit"}
					确认修改
				{else/}
					确认添加
				{/eq}
			</button>
		</div>
	</div>


</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}

<script type="text/javascript">
var action = '{$action}';
var id = '{$data.id}';
function sub(){
	var leixing = $('select[name=leixing]').val();
	var mingcheng = $('input[name=mingcheng]').val();
	var dizhi = $('input[name=dizhi]').val();
	var shunxu = $('input[name=shunxu]').val();
	var imglogo = $('input[name=imglogo]').val();
	var is_show = $("input[name=is_show]:checked").val();

	if( !leixing || !mingcheng || !dizhi ){
		return layer.msg('参数填写不完整！',{anim : 6});
	}

	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});

	if(action=='edit'){
		var url = '{:url("index/dofriend")}'
		var param = {id:id,action:action,leixing:leixing,mingcheng:mingcheng,dizhi:dizhi,shunxu:shunxu,imglogo:imglogo,is_show:is_show};
	}else{
		var url = '{:url("index/addfl")}'
		var param = {leixing:leixing,mingcheng:mingcheng,dizhi:dizhi,shunxu:shunxu,imglogo:imglogo,is_show:is_show};
	}
	// console.log(param);
	// return;
	$.ajax({
        url: url,
        type: "post",
        dataType: "json",
        data: param,
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );

            	var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){ 
					parent.reloadData();
					// parent.layer.close(index);
				}, 1000);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}


layui.use('upload', function(){
  	var $ = layui.jquery
  	,upload = layui.upload;
  
  	//普通图片上传
  	var uploadInst = upload.render({
  		accept:'images',
  		field: 'img',
	    elem: '#test1'
	    ,url: '{:url("index/upload")}'
	    ,before: function(obj){
	      	//预读本地文件示例，不支持ie8
	      	obj.preview(function(index, file, result){
				$('#demo1').attr('src', result); //图片链接（base64）
	      	});
	    }
	    ,done: function(res){
	    	// console.log(res,1);
	      	if(res.status == 1){
	      		//上传成功
	      		$('input[name=imglogo]').val(res.savename);
	      	}else{
	      		//如果上传失败
	        	return layer.msg( '文件上传失败！ '+res.message , {anim:6});
	      	}
	      	
	    }
	    ,error: function(){
	      	//演示失败状态，并实现重传
	      	var demoText = $('#demoText');
	      	demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
	      	demoText.find('.demo-reload').on('click', function(){
	        	uploadInst.upload();
	      	});
	    }
	});
});



layui.use('form', function(){
	var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
	//但是，如果你的HTML是动态生成的，自动渲染就会失效
	//因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
	form.render();
});

</script>