{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>全局设置</a>
		<a><cite>{eq name="action" value="edit"}修改广告{else/}添加广告{/eq}</cite></a>
	</span>
</div>

<br><br>

<form class="layui-form" action="{:url('index/doadv')}" method="post" onsubmit="return sub()">
	<div class="layui-form-item">
	    <label class="layui-form-label label2-title">广告名称：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="advname" value="{$data.title|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux">奥术大师大所多</div> -->
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label label2-title">是否显示/隐藏：</label>
	    <div class="layui-input-block">
	    	{eq name="action" value="edit"}
				<input type="radio" name="adv_show" value="1" title="显示" {eq name="data.hide" value="1"}checked{/eq} >
		      	<input type="radio" name="adv_show" value="2" title="隐藏" {eq name="data.hide" value="2"}checked{/eq} >
			{else/}
				<input type="radio" name="adv_show" value="1" title="显示" checked="">
		      	<input type="radio" name="adv_show" value="2" title="隐藏">
			{/eq}
	    </div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label label2-title">广告内容：</label>

		<div class="layui-input-inline " style="width:900px;padding-top: 8px;">
			<script id="container" name="contents" type="text/plain" >{eq name="action" value="edit"}{:htmlspecialchars_decode($data.content)}{/eq}</script>
		</div>
	</div>

	{:token()}
	
	{eq name="action" value="edit"}
		<input type="hidden" name="id" value="{$id}">
		<input type="hidden" name="action" value="{$action}">
	{else/}
		<input type="hidden" name="action" value="{$action}">
	{/eq}

	<br>
	<div class="layui-form-item">
		<label class="layui-form-label label2-title"></label>

		<button class="layui-btn">
			{eq name="action" value="edit"}确认修改{else/}确认提交{/eq}
		</button>
	</div>

</form>

{load href="/ueditor/ueditor.config.js" /}
{load href="/ueditor/ueditor.all.js" /}
<script type="text/javascript">
var ue = UE.getEditor('container',{
    autoHeightEnabled: true,
    autoFloatEnabled: false,
});

$(function(){
	// insert();
})

var isSub = false;
function sub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2});
		return false;
	}
	var advname = $('input[name=advname]').val();
	if(!advname){
		layer.msg('广告名称不能为空！',{icon:2});
		return false;
	}
	isSub = true;
	return true;
}
</script>
