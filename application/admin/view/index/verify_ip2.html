{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}
<style type="text/css">
.layui-form-label{
    padding: 9px 0;
    text-align: center;
    width: 100px;
}
.tip{
    width: 332px;
    height: 85px;
    border: 1px solid #e6e6e6;
    margin-left: 100px;
    background: #F9F9F9;
    padding: 8px 8px;
    font-size: 12px;
    line-height: 20px;
    color: #666;
}
.aaa2{
    margin-left: 100px;
    text-align: right;
    width: 350px;
    margin-top: -7px;
    margin-bottom: 7px;
    font-size: 12px;
    color: #333;
}
.tip a{
    margin-top: 8px;
    display: inline-block;
    color: #039;
}
</style>
<div class="layui-main" style="padding: 21px 0;">
    <!-- <form class="layui-form" action="{:url('test')}" method="POST" id="fsub"> -->
    <form class="layui-form" id="fsub">

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label ">后台白名单：</label>
            <div class="layui-input-inline ">
                <textarea name="content" placeholder="请输入内容" lay-verify="required" class="layui-textarea" style="width: 350px;min-height: 130px;font-family: MONOSPACE;">{$ip_txt|default=''}</textarea>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux"></div> -->
        </div>
        
        <div class="aaa2">
            还可以添加{$diff}个IP白名单
        </div>

        <div class="tip">
            <p>指定IP地址：*********** 允许***********的IP地址访问后台</p>
            <p>多个IP设置，用英文逗号隔开，如***********,***********</p>
            <p><b>需按照格式正确书写，否则无法生效</b></p>

            <a href="https://www.baidu.com/s?wd=IP" target="_blank">如何查看当前IP</a>
        </div>

        <div style="height:20px;"></div>

        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 100px;">
                <button type="button" class="layui-btn" lay-submit="" onclick="sub()">
                    确认提交
                </button>
            </div>
        </div>
    </form>
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">
function sub(){
    var content = $('textarea[name=content]').val();
    if( !content ){
        return layer.msg( 'IP列表不能为空，请重新输入',{icon:2,anim:6} );
    }

    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url:'{:url("index/verifyIp2")}',
        type: 'post',
        data: {action:'verify',content:content},
        dataType: "json",
        success:function(d){
            // console.log(d.message);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );
                var t1 = setTimeout(function(){
                        parent.location.href = '{:url("/")}';
                    },1500);
            }else if(d.status==-1){
                // 验证超时，关闭当前页面
                layer.msg( d.message ,{icon : 2,anim:6} );
                var test = parent.layer.getFrameIndex(window.name);
                var t1 = setTimeout(function(){
                        parent.layer.close(test);
                    },1500);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2,anim:6} );
            }
        }
    });
}


layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();
});
</script>