{__NOLAYOUT__}

{load href="/static/layui-v2.4.5/layui/css/layui.css" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-item .layui-input-inline{ width: 280px; }
.layui-form-select dl {
    max-height:200px;
}
</style>

<div class="layui-main" style="padding: 18px 0 0;">
<form class="layui-form" onsubmit="return false;" >

	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">导航名称：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="mingcheng" value="{$data.name|default=''}" lay-verify="required" placeholder="请输入内容" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"> <span style='color:red;'>*</span> </div>
	</div>
	
	<div class="layui-form-item" >
		<div class="layui-inline">
			<label class="layui-form-label">上级菜单：</label>
			<div class="layui-input-inline">
				<select name="shangji" lay-verify="required" lay-search="">
				{eq name="action" value="edit"}
					{eq name="data.parent_id" value="0"}
						<option value="0" selected>顶级菜单</option>
					{else/}
						<option value="0">顶级分类</option>
						{volist name="tree" id="vo"}
						<option value="{$vo.id}" {if condition="$vo.id eq $data.parent_id"}selected{/if} >{$vo.str}</option>
						{/volist}
					{/eq}
				{else/}
					<option value="0" selected>顶级菜单</option>
					{volist name="tree" id="vo"}
					<option value="{$vo.id}">{$vo.str}</option>
					{/volist}
				{/eq}
				</select>
				<!-- <div class="layui-form-select">
					<div class="layui-select-title">
						<input type="text" placeholder="直接选择或搜索选择" value="" class="layui-input">
						<i class="layui-edge"></i>
					</div>
				</div> -->
			</div>
	    	<div class="layui-form-mid layui-word-aux "> <span style='color:red;'>*</span></div>
	    </div>
	</div>

	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">排序：</label>
	    <div class="layui-input-inline ">
			<input type="number" name="paixu" value="{$data.sort_order|default=0}" lay-verify="number" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>

	<div class="layui-form-item" >
	    <label class="layui-form-label layui-width">链接地址：</label>
	    <div class="layui-input-inline ">
			<input type="text" name="lianjie" value="{$data.url|default=''}" lay-verify="required" placeholder="请输入内容" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux"> <span style='color:red;'>*</span> </div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">是否显示：</label>
		<div class="layui-input-block">
			{eq name="action" value="edit"}
			<input type="radio" name="xianshi" value="1" title="显示" {eq name="data.is_hidden" value="1"}checked{/eq} >
			<input type="radio" name="xianshi" value="2" title="隐藏" {neq name="data.is_hidden" value="1"}checked{/neq}>
			{else/}
			<input type="radio" name="xianshi" value="1" title="显示" checked >
			<input type="radio" name="xianshi" value="2" title="隐藏">
			{/eq}
		</div>
	</div>

	<br>

	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 110px;">
			<button type="button" class="layui-btn" lay-submit="" onclick="sub()">
				{eq name="action" value="edit"}
				确认修改
				{else/}
				确认提交
				{/eq}
			</button>
		</div>
	</div>
</form>
</div>

{load href="/static/layui-v2.4.5/layui/layui.js" /}

<script type="text/javascript">
var id = "{$id}";
var action = "{$action}";
function sub(){
	// alert('jinlai');return
	var mingcheng = $('input[name=mingcheng]').val();
	var shangji = $('select[name=shangji]').val();
	var paixu = $('input[name=paixu]').val();
	var lianjie = $('input[name=lianjie]').val();
	var xianshi = $("input[name=xianshi]:checked").val();
	// console.log(mingcheng,shangji,paixu,lianjie,xianshi);return
	if( !mingcheng || !paixu || !lianjie ){
		return layer.msg('参数填写不完整！',{anim : 6});
	}
	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	if(action=='edit'){
		var param = {mingcheng:mingcheng,shangji:shangji,paixu:paixu,lianjie:lianjie,xianshi:xianshi,id:id,action:'edit'};
	}else{
		var param = {mingcheng:mingcheng,shangji:shangji,paixu:paixu,lianjie:lianjie,xianshi:xianshi};
	}

	// return;
	$.ajax({
        url: '{:url("index/addNav")}',
        type: "POST",
        dataType: "json",
        data: param,
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );

            	var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){ 
					parent.reloadData();
					// parent.layer.close(index);
				}, 1000);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });
}


layui.use('form', function(){
	var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
	//但是，如果你的HTML是动态生成的，自动渲染就会失效
	//因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
	form.render();
});

</script>
