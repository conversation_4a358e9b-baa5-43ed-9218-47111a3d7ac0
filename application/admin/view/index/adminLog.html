{layout name="layout" /}

{load href="/static/laydate-v5.0.9/laydate.js" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>会员</a>
		<a>会员管理</a>
		<a><cite>会员列表</cite></a>
	</span>
</div>

<form class="layui-form layui-form-pane" action="{:url('index/adminLog')}" id="subform">
	<div class="layui-inline">
	    <label class="layui-form-label">日期范围</label>
	    <div class="layui-input-inline" style="width: 226px;">
	    	<input type="text" value="{$timestr}" class="layui-input" id="test6" placeholder="开始 到 结束" autocomplete="off">
	    </div>
	</div>
	<input type="hidden" name="time" value="">
</form>

<br>

<div class="layui-form form-border">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="width:5%;">编号</th>
				<th style="width:12%;">管理员</th>
				<th style="width:18%;">操作时间</th>
				<th style="width:15%;">操作IP</th>
				<th style="">操作信息</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
				<tr>
					<td>{$vo.id}</td>
					<td>{$vo.admin_name}</td>
					<td>{$vo.create_time}</td>
					<td>{$vo.deal_ip}</td>
					<td class="info" data-info="{$vo.deal_info}">{:cnsubstr($vo.deal_info, 35)}</td>
				</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>


<script type="text/javascript">
laydate.render({
	elem: '#test6'
	,range: true
	// ,format: 'yyyy年M月d日'
	,done: function(value, date, endDate){
		$('input[name=time]').val(value);
		$('#subform').submit();
	}
});


$(function(){
    $('.layui-form tbody tr .info').each(function(index, el) {
        $(this).mouseover(function () {
            var info = $(this).data('info');
            if(info){
                indexpc = layer.tips(
                    info,
                    $(this), {
                    area: ['330px', 'auto'],
                    tips: [1, '#23262E'],
                    time:0
                });
            }
        }).mouseout(function () {
            layer.close(indexpc);
        });
    });

})

function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除友情链接',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/dofriend')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( '删除成功' ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( '删除失败！',{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}


</script>