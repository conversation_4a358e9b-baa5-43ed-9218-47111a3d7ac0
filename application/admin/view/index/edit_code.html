{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>后台管理员</a>
		<a><cite>
			{eq name="action" value="edit"}
			修改
			{else/}
			添加管理员
			{/eq}
			</cite>
		</a>
	</span>
</div>
<style type="text/css">
.layui-form-label{ width: 88px; }
</style>

<br>

<form class="layui-form" action="{:url('index/editCode')}" method="post" onsubmit="return sub()">

	<div class="layui-form-item">
	    <label class="layui-form-label ">管理员账号：</label>
	    <div class="layui-input-inline w333px">

	    	{eq name="action" value="edit"}
			<input type="text" name="name" value="{$data.user_name|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			{else/}
			<input type="text" name="name" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			{/eq}

	    </div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label ">谷歌秘钥：</label>
		<div class="layui-input-inline w333px">
			{eq name="action" value="edit"}
			<input type="text" name="codeStr" value="{$data.codeStr|default=''}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			{else/}
			<input type="text" name="codeStr" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			{/eq}
		</div>
		<!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>




	{:token()}
	<input type="hidden" name="action" value="{$action}">
	<input type="hidden" name="admin_id" value="{$admin_id}">

	<br>
	<div class="layui-form-item">
		<label class="layui-form-label "></label>
		<button class="layui-btn">
			{eq name="action" value="edit"}
			确认修改
			{else/}
			确认提交
			{/eq}
		</button>
	</div>

</form>

<script type="text/javascript">
var isSub = false;
function sub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2,anim : 6});
		return false;
	}
	var action = "{$action}";
	var name = $('input[name=name]').val();
	var pass = $('input[name=pass]').val();
	var pass2 = $('input[name=pass2]').val();

	var namepat = /^[\da-zA-Z]{2,16}$/;


	isSub = true;
	return true;
}
</script>
