{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>全局设置</a>
		<a><cite>友情链接</cite></a>
	</span>
</div>

<style type="text/css">
table thead th{
	width: 14%;
}
</style>

<button class="layui-btn" onclick="addIframe('添加友情链接' , '' )">添加友情链接</button>

<br><br>

<div class="layui-form form-border" style="min-height:550px;">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="width:5%;">编号</th>
				<th style="width:10%;">链接名称</th>
				<th style="width:8%;">链接类型</th>
				<th>链接地址</th>
				<th>链接图片</th>
				<th>是否显示</th>
				<th>排序</th>
				<th>操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
			<tr class="list_{$vo.id}">
				<td>{$vo.id}</td>
				<td>{$vo.link_txt}</td>
				<td>{:str_replace([1,2], ['友情链接','合作伙伴'], $vo.f_type)}</td>
				<td>{$vo.link_href}</td>
				<td>
					{eq name="vo.link_img" value=""}
					{else/}
						<img src="{$vo.link_img}" height="30" alt="">
					{/eq}
				</td>
				<td>{:str_replace([1,2], ['是','否'], $vo.is_show)}</td>
				<td>{$vo.link_order}</td>
				<td>
					<!-- &action=edit -->
					<a href="javascript:;" onclick="addIframe('修改友情链接' , 'id={$vo.id}&action=edit' )" class="layui-bg-blue ft15">编辑</a>
					&nbsp;
					<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>



<script type="text/javascript">
function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除友情链接',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/dofriend')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( '删除成功' ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( '删除失败！',{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

function addIframe(title,ext){
	if( ext ){
		var url = '{:url("addfl")}?' + ext;
	}else{
		var url = '{:url("addfl")}';
	}
	// console.log(url);return
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false, //点击遮罩关闭层
		area : ['660px' , '450px'],
		content: url,
		// content: "{:url('addfl')}",
		end: function(){
			// location.reload();
		}
    });
}

$(function(){
	layui.use('upload', function(){
	  	var $ = layui.jquery
	  	,upload = layui.upload;
	  
	  	//普通图片上传
	  	var uploadInst = upload.render({
	  		accept:'images',
	  		field: 'img',
		    elem: '#test1'
		    ,url: '{:url("index/upload")}'
		    ,before: function(obj){
		      	//预读本地文件示例，不支持ie8
		      	obj.preview(function(index, file, result){
					$('#demo1').attr('src', result); //图片链接（base64）
		      	});
		    }
		    ,done: function(res){
		    	// console.log(res,1);
		      	if(res.status == 1){
		      		//上传成功
		      		$('input[name=imgname]').val(res.savename);
		      	}else{
		      		//如果上传失败
		        	return layer.msg( '文件上传失败！ '+res.message , {anim:6});
		      	}
		      	
		    }
		    ,error: function(){
		      	//演示失败状态，并实现重传
		      	var demoText = $('#demoText');
		      	demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
		      	demoText.find('.demo-reload').on('click', function(){
		        	uploadInst.upload();
		      	});
		    }
		});
	});

})

</script>