{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>全局设置</a>
		<a><cite>网站配置</cite></a>
	</span>
</div>

<button class="layui-btn" onclick="test('添加新配置')">添加新配置</button>

<style type="text/css">
.label2-title{ font-size: 13px; }
.layui-form-mid{ font-size: 12px; }
.layui-upload{ position: relative; }
.layui-upload-img{ 
	height: 66px;
    margin-left: 15px;
    position: absolute;
    top: -15px;
    left: 265px;
}
.layui-form-item{ padding-left: 15px;position: relative; }
.layui-form-item a.del-set{
	height: 14px;width: 14px;display: none;
    background: url(/static/img/A/delset.gif) no-repeat;
    position: absolute;top: 11px;cursor: pointer;z-index: 9;
}
</style>

<br><br>

<form action="{:url('/index/webSet')}" method="POST" onsubmit="return checkSub()">
	{volist name="data" id="vo"}
		{if condition="$vo.type EQ 'file'"}
			<div class="layui-form-item webset_{$vo.id} ">
				<a href="javascript:;" class="del-set" onclick="delConfirm( {$vo.id} )"></a>
			    <label class="layui-form-label label2-title" >网站Logo：</label>
			    <input type="hidden" name="{$vo.id}" id="logo" value="{$vo.text}">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal fl" id="test1">
						{if condition="$vo.text"}
						修改Logo
						{else/}
						上传Logo
						{/if}
					</button>
					<div class="layui-upload-list">
						{if condition="$vo.text"}
						<img class="layui-upload-img" src="{$vo.text}" id="demo1">
						{else/}
						<img class="layui-upload-img" id="demo1">
						{/if}
						<p id="demoText"></p>

						<div style="clear:both;"></div>
					</div>
				</div>
			</div>
		{elseif condition="$vo.type EQ 'input' "/}
			<div class="layui-form-item webset_{$vo.id} ">
				<a href="javascript:;" class="del-set" onclick="delConfirm( {$vo.id} )"></a>
			    <label class="layui-form-label label2-title">{$vo.name}：</label>
			    <div class="layui-input-inline w333px">
					<input type="text" name="{$vo.id}" value="{$vo.text}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux">{$vo.tip}({$vo.code})</div>
			</div>
		{else /}
			<div class="layui-form-item webset_{$vo.id}">
				<a href="javascript:;" class="del-set" onclick="delConfirm( {$vo.id} )"></a>
			    <label class="layui-form-label label2-title">{$vo.name}：</label>
			    <div class="layui-input-inline w333px">
					<textarea name="{$vo.id}" placeholder="请输入内容" class="layui-textarea">{$vo.text}</textarea>
			    </div>
			    <div class="layui-form-mid layui-word-aux">{$vo.tip}({$vo.code})</div>
			</div>
		{/if}

	{/volist}

	{:token()}

	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 169px;">
			<button class="layui-btn" lay-submit="" lay-filter="demo1">确认提交</button>
		</div>
	</div>
</form>

<!-- <div class="layui-form-item" id='tong'>
    <label class="layui-form-label label2-title">网站名称：</label>
    <div class="layui-input-inline w600px">
		<input type="text" name="username" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
    </div>
    <div class="layui-form-mid layui-word-aux">出现在每个页面的title后面(web_name)</div>
</div>
<div class="layui-form-item">
    <label class="layui-form-label label2-title">首页title：</label>
    <div class="layui-input-inline w600px">
		<input type="text" name="username" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
    </div>
    <div class="layui-form-mid layui-word-aux">首页标题(index_title)</div>
</div>
<div class="layui-form-item layui-form-text">
    <label class="layui-form-label label2-title">网站关键词：</label>
    <div class="layui-input-inline w600px">
		<textarea placeholder="请输入内容" class="layui-textarea"></textarea>
    </div>
    <div class="layui-form-mid layui-word-aux">在首页的keywords中显示(web_keywords)</div>
</div> -->


<script type="text/javascript">
function test(title,url){
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false, //点击遮罩关闭层
		area : ['660px' , '407px'],
		content: "{:url('addpz')}",
		end: function(){
			/*alert('hola');
			location.reload();*/
		}
    });
}
// del-set
$('.layui-form-item').each(function(index, el) {
	$(this).mouseover(function () {
        $(this).find('.del-set').show();
    }).mouseout(function () {
        $(this).find('.del-set').hide();
    });
});
function delConfirm(id){
	layer.confirm('您确定删除？将不可恢复', {
		title:'删除该网站配置，请谨慎操作',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/delSet')}",
	        type: "post",
	        dataType: "json",
	        data: {wid:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('.webset_'+id).remove();
	            }else{
	            	layer.msg( d.message,{icon : 2,anim : 6} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}
// del-set
$(function(){
	layui.use('upload', function(){
	  	var $ = layui.jquery
	  	,upload = layui.upload;
	  	//普通图片上传
	  	var uploadInst = upload.render({
	  		accept:'images',
	  		field: 'img',
		    elem: '#test1'
		    ,url: '{:url("index/upload")}'
		    ,before: function(obj){
		      	//预读本地文件示例，不支持ie8
		      	obj.preview(function(index, file, result){
					$('#demo1').attr('src', result); //图片链接（base64）
		      	});
		    }
		    ,done: function(res){
		    	// console.log(res,1);
		      	if(res.status == 1){
		      		//上传成功
		      		$('#logo').val(res.savename);
		      	}else{
		      		//如果上传失败
		        	return layer.msg( '文件上传失败！ '+res.message , {anim:6});
		      	}
		    }
		    ,error: function(){
		      	//演示失败状态，并实现重传
		      	var demoText = $('#demoText');
		      	demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
		      	demoText.find('.demo-reload').on('click', function(){
		        	uploadInst.upload();
		      	});
		    }
		});
	});


})
</script>
