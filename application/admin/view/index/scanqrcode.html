{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>充值设置</a>
		<a><cite>扫码充值设置</cite></a>
	</span>
</div>
<style type="text/css">
.layui-form-label{
	text-align: center;
    width: 100px;
}
.layui-upload-list{
	margin-left: 130px;
    width: 117px;
    min-height: 117px;
    max-height: 127px;
    border: 1px solid #dcd2d2;
    padding: 5px;
}
.layui-upload-list .layui-upload-img{
	width: 100%
}
</style>
<div class="layui-tab layui-tab-brief " lay-filter="docDemoTabBrief">
	<ul class="layui-tab-title">
		<li class="layui-this">微信扫码</li>
		<li>支付宝扫码</li>
	</ul>
	<div class="layui-tab-content" style="height: 100px;">
		<div class="layui-tab-item layui-show">

			<form action="{:url('index/scanqrcode')}" method="post" class="layui-form" onsubmit="return checkSub()">

				<div class="layui-form-item" >
				    <label class="layui-form-label layui-width" >微信二维码：</label>
				    <input type="hidden" name="wxqrcode" id="wxqrcode" value="{$weixin.qrcode|default=''}">
					<div class="layui-upload">
						<button type="button" class="layui-btn" id="test1">上传图片</button>
						<div class="layui-upload-list">
							{empty name="weixin.qrcode"}
							<img class="layui-upload-img" id="demo1">
							{else /}
							<img class="layui-upload-img" src="{$weixin.qrcode|default=''}" id="demo1">
							{/empty}
							<p id="demoText"></p>
						</div>
					</div>
				</div>

				<div class="layui-form-item" >
					<label class="layui-form-label layui-width" >微信账号：</label>
					<div class="layui-input-inline w333px">
						<input type="text" name="wxacccode" id="wxacccode" value="{$weixin.number|default=''}" placeholder="请输入账号" autocomplete="off" class="layui-input">
					</div>
				</div>

				<div class="layui-form-item">
			        <label class="layui-form-label layui-width">是否启用图片：</label>
			        <div class="layui-input-block">
			            <input type="radio" name="status_1" value="1" title="启用" {eq name="weixin.status" value="1"}checked{/eq} >
			            <input type="radio" name="status_1" value="0" title="停用" {neq name="weixin.status" value="1"}checked{/neq} >
			        </div>
			    </div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-width">是否启用账号：</label>
					<div class="layui-input-block">
						<input type="radio" name="status_2" value="1" title="启用" {eq name="weixin.payee" value="1"}checked{/eq} >
						<input type="radio" name="status_2" value="0" title="停用" {neq name="weixin.payee" value="1"}checked{/neq} >
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-width">充值说明：</label>
					<div class="layui-input-inline " style="">
						<script id="container" name="contents" type="text/plain">{notempty name="weixin.content"}{:htmlspecialchars_decode($weixin.content)}{/notempty}</script>
					</div>
				</div>

				<div class="layui-form-item">
					<div class="layui-input-block" style="margin-left: 130px;">
						<button class="layui-btn" lay-submit="" lay-filter="">确认提交</button>
					</div>
				</div>
				<input type="hidden" name="way" value="wx">
				{:token()}
			</form>

		</div>
		<div class="layui-tab-item">

			<form action="{:url('index/scanqrcode')}" method="post" class="layui-form" onsubmit="return checkSub()">

				<div class="layui-form-item" >
				    <label class="layui-form-label layui-width" >支付宝二维码：</label>
				    <input type="hidden" name="zfbqrcode" id="zfbqrcode" value="{$zhifubao.qrcode|default=''}">
					<div class="layui-upload">
						<button type="button" class="layui-btn" id="test2">上传图片</button>
						<div class="layui-upload-list">
							{empty name="zhifubao.qrcode"}
							<img class="layui-upload-img" id="demo2">
							{else /}
							<img class="layui-upload-img" src="{$zhifubao.qrcode|default=''}" id="demo2">
							{/empty}
							<p id="demoText"></p>
						</div>
					</div>
				</div>

				<div class="layui-form-item" >
					<label class="layui-form-label layui-width" >支付宝账号：</label>
					<div class="layui-input-inline w333px">
						<input type="text" name="zfbacccode" id="zfbacccode" value="{$zhifubao.number|default=''}" placeholder="请输入账号" autocomplete="off" class="layui-input">
					</div>
				</div>

				<div class="layui-form-item">
			        <label class="layui-form-label layui-width">是否启用图片：</label>
			        <div class="layui-input-block">
			            <input type="radio" name="status_1" value="1" title="启用" {eq name="zhifubao.status" value="1"}checked{/eq} >
			            <input type="radio" name="status_1" value="0" title="停用" {neq name="zhifubao.status" value="1"}checked{/neq} >
			        </div>
			    </div>

				<div class="layui-form-item">
					<label class="layui-form-label layui-width">是否启用账号：</label>
					<div class="layui-input-block">
						<input type="radio" name="status_2" value="1" title="启用" {eq name="zhifubao.payee" value="1"}checked{/eq} >
						<input type="radio" name="status_2" value="0" title="停用" {neq name="zhifubao.payee" value="1"}checked{/neq} >
					</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label layui-width">充值说明：</label>
					<div class="layui-input-inline " style="">
						<script id="container2" name="contents" type="text/plain">{notempty name="zhifubao.content"}{:htmlspecialchars_decode($zhifubao.content)}{/notempty}</script>
					</div>
				</div>
				<div class="layui-form-item">
					<div class="layui-input-block" style="margin-left: 130px;">
						<button class="layui-btn" lay-submit="" lay-filter="">确认提交</button>
					</div>
				</div>
				<input type="hidden" name="way" value="zfb">
				{:token()}
			</form>

		</div>

	</div>
</div>

{load href="/ueditor/ueditor.config.js" /}
{load href="/ueditor/ueditor.all.js" /}
<script type="text/javascript">
var ue = UE.getEditor('container',{
    // autoHeightEnabled: true,
    autoFloatEnabled: false,
    toolbars: [
        ['source', 'undo', 'redo', 'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc']
    ],
    initialFrameWidth: 666,
    initialFrameHeight: 300
});
var ue2 = UE.getEditor('container2',{
	toolbars: [
        ['source', 'undo', 'redo', 'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc']
    ],
    initialFrameWidth: 666,
    initialFrameHeight: 300
});

$(function(){
	layui.use('upload', function(){
	  	var $ = layui.jquery
	  	,upload = layui.upload;
	  	//普通图片上传
	  	var uploadInst = upload.render({
	  		accept:'images',
	  		field: 'img',
		    elem: '#test1'
		    ,url: '{:url("index/upload")}'
		    ,before: function(obj){
		      	//预读本地文件示例，不支持ie8
		      	obj.preview(function(index, file, result){
					$('#demo1').attr('src', result); //图片链接（base64）
		      	});
		    }
		    ,done: function(res){
		    	// console.log(res,1);
		      	if(res.status == 1){
		      		//上传成功
		      		$('#wxqrcode').val(res.savename);
		      	}else{
		      		//如果上传失败
		        	return layer.msg( '文件上传失败！ '+res.message , {anim:6});
		      	}
		    }
		    ,error: function(){
		      	//演示失败状态，并实现重传
		      	var demoText = $('#demoText');
		      	demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
		      	demoText.find('.demo-reload').on('click', function(){
		        	uploadInst.upload();
		      	});
		    }
		});
	});

	layui.use('upload', function(){
	  	var $ = layui.jquery
	  	,upload = layui.upload;
	  	//普通图片上传
	  	var uploadInst = upload.render({
	  		accept:'images',
	  		field: 'img',
		    elem: '#test2'
		    ,url: '{:url("index/upload")}'
		    ,before: function(obj){
		      	//预读本地文件示例，不支持ie8
		      	obj.preview(function(index, file, result){
					$('#demo2').attr('src', result); //图片链接（base64）
		      	});
		    }
		    ,done: function(res){
		    	// console.log(res,1);
		      	if(res.status == 1){
		      		//上传成功
		      		$('#zfbqrcode').val(res.savename);
		      	}else{
		      		//如果上传失败
		        	return layer.msg( '文件上传失败！ '+res.message , {anim:6});
		      	}
		    }
		    ,error: function(){
		      	//演示失败状态，并实现重传
		      	var demoText = $('#demoText');
		      	demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
		      	demoText.find('.demo-reload').on('click', function(){
		        	uploadInst.upload();
		      	});
		    }
		});
	});

})

</script>
