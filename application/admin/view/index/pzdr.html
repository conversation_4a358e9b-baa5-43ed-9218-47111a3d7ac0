{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>首页数据</a>
		<a><cite>配资达人</cite></a>
	</span>
</div>
<style type="text/css">

</style>


<button class="layui-btn" onclick="addIframe('添加数据','{:url('index/extend_data',['type'=>'pzdr'])}')">
	添加数据
</button>
<br><br>

<div class="layui-form" style="height:470px;">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="">编号</th>
				<th style="">字段1</th>
				<th style="">字段2</th>
				<th style="">字段3</th>
				<th style="">字段4</th>
				<th style="">字段5</th>
				<th style="">添加时间</th>
				<th style="width:12%;">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
			<tr id="list_{$vo.id}" data-id="{$vo.id}" parentid="0" class="leve_1">
				<td>{$vo.id}</td>
				<td>{$vo.field1}</td>
				<td>{$vo.field2}</td>
				<td>{$vo.field3}</td>
				<td>{$vo.field4}</td>
				<td>{$vo.field5}</td>
				<td>{$vo.create_time}</td>
				<td>
					<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
function addIframe(title,url){
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false,
		area : ['550px' , '400px'],
		content: url,
		end: function(){
		}
    });
}
$(function(){
	
})
function delConfirm(id,title){
	layer.confirm('您确定删除？', {
		title:title,
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('index/extend_data')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( d.message ,{icon : 1} );
	            	$('#list_'+id).remove();
	            }else{
	            	layer.msg( d.message ,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

$(function(){
	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});

})


</script>