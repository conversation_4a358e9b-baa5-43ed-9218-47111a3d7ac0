{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>全局</a>
		<a>首页</a>
		<a><cite>总览</cite></a>
	</span>
</div>
<style type="text/css">
.audit{ color: red!important;font-size: 14px;font-weight: bold; }
.tiyan{ text-align: center;font-size: 14px;color: #000;position: relative;top: -7px;display: none; }
.tiyan a{ color: red;font-size: 16px; }
.layui-col-xs2 { width: 14%; }
.daiban{ margin-top: 0;padding: 5px 0 10px; }
.daiban div { margin: 5px 20px 0; /* height: 50px; */ line-height: 30px; }
.daiban div.category{ font-size:15px;height: 35px;margin-right: 0; }
.daiban div.test{ float: left; }
</style>
<!--[if IE]>
<style type="text/css">
.layui-nav2{ margin-bottom: 0; }
.tiyan{ display: block; }
</style>
<![endif]-->

<div class="tiyan">为了更好体验，请使用<a href="/static/img/A/jisu.png" target="_blank">极速模式</a>或<a href="/static/img/A/guge.png" target="_blank">谷歌浏览器！</a></div>

<fieldset class="layui-elem-field site-demo-button daiban clearfix">
	<legend>代办事项</legend>

	<!-- <div class="test">
		实名认证 <span class="layui-badge-rim">0</span> 个
	</div> -->
	<div class="category fl"><strong>充值提现：</strong></div>
		<div class="test">
			充值审核
			{egt name="chongzhi" value="1"}
				<a href="{:url('money/rechargeApply')}"><span class="layui-badge-rim audit">{$chongzhi}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>

		<div class="test">
			提现审核
			{egt name="tixian" value="1"}
				<a href="{:url('money/withdrawApply')}"><span class="layui-badge-rim audit">{$tixian}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>

	<div style="clear:both;"></div>
	<div class="category fl"><strong>股票配资：</strong></div>
		<div class="test">
			配资申请
			{egt name="peizi" value="1"}
				<a href="{:url('stock/stockApply')}"><span class="layui-badge-rim audit">{$peizi}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			扩大配资申请
			{egt name="kuoda" value="1"}
				<a href="{:url('stock/addfinancing')}"><span class="layui-badge-rim audit">{$kuoda}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			增加延期申请
			{egt name="xuqi" value="1"}
				<a href="{:url('stock/renewalApply')}"><span class="layui-badge-rim audit">{$xuqi}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			提取盈利申请
			{egt name="tiying" value="1"}
				<a href="{:url('stock/drawprofitApply')}"><span class="layui-badge-rim audit">{$tiying}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			补充亏损申请
			{egt name="bukui" value="1"}
				<a href="{:url('stock/fillApply')}"><span class="layui-badge-rim audit">{$bukui}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			提前终止申请
			{egt name="zhongzhi" value="1"}
				<a href="{:url('stock/stopfinancingApply')}"><span class="layui-badge-rim audit">{$zhongzhi}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
	
	<div style="clear:both;"></div>
	<!-- <div class="category fl"><strong>期货配资：</strong></div>
		<div class="test">
			配资申请
			{egt name="future.peizi" value="1"}
				<a href="{:url('future/stockApply')}"><span class="layui-badge-rim audit">{$future.peizi}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			扩大配资申请
			{egt name="future.kuoda" value="1"}
				<a href="{:url('future/addfinancing')}"><span class="layui-badge-rim audit">{$future.kuoda}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			增加延期申请
			{egt name="future.xuqi" value="1"}
				<a href="{:url('future/renewalApply')}"><span class="layui-badge-rim audit">{$future.xuqi}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			提取盈利申请
			{egt name="future.tiying" value="1"}
				<a href="{:url('future/drawprofitApply')}"><span class="layui-badge-rim audit">{$future.tiying}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			补充亏损申请
			{egt name="future.bukui" value="1"}
				<a href="{:url('future/fillApply')}"><span class="layui-badge-rim audit">{$future.bukui}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div>
		<div class="test">
			提前终止申请
			{egt name="future.zhongzhi" value="1"}
				<a href="{:url('future/stopfinancingApply')}"><span class="layui-badge-rim audit">{$future.zhongzhi}</span></a>
			{else/}
				<span class="layui-badge-rim">0</span>
			{/egt}
			个
		</div> -->

</fieldset>



<fieldset class="layui-elem-field site-demo-button" style="margin-top: 15px;">
	<legend>综合事项</legend>

	<div class="layui-form" style="margin: 25px 20px 15px;">
	    <table class="layui-table table-center" >
	        <thead>
	            <tr>
	                <th style="width:33%;">昨日新增用户数</th>
	                <th style="width:33%;">近7日新增用户数</th>
	                <th style="width:33%;">近30日新增用户数</th>
	            </tr>
	        </thead>
	        <tbody>
	            <tr>
	                <td>{$data.zrxzyh}</td>
	                <td>{$data.j7rxzyh}</td>
	                <td>{$data.j30rxzyh}</td>
	            </tr>
	        </tbody>
	    </table>
	</div>

	<div class="layui-form" style="margin: 20px 20px 25px;">
	    <table class="layui-table table-center" >
	        <thead>
	            <tr>
	                <th>总用户数</th>
	                <th>配资用户数</th>
	                <th>配资用户占总用户数</th>
	                <th>免费体验用户</th>
	                <th>免息配资用户</th>
	                <th>按天配资用户</th>
	                <th>按月配资用户</th>
	                <th>VIP配资用户</th>
	            </tr>
	        </thead>
	        <tbody>
	            <tr>
	                <td>{$data.zyhs}</td>
	                <td>{$data.pzyhs}</td>
	                <td>{$data.pzyhzb}%</td>
	                <td>{$data.mftyyh}</td>
	                <td>{$data.mxpzyh}</td>
	                <td>{$data.antianpzyh}</td>
	                <td>{$data.anyuepzyh}</td>
	                <td>{$data.vippzyh}</td>
	            </tr>
	        </tbody>
	    </table>
	</div>


	<!-- <div class="layui-form" style="margin: 25px 20px 15px;">
	    <table class="layui-table table-center" >
	        <thead>
	            <tr>
	                <th style="width:16%;">昨日登录用户数</th>
	                <th style="width:16%;">总占比</th>
	                <th style="width:16%;">近7日登录用户数</th>
	                <th style="width:16%;">总占比</th>
	                <th style="width:16%;">近30日登录用户数</th>
	                <th style="width:16%;">总占比</th>
	            </tr>
	        </thead>
	        <tbody>
	            <tr>
	                <td>10</td>
	                <td>10%</td>
	                <td>10</td>
	                <td>10%</td>
	                <td>10</td>
	                <td>10%</td>
	            </tr>
	        </tbody>
	    </table>
	</div> -->

</fieldset>


<fieldset class="layui-elem-field site-demo-button" style="margin-top: 15px;">
	<legend>财务数据</legend>
	
	<div class="search-box" style="margin: 0px -8px -10px;">
        <form class="layui-form layui-form-pane" action="{:url('/index')}" id="subform">
            <div class="layui-inline" style="margin: 20px 15px 16px 30px;float: left;">
                <label class="layui-form-label">时间范围</label>
                <div class="layui-input-inline" style="width: 226px;margin: 0 0 0 -1px;">
                    <input type="text" value="{$timestr|default=''}" class="layui-input" id="test6" placeholder="开始 到 结束" autocomplete="off">
                </div>
            </div>
            <input type="hidden" name="time" value="">

        	<p style="float: left;margin: 30px 30px 10px 0;color: #666;">默认显示当天数据</p>
        </form>
    </div>

	<div class="layui-form" style="margin: 20px 20px 15px;">
	    <table class="layui-table table-center" >
	        <thead>
	            <tr>
	                <th style="width:12.5%;">线下充值总额</th>
	                <th style="width:12.5%;">微信扫码充值总额</th>
	                <th style="width:12.5%;">支付宝扫码充值总额</th>
	                <th style="width:12.5%;">充值总额</th>
                    <th style="width:12.5%;">提现总额</th>
	                <th style="width:12.5%;">已处理提盈</th>
	                <th style="width:12.5%;">后台转账总额</th>
	                <th style="width:12.5%;">后台赠送管理费</th>
	            </tr>
	        </thead>
	        <tbody>
	            <tr>
	                <td>{$caiwu.xianxiazonge/100}</td>
	                <td>{$caiwu.wxzonge/100}</td>
	                <td>{$caiwu.zfbzonge/100}</td>
	                <td>{$caiwu.chongzhizonge/100}</td>
	                <td>{$caiwu.tixianzonge/100}</td>
                    <td>{$caiwu.tiyingzonge/100}</td>
	                <td>{$caiwu.zhuanzhang/100}</td>
	                <td>{$caiwu.zsglf/100}</td>
	            </tr>
	        </tbody>
	    </table>
	</div>
	<br>
</fieldset>


<div style="height:200px;"></div>

{load href="/static/laydate-v5.0.9/laydate.js" /}
<script type="text/javascript">
laydate.render({
    elem: '#test6'
    ,range: true
    // ,format: 'yyyy年M月d日'
    ,done: function(value, date, endDate){
        $('input[name=time]').val(value);
        $('#subform').submit();
    }
});


$(function(){

	if( IEVersion()!==false ){
		$('.tiyan').show();
		$('.layui-nav2').css("marginBottom","0");
	}
})
</script>
