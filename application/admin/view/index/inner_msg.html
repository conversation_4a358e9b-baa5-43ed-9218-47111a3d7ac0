{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-label{
    width: 80px;
    padding: 9px 6px;
}
.layui-main{
    margin: -3px 0 21px 0;
    padding: 0 10px;
}
</style>

<div class="layui-main">
    
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <ul class="layui-tab-title">
                <li class="layui-this">站内信</li>
                <li>站内信群发</li>
                <li>短信群发</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <form class="layui-form" onsubmit="return false;">
                        <br>
                        <div class="layui-form-item" >
                            <label class="layui-form-label label2-title">用户名：</label>
                            <div class="layui-input-inline ">
                                <input type="text" name="uname" maxlength="11" lay-verify="required" placeholder="请输入用户名" autocomplete="" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        
                        <div class="layui-form-item" >
                            <label class="layui-form-label label2-title">标题：</label>
                            <div class="layui-input-inline ">
                                <input type="text" name="onetitle" maxlength="30" lay-verify="required" placeholder="请输入内容" autocomplete="" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">站内信标题，最多30个字</div>
                        </div>

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label label2-title">发送信息：</label>
                            <div class="layui-input-inline w333px">
                                <textarea name="oneinfo" placeholder="请输入内容" lay-verify="required" class="layui-textarea" style="width: 350px;min-height: 100px;"></textarea>
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block" style="margin-left: 112px;">
                                <button class="layui-btn" lay-submit="" onclick="sub('one')">确认提交</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="layui-tab-item">
                    <form class="layui-form" onsubmit="return false;">
                        <br>
                        <div class="layui-form-item" >
                            <label class="layui-form-label label2-title">发送对象：</label>
                            
                            <div class="layui-form-mid layui-word-aux"><b style='color: #333;'>当前所有用户站内信</b></div>
                        </div>
                        <div class="layui-form-item" >
                            <label class="layui-form-label label2-title">标题：</label>
                            <div class="layui-input-inline ">
                                <input type="text" name="alltitle" maxlength="30" lay-verify="required" placeholder="请输入内容" autocomplete="" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">站内信标题，最多30个字</div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label label2-title">发送信息：</label>
                            <div class="layui-input-inline w333px">
                                <textarea name="allinfo" placeholder="请输入内容" lay-verify="required" class="layui-textarea" style="width: 350px;min-height: 100px;"></textarea>
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block" style="margin-left: 112px;">
                                <button class="layui-btn" lay-submit="" onclick="sub('all')">确认提交</button>
                            </div>
                        </div>
                    </form>
                </div>
                    
                <div class="layui-tab-item">
                    <form class="layui-form" onsubmit="return false;">
                        <br>
                        <div class="layui-form-item" >
                            <label class="layui-form-label label2-title">发送对象：</label>
                            
                            <div class="layui-form-mid layui-word-aux"><b style='color: #333;'>当前所有用户手机号</b></div>
                        </div>
                        <!-- <div class="layui-form-item" >
                            <label class="layui-form-label label2-title">标题：</label>
                            <div class="layui-input-inline ">
                                <input type="text" name="alltitle" maxlength="30" lay-verify="required" placeholder="请输入内容" autocomplete="" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">站内信标题，最多30个字</div>
                        </div> -->
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label label2-title">发送信息：</label>
                            <div class="layui-input-inline w333px">
                                <textarea name="smsallinfo" placeholder="请输入内容" lay-verify="required" class="layui-textarea" style="width: 350px;min-height: 138px;"></textarea>
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block" style="margin-left: 112px;">
                                <button class="layui-btn" lay-submit="" onclick="sub('smsall')">确认提交</button>
                            </div>
                        </div>
                    </form>
                </div>

            </div>
        </div>

    
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">
function sub(action){
    var uname = $('input[name=uname]').val();
    var onetitle = $('input[name=onetitle]').val();
    var oneinfo = $('textarea[name=oneinfo]').val();

    var alltitle = $('input[name=alltitle]').val();
    var allinfo = $('textarea[name=allinfo]').val();

    var smsallinfo = $('textarea[name=smsallinfo]').val();
    if( action=='one' ){
        if( !uname || !oneinfo || !onetitle ){
            return layer.msg('信息填写不完整！',{anim : 6});
        }
    }else if( action=='all' ){
        if( !allinfo || !alltitle ){
            return layer.msg('信息填写不完整！',{anim : 6});
        }
    }else if( action=='smsall' ){
        if( !smsallinfo ){
            return layer.msg('信息填写不完整！',{anim : 6});
        }
    }

    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("index/innerMsg")}',
        type: "post",
        dataType: "json",
        data: {uname:uname,oneinfo:oneinfo,allinfo:allinfo,onetitle:onetitle,alltitle:alltitle,smsallinfo:smsallinfo,action:action},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );

                var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){
                    parent.reloadData();
                    // parent.layer.close(index);
                }, 1000);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2,anim : 6} );
            }
        }
    });

}

layui.use('element', function(){
    var element = layui.element;
});
layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();
});
</script>
