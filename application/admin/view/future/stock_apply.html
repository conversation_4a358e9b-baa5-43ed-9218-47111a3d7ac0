{layout name="layout" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>期货配资</a>
        <a>配资管理</a>
        <a><cite>待审核配资</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('future/stockApply')}">
    <div class="layui-inline">
        <label class="layui-form-label">搜索用户名</label>
        <div class="layui-input-inline">
            <input type="text" name="user_name" value="{$user_name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div>

    <br>
    <br>
</form>


<div class="layui-form form-border" style="overflow-x: scroll;">
    <table class="layui-table" lay-skin="line" style="min-width: 1440px;">
        <thead>
            <tr>
                <th style="width: 5%;">编号</th>
                <th style="">用户名</th>
                <th style="">真实姓名</th>
                <th style="">配资类型</th>
                <th style="">保证金</th>
                <th style="">总配资金额</th>
                <th style="">倍数</th>
                <th style="">周期</th>
                <th style="">利率</th>
                <th style="">管理费</th>
                <th style="">警戒线</th>
                <th style="">平仓线</th>
                <th style="">开始时间</th>
                <th style="">结束时间</th>
                <th style="width:8%;">操作</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $vo.type)}</td>
                    <td>{$vo.deposit_money / 100}</td>
                    <td>{$vo.borrow_money / 100}</td>
                    <td>{$vo.multiple}</td>
                    <td>{$vo.borrow_duration}</td>
                    <td>{$vo.rate}</td>
                    <td>{$vo.borrow_interest / 100}</td>
                    <td>{$vo.loss_warn / 100}</td>
                    <td>{$vo.loss_close / 100}</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>{:date('Y-m-d H:i',$vo.end_time)}</td>
                    <td>
                        <a href="javascript:;" onclick="addIframe( '{:url('future/sendFinancingUser',['id'=>$vo.id])}' )" class="layui-bg-blue ft15">编辑</a>
                        <!-- &nbsp; -->
                        <!-- <a href="javascript:;" onclick="delConfirm( 1 )" class="layui-bg-red ft15">删除</a> -->
                    </td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
function addIframe(url){
    layer.open({
        type: 2,
        title: '审核配资',
        maxmin: false,
        shadeClose: false,
        area : ['680px' , '479px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
</script>