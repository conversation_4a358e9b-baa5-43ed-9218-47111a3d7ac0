{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>期货配资</a>
		<a>期货配资</a>
		<a><cite>配资设置</cite></a>
	</span>
</div>
<style type="text/css">
.label2-title{
	font-size: 13px;
}
.layui-form-mid{ font-size: 12px; }
</style>

<br>



<form class="layui-form" action="{:url('/future/stockSet')}" method="POST" onsubmit="return checkSub()">
	{volist name="data" id="vo"}
		{eq name="vo.type" value="input"}
			<div class="layui-form-item">
			    <label class="layui-form-label label2-title">{$vo.name}：</label>
			    <div class="layui-input-inline w333px">
					<input type="text" name="{$vo.id}" value="{$vo.text}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
			    </div>
			    <div class="layui-form-mid layui-word-aux">{$vo.tip}({$vo.code})</div>
			</div>
		{else/}
			<div class="layui-form-item layui-form-text">
			    <label class="layui-form-label label2-title">{$vo.name}：</label>
			    <div class="layui-input-inline w333px">
					<textarea name="{$vo.id}" placeholder="请输入内容" class="layui-textarea">{$vo.text}</textarea>
			    </div>
			    <div class="layui-form-mid layui-word-aux">{$vo.tip}({$vo.code})</div>
			</div>
		{/eq}
	{/volist}

	{:token()}

	<div class="layui-form-item">
		<div class="layui-input-block" style="margin-left: 169px;">
			<button class="layui-btn" lay-submit="" lay-filter="demo1">确认提交</button>
		</div>
	</div>
</form>



<script type="text/javascript">
function open_url(title,url){
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false, //点击遮罩关闭层
		area : ['660px' , '390px'],
		content: "{:url('addpz')}",
		end: function(){
			/*alert('hola');
			location.reload();*/
		}
    });
}


$(function(){

	 
})
</script>
