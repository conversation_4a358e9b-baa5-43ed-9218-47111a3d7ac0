{layout name="layout" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>期货配资</a>
        <a>补充亏损</a>
        <a><cite>已处理补亏</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('future/fillList')}" id='subform'>
    <div class="layui-form-item">
        <div class="layui-form-item">
        <div class="layui-input-inline" style="width: 113px;">
            <select name="action" lay-verify="required" lay-search="">
                <option value="user_name" {eq name="action" value="user_name"}selected{/eq}>搜索用户名</option>
                <option value="home_user" {eq name="action" value="home_user"}selected{/eq}>搜索子账号</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width: 185px;margin-left: -11px;">
            <input type="text" name="name" value="{$name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div>
    </div>
</form>


<div class="layui-form form-border" style="overflow-x: scroll;">
    <table class="layui-table" lay-skin="line" style="min-width: 1300px;">
        <thead>
            <tr>
                <th style="width: 7%;">编号</th>
                <th style="">用户名</th>
                <th style="">真实姓名</th>
                <th style="">子账号</th>
                <th style="">申请补亏金额</th>
                <th style="">配资类型</th>
                <th style="">申请时间</th>
                <th style="">状态</th>
                <th style="">审核时间</th>
                <th style="width:15%;">备注</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{$vo.home_user}</td>
                    <td>{$vo.money/100}</td>
                    <td>{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $vo.type)}</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>{:str_replace([-1,0,1], ['未通过','待审核','审核成功'], $vo.status)}</td>
                    <td>{:date('Y-m-d H:i',$vo.verify_time)}</td>
                    <td class="info" data-info="{$vo.content|default=''}">{:cnsubstr($vo.content,20)}</td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
$('input[name=name]').keypress(function (e) {
    if( e.which==13 ){
        
        $('#subform').submit();
    }
});

$(function(){
    $('.layui-form tbody tr .info').each(function(index, el) {
        $(this).mouseover(function () {
            var info = $(this).data('info');
            if(info){
                indexpc = layer.tips(
                    info,
                    $(this), {
                    area: ['330px', 'auto'],
                    tips: [1, '#23262E'],
                    time:0
                });
            }
        }).mouseout(function () {
            layer.close(indexpc);
        });
    });

})
</script>