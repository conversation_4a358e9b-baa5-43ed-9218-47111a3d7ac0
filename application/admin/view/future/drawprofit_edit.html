{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-label{
    width: 100px;
    padding: 9px 6px;
}
.details{
    width: 240px;
    float: left;
    line-height: 23px;
    color: #333;
    font-size: 14px;
}

</style>

<div class="layui-main" style="padding: 21px 0;">
    <form class="layui-form" onsubmit="return false;">
        <input type="hidden" name='id' value="{$id}">
        <div class="layui-form-item layui-form-text" style="margin-bottom: 6px;">
            <label class="layui-form-label label2-title">配资详情：</label>
            <div class="details">
                <p>会员：{$data.user_name}</p>
                <p>配资类型：{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $data.type)}</p>
                <p>保证金：{$data.deposit_money/100}</p>
                <p>总操盘资金：{$data.borrow_money/100}</p>
            </div>
            <div class="details">
                <p>真实姓名：{$data.real_name|default='未实名'}</p>
                <p>子账号：{$data.home_user}</p>
                <p>倍数：{$data.multiple}倍</p>

            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom: 6px;">
            <label class="layui-form-label label2-title">审核状态：</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="通过" checked >
                <input type="radio" name="status" value="-1" title="拒绝">
            </div>
        </div>


        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">备注信息：</label>
            <div class="layui-input-inline w333px">
                <textarea name="beizhu" placeholder="请输入内容" class="layui-textarea" style="width: 380px;min-height: 60px;"></textarea>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux"></div> -->
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 112px;">
                <button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="sub()">确认提交</button>
            </div>
        </div>

    </form>
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">
function sub(){
    var id = parseInt($('input[name=id]').val());
    var status = $("input[name=status]:checked").val();
    var info = $('textarea[name=beizhu]').val();
    if( !id ){
        return layer.msg('id不存在，请按规则重试！',{anim : 6});
    }
    if( status!=1 && !info ){
        return layer.msg('拒绝申请需要填写备注信息',{anim : 6});
    }
    // console.log( id,status,info );return
    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("future/drawprofitEdit")}',
        type: "POST",
        dataType: "json",
        data: {id:id,status:status,info:info},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){ 
                    parent.reloadData();
                    // parent.layer.close(index);
                }, 1000);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}
layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();
});
</script>
