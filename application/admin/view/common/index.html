{__NOLAYOUT__}

<!DOCTYPE html>
<html lang="en">
<head>
	<title>后台管理系统 - Login</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">

	{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
	

	{load href="/static/css/A/admin.css" /}
	{load href="/static/css/A/login.css" /}
	{load href="/static/js/jquery172.js" /}
	{load href="/static/js/A/admin.js" /}

	{load href="/static/layer-v3.1.1/layer/layer.js" /}
</head>
<body onkeydown="keyEnter()">
<style type="text/css">
.tiyan{ text-align: center;font-size: 14px;color: #000;position: relative;top: 50px;z-index: 9;display: none; }
.tiyan a{ color: red;font-size: 16px; }
</style>
<!--[if IE]>
<style type="text/css">
.tiyan{ display: block; }
</style>
<![endif]-->
    
    <div class="tiyan">为了更好体验，请使用<a href="/static/img/A/jisu.png" target="_blank">极速模式</a>或<a href="/static/img/A/guge.png" target="_blank">谷歌浏览器！</a></div>

	<div id="LAY_app" class="layadmin-tabspage-none" >
		
		<div class="layadmin-user-login layadmin-user-display-show" id="LAY-user-login">
		    <div class="layadmin-user-login-main">
		        <div class="layadmin-user-login-box layadmin-user-login-header">
		            <h2>后台管理系统 - Login</h2>
		        </div>
		        <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
		            <div class="layui-form-item">
		                <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="LAY-user-login-username"></label>
		                <input type="text" name="username" id="LAY-user-login-username" lay-verify="required" placeholder="账号" class="layui-input">
		            </div>

		            <div class="layui-form-item">
		                <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
		                <input type="password" name="password" id="LAY-user-login-password" lay-verify="required" placeholder="密码" class="layui-input">
		            </div>
		            		<div class="layui-form-item">
						<label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
						<input type="text" name="code" id="LAY-user-login-gg-code" lay-verify="required" placeholder="谷歌验证码" class="layui-input">
					</div>

		            <div class="layui-form-item">
		                <div class="layui-row">
		                    <div class="layui-col-xs7">
		                        <label class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="LAY-user-login-vercode"></label>
		                        <input type="text" name="vercode" id="LAY-user-login-vercode" maxlength="4" lay-verify="required" placeholder="图形验证码" class="layui-input">
		                    </div>
		                    <div class="layui-col-xs5">
		                        <div style="margin-left: 10px;">
		                            <img src="{:captcha_src()}" onclick="this.src='{:captcha_src()}'+'?cd='+Math.random()" alt="captcha" class="layadmin-user-login-codeimg" id="txcode">
		                        </div>
		                    </div>
		                </div>
		            </div>

		            <div class="layui-form-item" style="margin-bottom: 20px;">
		                
		            </div>

		            <div class="layui-form-item">
		                <button class="layui-btn layui-btn-fluid" lay-submit="" lay-filter="LAY-user-login-submit" onclick="sub()">登 入</button>
		            </div>

		        </div>
		    </div>
		    <div class="layui-trans layadmin-user-login-footer">
		        <p>
		        	© 2019
		            <a href="">Powered by layui.com！</a></p>
		        <!-- <p>
		            <span>
		                <a href="">获取授权</a>
		            </span>
		            <span>
		                <a href="" >在线演示</a>
		            </span>
		            <span>
		                <a href="">前往官网</a>
		            </span>
		        </p> -->
		    </div>
		</div>

	</div>
</body>
<script type="text/javascript">
function keyEnter(){
	if (event.keyCode==13){
		sub()
	}
}
function sub(){
	var uname = $('input[name=username]').val();
	var passwd = $('input[name=password]').val();
	var vcode = $('input[name=vercode]').val();
	var code = $('input[name=code]').val();
	var namepat = /^[\da-zA-Z]{2,16}$/;
    if( !namepat.test(uname) ){
        return layer.msg('账号格式不正确！',{icon:5});
    }
    if( !((passwd.length>=5)&&(passwd.length<=16)) ){
    	return layer.msg('输入密码不能小于5位数或大于16位数',{icon:5});
    }
    if( vcode.length !=4 ){
    	return layer.msg('图形验证码格式不正确！',{icon:5});
    }
    var la_load = layer.load(0,{
    			shade: [0.15,'#000']
    		});
	$.ajax({
        url: '{:url("Common/index")}'+"?"+"t="+Math.random(),
        type: "post",
        dataType: "json",
        cache: false,
        data: {uname:uname,passwd:passwd,vcode:vcode,code:code},
        success: function(d,e,x){  
            if(d.status==1){
                layer.msg( d.message,{icon:1} );
                var t1 = setTimeout(function(){
                        layer.close(la_load);
                        // window.location.href = member_index;//页面刷新
                        window.location.href = "{:url('/index')}";
                    },1500);
            }else{
                layer.close(la_load);
            	$("#txcode").trigger("click")
            	return layer.msg(d.message,{icon:2});
            }
        }
    });
}



$(function(){

	if( IEVersion()!==false ){
		$('.tiyan').show();
	}
})
</script>
</html>