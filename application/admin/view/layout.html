<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"> -->
	<title>{$global.web_name} - 配资管理后台</title>

	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	
	{load href="/static/layui-v2.4.5/layui/css/layui.css" /}

	{load href="/static/js/jquery183.js" /}
	{load href="/static/css/A/style.css" /}
	{load href="/static/layer-v3.1.1/layer/layer.js" /}
	{load href="/static/js/A/admin.js" /}

	{load href="/static/css/A/layui-global.css" /}
</head>

<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
  	<div class="layui-header">
	    <div class="layui-logo"><b>配资管理后台</b></div>

	    <ul class="layui-nav layui-layout-left" id="topMenus">
			{volist name="menu" id="vo"}
				<li class="layui-nav-item {eq name='$vo.bind' value='$controllerName'}active{/eq}">
					<a href="javascript:;" onclick="setMenu( {$key} )">
						{$vo.module}

						{if condition="$key eq 0"}
							<span class="layui-badge tip" 
									style='margin: -15px 28px 0;
										    line-height: 16px;
										    padding: 0px 3px 0 2px;
										    width: auto;
										    height: 15px;
										    display: none;'>0</span>
						{/if}
					</a>
				</li>
			{/volist}

			<!-- <li class="layui-nav-item"><a href="">aaa</a></li>
			<li class="layui-nav-item">
				<a href="javascript:;">bbb</a>
				<dl class="layui-nav-child">
					<dd><a href="">bbba</a></dd>
				</dl>
			</li> -->
	    </ul>
	    <ul class="layui-nav layui-layout-right">
			<li class="layui-nav-item"><a href="/" target="_blank">查看前台</a></li>
			<li class="layui-nav-item"><a href="{:url('index/clearAll')}">清空缓存</a></li>
			<li class="layui-nav-item"><a href="javascript:;" onclick="innerMsg()">通讯系统</a></li>
			<li class="layui-nav-item"><a href="javascript:;" onclick="verifyIp()">设置白名单</a></li>

			<li class="layui-nav-item">
				<a href="javascript:;">
				<img src="/static/img/A/u2345.jpg" class="layui-nav-img">
				{$admin_name}
				</a>
				<dl class="layui-nav-child">
					<dd><a href="{:url('index/editAdmin','action=edit')}">编辑资料</a></dd>
					<dd><a href="{:url('common/logout')}">退出</a></dd>
				</dl>
			</li>
	    </ul>
  	</div>
  
	<div class="layui-side layui-bg-black">
		<div class="layui-side-scroll">
			<!-- 左侧导航区域（可配合layui已有的垂直导航） -->
			
			{include file="public/menu" /}

		</div>
	</div>
  
	<div class="layui-body">
		<!-- 内容主体区域 -->
		<div style="padding: 9px;">

			{__CONTENT__}

		</div>
	</div>
  
	<!-- <div class="layui-footer">
		© layui.com - 底部固定区域
	</div> -->
</div>

<audio src="/static/tip.mp3" id='tipmp3' style='display:none;'></audio>

{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">
//JavaScript代码区域
layui.use('element', function(){
	var element = layui.element;
});

//当前页的刷新
function reloadData(){
    location.reload();
}

// 菜单
var menuStr = '{$menuJson}';
function setMenu(key){
	eval("var menu =" + menuStr);
	var menuObj = menu[key]['info'];
	// console.log( menuObj );
	var tempUrl = menuObj[0]['list'][0][1];
	window.location.href = tempUrl;
}


$(function(){
	// 消息提醒
	var time = 30;//间隔时间
	tipIntervel = setInterval("tip()", time*1000);
})
function tip(){
    // clearInterval(tipIntervel);
	$.ajax({
		url:'{:url("index/getTip")}'+'?tip='+Math.random(),
      	type: 'post',
      	// data: '',
        dataType: "json",
      	success:function(d){
          	console.log(d.message);
        	if(d.status==1){ 
          		$('.tip').show().text(d.count);
          		playmp3();
         	}else{
          		$('.tip').hide();
         	} 
      	}

    });
}
function playmp3(){
	document.getElementById("tipmp3").play();
}

// 站内信
function innerMsg(){
	layer.open({
		type: 2,
		title: '通讯系统',
		maxmin: false,
		shadeClose: false, //点击遮罩关闭层
		area : ['600px' , '427px'],
		content: '{:url("index/innerMsg")}',
		end: function(){
			// location.reload();
		}
    });
}
// 设置白名单
function verifyIp(){
	layer.open({
		type: 2,
		title: '设置白名单',
		maxmin: false,
		shadeClose: false, //点击遮罩关闭层
		area : ['520px' , '425px'],
		content: '{:url("Index/verifyIp1")}',
		end: function(){
			// location.reload();
		}
    });
}

layui.use('form', function(){
	var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
	//……
	//但是，如果你的HTML是动态生成的，自动渲染就会失效
	//因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
	form.render();
});  
</script>
</body>
</html>