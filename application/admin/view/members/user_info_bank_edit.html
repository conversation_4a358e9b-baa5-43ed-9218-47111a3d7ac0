{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
    .layui-form-label{
        width: 100px;
        padding: 9px 6px;
    }
    .details{
        width: 240px;
        float: left;
        line-height: 23px;
        color: #333;
        font-size: 14px;
    }

</style>

<div class="layui-main" style="padding: 21px 0;">
    <form class="layui-form" onsubmit="return false;">

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">银行名称：</label>
            <div class="layui-input-inline w333px">
                <input name="bank_name"  class="layui-input" value="{$data.bank_name}"></input>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">银行简称：</label>
            <div class="layui-input-inline w333px">
                <input name="bank_code"  class="layui-input" value="{$data.bank_code}"></input>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">开户行：</label>
            <div class="layui-input-inline w333px">
                <input name="bank_address"  class="layui-input" value="{$data.bank_address}"></input>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">银行卡：</label>
            <div class="layui-input-inline w333px">
                <input name="number"  class="layui-input" value="{$data.number}"></input>
            </div>
        </div>
       <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">开户省：</label>
            <div class="layui-input-inline w333px">
                <input name="province"  class="layui-input" value="{$data.province}"></input>
            </div>
        </div>
       <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">开户市：</label>
            <div class="layui-input-inline w333px">
                <input name="city"  class="layui-input" value="{$data.city}"></input>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 112px;">
                <button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="sub()" lay-filter="formDemo">确认提交</button>
            </div>
        </div>

    </form>
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">
    var id = parseInt("{$id}");
    function sub(){
        // var status = $("input[name=status]:checked").val();
        var bank_name = $('input[name=bank_name]').val();
        var bank_code = $('input[name=bank_code]').val();
        var bank_address = $('input[name=bank_address]').val();
        var number = $('input[name=number]').val();
        var province = $('input[name=province]').val();
        var city = $('input[name=city]').val();
        if( !id ){
            return layer.msg('id不存在，请按规则重试！',{anim : 6});
        }
        var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
        $.ajax({
            url: '{:url("members/userInfoBankEdit")}',
            type: "POST",
            dataType: "json",
            data: {id:id,bank_name:bank_name,bank_code:bank_code,bank_address:bank_address,number:number,province:province,city:city},
            success: function(d,e,x){
                if(d.status==1){
                    layer.msg( d.message ,{icon : 1} );
                    // var index = parent.layer.getFrameIndex(window.name);
                    setTimeout(function(){
                        // parent.layer.close(index);
                        parent.reloadData();
                    }, 1000);
                }else{
                    layer.close(la_load);
                    return layer.msg( d.message,{icon : 2} );
                }
            }
        });

    }

    layui.use('form', function(){
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        //但是，如果你的HTML是动态生成的，自动渲染就会失效
        //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
        form.render();
    });
</script>
