{layout name="layout" /}
<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>会员</a>
		<a>会员管理</a>
		<a><cite>修改信息</cite></a>
	</span>
</div>
<style type="text/css">
.layui-form-item .layui-input-inline { width: 380px; }
</style>

<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief" style="margin-top: 0;">
	<ul class="layui-tab-title">
		<li class="layui-this">基本资料</li>
		<li>实名认证</li>
		<li>银行卡信息</li>
	</ul>

	<div class="layui-tab-content">
		<div class="layui-tab-item layui-show">
			<!-- 基本资料 -->
			<form class="layui-form" action="{:url('members/edit')}" method="post" onsubmit="return checkSub()">
				<div class="layui-form-item">
					<label class="layui-form-label">用户名</label>
					<div class="layui-input-inline">
						<input type="text" name="user_name" value="{$info.user_name}" disabled="" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input disabled">
					</div>
					<div class="layui-form-mid layui-word-aux">用户名不可更改</div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">手机号</label>
					<div class="layui-input-inline">
						<input type="text" name="user_phone" value="{$info.phone}" lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
					<div class="layui-form-mid layui-word-aux"></div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">登录密码</label>
					<div class="layui-input-inline">
						<input type="text" name="user_pass" value="" lay-verify="" placeholder="不修改请留空。修改时必填，6-20位" autocomplete="off" class="layui-input">
					</div>
					<div class="layui-form-mid layui-word-aux"></div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">支付密码</label>
					<div class="layui-input-inline">
						<input type="text" name="pin_pass" value="" lay-verify="" placeholder="不修改请留空。修改时必填，6-20位" autocomplete="off" class="layui-input">
					</div>
					<div class="layui-form-mid layui-word-aux"></div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">登录状态</label>
					<div class="layui-input-block">
						<input type="radio" name="is_ban" value="0" {neq name="info.is_ban" value="1"}checked{/neq} title="正常">
						<input type="radio" name="is_ban" value="1" {eq name="info.is_ban" value="1"}checked{/eq} title="禁用">
					</div>
				</div>
				{:token()}
				<input type="hidden" name="uid" value="{$uid}">
				<input type="hidden" name="action" value="1">
				<div class="layui-form-item">
					<label class="layui-form-label"></label>
			    	<button class="layui-btn" lay-submit="" lay-filter="">确认提交</button>
			  		<a href="{$back_url}" class="layui-btn layui-btn-primary">返回列表</a>
			  	</div>
			</form>
		</div>
		<div class="layui-tab-item">
			<!-- 实名认证 -->
			<form class="layui-form" action="{:url('members/edit')}" method="post" onsubmit="return checkSub()">
				<div class="layui-form-item">
					<label class="layui-form-label">真实姓名</label>
					<div class="layui-input-inline">
						<input type="text" name="real_name" value="{$info.real_name|default=''}" lay-verify="" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
					<div class="layui-form-mid layui-word-aux"></div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">身份证号</label>
					<div class="layui-input-inline">
						<input type="text" name="id_card" value="{$info.id_card|default=''}" lay-verify="" placeholder="请输入" autocomplete="off" class="layui-input">
					</div>
					<div class="layui-form-mid layui-word-aux"></div>
				</div>
				<div class="layui-form-item">
					<label class="layui-form-label">实名状态</label>
					<div class="layui-input-block">
						<input type="radio" name="id_status" value="1" {eq name="info.id_status" value="1"}checked{/eq} title="是">
						<input type="radio" name="id_status" value="0" {neq name="info.id_status" value="1"}checked{/neq} title="否">
					</div>
				</div>

				{:token()}
				<input type="hidden" name="uid" value="{$uid}">
				<input type="hidden" name="action" value="2">
				<div class="layui-form-item">
					<label class="layui-form-label"></label>
			    	<button class="layui-btn" lay-submit="" lay-filter="">确认提交</button>
			    	<a href="{$back_url}" class="layui-btn layui-btn-primary">返回列表</a>
			  	</div>
			</form>

		</div> 
		<div class="layui-tab-item">
			<!-- 银行卡信息 -->
			<table class="layui-table" lay-even="" lay-skin="nob">
				<thead>
					<tr>
						<th>编号</th>
						<th>银行卡号</th>
						<th>银行</th>
						<th>所在地</th>
						<th>开户行</th>
						<th>添加时间</th>
						<th>操作</th>
					</tr> 
				</thead>
				<tbody>
					{volist name="bank" id="vo" empty="$empty_data"}
					<tr class="list_{$vo.id}">
						<td>{$vo.id}</td>
						<td>{$vo.number}</td>
						<td>{$vo.bank_name}</td>
						<td>{$vo.province}{$vo.city}</td>
						<td>{$vo.bank_address}</td>
						<td>{:date('Y-m-d H:i',$vo.add_time)}</td>
						<td>
							<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
						</td>
					</tr>
					{/volist}
	  			</tbody>
			</table>
		</div>
	</div> 
</div>




<script type="text/javascript">
function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除银行卡',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('members/delBank')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( '删除成功' ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}


</script>