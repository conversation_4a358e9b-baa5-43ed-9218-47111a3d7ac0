{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>会员</a>
		<a>代理管理</a>
		<a><cite>代理列表</cite></a>
	</span>
</div>

<style type="text/css">
.ymtd{
	max-width: 200px !important;
	word-wrap:break-word !important; 
    overflow: hidden !important;
    padding: 8px 2px !important;
}
</style>
<form class="layui-form layui-form-pane" action="{:url('members/daili')}" id='subform'>
	<div class="layui-form-item">
	    <label class="layui-form-label">搜索用户名</label>
	    <div class="layui-input-inline" style="margin-left: -12px;">
			<input type="text" name="uname" value="{$uname|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-inline" style="margin-left: 10px;">
			<label class="layui-form-label" label2-title>显示行数</label>
			<div class="layui-input-inline" style="width:80px;">
				<select name="page_num" lay-filter="test" id="page_num">
					<option value="10" {eq name="page_num" value="10"}selected{/eq}> 10</option>
					<option value="20" {eq name="page_num" value="20"}selected{/eq}> 20</option>
					<option value="30" {eq name="page_num" value="30"}selected{/eq}> 30</option>
					<option value="40" {eq name="page_num" value="40"}selected{/eq}> 40</option>
					<option value="50" {eq name="page_num" value="50"}selected{/eq}> 50</option>
					<option value="100" {eq name="page_num" value="100"}selected{/eq}> 100</option>
					<option value="200" {eq name="page_num" value="200"}selected{/eq}> 200</option>
					<option value="300" {eq name="page_num" value="300"}selected{/eq}> 300</option>
					<option value="400" {eq name="page_num" value="400"}selected{/eq}> 400</option>
					<option value="500" {eq name="page_num" value="500"}selected{/eq}> 500</option>
				</select>
			</div>
			<!--<input type="submit" value="刷新" class="layui-btn layui-btn-primary"/>-->
		</div>

	</div>
</form>

<div class="layui-form form-border" style="overflow-x: scroll;">
	<table class="layui-table" lay-skin="line" style="min-width: 1850px;">
		<thead>
			<tr>
				<th style="">编号</th>
				<th style="">用户名</th>
				<th style="">真实姓名</th>
				<th style="">代理下线会员数量</th>
				<th style="">今日新增会员</th>
				<th style="">下线配资总额</th>
				<th style="">获得配资返利总额</th>
				<th style="">下线线下充值总额</th>
				<th style="">获得线下充值返利总额</th>
				<th style="">下线提现总额</th>
				<th style="">返佣比例</th>
				<th style="">代理推广域名</th>
				<th style="">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
				<tr class="list_{$vo.id}">
					<td>{$vo.id}</td>
					<td>
						<a href="javascript:;" onclick="infoCard( {$vo.id} )" class="infocard">{$vo.user_name}</a>
					</td>
					<td>{$vo.real_name|default='未实名'}</td>
					<td>{$vo.huiyuan_zong}</td>
					<td>{$vo.jinri_zhuce}</td>
					<td>{$vo.peizi_zong/100}</td>
					<td>{$vo.pz_fanli/100}</td>
					<td>{$vo.chongzhi_zong/100}</td>
					<td>{$vo.cz_fanli/100}</td>
					<td>{$vo.tixian_zong/100}</td>
					<td>
						管理费的{$vo.czpz_bl.1}%
						充值金额{$vo.czpz_bl.0}%
					</td>
					<td class="ymtd" title="{$vo.dl_yuming|default='未设置'}">{$vo.dl_yuming|default='未设置'}</td>
					<td>
						<a href="javascript:;" onclick="addIframe( '{:url('members/ckxx',['rid'=>$vo.id])}' )" class="layui-bg-green ft15">查看下线</a>
					</td>
				</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>



<script type="text/javascript">
function addIframe(url){
    layer.open({
        type: 2,
        title: '查看下线',
        maxmin: false,
        shadeClose: false,
        area : ['700px' , '505px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}

$('input[name=uname]').keypress(function (e) {
    if( e.which==13 ){
        
        $('#subform').submit();
    }
});

$(function(){
	layui.use('form', function(){
		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
		form.on('select(test)', function(data){
			$('#subform').submit();
		});
	});
})

</script>