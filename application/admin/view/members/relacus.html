{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}
<style type="text/css">
.layui-tab{
    margin: 0;
}
.layui-form-label{
    width: 86px;
    padding: 9px 6px;
}
.details{
    width: 40%;
    float: left;
    /*line-height: 23px;*/
    padding: 6px 5%;
    color: #333;
    font-size: 14px;
}
.layui-form-item{
    padding-bottom: 6px;
    margin-bottom: 8px;
    border-bottom: 1px dashed #e2e2e2;
}
.flspan{
    width: 86px;display: inline-block;text-align: right;
}
.layui-table tr{
    border-bottom: 1px dashed #e2e2e2;
}
</style>

<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <ul class="layui-tab-title">
        <li class="layui-this">关联客户明细</li>
    </ul>
    <div class="layui-tab-content">
<!--        <div class="layui-tab-item">-->

            <table class="layui-table" lay-skin="nob">
                <tr>
                    <th>用户名</th>
                    <th>真实姓名</th>
                </tr>
                {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.user_name}</td>
                    <td>{$vo.real_name}</td>
                </tr>
                {/volist}
            </table>

<!--        </div>-->

    </div>
</div> 



<script type="text/javascript">
layui.use('element', function(){
  var $ = layui.jquery
  ,element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
});
</script>