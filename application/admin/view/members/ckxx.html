{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}
<style type="text/css">
.layui-tab{
    margin: 0;
}
.layui-form-label{
    width: 86px;
    padding: 9px 6px;
}
.layui-table tr{
    border-bottom: 1px dashed #e2e2e2;
}
.layui-table td, .layui-table th{
    padding: 9px;
}
.empty_data {
    border: none;
}
.empty_data td{
    padding: 88px 0;
}
</style>


    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <table class="layui-table" lay-skin="nob">
                <tr>
                    <th>编号</th>
                    <th>用户名</th>
                    <!-- <th>真实姓名</th> -->
                    <th>注册时间</th>
                    <th>注册地址</th>
                    <th>上次登录时间</th>
                    <th style="width:12%;">操作</th>
                </tr>
                {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>{$vo.user_name}</td>
                    <!-- <td>{$vo.real_name|default='未实名'}</td> -->
                    <td>{:date('Y-m-d H:i', $vo.reg_time)}</td>
                    <td>{$vo.reg_domain}</td>
                    <td>{:date('Y-m-d H:i', $vo.last_log_time)}</td>
                    <td>
                        <a href="{:url('members/ckqk',['uid'=>$vo.id])}" style="line-height: 16px;" class="layui-bg-green ft15">充值配资情况</a>
                    </td>
                </tr>
                {/volist}
            </table>

            <div class="pager2" style="">{$page}</div>
        </div>

    </div>



<script type="text/javascript">
layui.use('element', function(){
  var $ = layui.jquery
  ,element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
});
</script>