{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}
<style type="text/css">
.layui-tab {
    margin: 5px 0;
}
.layui-form-t1{
    padding-top: 6px;
    padding-bottom: 10px;
    margin-bottom: 8px;
    border-bottom: 1px dashed #e2e2e2;
}
.label2-title {
    width: 100px;
    text-align: left;
    padding: 9px 0 0 10px;
}
.layui-textarea{
    min-height: 65px;
    font-family: MONOSPACE;
}
.daili-tip{
    padding: 3px 0 0 !important;
    width: 230px;
    margin-right: 0;
    font-family: MONOSPACE;
    font-size: 12.5px;
}
</style>

<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <ul class="layui-tab-title">
        <li class="layui-this">设置代理</li>
        <li>设置上级</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <div class="layui-main" style="">
                <div class="layui-form-item layui-form-text layui-form-t1" style="">
                    <p>设置{$minfo.user_name}为代理人</p>
                </div>
            </div>
            
            <form class="layui-form" >
                <div class="layui-form-item">
                    <label class="layui-form-label label2-title">设置代理人：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="是" {if condition="$minfo.user_type EQ $DAILIREN_TYPE"}checked{/if} >
                        <input type="radio" name="status" value="-1" title="否" {if condition="$minfo.user_type NEQ $DAILIREN_TYPE"}checked{/if}>
                    </div>
                </div>

                <div class="layui-main" style="">
                    <div class="layui-form-item layui-form-text" style="">
                        <p>设置返佣比例给代理人：</p>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label label2-title">配资比例：</label>
                    <div class="layui-input-inline ">
                        <input type="text" name="peizi" value="{$bili.1|default='50'}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">单位（%）</div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label label2-title">充值比例：</label>
                    <div class="layui-input-inline ">
                        <input type="text" name="chongzhi" value="{$bili.0|default='0'}" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">单位（%）</div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label label2-title">设置代理域名：</label>
                    <div class="layui-input-inline " style="width:282px;">
                        <textarea name="yuming" placeholder="注意域名书写格式，例：www.baidu.com 使用域名全称，否则无效！没有为空不填" class="layui-textarea" >{$minfo.dl_yuming|default=''}</textarea>
                    </div>
                    <div class="layui-form-mid layui-word-aux daili-tip">
                        多条代理域名使用英文逗号“,”隔开
                        例：http://www.baidu.com,http://w
                        ww.sina.com.cn
                    </div>

                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" lay-submit="" onclick="shezhi()">
                            确定提交
                        </button>
                    </div>
                </div>
            </form>
        </div>


        <div class="layui-tab-item">
            <div class="layui-main" style="">
                <div class="layui-form-item layui-form-text layui-form-t1" style="">
                    <p>设置{$minfo.user_name}的上级代理人</p>
                </div>
            </div>
            
            <br>

            <form class="layui-form" >
                <div class="layui-form-item" >
                    <div class="layui-inline">
                        <label class="layui-form-label label2-title">选择上级代理：</label>
                        <div class="layui-input-inline">
                            <select name="sj" lay-verify="" lay-search="">
                                <option value="" selected >选择代理人</option>
                                {volist name="dailiren" id="vo"}
                                <option value="{$vo.id}" {if condition="$minfo.recommend_id EQ $vo.id"}selected{/if} >{$vo.user_name}</option>
                                {/volist}
                            </select>
                        </div>
                        <div class="layui-form-mid layui-word-aux "><span style='color:red;'></span></div>
                    </div>
                </div>
                
                <br><br><br>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" lay-submit="" onclick="shangji()">
                            确定提交
                        </button>
                    </div>
                </div>
            </form>
        </div>

    </div>
</div> 



<script type="text/javascript">
var uid = parseInt("{$uid}");
function shezhi(){
    var status = $('input[name=status]:checked').val();
    var peizi = $('input[name=peizi]').val();
    var chongzhi = $('input[name=chongzhi]').val();
    var yuming = $('textarea[name=yuming]').val();
    if( !status || !peizi || !chongzhi ){
        return layer.msg('数据填写不完整！',{anim : 6});
    }

    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("members/shezhidaili")}',
        type: "POST",
        dataType: "json",
        data: {uid:uid,status:status,peizi:peizi,chongzhi:chongzhi,yuming:yuming,action:'shezhi'},
        success: function(d,e,x){
            // console.log(d,e,x);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){ 
                    // parent.layer.close(index);
                    parent.reloadData();
                }, 1000);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2} );
            }
        }
    });
}

function shangji(){
    var sj_uid = $('select[name=sj]').val();

    // if( !sj_uid ){
    //     return layer.msg('请选择上级代理人',{anim : 6});
    // }
    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("members/shezhidaili")}',
        type: "POST",
        dataType: "json",
        data: {uid:uid,sj_uid:sj_uid,action:'shangji'},
        success: function(d,e,x){
            // console.log(d,e,x);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){ 
                    // parent.layer.close(index);
                    parent.reloadData();
                }, 1000);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2} );
            }
        }
    });
}


layui.use('element', function(){
    var $ = layui.jquery
    ,element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
});
layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();
});
</script>