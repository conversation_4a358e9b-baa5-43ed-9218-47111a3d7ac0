{layout name="layout" /}
{load href="/static/js/jquery.sortElements.js" /}
<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>会员</a>
		<a>会员管理</a>
		<a><cite>登录IP统计</cite></a>
	</span>
</div>

<form class="layui-form layui-form-pane" action="{:url('members/ipsummary')}" id='subform'>
	<div class="layui-form-item">
	    <!-- <label class="layui-form-label">搜索用户名</label> -->
	    <div class="layui-input-inline" style="width: 113px;">
	        <select name="action" lay-verify="required" lay-search="">
	            <option value="ip_address" {eq name="action" value="ip_address"}selected{/eq}>搜索IP</option>
	        </select>
	    </div>

		<div class="layui-input-inline" style="margin-left: -12px;">
			<input type="text" name="name" value="{$name|default=''}" placeholder="请输入IP地址" lay-verify="" autocomplete="off" class="layui-input">
		</div>

		<div class="layui-inline" style="position: absolute;right: 0px;">
			<label class="layui-form-label" label2-title>显示行数</label>
			<div class="layui-input-inline" style="width:80px;">
				<select name="page_num" lay-filter="test" id="page_num">
					<option value="10" {eq name="page_num" value="10"}selected{/eq}> 10</option>
					<option value="20" {eq name="page_num" value="20"}selected{/eq}> 20</option>
					<option value="30" {eq name="page_num" value="30"}selected{/eq}> 30</option>
					<option value="40" {eq name="page_num" value="40"}selected{/eq}> 40</option>
					<option value="50" {eq name="page_num" value="50"}selected{/eq}> 50</option>
					<option value="100" {eq name="page_num" value="100"}selected{/eq}> 100</option>
					<option value="200" {eq name="page_num" value="200"}selected{/eq}> 200</option>
					<option value="300" {eq name="page_num" value="300"}selected{/eq}> 300</option>
					<option value="400" {eq name="page_num" value="400"}selected{/eq}> 400</option>
					<option value="500" {eq name="page_num" value="500"}selected{/eq}> 500</option>
				</select>
			</div>
			<!--<input type="submit" value="刷新" class="layui-btn layui-btn-primary"/>-->
		</div>
	</div>
</form>

<div class="layui-form form-border" style="overflow-x: scroll;">
	<table class="layui-table" lay-skin="line" style="min-width: 1760px;" id ='mytable'>
		<thead>
			<tr>
				<th style="">登录IP</th>
				<th style="">登录次数</th>
				<th style="">关联客户数</th>
				<th style="width:18%;">操作</th>
			</tr>
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
				<tr>
					<td>{$vo.ip}</td>
					<td>{$vo.log_num}</td>
					<td>
						<a href="javascript:;" onclick="relaCus('{$vo.ip}')" class="infocard">{$vo.cum_num}</a>
					</td>
					<td>
						<a href="javascript:;" onclick="details('{$vo.ip}')" class="layui-bg-green ft15">详情</a>
					</td>
				</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
	$(document).ready(function(){
		var table = $('#mytable');//table的id
		$('#sort_header,#secode222,#dongjiezijin')//要排序的headerid
		// $//要排序的headerid
				.each(function(){
					var th = $(this),
							thIndex = th.index(),
							inverse = false;

					th.click(function(){
						table.find('td').filter(function(){
							return $(this).index() === thIndex;
						}).sortElements(function(a, b){
							return $.text([a]) > $.text([b]) ?

									inverse ? -1 : 1
									: inverse ?

											1 : -1;
						}, function(){
							return this.parentNode;
						});
						inverse = !inverse;

					});
				});
	});

	function relaCus(ip){
		if(!ip){
			return false;
		}
		layer.open({
			type: 2,
			title: '客户明细',
			maxmin: false,
			shadeClose: false,
			area : ['680px' , '487px'],
			content: '../members/relacus?ip='+ip,
			end: function(){
				// location.reload();
			}
		});
	}

	function details(ip){
		if(!ip){
			return false;
		}
		debugger
		layer.open({
			type: 2,
			title: '登录详情',
			maxmin: false,
			shadeClose: false,
			area : ['680px' , '487px'],
			content: '../members/details?ip='+ip,
			end: function(){
				// location.reload();
			}
		});
	}

$('input[name=name]').keypress(function (e) {
    if( e.which==13 ){

        $('#subform').submit();
    }
});

function submitForm(){
	$('#subform').submit();
}


$(function(){
	layui.use('form', function(){
		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
		form.on('select(test)', function(data){
			$('#subform').submit();
		});
	});
})

$(function(){
	layui.use('form', function(){
		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
		form.on('select(test)', function(data){
			$('#subform').submit();
		});
	});
})


</script>
