{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}
<style type="text/css">
.layui-tab{
    margin: 0;
}
.layui-form-label{
    width: 86px;
    padding: 9px 6px;
}
.details{
    width: 40%;
    float: left;
    /*line-height: 23px;*/
    padding: 6px 5%;
    color: #333;
    font-size: 14px;
}
.layui-form-item{
    padding-bottom: 6px;
    margin-bottom: 8px;
    border-bottom: 1px dashed #e2e2e2;
}
.flspan{
    width: 86px;display: inline-block;text-align: right;
}
.layui-table tr{
    border-bottom: 1px dashed #e2e2e2;
}
</style>

<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
    <ul class="layui-tab-title">
        <li class="layui-this">基本信息</li>
        <li>资金信息</li>
        <li>银行卡信息</li>
        <li>登录记录</li>
        <li>关联账户</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">

            <div class="layui-main" style="margin: 0;">
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>会员：</span>{$data.user_name}
                        </p>
                    </div>
                    <div class="details">
                        <p>
                            <span class='flspan'>真实姓名：</span>{$data.real_name|default='未实名'}
                        </p>
                    </div>
                </div>
        
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>手机号码：</span>{$data.phone}
                        </p>
                    </div>

                    <div class="details">
                        <p>
                            <span class='flspan'>登录状态：</span>
                            {eq name="data.is_ban" value="1"}
                            禁用
                            {else /}
                            正常
                            {/eq}
                        </p>
                    </div>
                    
                </div>

                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>实名认证：</span>
                            {eq name="data.id_status" value="1"}已实名{else/}未实名{/eq}
                        </p>
                    </div>
                    <div class="details">
                        <p>
                            <span class='flspan'>绑定银行卡：</span>
                            {if condition="$bank"}
                            已绑定
                            {else /}
                            未绑定
                            {/if}
                        </p>
                    </div>
                </div>
                
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>注册时间：</span>{:date('Y-m-d H:i',$data.reg_time)}
                        </p>
                    </div>

                    <div class="details">
                        <p>
                            <span class='flspan'>身份证信息：</span>
                            {$data.id_card}
                        </p>
                    </div>
                    
                </div>
                
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>推荐人：</span>{:get_user_value($data.recommend_id)}
                        </p>
                    </div>

                    <div class="details">
                        <p>
                            <span class='flspan'></span>
                           
                        </p>
                    </div>
                    
                </div>

                

            </div>

        </div>


        <div class="layui-tab-item">
            
            <div class="layui-main" style="margin: 0;">
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>总资产：</span>
                            {php}
                                echo number_format( ($data['account_money']+$data['money_freeze']+$data['interest_money'])/100 , 2 )
                            {/php}
                        </p>
                    </div>
                    <div class="details">
                        <p>&nbsp;</p>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>账户余额：</span>
                            {:number_format($data.account_money/100,2)}
                        </p>
                    </div>
                    <div class="details">
                        <p>
                            <span class='flspan'>冻结资金：</span>
                            {:number_format($data.money_freeze/100,2)}
                        </p>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>赠送管理费：</span>
                            {:number_format($data.interest_money/100,2)}
                        </p>
                    </div>
                    <div class="details">
                        <p><span class='flspan'>保证金：</span>
                            {:number_format($baozhengjin/100,2)}
                        </p>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>充值成功：</span>
                            {$recharge_count1}次
                        </p>
                    </div>
                    <div class="details">
                        <p>
                            <span class='flspan'>充值金额：</span>
                            {$recharge_count2/100}
                        </p>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text" style="">
                    <div class="details">
                        <p>
                            <span class='flspan'>提现成功：</span>
                            {$withdraw_count1}次
                        </p>
                    </div>
                    <div class="details">
                        <p>
                            <span class='flspan'>提现金额：</span>
                            {$withdraw_count2/100}
                        </p>
                    </div>
                </div>
            </div>

        </div>

        <div class="layui-tab-item">
            
            <table class="layui-table" lay-skin="nob">
                <tr>
                    <th>银行卡号</th>
                    <th>银行</th>
                    <th>所在地</th>
                    <th>开户行</th>
                </tr>
                {volist name="bank" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.number}</td>
                    <td>{$vo.bank_name}</td>
                    <td>{$vo.bank_address}</td>
                    <td>{$vo.province}{$vo.city}</td>
                </tr>
                {/volist}
            </table>

        </div>

        <div class="layui-tab-item">
            
            <table class="layui-table" lay-skin="nob">
                <tr>
                    <th>登录IP</th>
                    <th>登录地址</th>
                    <th>登录时间</th>
                </tr>
                {volist name="login" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.ip}</td>
                    <td>{$vo.domain}</td>
                    <td>{$vo.create_time}</td>
                </tr>
                {/volist}
            </table>

        </div>
        
        <div class="layui-tab-item">

            <table class="layui-table" lay-skin="nob">
                <tr>
                    <th>用户名</th>
                    <th>真实姓名</th>
                    <th>共同IP数量</th>
                </tr>
                {volist name="ip_count" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.user_name}</td>
                    <td>{$vo.real_name}</td>
                    <td>{$vo.num}</td>
                </tr>
                {/volist}
            </table>

        </div>

    </div>
</div> 



<script type="text/javascript">
layui.use('element', function(){
  var $ = layui.jquery
  ,element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
});
</script>