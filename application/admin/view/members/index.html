{layout name="layout" /}
{load href="/static/js/jquery.sortElements.js" /}
<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>会员</a>
		<a>会员管理</a>
		<a><cite>会员列表</cite></a>
	</span>
</div>

<form class="layui-form layui-form-pane" action="{:url('members/index')}" id='subform'>
	<div class="layui-form-item">
	    <!-- <label class="layui-form-label">搜索用户名</label> -->
	    <div class="layui-input-inline" style="width: 113px;">
	        <select name="action" lay-verify="required" lay-search="">
	            <option value="user_name" {eq name="action" value="user_name"}selected{/eq}>搜索用户名</option>
	            <option value="real_name" {eq name="action" value="real_name"}selected{/eq}>搜索真实姓名</option>
	            <option value="recommend" {eq name="action" value="recommend"}selected{/eq}>搜索推荐人</option>
	            <option value="domain" {eq name="action" value="domain"}selected{/eq}>搜索注册地址</option>
	        </select>
	    </div>
	    <div class="layui-input-inline" style="margin-left: -12px;">
			<input type="text" name="name" value="{$name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
	    </div>
		<div class="layui-input-inline" style="width: 140px;">
			<select  name="select" lay-verify="required" lay-search="">
				<option value="id" {eq name="select" value="id"}selected{/eq}>按编号排序</option>
				<option value="account_money" {eq name="select" value="account_money"}selected{/eq}>按账户余额排序</option>
				<option value="interest_money" {eq name="select" value="interest_money"}selected{/eq}>按管理费余额排序</option>
				<option value="money_freeze" {eq name="select" value="money_freeze"}selected{/eq}>按冻结资金排序</option>
				<option value="last_log_time" {eq name="select" value="last_log_time"}selected{/eq}>按上次登录时间排序</option>
			</select>
		</div>
		<div class="layui-input-inline" style="margin-left: -12px; width: 70px">
			<select onchange="submitForm();" name ="order" lay-filter="test">
				<option value="desc" {eq name="order" value="desc"}selected{/eq}>降序</option>
				<option value="asc" {eq name="order" value="asc"}selected{/eq}>升序</option>
			</select>
		</div>
		<div class="layui-inline" style="position: absolute;right: 0px;">
			<label class="layui-form-label" label2-title>显示行数</label>
			<div class="layui-input-inline" style="width:80px;">
				<select name="page_num" lay-filter="test" id="page_num">
					<option value="10" {eq name="page_num" value="10"}selected{/eq}> 10</option>
					<option value="20" {eq name="page_num" value="20"}selected{/eq}> 20</option>
					<option value="30" {eq name="page_num" value="30"}selected{/eq}> 30</option>
					<option value="40" {eq name="page_num" value="40"}selected{/eq}> 40</option>
					<option value="50" {eq name="page_num" value="50"}selected{/eq}> 50</option>
					<option value="100" {eq name="page_num" value="100"}selected{/eq}> 100</option>
					<option value="200" {eq name="page_num" value="200"}selected{/eq}> 200</option>
					<option value="300" {eq name="page_num" value="300"}selected{/eq}> 300</option>
					<option value="400" {eq name="page_num" value="400"}selected{/eq}> 400</option>
					<option value="500" {eq name="page_num" value="500"}selected{/eq}> 500</option>
				</select>
			</div>
			<!--<input type="submit" value="刷新" class="layui-btn layui-btn-primary"/>-->
		</div>
	</div>
</form>

<div class="layui-form form-border" style="overflow-x: scroll;">
	<table class="layui-table" lay-skin="line" style="min-width: 1810px;" id ='mytable'>
		<thead>
			<tr>
				<th style="">编号</th>
				<th style="">用户名</th>
				<th style="">真实姓名</th>
<!--				<th style="">实名认证</th>-->
<!--				<th style="">会员类型</th>-->
				<th style="">推荐人</th>
				<th style="" id ="sort_header">账户余额</th>
				<th style="" id ="secode222">管理费余额</th>
				<th style="" id="dongjiezijin">冻结资金</th>
				<th style="">最后登录网址</th>
				<th style="">注册IP</th>
				<th style="">注册来源</th>
				<th style="width:9%">上次登录时间</th>
				<th style="">注册时间</th>
				<th style="width:6%">黑名单状态</th>
				<th style="">操作</th>
			</tr>
		</thead>
		<tbody>
			{volist name="data" id="vo" empty="$empty_data"}
				<tr class="list_{$vo.id}">
					<td>{$vo.id}</td>
					<td>
						<a href="javascript:;" onclick="infoCard( {$vo.id} )" class="infocard">{$vo.user_name}</a>
					</td>
					<td>{$vo.real_name|default='  '}</td>
<!--					<td>{:str_replace([0,1,2], ['未认证','已认证','待审核'], $vo.id_status)}</td>-->
<!--					<td>{:str_replace([1,9], ['普通会员','平台代理人'], $vo.user_type)}</td>-->
					<td>
						<a href="javascript:;" onclick="infoCard( {$vo.recommend_id} )" class="infocard">
						{:get_user_value($vo.recommend_id)}
						</a>
					</td>
					<td>{$vo.account_money/100}</td>
					<td>{$vo.interest_money/100}</td>
					<td>{$vo.money_freeze/100}</td>
					<td>{$vo.domain}</td>
					<td>{$vo.reg_ip}</td>
					<td>{$vo.reg_domain}</td>
					<td>{:date('Y-m-d H:i',$vo.last_log_time)}</td>
					<td>{:date('Y-m-d H:i',$vo.reg_time)}</td>
					<td>{:str_replace([0,1], ['正常','黑名单'], $vo.is_blacklist)}</td>
					<td>
						<a href="{:url('members/edit',['uid'=>$vo.id])}" class="layui-bg-blue ft15">修改信息</a>
						&nbsp;
						<a href="javascript:;" onclick="addIframe( '设置代理','{:url('members/shezhidaili',['uid'=>$vo.id])}' )" class="layui-bg-green ft15">设置代理</a>
						&nbsp;
						<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
						<a href="javascript:;" onclick="blacklist( {$vo.id} )" class="layui-bg-red ft15">黑名单</a>
					</td>
				</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>



<script type="text/javascript">
	$(document).ready(function(){
		var table = $('#mytable');//table的id
		$('#sort_header,#secode222,#dongjiezijin')//要排序的headerid
		// $//要排序的headerid
				.each(function(){
					var th = $(this),
							thIndex = th.index(),
							inverse = false;

					th.click(function(){
						table.find('td').filter(function(){
							return $(this).index() === thIndex;
						}).sortElements(function(a, b){
							return $.text([a]) > $.text([b]) ?

									inverse ? -1 : 1
									: inverse ?

											1 : -1;
						}, function(){
							return this.parentNode;
						});
						inverse = !inverse;

					});
				});
	});

function addIframe(titile,url){
    layer.open({
        type: 2,
        title: titile,
        maxmin: false,
        shadeClose: false,
        area : ['680px' , '487px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
function delConfirm(id){
	layer.confirm('您确定删除？将不可恢复', {
		title:'删除会员信息，请谨慎操作',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		$.ajax({
	        url: "{:url('members/delMember')}",
	        type: "post",
	        dataType: "json",
	        data: {uid:id,action:'delete'},
	        success: function(d,e,x){
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( '删除成功' ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( d.message,{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}

	function blacklist(id){
		layer.confirm('您确定加入黑名单', {
			title:'黑名单',
			btn: ['确定','取消'] //按钮
		}, function(){
			var la_load = layer.load(0,{
				shade: [0.2,'#000']
			});
			$.ajax({
				url: "{:url('members/blacklist')}",
				type: "post",
				dataType: "json",
				data: {uid:id,action:'add'},
				success: function(d,e,x){
					// console.log(d,e,x);
					if(d.status==1){
						layer.msg( '加入黑名单成功' ,{icon : 1} );
						location.reload()
					}else{
						layer.msg( d.message,{icon : 2} );
					}
					layer.close(la_load);
				}
			});
		}, function(){
		});
	}

$('input[name=name]').keypress(function (e) {
    if( e.which==13 ){

        $('#subform').submit();
    }
});

function submitForm(){
	$('#subform').submit();
}

$(function(){
	layui.use('form', function(){
		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
		form.on('select(test)', function(data){
			$('#subform').submit();
		});
	});
})

function exportUser(){
	var la_load = layer.load(0,{
			shade: [0.3,'#000'],
			content: '数据正在导出中...',
			success: function (layero) {
		        layero.find('.layui-layer-content').css({
		            'padding-top': '35px',
		            'width': 'auto',
				    'font-size': '15px',
				    'text-align': 'center',
				    'text-indent': '-22px',
				    'font-weight': 'bold'
		        });
		    }
		});
	setTimeout(function(){
                layer.close(la_load);
            }, 4000);

	window.location.href = '{:url("members/exportUser")}';
}

$(function(){
	layui.use('form', function(){
		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
		form.on('select(test)', function(data){
			$('#subform').submit();
		});
	});
})
</script>
