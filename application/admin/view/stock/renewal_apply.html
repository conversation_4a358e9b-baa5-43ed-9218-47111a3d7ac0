{layout name="layout" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>股票配资</a>
        <a>申请延期</a>
        <a><cite>待审核延期</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('stock/renewalApply')}">
    <div class="layui-form-item">
        <label class="layui-form-label">搜索用户名</label>
        <div class="layui-input-inline">
            <input type="text" name="user_name" value="{$user_name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div>
</form>


<div class="layui-form form-border" >
    <table class="layui-table" lay-skin="line" >
        <thead>
            <tr>
                <th style="width: 7%;">编号</th>
                <th style="">用户名</th>
                <th style="">真实姓名</th>
                <th style="">子账号</th>
                <th style="">配资类型</th>
                <th style="">配资到期时间</th>
                <th style="">延期时间</th>
                <th style="">利息</th>
                <th style="">状态</th>
                <th style="">延期类型</th>
                <th style="">申请时间</th>
                <th style="width:8%;">操作</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{$vo.home_user}</td>
                    <td>{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $vo.type)}</td>
                    <td>{:date('Y-m-d H:i', $vo.end_time)}</td>
                    <td>
                        {$vo.borrow_duration}{:str_replace([1,2,8], ['天','月','月'], $vo.type)}
                    </td>
                    <td>{$vo.borrow_fee/100}</td>
                    <td>待审核</td>
                    <td>{:str_replace([0,1], ['用户申请延期','自动延期申请'], $vo.auto_type)}</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>
                        <a href="javascript:;" onclick="addIframe( '{:url('stock/renewalEdit',['id'=>$vo.id])}' )" class="layui-bg-blue ft15">编辑</a>
                    </td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
function addIframe(url){
    layer.open({
        type: 2,
        title: '审核延期',
        maxmin: false,
        shadeClose: false,
        area : ['680px' , '370px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
</script>