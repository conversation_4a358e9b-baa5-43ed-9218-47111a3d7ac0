{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>配资</a>
		<a>股票配资</a>
		<a><cite>子账户管理</cite></a>
	</span>
</div>
<style type="text/css">
.layui-btn{ float: left; }
</style>

<button class="layui-btn" onclick="addIframe('添加子账户' , '' )">添加子账户</button>
<form class="layui-form">
<div class="layui-inline">
	<label class="layui-form-label" style="width:0;"></label>
	<div class="layui-input-inline">
        <select name="status"  lay-filter="test" id='cstatus'>
        	<option value="" data-url='{:url("stock/subaccount")}' >所有账户</option>
			<option value="1" data-url='{:url("stock/subaccount","action=select&status=1")}' {eq name="status" value="1"}selected{/eq} >未使用</option>
			<option value="2" data-url='{:url("stock/subaccount","action=select&status=2")}' {eq name="status" value="2"}selected{/eq} >使用中</option>
			<option value="3" data-url='{:url("stock/subaccount","action=select&status=3")}' {eq name="status" value="3"}selected{/eq} >已终止</option>
        </select>
	</div>
</div>
</form>

<br>

<div class="layui-form form-border">
	<table class="layui-table" lay-skin="line">
		<thead>
			<tr>
				<th style="width:5%;">编号</th>
				<th style="width:15%;">子账户名称</th>
				<th style="width:15%;">子账户密码</th>
				<th style="width:9%;">状态</th>
				<th style="width:30%;">备注</th>
				<th style="">添加时间</th>
				<th style="width:12%;">操作</th>
			</tr> 
		</thead>
		<tbody>
			{volist name="data" id="vo"}
			<tr class="list_{$vo.id}">
				<td>{$vo.id}</td>
				<td>{$vo.name}</td>
				<td>{$vo.password}</td>
				<td>{:str_replace([1,2,3], ['未使用','使用中','已终止'], $vo.status)}</td>
				<td>{:mb_substr($vo.remark,0,25)}</td>
				<td>{:date('Y-m-d H:s',$vo.add_time)}</td>
				<td>
					<!-- <a href="" class="layui-bg-blue ft15">编辑</a>
					&nbsp; -->
					<a href="javascript:;" onclick="delConfirm( {$vo.id} )" class="layui-bg-red ft15">删除</a>
				</td>
			</tr>
			{/volist}
		</tbody>
  	</table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
function addIframe(title){
	layer.open({
		type: 2,
		title: title,
		maxmin: true,
		shadeClose: false,
		area : ['660px' , '417px'],
		content: "{:url('stock/doAccount')}",
		end: function(){
			// location.reload();
		}
    });
}

$(function(){

	layui.use('form', function(){
  		var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
  		// form.render();
  		form.on('select(test)', function(data){
			// console.log(data.elem); //得到select原始DOM对象
			// console.log(data.value); //得到被选中的值2
			// console.log(data.othis); //得到美化后的DOM对象
			// if( status ){
				var url = $(data.elem).find("option:selected").data('url');
				window.location.href = url;
			// } 
		});
	});


})
	

function delConfirm(id){
	layer.confirm('您确定删除？', {
		title:'删除该子账户',
		btn: ['确定','取消'] //按钮
	}, function(){
		var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
		// return false;
		$.ajax({
	        url: "{:url('stock/doAccount')}",
	        type: "post",
	        dataType: "json",
	        data: {id:id,action:'delete'},
	        success: function(d,e,x){  
	            // console.log(d,e,x);
	            if(d.status==1){
	            	layer.msg( '删除成功' ,{icon : 1} );
	            	$('.list_'+id).remove();
	            }else{
	            	layer.msg( '删除失败！',{icon : 2} );
	            }
	            layer.close(la_load);
	        }
	    });
	}, function(){
	});
}


</script>