{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-label{
    width: 100px;
    padding: 9px 6px;
}
.details{
    width: 240px;
    float: left;
    line-height: 23px;
    color: #333;
    font-size: 14px;
}

</style>

<div class="layui-main" style="padding: 21px 0;">
    <form class="layui-form" onsubmit="return false;">
        <div class="layui-form-item layui-form-text" style="margin-bottom: 12px;">
            <label class="layui-form-label label2-title">配资详情：</label>
            <div class="details">
                <p>会员：{$data.user_name}</p>
                <p>子账号：{$data.home_user}</p>
                <p>开始时间：{:date('Y-m-d H:i',$data.add_time)}</p>
                <strong>所需费用：<span id='fee'></span></strong>
            </div>
            <div class="details">
                <p>真实姓名：{$data.real_name|default='未实名'}</p>
                <p>配资类型：{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $data.type)}</p>
                <p>结束时间：{:date('Y-m-d H:i',$data.end_time)}</p>
                <strong>延期后结束时间：<span id='after_etime'></span></strong>
            </div>
        </div>

        
        <div class="layui-form-item" >
            <div class="layui-inline">
                <label class="layui-form-label label2-title">续期时间：</label>
                <div class="layui-input-inline">
                    <select id="renewal_time" lay-filter="test">
                        <!-- <option value="">直接选择或搜索选择</option> -->
                        {volist name="use_time" id="vo"}
                        <option value="{$vo}">{$vo}{$unit}</option>
                        {/volist}
                    </select>
                    <div class="layui-form-select">
                        <div class="layui-select-title">
                            <input type="text" placeholder="直接选择或搜索选择" value="" class="layui-input">
                            <i class="layui-edge"></i>
                        </div>
                    </div>
                </div>
                <div class="layui-form-mid layui-word-aux extend-inline-text"></div>
            </div>
        </div>
    
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">备注信息：</label>
            <div class="layui-input-inline w333px">
                <textarea name="content" placeholder="请输入内容，用于发送站内信" class="layui-textarea" style="width: 380px;min-height: 60px;"></textarea>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux"></div> -->
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 112px;">
                <button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="sub()">确认提交</button>
            </div>
        </div>

    </form>
</div>


{load href="/static/js/underscore-min.js" /}

<script type="text/javascript">
var borrow_id = parseInt("{$id}");
var type = parseInt("{$data.type}");
var end_time = "{$data.end_time}";
var duration = parseInt("{$use_time.0}");
function sub(){
    var renewal_time = $('#renewal_time').val();
    var content = $('textarea[name=content]').val();
    if( !borrow_id ){
        return layer.msg('id不存在，请按规则重试！',{anim : 6});
    }
    if( !renewal_time ){
        return layer.msg('请选择延期时间',{anim : 6});
    }
    // console.log( id,money,status,info );return
    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("stock/adminRenewal")}',
        type: "POST",
        dataType: "json",
        data: {borrow_id:borrow_id,duration:renewal_time,content:content,action:'adminRenewal'},
        success: function(d,e,x){
            // console.log(d,e,x);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );
                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){
                    // parent.layer.close(index);
                    parent.reloadData();
                }, 1000);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2} );
            }
        }
    });
}

layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();

    form.on('select(test)', _.debounce( function(data){
        // console.log(data.elem); //得到select原始DOM对象
        // console.log(data.value); //得到被选中的值
        // console.log(data.othis); //得到美化后的DOM对象

        duration = data.value;
        calculate(borrow_id, type,end_time, duration);
    },325 ));
});
function calculate(borrow_id, type,end_time, duration){
    if( !borrow_id || !type || !duration ){
        return false;
    }

    $('#fee').html('计算中...');
    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("stock/adminRenewal")}',
        type: "POST",
        dataType: "json",
        data: {borrow_id:borrow_id,type:type,end_time:end_time,duration:duration,action:'calculate'},
        success: function(d,e,x){  
            // console.log(d,e,x);
            layer.close(la_load);
            if(d.status==1){
                $('#fee').html( (d.data.fee/100) );
                $('#after_etime').html( d.data.addEndTime );
            }else{
                $('#fee').html('');
                return layer.msg( d.message,{anim:6} );
            }
        }
    });
}
window.onload = function(){
    calculate(borrow_id, type,end_time, duration);
}

</script>
