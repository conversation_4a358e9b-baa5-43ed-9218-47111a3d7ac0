{layout name="layout" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>股票配资</a>
        <a>配资管理</a>
        <a><cite>未通过配资</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('stock/stockPass')}" id='subform'>
    <div class="layui-form-item">
        <label class="layui-form-label">搜索用户名</label>
        <div class="layui-input-inline">
            <input type="text" name="user_name" value="{$user_name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-inline" style="margin-left: 10px;">
        <label class="layui-form-label" label2-title>显示行数</label>
        <div class="layui-input-inline" style="width:80px;">
            <select name="page_num" lay-filter="test" id="page_num">
                <option value="10" {eq name="page_num" value="10"}selected{/eq}> 10</option>
                <option value="20" {eq name="page_num" value="20"}selected{/eq}> 20</option>
                <option value="30" {eq name="page_num" value="30"}selected{/eq}> 30</option>
                <option value="40" {eq name="page_num" value="40"}selected{/eq}> 40</option>
                <option value="50" {eq name="page_num" value="50"}selected{/eq}> 50</option>
                <option value="100" {eq name="page_num" value="100"}selected{/eq}> 100</option>
                <option value="200" {eq name="page_num" value="200"}selected{/eq}> 200</option>
                <option value="300" {eq name="page_num" value="300"}selected{/eq}> 300</option>
                <option value="400" {eq name="page_num" value="400"}selected{/eq}> 400</option>
                <option value="500" {eq name="page_num" value="500"}selected{/eq}> 500</option>
            </select>
        </div>
        <!--<input type="submit" value="刷新" class="layui-btn layui-btn-primary"/>-->
    </div>
    </div>
</form>


<div class="layui-form form-border" style="overflow-x: scroll;">
    <table class="layui-table" lay-skin="line" style="min-width: 1400px;">
        <thead>
            <tr>
                <th style="width: 5%;">编号</th>
                <th style="">用户</th>
                <th style="">真实姓名</th>
                <th style="">配资类型</th>
                <th style="">保证金</th>
                <th style="">总配资金额</th>
                <th style="">倍数</th>
                <th style="">周期</th>
                <th style="">申请时间</th>
                <th style="">审核时间</th>
                <th style="">备注信息</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $vo.type)}</td>
                    <td>{$vo.deposit_money / 100}</td>
                    <td>{$vo.borrow_money / 100}</td>
                    <td>{$vo.multiple}</td>
                    <td>{$vo.borrow_duration}</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>{:date('Y-m-d H:i',$vo.verify_time)}</td>
                    <td class="info" data-info="{$vo.content|default=''}">{:cnsubstr($vo.content,20)}</td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
$(function(){
    $('.layui-form tbody tr .info').each(function(index, el) {
        $(this).mouseover(function () {
            var info = $(this).data('info');
            if(info){
                indexpc = layer.tips(
                    info,
                    $(this), {
                    area: ['330px', 'auto'],
                    tips: [1, '#23262E'],
                    time:0
                });
            }
        }).mouseout(function () {
            layer.close(indexpc);
        });
    });
    
    layui.use('form', function(){
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        form.on('select(test)', function(data){
            $('#subform').submit();
        });
    });

})

$('input[name=user_name]').keypress(function (e) {
    if( e.which==13 ){

        $('#subform').submit();
    }
});
/*function addIframe(url){
    layer.open({
        type: 2,
        title: '配资到期处理',
        maxmin: false,
        shadeClose: false,
        area : ['600px' , '400px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}*/


</script>