{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
    .layui-form-label {
        width: 100px;
        padding: 9px 6px;
    }
</style>

<div class="layui-main" style="padding: 21px 0;">
    <form class="layui-form" onsubmit="return false;">
        <div class="layui-form-item">
            <label class="layui-form-label ">子账户名称：</label>
            <div class="layui-input-inline ">
                <input type="text" name="mingcheng" value="" lay-verify="required" placeholder="请输入内容"
                       autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">子账号登陆时的用户名</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label ">子账户密码：</label>
            <div class="layui-input-inline ">
                <input type="text" name="mima" value="" lay-verify="required" placeholder="请输入内容"
                       autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">子账号登陆时的密码，密码不区分大小写</div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">备注信息：</label>
            <div class="layui-input-inline w333px">
                <textarea name="beizhu" placeholder="请输入内容" class="layui-textarea"
                          style="width: 338px;height: 155px;"></textarea>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux"></div> -->
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 112px;">
                <button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="sub()">确认添加</button>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    function sub() {
        var mingcheng = $('input[name=mingcheng]').val();
        var mima = $('input[name=mima]').val();
        var beizhu = $('textarea[name=beizhu]').val();
        if (!mingcheng || !mima) {
            return false;
        }
        // console.log(mingcheng,mima );
        var la_load = layer.load(0, {
            shade: [0.2, '#000']
        });
        // return;
        $.ajax({
            url: "{:url('stock/doAccount')}",
            type: "post",
            dataType: "json",
            data: {mingcheng, mima, beizhu, action: 'add'},
            success: function (d, e, x) {
                // console.log(d,e,x);
                if (d.status == 1) {
                    layer.msg(d.message, {icon: 1});
                    var index = parent.layer.getFrameIndex(window.name);
                    setTimeout(function () {
                        parent.reloadData();
                        // parent.layer.close(index);
                    }, 1000);
                } else {
                    layer.close(la_load);
                    return layer.msg(d.message, {icon: 2});
                }
            }
        });

    }


    layui.use('form', function () {
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        //但是，如果你的HTML是动态生成的，自动渲染就会失效
        //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
        form.render();
    });

</script>