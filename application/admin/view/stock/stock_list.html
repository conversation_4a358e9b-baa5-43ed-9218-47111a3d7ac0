{layout name="layout" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>股票配资</a>
        <a>配资管理</a>
        <a><cite>使用中配资</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('stock/stockList')}" id='subform'>
    <!-- <div class="layui-inline">
        <label class="layui-form-label" style="width: 95px;padding: 8px 0;">搜索用户名</label>
        <div class="layui-input-inline" style="width: 150px;">
            <input type="text" name="user_name" value="{$user_name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div> -->

    <div class="layui-inline">
        <div class="layui-input-inline" style="width: 113px;">
            <select name="action" lay-verify="required" lay-search="">
                <option value="user_name" {eq name="action" value="user_name"}selected{/eq}>搜索用户名</option>
                <option value="home_user" {eq name="action" value="home_user"}selected{/eq}>搜索子账号</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width: 160px;margin-left: -5px;">
            <input type="text" name="name" value="{$name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-inline">
        <label class="layui-form-label" style="width:0;padding: 0;border: none;"></label>
        <div class="layui-input-inline" style="width: 126px;margin-left: 5px;">
            <select name="type" lay-filter="test" lay-verify="required" lay-search="">
                <option value="">所有类型</option>
                <option value="4" {eq name="type" value="4"}selected{/eq}>体验</option>
                <option value="3" {eq name="type" value="3"}selected{/eq}>免息</option>
                <option value="1" {eq name="type" value="1"}selected{/eq}>按天</option>
                <option value="2" {eq name="type" value="2"}selected{/eq}>按月</option>
                <option value="8" {eq name="type" value="8"}selected{/eq}>VIP</option>
            </select>
        </div>
    </div>
    <div class="layui-inline" style="margin-left: 10px;">
        <label class="layui-form-label" label2-title>显示行数</label>
        <div class="layui-input-inline" style="width:80px;">
            <select name="page_num" lay-filter="test" id="page_num">
                <option value="10" {eq name="page_num" value="10"}selected{/eq}> 10</option>
                <option value="20" {eq name="page_num" value="20"}selected{/eq}> 20</option>
                <option value="30" {eq name="page_num" value="30"}selected{/eq}> 30</option>
                <option value="40" {eq name="page_num" value="40"}selected{/eq}> 40</option>
                <option value="50" {eq name="page_num" value="50"}selected{/eq}> 50</option>
                <option value="100" {eq name="page_num" value="100"}selected{/eq}> 100</option>
                <option value="200" {eq name="page_num" value="200"}selected{/eq}> 200</option>
                <option value="300" {eq name="page_num" value="300"}selected{/eq}> 300</option>
                <option value="400" {eq name="page_num" value="400"}selected{/eq}> 400</option>
                <option value="500" {eq name="page_num" value="500"}selected{/eq}> 500</option>
            </select>
        </div>
        <!--<input type="submit" value="刷新" class="layui-btn layui-btn-primary"/>-->
    </div>

    <br><br>
</form>


<div class="layui-form form-border" style="overflow-x: scroll;">
    <table class="layui-table" lay-skin="line" style="min-width: 1550px;">
        <thead>
            <tr>
                <th style="width: 5%;">编号</th>
                <th style="">用户</th>
                <th style="">真实姓名</th>
                <th style="">子账号</th>
                <th style="">配资类型</th>
                <th style="">保证金</th>
                <th style="">总配资金额</th>
                <th style="">补亏金额</th>
                <th style="">提取金额</th>
                <th style="">倍数</th>
                <th style="">周期</th>
                <th style="">利率</th>
                <th style="">管理费</th>
                <th style="">警戒线</th>
                <th style="">平仓线</th>
                <th style="">开始时间</th>
                <th style="">结束时间</th>
                <th style="width:10%;">操作</th>
            </tr>
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{$vo.home_user}</td>
                    <td>{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $vo.type)}</td>
                    <td>{$vo.deposit_money / 100}</td>
                    <td>{$vo.borrow_money / 100}</td>
                    <td>{$vo.add / 100}</td>
                    <td>{$vo.draw / 100}</td>
                    <td>{$vo.multiple}</td>
                    <td>{$vo.borrow_duration}</td>
                    <td>{$vo.rate}</td>
                    <td>{$vo.borrow_interest / 100}</td>
                    <td>{$vo.loss_warn / 100}</td>
                    <td>{$vo.loss_close / 100}</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>{:date('Y-m-d H:i',$vo.end_time)}</td>
                    <td>
                        <a href="javascript:;" onclick="addIframe( '{:url('stock/stockEdit',['id'=>$vo.id])}' )" class="layui-bg-blue ft15">结算</a>
                        &nbsp;
                        <a href="javascript:;" onclick="addIframe2( '{:url('stock/adminRenewal',['id'=>$vo.id])}' )" class="layui-bg-green ft15">手动延期</a>
                    </td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
function addIframe(url){
    layer.open({
        type: 2,
        title: '配资结算处理',
        maxmin: false,
        shadeClose: false,
        area : ['690px' , '400px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
function addIframe2(url){
    layer.open({
        type: 2,
        title: '管理员手动延期',
        maxmin: false,
        shadeClose: false,
        area : ['666px' , '400px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}


$('input[name=name]').keypress(function (e) {
    if( e.which==13 ){

        $('#subform').submit();
    }
});
$(function(){
    layui.use('form', function(){
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        // form.render();
        form.on('select(test)', function(data){
            // console.log(data.elem); //得到select原始DOM对象
            // console.log(data.value); //得到被选中的值2
            // console.log(data.othis); //得到美化后的DOM对象

            $('#subform').submit();
        });
    });
})
</script>
