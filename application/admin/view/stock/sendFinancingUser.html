{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-label{
    width: 100px;
    padding: 9px 6px;
}
.details{
    width: 240px;
    float: left;
    line-height: 23px;
    color: #333;
    font-size: 14px;
}
.layui-form-select dl {
    max-height:200px;
}
</style>

<div class="layui-main" style="padding: 21px 0;">
    <form class="layui-form" onsubmit="return false;">
        <input type="hidden" name='borrow_id' value="{$borrow_id}">
        <div class="layui-form-item layui-form-text" style="margin-bottom: 6px;">
            <label class="layui-form-label label2-title">配资详情：</label>
            <div class="details">
                <p>会员：{$data.user_name}</p>
                <p>真实姓名：{$data.real_name|default='未实名'}</p>
                <p>配资类型：{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $data.type)}</p>
                <p>周期：{$data.borrow_duration}{:str_replace([1,2,3,4,8],['天','月','天','天','月'],$data.type)}</p>
                <p>保证金：{$data.deposit_money/100}</p>
                <p>操盘资金：{php} echo ($data['borrow_money']-$data['deposit_money'])/100 {/php}</p>
                <p>警戒线：{$data.loss_warn/100}</p>
                <p>开始时间：{:date('Y-m-d H:i',$data.add_time)}</p>
            </div>
            <div class="details">
                <p>&nbsp;</p>
                <p>身份证号：{$data.id_card|default='未实名'}</p>
                <p>倍数：{$data.multiple}倍</p>
                <p>利率：{$data.rate}%</p>
                <p>管理费：{$data.borrow_interest/100}</p>
                <p>总操盘资金：{$data.borrow_money/100}</p>
                <p>平仓线：{$data.loss_close/100}</p>
                <p>结束时间：{:date('Y-m-d H:i',$data.end_time)}</p>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom: 6px;">
            <label class="layui-form-label label2-title">审核状态：</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="2" title="通过" checked >
                <input type="radio" name="status" value="-1" title="拒绝">
            </div>
        </div>

        <div class="layui-form-item" >
            <div class="layui-inline">
                <label class="layui-form-label label2-title">子账号信息：</label>
                <div class="layui-input-inline">
                    {if condition="$sub_account"}
                    <div style="padding: 9px 15px; background: #f2f2f2; border: 1px solid #e6e6e6; border-radius: 2px;">
                        <p><strong>账号：</strong>{$sub_account.name}</p>
                        <p><strong>密码：</strong>{$sub_account.password}</p>
                        <p style="color: #5FB878; font-size: 12px;">已自动创建</p>
                    </div>
                    {else /}
                    <div style="padding: 9px 15px; background: #ffebee; border: 1px solid #f44336; border-radius: 2px; color: #f44336;">
                        未找到对应的子账号，请联系管理员
                    </div>
                    {/if}
                </div>
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">备注信息：</label>
            <div class="layui-input-inline w333px">
                <textarea name="beizhu" placeholder="请输入内容" class="layui-textarea" style="width: 380px;min-height: 60px;"></textarea>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux"></div> -->
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 112px;">
                <button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="sub()">确认提交</button>
            </div>
        </div>

    </form>
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">
function sub(){
    var borrow_id = parseInt($('input[name=borrow_id]').val());
    var status = $("input[name=status]:checked").val();
    var info = $("textarea[name=beizhu]").val();

    if( !borrow_id ){
        return layer.msg('id不存在，请按规则重试！',{anim : 6});
    }
    if( status!=2 && !info ){
        return layer.msg('拒绝申请需要填写备注信息',{anim : 6});
    }

    var requestData = {
        borrow_id:borrow_id,
        status:status,
        info:info
    };

    // console.log( requestData )
    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("stock/sendFinancingUser")}',
        type: "POST",
        dataType: "json",
        data: requestData, // 使用新的 requestData 物件
        success: function(d,e,x){
            // console.log(d,e,x);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );

                // var index = parent.layer.getFrameIndex(window.name);
                setTimeout(function(){ 
                    parent.reloadData();
                    // parent.layer.close(index);
                }, 1000);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}
layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();
});
</script>
