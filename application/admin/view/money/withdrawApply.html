{layout name="layout" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>资金</a>
        <a>提现管理</a>
        <a><cite>待审核提现</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('money/withdrawApply')}">
    <div class="layui-form-item">
        <label class="layui-form-label">搜索用户名</label>
        <div class="layui-input-inline">
            <input type="text" name="user_name" value="{$user_name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div>
</form>


<div class="layui-form form-border">
    <table class="layui-table" lay-skin="line">
        <thead>
            <tr>
                <th style="width: 5%;">编号</th>
                <th style="">用户名</th>
                <th style="">真实姓名</th>
                <th style="">账户姓名</th>
                <th style="">提现金额</th>
                <th style="">提现手续费</th>
                <th style="">所在地</th>
                <th style="">开户银行</th>
                <th style="">状态</th>
                <th style="">申请时间</th>
                <th style="width:8%;">操作</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{$vo.account_name}</td>
                    <td>{$vo.money/100}</td>
                    <td>{$vo.fee/100}</td>
                    <td>{$vo.province}{$vo.city}</td>
                    <td>{$vo.bank_address}</td>
                    <td>待审核</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>
                        <a href="javascript:;" onclick="addIframe( '{:url('money/withdrawDeal',['id'=>$vo.id])}' )" class="layui-bg-blue ft15">编辑</a>
                    </td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
function addIframe(url){
    // console.log(url);return
    layer.open({
        type: 2,
        title: '审核提现',
        maxmin: false,
        shadeClose: false,
        area : ['680px' , '479px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
</script>