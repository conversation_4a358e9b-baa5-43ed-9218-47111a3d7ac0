{layout name="layout" /}

<div class="layui-nav2 ft16a">
	<span class="layui-breadcrumb">
		<a>资金</a>
		<a>后台转账</a>
		<a><cite>会员转账</cite></a>
	</span>
</div>

<br><br>

<form class="layui-form" action="{:url('money/transfer')}" method="post" onsubmit="return sub()">
	<div class="layui-form-item">
	    <label class="layui-form-label layui-width">用户名：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="uname" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <!-- <div class="layui-form-mid layui-word-aux"></div> -->
	</div>

	<div class="layui-form-item">
	    <label class="layui-form-label layui-width">转账金额：</label>
	    <div class="layui-input-inline w333px">
			<input type="text" name="money" value="" lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input">
	    </div>
	    <div class="layui-form-mid layui-word-aux">如果是要扣费，输入负值</div>
	</div>
	
	<div class="layui-form-item">
	    <label class="layui-form-label layui-width">转账类型：</label>
	    <div class="layui-input-block">
			<input type="radio" name="type" value="1" title="转至账户余额" checked="">
	      	<input type="radio" name="type" value="2" title="赠送管理费">
	    </div>
	</div>

	<div class="layui-form-item layui-form-text">
        <label class="layui-form-label layui-width">备注信息：</label>
        <div class="layui-input-inline w333px">
            <textarea name="content" placeholder="请输入内容" class="layui-textarea" style="width: 333px;min-height: 100px;"></textarea>
        </div>
        <!-- <div class="layui-form-mid layui-word-aux"></div> -->
    </div>

	{:token()}

	<br>
	<div class="layui-form-item">
		<label class="layui-form-label layui-width"></label>

		<button class="layui-btn" lay-submit="">
			确认提交
		</button>
	</div>

</form>


<script type="text/javascript">
$(function(){
})
var isSub = false;
function sub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2});
		return false;
	}
	var uname = $('input[name=uname]').val();
	var money = parseFloat($('input[name=money]').val());
	if( !uname ){
		layer.msg('用户名不能为空',{icon:2, anim:6});
		return false;
	}
	if( !money || money==0 ){
		layer.msg('转账金额输入不正确',{icon:2, anim:6});
		return false;
	}
	if( money%1 != 0 ){
		layer.msg('转账金额必须是整数',{icon:2, anim:6});
		return false;
	}

	isSub = true;
	return true;
}
</script>
