{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/A/style.css" /}
{load href="/static/css/A/layui-global.css" /}

<style type="text/css">
.layui-form-label{
    width: 100px;
    padding: 9px 6px;
}
.details{
     width: 240px;
    float: left;
    line-height: 23px;
    color: #333;
    font-size: 14px; 
    line-height: 25px;
    /* width: 240px;
    float: left;
    color: #333;
    font-size: 14px;
    margin-top: 8px; */
}
.layui-form-select dl {
    max-height:200px;
}
</style>

<div class="layui-main" style="padding: 21px 0;">
    <form class="layui-form" onsubmit="return false;">
        <input type="hidden" name='id' value="{$id|default=0}">
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">提现详情：</label>
            <div class="details">
                <p>用户名：{$data.user_name}</p>
                <p>提现金额：{$data.money/100}</p>
                <p>所在地：{$data.province}{$data.city}</p>
                <p>开户行：{$data.bank_address}</p>
            </div>
            <div class="details">
                <p>真实姓名：{$data.real_name|default='未实名'}</p>
                <p>账户姓名：{$data.account_name}</p>
                <p>申请时间：{:date('Y-m-d H:i',$data.add_time)}</p>
                <p>银行：{$data.bank_name}</p>
                <p>银行账号：{$data.number}</p>
            </div>
        </div>

        <div class="layui-form-item" >
            <label class="layui-form-label ">手续费：</label>
            <div class="layui-input-inline ">
                <input type="text" name="fee" value="0" lay-verify="" placeholder="请输入提现所需手续费" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">请输入提现所需手续费</div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label ">到账金额：</label>
            <div class="layui-input-inline ">
                <input type="text" name="account" value="" lay-verify="" placeholder="请输入到账金额" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">扣除手续费实际到账金额</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label2-title">审核状态：</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="通过" checked >
                <input type="radio" name="status" value="-1" title="拒绝">
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label label2-title">备注信息：</label>
            <div class="layui-input-inline w333px">
                <textarea name="info" placeholder="请输入内容" class="layui-textarea" style="width: 380px;min-height: 60px;"></textarea>
            </div>
            <!-- <div class="layui-form-mid layui-word-aux"></div> -->
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 112px;">
                <button class="layui-btn" lay-submit="" lay-filter="demo1" onclick="sub()">确认提交</button>
            </div>
        </div>

    </form>
</div>


{load href="/static/layui-v2.4.5/layui/layui.js" /}
<script type="text/javascript">
function sub(){
    var id = parseInt($('input[name=id]').val());
    var fee = parseFloat($("input[name=fee]").val());
    var account = parseFloat($("input[name=account]").val());
    var status = $("input[name=status]:checked").val();
    var info = $("textarea[name=info]").val();
    if( !id ){
        return layer.msg('id不存在，请按规则重试！',{anim : 6});
    }
    if( status==1 ){
        if( isNaN(fee) || isNaN(account) ){
            return layer.msg('请输入正确金额！',{anim : 6});
        }
        if( fee<0 || account<=0 ){
            return layer.msg('金额不能小于0！',{anim : 6});
        }
    }
    if( status!=1 && !info){
        return layer.msg('如果拒绝审核，需要填写拒绝理由',{anim : 6});
    }
    // console.log(id,status,info,fee,account);return
    var la_load = layer.load(0,{
                shade: [0.2,'#000']
            });
    $.ajax({
        url: '{:url("money/withdrawDeal")}',
        type: "POST",
        dataType: "json",
        data: {id:id,fee:fee,account:account,status:status,info:info},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
                layer.msg( d.message ,{icon : 1} );
                setTimeout(function(){ 
                    parent.reloadData();
                }, 1000);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}
layui.use('form', function(){
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    //但是，如果你的HTML是动态生成的，自动渲染就会失效
    //因此你需要在相应的地方，执行下述方法来手动渲染，跟这类似的还有 element.init();
    form.render();
});
</script>
