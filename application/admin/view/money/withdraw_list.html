{layout name="layout" /}
{load href="/static/laydate-v5.0.9/laydate.js" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>资金</a>
        <a>提现管理</a>
        <a><cite>待审核提现</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('money/withdrawList')}" id="subform" onkeydown="if(event.keyCode==13){ sub() }">
    <div class="layui-inline" style="">
        <label class="layui-form-label" style="width: 92px;">申请时间</label>
        <div class="layui-input-inline" style="width: 110px;">
            <input type="text" value="{$timestr}" class="layui-input" id="test6" placeholder="开始 到 结束" autocomplete="off">
        </div>
    </div>
    <input type="hidden" name="time" value="{$timestr}">

    <div class="layui-inline" style="margin-left: 10px;">
        <label class="layui-form-label" style="padding: 8px 0;
                                               width: 75px;">状态</label>
        <div class="layui-input-inline" style="width: 115px;">
            <select name="status" lay-filter="test" lay-verify="required" lay-search="">
                <option value="">按状态筛选</option>

                <option value="1" {if condition="$status EQ 2"}selected{/if} >处理中</option>
                <option value="1" {if condition="$status EQ 1"}selected{/if} >已通过</option>
                <option value="-1" {if condition="$status EQ -1"}selected{/if} >未通过</option>
            </select>
        </div>
    </div>

    <div class="layui-inline" style="margin-left: 10px;">
        <label class="layui-form-label" style="padding: 8px 0;
                                               width: 82px;">搜索用户名</label>
        <div class="layui-input-inline" style="width: 100px;">
            <input type="text" name="user_name" value="{$user_name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div>
    
    <div class="layui-inline" style="margin-left: 10px;">
		<label class="layui-form-label" style="width:105px;" label2-title>显示行数</label>
		<div class="layui-input-inline" style="width:70px;">
			<select name="page_num" lay-filter="test" id="page_num">
				<option value="10" {eq name="page_num" value="10"}selected{/eq}> 10</option>
				<option value="20" {eq name="page_num" value="20"}selected{/eq}> 20</option>
				<option value="30" {eq name="page_num" value="30"}selected{/eq}> 30</option>
				<option value="40" {eq name="page_num" value="40"}selected{/eq}> 40</option>
				<option value="50" {eq name="page_num" value="50"}selected{/eq}> 50</option>
				<option value="100" {eq name="page_num" value="100"}selected{/eq}> 100</option>
				<option value="200" {eq name="page_num" value="200"}selected{/eq}> 200</option>
				<option value="300" {eq name="page_num" value="300"}selected{/eq}> 300</option>
				<option value="400" {eq name="page_num" value="400"}selected{/eq}> 400</option>
				<option value="500" {eq name="page_num" value="500"}selected{/eq}> 500</option>
			</select>
		</div>
		<!--<input type="submit" value="刷新" class="layui-btn layui-btn-primary"/>-->
	</div>

    <button type="button" onclick="exportData()" class="layui-btn fr">导出当前数据</button>
</form>

<br>

<div class="layui-form form-border" style="overflow-x: scroll;">
    <table class="layui-table" lay-skin="line" style="min-width: 1300px;">
        <thead>
            <tr>
                <th style="width: 5%;">编号</th>
                <th style="">用户名</th>
                <th style="">真实姓名</th>
                <th style="">提现金额</th>
                <th style="">提现手续费</th>
                <th style="">所在地</th>
                <th style="">开户银行</th>
                <th style="">申请时间</th>
                <th style="">状态</th>
                <th style="">处理人</th>
                <th style="">处理时间</th>
                <th style="width:17%;">备注</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{$vo.money/100}</td>
                    <td>{$vo.fee/100}</td>
                    <td>{$vo.province}{$vo.city}</td>
                    <td>{$vo.bank_address}</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>{:str_replace([-1,1,2], ['未通过','已通过','处理中'], $vo.status)}</td>
                    <td>{$vo.deal_user}</td>
                    <td>{:date('Y-m-d H:i',$vo.deal_time)}</td>
                    <td class="info" data-info="{$vo.deal_info}">{:mb_substr($vo.deal_info,0,26)}...</td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
laydate.render({
    elem: '#test6'
    ,range: true
    // ,format: 'yyyy年M月d日'
    ,done: function(value, date, endDate){
        $('input[name=time]').val(value);

        $('#subform').submit();
    }
});
function sub(){
    $('#subform').submit();
}

$(function(){
    $('.layui-form tbody tr .info').each(function(index, el) {
        $(this).mouseover(function () {
            var info = $(this).data('info');
            if(info){
                indexpc = layer.tips(
                    info,
                    $(this), {
                    area: ['330px', 'auto'],
                    tips: [1, '#23262E'],
                    time:0
                });
            }
        }).mouseout(function () {
            layer.close(indexpc);
        });
    });

    layui.use('form', function(){
        var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
        // form.render();
        form.on('select(test)', function(data){
            // console.log(data.elem); //得到select原始DOM对象
            // console.log(data.value); //得到被选中的值2
            // console.log(data.othis); //得到美化后的DOM对象
            
            sub();
        });
    });
})

function exportData(){
    var la_load = layer.load(0,{
            shade: [0.3,'#000'],
            content: '数据正在导出中...',
            success: function (layero) {
                layero.find('.layui-layer-content').css({
                    'padding-top': '35px',
                    'width': 'auto',
                    'font-size': '15px',
                    'text-align': 'center',
                    'text-indent': '-22px',
                    'font-weight': 'bold'
                });
            }
        });
    setTimeout(function(){
                layer.close(la_load);
            }, 4000);
    
    var timestr = "{$timestr}";
    var status = "{$status}";
    var user_name = "{$user_name}";
    window.location.href = "{:url('money/exportWithdraw')}"+"?time="+timestr+"&status="+status+"&user_name="+user_name;
}
</script>