{layout name="layout" /}
<div class="layui-nav2 ft16a">
    <span class="layui-breadcrumb">
        <a>资金</a>
        <a>充值管理</a>
        <a><cite>待审核充值</cite></a>
    </span>
</div>

<form class="layui-form layui-form-pane" action="{:url('money/rechargeApply')}">
    <div class="layui-form-item">
        <label class="layui-form-label">搜索用户名</label>
        <div class="layui-input-inline">
            <input type="text" name="user_name" value="{$user_name|default=''}" placeholder="请输入" lay-verify="" autocomplete="off" class="layui-input">
        </div>
    </div>
</form>


<div class="layui-form form-border">
    <table class="layui-table" lay-skin="line">
        <thead>
            <tr>
                <th style="width: 5%;">编号</th>
                <th style="">用户名</th>
                <th style="">真实姓名</th>
                <th style="">充值金额</th>
                <th style="">充值方式</th>
                <th style="">付款账户</th>
                <th style="">对账订单号</th>
                <th style="">状态</th>
                <th style="">申请时间</th>
                <th style="width:8%;">操作</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data" id="vo" empty="$empty_data"}
                <tr>
                    <td>{$vo.id}</td>
                    <td>
                        <a href="javascript:;" onclick="infoCard( {$vo.uid} )" class="infocard">{$vo.user_name}</a>
                    </td>
                    <td>{$vo.real_name|default='未实名'}</td>
                    <td>{$vo.money/100}</td>
                    <td>{:str_replace(['offline','wx','zfb'], ['银行转账','微信扫码','支付宝扫码'], $vo.way)}</td>
                    <td>{$vo.pay_account}</td>
                    <td>{$vo.tran_id}</td>
                    <td>待审核</td>
                    <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                    <td>
                        <a href="javascript:;" onclick="addIframe( '{:url('money/rechargeDeal',['id'=>$vo.id])}' )" class="layui-bg-blue ft15">编辑</a>
                    </td>
                </tr>
            {/volist}
        </tbody>
    </table>
</div>

<div class="pager2" style="">{$page}</div>

<script type="text/javascript">
function addIframe(url){
    // console.log(url);return
    layer.open({
        type: 2,
        title: '审核充值',
        maxmin: false,
        shadeClose: false,
        area : ['680px' , '385px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
</script>