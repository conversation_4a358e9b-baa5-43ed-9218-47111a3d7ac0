<ul class="layui-nav layui-nav-tree" lay-filter="test" id="leftMenus">
	<!-- <li class="layui-nav-item layui-nav-itemed">
		<a class="" href="javascript:;">所有商品</a>
		<dl class="layui-nav-child">
			<dd class="layui-this"><a href="javascript:;">列表一</a></dd>
			<dd><a href="javascript:;">列表二</a></dd>
			<dd><a href="javascript:;">列表三</a></dd>
			<dd><a href="">超链接</a></dd>
		</dl>
	</li>
	<li class="layui-nav-item">
		<a href="javascript:;">解决方案</a>
		<dl class="layui-nav-child">
			<dd><a href="javascript:;">列表一</a></dd>
			<dd><a href="javascript:;">列表二</a></dd>
			<dd><a href="">超链接</a></dd>
		</dl>
	</li> -->
	{volist name="bindMenu" id="vo"}
		<li class="layui-nav-item {if condition='$vo.is_open'}layui-nav-itemed active{/if}">
			<a class="" href="javascript:;">{$vo.title}</a>
			<dl class="layui-nav-child">
				{volist name="$vo['list']" id="vo2"}
					<dd {if condition="changeNavc($vo2[2],$actionName)"}class="layui-this"{/if} ><a href="{$vo2.1}">{$vo2.0}</a></dd>
				{/volist}
			</dl>
		</li>
	{/volist}
</ul>
<script type="text/javascript">
// 废弃
/*function setMenu_los(key){
	if(nowKey == key){
		console.log('yi yang');return false;
	}
	nowKey = key;

	eval("var menu =" + menuStr);
	var menuObj = menu[key]['info'];
	console.log( menuObj );
	var dom = '';
	for(let i in menuObj) {
		var tempObj = menuObj[i];
        var itemed = i == 0 ? 'layui-nav-itemed' : '';//是否展开
        var tempList = tempObj.list;
		// console.log( tempObj ,itemed ,tempList[0][0] ,tempList[0][1] );

		dom += '<li class="layui-nav-item '+itemed+' ">'+
					'<a class="" href="javascript:;">'+tempObj.title+'</a>'+
					'<dl class="layui-nav-child">';
        for(let j in tempList) {
        	// console.log(j,tempList[j]);
				dom += '<dd><a href="'+tempList[j][1]+'">'+tempList[j][0]+'</a></dd>';
		}
		dom += 		'</dl>'+
				'</li>';
    }; 
	console.log(dom);
	$('#leftMenus').html(dom);
}*/
</script>