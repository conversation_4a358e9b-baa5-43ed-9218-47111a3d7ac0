<?php
namespace app\admin\model;

use think\Model;

class Stock extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'zh_stock_borrow';
    // 如果设置了模型的数据集返回类型的话，则可以简化使用
    protected $resultSetType = 'collection';

    //自定义初始化
    protected function initialize(){
        //需要调用`Model`的`initialize`方法
        parent::initialize();
        //TODO:自定义的初始化
    }

    public function test(){
        $data = self::where('uid',8)->select();
        // $data = self::find(1);
        return $data->toArray();
    }
    public function stockList($w=array()){
        if( empty($w['s.category']) ){
            $w['s.category'] = 1;
        }

        $data = self::alias('s')
                ->join('members m1','m1.id = s.uid','left')
                ->join('member_info m2','m2.uid = s.uid','left')
                ->join('(SELECT sum(money) as "add",borrow_id,status FROM zh_stock_addmoney WHERE status=1 group by borrow_id) f', 'f.borrow_id = s.id','left')
                ->join('(SELECT sum(money) as "draw",borrow_id,status FROM zh_stock_drawprofit WHERE status=1 group by borrow_id) d','d.borrow_id = s.id','left')
                ->field('s.*,m1.user_name,m2.phone,m2.real_name,f.add,d.draw')
                ->order('id DESC')
                ->where($w);
                //->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        return $data;
    }
    public function getStock($borrow_id=0){
        $data = self::alias('s')
                ->join('members m1','m1.id = s.uid','left')
                ->join('member_info m2','m2.uid = s.uid','left')
                ->field('s.*,m1.user_name,m2.phone,m2.real_name,id_card')
                ->where('s.id',$borrow_id)
                ->find();
        if($data){
            return $data->toArray();
        }else{
            return false;
        }
    }
    // 获取uid下的配资列表
    static public function stockListByUid($uid=0,$limit=10,$simple=false){
        if( !$uid ){
            return false;
        }
        $data = self::alias('s')
                ->join('members m1','m1.id = s.uid','left')
                ->join('member_info m2','m2.uid = s.uid','left')
                ->field('s.*,m1.user_name,m2.phone,m2.real_name')
                ->order('id DESC,s.status asc')
                ->where('s.status','NEQ',-1)
                ->where('s.uid',$uid)
                // ->select();
                ->paginate($limit, $simple, ['query'=>request()->param()]);

        return $data;
    }


}
