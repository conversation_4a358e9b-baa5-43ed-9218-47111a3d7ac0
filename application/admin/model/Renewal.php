<?php 
namespace app\admin\model;

use think\Model;

class Renewal extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'zh_stock_renewal';
    // 如果设置了模型的数据集返回类型的话，则可以简化使用
    protected $resultSetType = 'collection';

    //自定义初始化
    protected function initialize(){
        //需要调用`Model`的`initialize`方法
        parent::initialize();
        //TODO:自定义的初始化
    }

    public function test(){
        $data = self::where('uid',8)->select();
        // $data = self::find(1);
        return $data->toArray();
    }
    public function renewalList($w=array()){
        if( empty($w['r.category']) ){
            $w['r.category'] = 1;
        }

        $data = self::alias('r')
                ->join('stock_borrow s','r.borrow_id = s.id','left')
                ->join('members m1','m1.id = r.uid','left')
                ->join('member_info m2','m2.uid = r.uid','left')
                ->field('r.*,r.type AS auto_type,s.home_user,s.type,s.end_time,m1.user_name,m2.phone,m2.real_name')
                ->order('id DESC')
                ->where($w)
                // ->select();
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        return $data;
    }
    public function getRenewal($id=0){
        $data = self::alias('r')
                ->join('stock_borrow s','r.borrow_id = s.id','left')
                ->join('members m1','m1.id = r.uid','left')
                ->join('member_info m2','m2.uid = r.uid','left')
                ->field('r.*,r.type AS auto_type,s.home_user,s.type,s.status AS stock_status,s.end_time,s.borrow_duration AS last_duration,m1.user_name,m2.phone,m2.real_name')
                ->where('r.id',$id)
                ->find();
        if($data){
            return $data->toArray();
        }else{
            return false;
        }
    }
    

}