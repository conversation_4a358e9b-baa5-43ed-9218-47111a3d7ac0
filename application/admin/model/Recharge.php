<?php 
namespace app\admin\model;

use think\Model;

class Recharge extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'zh_member_recharge';
    // 如果设置了模型的数据集返回类型的话，则可以简化使用
    protected $resultSetType = 'collection';

    //自定义初始化
    protected function initialize(){
        //需要调用`Model`的`initialize`方法
        parent::initialize();
        //TODO:自定义的初始化
    }

    public function test(){
        $data = self::where('uid',17)->field('create_time',true)->select();
        // $data = self::find(1);
        return $data->toArray();
    }

    public function rechargeList($w=array()){
        $data = self::alias('r')
                ->join('member_info m1','r.uid = m1.uid','left')
                ->field('r.*,m1.real_name')
                ->order('id DESC')
                ->where($w);
                // ->select();
                //->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        return $data;
    }
    public function getRecharge($id=0){
        $data = self::alias('r')
                ->join('member_info m1','m1.uid = r.uid','left')
                ->field('r.*,m1.real_name')
                ->where('r.id',$id)
                ->find();
        if($data){
            return $data->toArray();
        }else{
            return false;
        }
    }


}