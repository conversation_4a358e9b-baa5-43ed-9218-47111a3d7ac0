<?php 
namespace app\admin\model;

use think\Model;

class Stopfinancing extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'zh_stock_stopfinancing';
    // 如果设置了模型的数据集返回类型的话，则可以简化使用
    protected $resultSetType = 'collection';

    //自定义初始化
    protected function initialize(){
        //需要调用`Model`的`initialize`方法
        parent::initialize();
        //TODO:自定义的初始化
    }

    public function test(){
        $data = self::where('uid',8)->select();
        // $data = self::find(1);
        return $data->toArray();
    }
    public function stopfinancingList($w=array()){
        if( empty($w['s.category']) ){
            $w['s.category'] = 1;
        }

        $data = self::alias('s')
                ->join('stock_borrow s2','s.borrow_id = s2.id','left')
                ->join('members m1','m1.id = s.uid','left')
                ->join('member_info m2','m2.uid = s.uid','left')
                ->field('s.*,s2.home_user,s2.borrow_duration,s2.type,s2.end_time,m1.user_name,m2.phone,m2.real_name')
                ->order('id DESC')
                ->where($w)
                // ->select();
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        return $data;
    }
    public function getStopfinancing($id=0){
        $data = self::alias('s')
                ->join('stock_borrow s2','s.borrow_id = s2.id','left')
                ->join('members m1','m1.id = s.uid','left')
                ->join('member_info m2','m2.uid = s.uid','left')
                ->field('s.*,s2.home_user,s2.type,s2.deposit_money,s2.borrow_money,s2.borrow_duration,s2.multiple,s2.end_time,s2.status AS stock_status,m1.user_name,m2.phone,m2.real_name')
                ->where('s.id',$id)
                ->find();
        if($data){
            return $data->toArray();
        }else{
            return false;
        }
    }
    

}