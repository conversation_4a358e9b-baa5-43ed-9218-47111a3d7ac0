<?php 
namespace app\admin\model;

use think\Model;

class Withdraw extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'zh_member_withdraw';
    // 如果设置了模型的数据集返回类型的话，则可以简化使用
    protected $resultSetType = 'collection';

    //自定义初始化
    protected function initialize(){
        //需要调用`Model`的`initialize`方法
        parent::initialize();
        //TODO:自定义的初始化
    }

    public function test(){
        $data = self::where('uid',17)->field('create_time',true)->select();
        // $data = self::find(1);
        return $data->toArray();
    }

    public function withdrawList($w=array()){
        $data = self::alias('w')
                ->join('member_info m1','w.uid = m1.uid','left')
                ->join('member_bank m2','w.bank_id = m2.id','left')
                ->where($w)
                ->field('w.*,m1.real_name,m2.bank_name,m2.bank_address,m2.bank_address,m2.province,m2.city,m2.account_name')
                ->order('id DESC');
                // ->select();
                //->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        return $data;
    }
    public function getWithdraw($id=0){
        $data = self::alias('w')
                ->join('member_info m1','w.uid = m1.uid','left')
                ->join('member_bank m2','w.bank_id = m2.id','left')
                ->where('w.id',$id)
                ->field('w.*,m1.real_name,m2.bank_name,m2.bank_address,m2.number,m2.bank_address,m2.province,m2.city,m2.account_name')
                ->find();
        if($data){
            return $data->toArray();
        }else{
            return false;
        }
    }


}