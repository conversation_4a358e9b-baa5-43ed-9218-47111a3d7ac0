<?php
namespace app\admin\controller;

use think\Controller;
use think\Request;
use app\common\controller\Admin;
use think\Loader;
use \think\Validate;

class Index extends Admin{

    protected function _initialize(){
        parent::_initialize();
        // echo 'Admin Index init<hr/>';
    }

    public function index(){
        $stock_borrow = db('stock_borrow');
        $member_recharge = db('member_recharge');
        $member_withdraw = db('member_withdraw');
        $stock_addfinancing = db('stock_addfinancing');
        $member_money = db('member_money');
        $stock_renewal = db('stock_renewal');
        $transfer = db('transfer');

        // 代办事项
        $chongzhi = $member_recharge->where('status',0)->count('id');//线下充值
        $tixian = $member_withdraw->where('status',0)->count('id');
        // 股票配资
        $peizi = $stock_borrow->where('status',0)->where('category',1)->count('id');
        $kuoda = $stock_addfinancing->where('status',0)->where('category',1)->count('id');
        $tiying = db('stock_drawprofit')->where('status',0)->where('category',1)->count('id');
        $bukui = db('stock_addmoney')->where('status',0)->where('category',1)->count('id');//补亏
        $xuqi = $stock_renewal->where('status',0)->where('category',1)->count('id');
        $zhongzhi = db('stock_stopfinancing')->where('status',0)->where('category',1)->count('id');
        // 期货配资
        $future['peizi'] = $stock_borrow->where('status',0)->where('category',2)->count('id');
        $future['kuoda'] = $stock_addfinancing->where('status',0)->where('category',2)->count('id');
        $future['tiying'] = db('stock_drawprofit')->where('status',0)->where('category',2)->count('id');
        $future['bukui'] = db('stock_addmoney')->where('status',0)->where('category',2)->count('id');//补亏
        $future['xuqi'] = $stock_renewal->where('status',0)->where('category',2)->count('id');
        $future['zhongzhi'] = db('stock_stopfinancing')->where('status',0)->where('category',2)->count('id');
        $this->assign('future',$future);

        $this->assign('chongzhi',$chongzhi);//代办事项
        $this->assign('tixian',$tixian);
        $this->assign('peizi',$peizi);
        $this->assign('kuoda',$kuoda);
        $this->assign('tiying',$tiying);
        $this->assign('bukui',$bukui);
        $this->assign('xuqi',$xuqi);
        $this->assign('zhongzhi',$zhongzhi);

        $data = array();//综合事项
        if( cache('admin_data') ){
            $data = cache('admin_data');
        }else{
            $members = db('members');
            $count1 = $members->count('id');//总用户数
            $count2 = $stock_borrow->where('status','EGT',2)->group('uid')->count('id');//配资用户数
            if( !$count1 ){
                $count3 = 0;
            }else{
                $count3 = (round($count2/$count1,2)) * 100;//配资用户数占比(%)
            }
            $count4 = $stock_borrow
                        ->where('status','EGT',2)->where('type',4)
                        ->group('uid')->count('id');//免费体验用户
            $count5 = $stock_borrow
                        ->where('status','EGT',2)->where('type',3)
                        ->group('uid')->count('id');//免息配资用户
            $count6 = $stock_borrow
                        ->where('status','EGT',2)->where('type',1)
                        ->group('uid')->count('id');//按天配资用户
            $count7 = $stock_borrow
                        ->where('status','EGT',2)->where('type',2)
                        ->group('uid')->count('id');//按月配资用户
            $count8 = $stock_borrow
                        ->where('status','EGT',2)->where('type',8)
                        ->group('uid')->count('id');//VIP配资用户
            $count9 = $members->whereTime('reg_time','yesterday')
                        ->count('id');//昨日新增用户数
            $count10 = $members->whereTime('reg_time','-7 days')
                        ->count('id');//近7日新增用户数
            $count11 = $members->whereTime('reg_time','-30 days')
                        ->count('id');//近30日新增用户数

            // 财务数据-总览 单位（分）//暂时废弃
            /*$count12 = $member_recharge->where('status',1)->sum('money');
            $count13 = $member_withdraw->where('status',1)->sum('money');
            $glf1 = $stock_borrow->where('status','EGT',2)->sum('borrow_interest');
            $glf2 = $stock_addfinancing->where('status',1)->sum('borrow_fee');
            $glf3 = db('stock_renewal')->where('status',1)->sum('borrow_fee');
            $count14 = $member_money->sum('money_freeze');//总冻结金额
            $count15 = $member_money->sum('account_money');//总余额*/

            $data = array(
                    'zyhs'=>$count1,//总用户数
                    'pzyhs'=>$count2,//配资用户数
                    'pzyhzb'=>$count3,//配资用户数占比
                    'mftyyh'=>$count4,//免费体验用户
                    'mxpzyh'=>$count5,//免息配资用户
                    'antianpzyh'=>$count6,//按天配资用户
                    'anyuepzyh'=>$count7,//按天配资用户
                    'vippzyh'=>$count8,//VIP配资用户
                    'zrxzyh'=>$count9,//昨日新增用户数
                    'j7rxzyh'=>$count10,//近7日新增用户数
                    'j30rxzyh'=>$count11,//近30日新增用户数

                    /*'zongchongzhi'=>$count12,//总充值
                    'zongtixian'=>$count13,//总提现
                    'zonglixi'=>$glf1+$glf2+$glf3,//总管理费
                    'zongdongjie'=>$count14,//总冻结金额
                    'zongyue'=>$count15,//总余额*/
                );
            cache('admin_data',$data,60*10);
        }
        // dump($data);die;

        // 财务数据-日期范围-默认当天
        $timestr = input('get.time/s','');
        if( $timestr ){
            $timestr = input('get.time/s');
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1].' 23:59:59');
        }else{
            $stime = strtotime(date('Y-m-d',time()));
            $etime = strtotime(date('Y-m-d'.' 23:59:59',time()));
        }
        $caiwu_where['add_time'] = array('between time', [$stime,$etime]);
        $count16 = $member_recharge
                        ->where(['status'=>1,'way'=>'offline'])
                        ->where($caiwu_where)
                        ->sum('money');
        $count17 = $member_recharge
                        ->where(['status'=>1,'way'=>'wx'])
                        ->where($caiwu_where)
                        ->sum('money');
        $count18 = $member_recharge->where(['status'=>1,'way'=>'zfb'])
                                    ->where($caiwu_where)
                                    ->sum('money');
        $count19 = $transfer->where('type',1)->where($caiwu_where)->sum('money');
        $count20 = $transfer->where('type',2)->where($caiwu_where)->sum('money');
        $count21 = $member_withdraw->where('status',1)->where($caiwu_where)->sum('money');
        $count22 = $member_recharge->where('status',1)->where($caiwu_where)->sum('money');
        $count23 = db('stock_drawprofit')->where('status',1)->where('category',1)->where($caiwu_where)->sum('money');

        $caiwu = array(
                    'xianxiazonge'=>$count16,//线下充值总额
                    'wxzonge'=>$count17,//wx充值总额
                    'zfbzonge'=>$count18,//zfb充值总额
                    'zhuanzhang'=>$count19,//后台转账总额
                    'zsglf'=>$count20,//后台赠送管理费总额
                    'tiyingzonge'=>$count23,//提盈总额
                    'tixianzonge'=>$count21,//提现总额
                    'chongzhizonge'=>$count22,//充值总额

                );

        $this->assign('data',$data);//综合事项
        $this->assign('caiwu',$caiwu);//财务数据
        $this->assign('timestr',$timestr);//时间范围

        return view();
    }

    // 网站设置
    public function webSet(){
        $request = request();
        if($request->method()=='POST'){
            $data = input('post.');
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                foreach ($data as $k => $v) {
                    if( is_numeric($k) ){
                        db('global')->where('id',$k)->setField('text',$v);
                    }
                }

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'修改了网站设置';
                admin_log(session('admin_name'), $admin_info, $request->ip());

                cache('global_set',null);
                $this->success('更新成功');
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{
            $data = db('global')->field(true)->order('order_sn desc')->select();

            $this->assign('data',$data);
            return view();
        }
    }
    // 添加网站设置
    public function addpz(){
        $request = request();
        if($request->isAjax()){
            $data = input('post.');

            $add = array(
                    'name'=>$data['csname'],
                    'type'=>$data['csleix'],
                    'tip'=>$data['csshuom'],
                    'order_sn'=>$data['cspaix'],
                    'code'=>$data['csdaima'],
                );
            $res = db('global')->insert( $add );
            if($res){
                return ['status'=>1,'message'=>'添加成功'];
            }else{
                return ['status'=>-1,'message'=>'添加失败'];
            }
        }else{
            return view();
        }
    }
    public function delSet(){
        $request = request();
        if($request->isAjax()){
            $wid = input('post.wid/d',0);
            if( !$wid ){
                return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试'];
            }

            $mod = db('global');
            $must = $mod->where('id', $wid)->value('must');
            if( $must==1 ){
                return ['status'=>-1,'message'=>'删除失败，不能删除系统默认创建配置'];
            }
            $res = $mod->where('id', $wid)->delete();
            if($res){
                return ['status'=>1,'message'=>'删除成功'];
            }else{
                return ['status'=>-1,'message'=>'删除失败，请稍后再试'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }

    // 友情链接
    public function friendLink(){
        $data = db('friend')->field(true)->order('link_order desc,id desc')->paginate();

        $this->assign('data',$data);
        $this->assign('page', $data->render());

        return view();
    }
    public function dofriend(){
        $request = request();
        if($request->isAjax()){
            $data = input('post.');
            $id = input('post.id/d');
            $action = input('post.action/s');

            switch ($action) {
                case 'edit':
                        $save = array(
                            'link_txt'=>$data['mingcheng'],
                            'link_href'=>$data['dizhi'],
                            'link_img'=>$data['imglogo'],
                            'link_order'=>$data['shunxu'],
                            'is_show'=>$data['is_show'],
                            'f_type'=>$data['leixing']
                        );
                        $res = db('friend')->where('id',$id)->update($save);
                    break;
                case 'delete':
                    $res = db('friend')->where('id',$id)->delete();
                    break;
                default:
                    break;
            }
            if($res){
                return ['status'=>1,'message'=>'处理成功'];
            }else{
                return ['status'=>-1,'message'=>'处理失败'];
            }

        }else{
            abort(404,'页面不存在');
        }
    }
    public function addfl(){
        $request = request();
        if($request->isAjax()){
            $data = input('post.');
            $add = array(
                    'link_txt'=>$data['mingcheng'],
                    'link_href'=>$data['dizhi'],
                    'link_img'=>$data['imglogo'],
                    'link_order'=>$data['shunxu'],
                    'is_show'=>$data['is_show'],
                    'f_type'=>$data['leixing']
                );
            $res = db('friend')->insert($add);
            if($res){
                return ['status'=>1,'message'=>'添加成功'];
            }else{
                return ['status'=>-1,'message'=>'添加失败'];
            }

        }else{
            $data = [
                'id'         => '',    // JS 中会用到: var id = '{$data.id}';
                'f_type'     => '1',   // 预设选中「友情链接」
                'link_txt'   => '',
                'link_href'  => '',
                'link_img'   => '',
                'link_order' => '50',  // 预设排序值
                'is_show'    => '1',   // 预设选中「是」
            ];
            $action = 'add'; // 明确设定操作类型为 'add'

            // 检查是否为编辑操作
            if( input('?get.action') && input('get.action') === 'edit' ){
                $id_val = input('get.id/d');
                $action = 'edit'; // 更新操作类型为 'edit'
                $db_data = db('friend')->where('id', $id_val)->field(true)->find();
                
                if($db_data){
                    $data = $db_data; // 如果是编辑且找到了记录，则用数据库中的数据覆盖预设值
                } else {
                    // 如果请求编辑但未找到记录，可以选择报错或回退到添加模式
                    // 此处示例为报错并终止，避免表单显示不正确的状态
                    return $this->error('要编辑的友情链接不存在！'); 
                }
            }

            $this->assign('data',$data);
            $this->assign('action',$action); // 将 'add' 或 'edit' 传递给视图

            return view();
        }
    }


    public function upload(){
        if(request()->isAjax()){
            $file = request()->file('img');
            if($file){
                $info = $file
                        ->validate(['size'=>1024000,'ext'=>'jpg,jpeg,png,gif'])
                        ->move(ROOT_PATH . 'public' . DS . 'up' . DS . 'A' . DS);
                if($info){
                    $file_name = str_replace("\\", "/", $info->getSaveName());
                    $res = array(
                            'status'=>1,
                            'msessage'=>'图片上传成功',
                            'savename'=>'/up/A/'.$file_name,
                        );
                    // var_dump($res);die;
                }else{
                    // var_dump( $file->getError());
                    $res = array(
                            'status'=>-1,
                            'message'=>$file->getError(),
                        );
                }
            }else{
                $res = array(
                            'status'=>-2,
                            'message'=>'上传失败！请检查该文件大小格式',
                        );
            }

            return $res;
        }else{
            abort(404,'页面不存在');
        }
    }



    public function adv(){
        $action = input('param.action/s');
        switch ($action) {
            case 'add':
                    /*$data = db('ad')->where('id',1)->value('content');
                    $this->assign('data', htmlspecialchars_decode($data));*/
                    $this->assign('action',$action);
                    return view('addAdv');
                break;
            case 'edit':
                    $id = input('param.id/d');
                    if(!$id){
                        $this->error('缺少参数，请稍后再试！');
                    }
                    $data = db('ad')->where('id',$id)->field(true)->find();
                    $this->assign('id',$id);
                    $this->assign('action',$action);
                    $this->assign('data',$data);
                    // dump($data);die;
                    return view('addAdv');
                break;
            default:
                    $data = db('ad')->field(true)->order('id desc')->paginate();
                    $this->assign('data',$data);
                    $this->assign('page', $data->render());
                    return view();
                break;
        }
    }
    public function doadv(){
        if( request()->method()=='POST'){
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $data = input('post.');
                switch ($data['action']) {
                    case 'add':
                            $add = array(
                                    'title'=>$data['advname'],
                                    'hide'=>$data['adv_show'],
                                    'content'=>empty($data['contents']) ? '' : $data['contents'],
                                    'add_time'=>time(),
                                );
                            $res = db('ad')->insert( $add );
                        break;
                    case 'edit':
                            $id = input('post.id/d',0);
                            if(!$id){
                                $this->error('缺少参数，请稍后再试！');
                            }
                            $save = array(
                                    'title'=>$data['advname'],
                                    'hide'=>$data['adv_show'],
                                    'content'=>empty($data['contents']) ? '' : $data['contents'],
                                );
                            $res = db('ad')->where('id',$id)->update($save);
                            cache("ad{$id}", null);
                        break;
                    default:
                        break;
                }
                if($res){
                    $this->success('操作成功','index/adv');
                }else{
                    $this->error('操作失败');
                }

            }else{
                $this->error('不要重复提交数据！');
            }
        }else{
            abort(404,'页面不存在');
        }

    }
    public function delAdv(){
        $request = request();
        if( $request->method()=='POST'){
            if($request->isAjax()){
                $id = input('post.id/d',0);
                if( !$id ){
                    return ['status'=>-1,'message'=>'缺少参数，请稍后再试！'];
                }
                $res = db('ad')->where('id',$id)->delete();
                if($res){
                    return ['status'=>1,'message'=>'删除成功'];
                }else{
                    return ['status'=>2,'message'=>'删除失败！'];
                }
            }
        }else{
            abort(404,'页面不存在');
        }
    }


    public function hetong(){
        if( request()->method()=='POST' ){
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $data = input('post.');
                $save = array(
                        'name'=>$data['mingcheng'],
                        'dizhi'=>$data['dizhi'],
                        'tel'=>$data['dianhua'],
                        'hetong_img'=>$data['upimg'],
                        'add_time'=>time(),
                        'deal_user'=>session('admin_name'),
                        'content'=>$data['contents']
                    );
                $res = db('hetong')->where('category',1)->update($save);
                if($res){
                    cache("hetong{$data['category']}",null);
                    $this->success('信息修改成功');
                }else{
                    $this->error('信息修改失败！');
                }
            }else{
                $this->error('不要重复提交数据！');
            }
        }else{
            $data = db('hetong')->where('category',1)->field(true)->find();

            $this->assign('data',$data);
            return view();
        }
    }
    public function qihuohetong(){
        if( request()->method()=='POST' ){
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $data = input('post.');
                $save = array(
                        'name'=>$data['mingcheng'],
                        'dizhi'=>$data['dizhi'],
                        'tel'=>$data['dianhua'],
                        'hetong_img'=>$data['upimg'],
                        'add_time'=>time(),
                        'deal_user'=>session('admin_name'),
                        'content'=>$data['contents']
                    );
                $res = db('hetong')->where('category',2)->update($save);
                if($res){
                    cache("hetong{$data['category']}",null);
                    $this->success('信息修改成功');
                }else{
                    $this->error('信息修改失败！');
                }
            }else{
                $this->error('不要重复提交数据！');
            }
        }else{
            $data = db('hetong')->where('category',2)->field(true)->find();

            $this->assign('data',$data);
            return view();
        }
    }
    public function zcxy(){
        if( request()->method()=='POST' ){
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $data = input('post.');
                $save = array(
                        'name'=>$data['mingcheng'],
                        'add_time'=>time(),
                        'deal_user'=>session('admin_name'),
                        'content'=>$data['contents']
                    );
                $res = db('hetong')->where('category',3)->update($save);
                if($res){
                    cache("hetong{$data['category']}",null);
                    $this->success('信息修改成功');
                }else{
                    $this->error('信息修改失败！');
                }
            }else{
                $this->error('不要重复提交数据！');
            }
        }else{
            $data = db('hetong')->where('category',3)->field(true)->find();

            $this->assign('data',$data);
            return view();
        }
    }


    public function adminLog(){

        $w = array();
        $timestr = '';
        if( input('get.time/s') ){
            $timestr = input('get.time/s');
            $time = explode( ' - ',input('get.time') );
            /*dump($timestr);
            dump($time);
            die;*/
            $w['create_time'] = array( 'between time',[$time[0],$time[1]] );
        }
        $data = db('admin_log')
                ->order('id DESC')
                ->where($w)
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        // dump($data);die;
        $this->assign('data',$data);
        $this->assign('timestr',$timestr);
        $this->assign('page', $data->render());
        return view('adminLog');
    }


    public function clearAll(){

        clearAll();

        return view();
    }





    // 导航菜单
    public function nav(){
        $data = db('navigation')
                ->where('parent_id',0)
                ->field('id,name,parent_id AS pid,url,sort_order,add_time,is_hidden')
                ->order('sort_order DESC')
                ->select();
        foreach ($data as $k => &$v) {
            $v['has'] = $this->_typeSon($v['id']);
        }
        unset($v);

        $this->assign('data',$data);
        return view();
    }
    public function addNav(){
        $request = request();
        if($request->isAjax()){
            $data = input('post.');
            $action = input('post.action','');
            $id = input('post.id/d',0);
            switch ($action) {
                case 'edit':
                    if( !$id ){
                        return ['status'=>-2,'message'=>'数据解析错误，请按规则重试！'];
                    }
                    $save = array(
                            'name'=>$data['mingcheng'],
                            'url'=>$data['lianjie'],
                            'sort_order'=>$data['paixu'],
                            'parent_id'=>$data['shangji'],
                            'is_hidden'=>$data['xianshi'],
                        );
                    $res = db('navigation')->where('id',$id)->update($save);
                    break;
                case 'delete':
                        if( !$id ){
                            return ['status'=>-3,'message'=>'数据解析错误，请按规则重试！'];
                        }
                        $mod = db('navigation');
                        $has = $mod->where('parent_id',$id)->field('id')->find();
                        if( $has ){
                            return ['status'=>-3,'message'=>'删除失败，该栏目下包含有子栏目，不能删除！'];
                        }
                        $res = $mod->where('id',$id)->delete();
                    break;
                default:
                    $add = array(
                            'name'=>$data['mingcheng'],
                            'url'=>$data['lianjie'],
                            'sort_order'=>$data['paixu'],
                            'parent_id'=>$data['shangji'],
                            'is_hidden'=>$data['xianshi'],
                            'add_time'=>time(),
                            'create_time'=>date('Y-m-d H:i:s',time())
                        );
                    $res = db('navigation')->insert($add);
                    break;
            }
            if($res){
                cache('navigation',null);
                return ['status'=>1,'message'=>'操作成功'];
            }else{
                return ['status'=>-1,'message'=>'操作失败！'];
            }
        }else{
            $id = input('param.id/d',0);
            $action = input('param.action/s','add');
            $data = array();
            if($action=='edit'){
                $data = db('navigation')->where('id',$id)->find();
            }

            $tree = getCategoryTree('navigation');
            $this->assign('tree',$tree);
            $this->assign('id',$id);
            $this->assign('data',$data);
            $this->assign('action',$action);
            return view('addNav');
        }
    }
    public function getNavList(){
        $type_id = input('post.type_id/d');
        if($type_id <= 0){
            return ['status'=>'-1','message'=>'数据错误！请重试'];
        }
        $data = db('navigation')->where('parent_id',$type_id)
                ->field('id,name,parent_id,url,sort_order,is_hidden,add_time')
                ->order('sort_order desc')
                ->select();
        $html = '';
        foreach ($data as &$v) {
            $v['has'] = $this->_typeSon($v['id']);
            $temp_iframe = url('index/addNav',['id'=>$v['id'],'action'=>'edit']);
            $temp_zs = $v['has']===true?'<i class="zhanshi"></i>':'';
            $temp_level = $this->_typeLeve($v['id']);

            $html .= '<tr id="list_'.$v['id'].'" data-id="'.$v['id'].'" parentid="'.$type_id.'" class="leve_'.$temp_level.'">
                        <td>'.$v['id'].'</td>
                        <td class="zsyc">
                            '.$temp_zs.$v['name'].'
                        </td>
                        <td>'.$v['url'].'</td>
                        <td>'.str_replace([1,2], ['显示','隐藏'], $v['is_hidden']).'</td>
                        <td>'.$v['sort_order'].'</td>
                        <td>'.date('Y-m-d H:i',$v['add_time']).'</td>
                        <td>
                            <a href="javascript:;" data-iframe="'.$temp_iframe.'" class="editAdv layui-bg-blue ft15">编辑</a>
                            &nbsp;
                            <a href="javascript:;" onclick="delConfirm( '.$v['id'].' )" class="layui-bg-red ft15">删除</a>
                        </td>
                    </tr>';
            unset($temp_iframe);
            unset($temp_level);
            unset($temp_level);
        }
        unset($v);
        return ['status'=>1,'data'=>$html];
    }
    private function _typeSon($pid){
        $condition['parent_id'] = intval($pid);
        $val = db('navigation')->field('id')->where($condition)->find();
        if($val){
            return true;
        }else{
            return false;
        }
    }
    private $typeleve=1;
    private $typeleve_default=1;
    private function _typeLeve($typeid){
        static $level=0;
        $condition['id'] = intval($typeid);
        $v = db('navigation')->field('parent_id')->where($condition)->find();
        if($v['parent_id']>0){
            $this->typeleve++;
            $this->_typeLeve($v['parent_id']);
        }else{
            $level = $this->typeleve;
            $this->typeleve = $this->typeleve_default;
        }
        return $level;
    }



    public function getTip(){
        $request = request();
        if($request->isAjax()){
            $now = time();
            $start = strtotime(date("Y-m-d 07:00:00"),$now);
            $end = strtotime(date("Y-m-d 22:00:00"),$now);
            if( $now>$start && $now<$end ){
                $res = db('tip')->where('status',0)->field('id,status,type')->select();
                if($res){
                    $tip_type = config('tip_type');
                    $message = '有任务待处理-';
                    foreach ($res as &$v) {
                        if( $v['type'] ){
                            $message .= $tip_type[$v['type']] . '-';
                        }
                    }
                    return [ 'status'=>1,'message'=>trim($message,'-'),'count'=>count($res) ];
                }else{
                    return ['status'=>-2,'message'=>'无任务'];
                }
            }else{
                return ['status'=>-1,'message'=>'休息中'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }

    // 线下充值
    public function offline(){
        $mod = db('recharge_set');
//        $offline_set = $mod->where('way','offline')->field(true)->find();
        $data = $mod->where('way','offline')->field(true)->select();

        $request = request();
        if($request->method()=='POST'){
            $data = input('post.');
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $save = array(
                    'payee'=>$data['shoukuan'],
                    'bank_name'=>$data['yinhang'],
                    'open_bank'=>$data['kaihuhang'],
                    'number'=>$data['zhanghao'],
                    'status'=>$data['status'],
                    'content'=>$data['contents'],
                );
                if( $offline_set ){
                    $res = $mod->where('way','offline')->update($save);
                }else{
                    $save['way'] = 'offline';
                    $save['create_time'] = date('Y-m-d H:i:s',time());
                    $res = $mod->insert($save);
                }
                if( $res ){
                    // 管理员日志
                    $admin_info = '管理员'.session('admin_name').'修改了线下充值设置';
                    admin_log(session('admin_name'), $admin_info, $request->ip());

                    $this->success('更新成功');
                }else{
                    $this->error('更新失败，稍后再试');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{

            $this->assign('data',$data);
//            $this->assign('offline_set',$offline_set);
            return view();
        }
    }

    // 线下充值
    public function offlineInfo(){
        $mod = db('recharge_set');
        $where = [
            'way' => 'offline',
            'id' => isset($_REQUEST['id']) ? $_REQUEST['id']  : input('id'),
        ];
        $offline_set = $mod->where($where)->field(true)->find();

        $request = request();
        // print_r($request);exit;
        if($request->method()=='POST'){

            $data = $_REQUEST['info'];

            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $save = array(
                    'payee'=>$data['shoukuan'],
                    'bank_name'=>$data['yinhang'],
                    'open_bank'=>$data['kaihuhang'],
                    'number'=>$data['zhanghao'],
                    'status'=>$data['status'],
                    'content'=> isset($data['contents']) ? $data['contents'] : '',
                );
                if( $offline_set ){
                    $res = $mod->where($where)->update($save);
                }else{
                    $save['way'] = 'offline';
                    $save['create_time'] = date('Y-m-d H:i:s',time());
                    $res = $mod->insert($save);
                }
                if( $res ){
                    // 管理员日志
                    $admin_info = '管理员'.session('admin_name').'修改了线下充值设置';
                    admin_log(session('admin_name'), $admin_info, $request->ip());

                    $this->success('更新成功', url('index/offline'));
                }else{
                    $this->error('更新失败，稍后再试');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{

            $this->assign('offline_set',$offline_set);
            return view();
        }
    }

    // 线下充值
    public function offlineAdd(){
        $mod = db('recharge_set');
        $where = [
            'way' => 'offline',
        ];
        $offline_set = $mod->where($where)->field(true)->find();
        $request = request();
        if($request->method()=='POST'){

            $data = $_REQUEST['info'];
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $save = array(
                    'payee'=>$data['shoukuan'],
                    'bank_name'=>$data['yinhang'],
                    'open_bank'=>$data['kaihuhang'],
                    'number'=>$data['zhanghao'],
                    'status'=>$data['status'],
                    'content'=>isset($data['contents']) ? $data['contents'] : '',
                    'way'=>'offline',
                );

                $save['way'] = 'offline';
                $save['create_time'] = date('Y-m-d H:i:s',time());
                $res = $mod->insert($save);

                if( $res ){
                    // 管理员日志
                    $admin_info = '管理员'.session('admin_name').'修改了线下充值设置';
                    admin_log(session('admin_name'), $admin_info, $request->ip());

                    $this->success('更新成功', url('index/offline'));
                }else{
                    $this->error('更新失败，稍后再试');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{

            $this->assign('offline_set',$offline_set);
            return view();
        }
    }

    public function scanqrcode(){
        $mod = db('recharge_set');
        $weixin = $mod->where('way','wx')->field(true)->find();
        $zhifubao = $mod->where('way','zfb')->field(true)->find();

        $request = request();
        if($request->method()=='POST'){
            $data = input('post.');
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $way = input('post.way/s','');
                $wxqrcode = input('post.wxqrcode/s','');
                $wxacccode = input ('post.wxacccode/s','');
                $zfbqrcode = input('post.zfbqrcode/s','');
                $zfbacccode = input ('post.zfbacccode/s','');
                if( !$way ){
                    $this->error('操作失败，请按照平台规则重试');
                }
                /*
                $save = array(
                        'status'=>$data['status'],
                        'content'=>$data['contents'],
                    );
                */
                switch ($way) {
                    case 'wx':
                        if( !$wxqrcode && !$wxacccode){
                            $this->error('操作失败，二维码图片或账号不能为空');
                        }
                        if($wxqrcode){
                            $save['qrcode'] = $wxqrcode;
                        }
                        if($wxacccode){
                            $save['number'] = $wxacccode;
                        }
                        $save['status'] = $data['status_1'];
                        $save['payee'] = $data['status_2'];
                        $save['content'] = $data['contents'];
                        if( $weixin ){
                            $res = $mod->where('way','wx')->update($save);
                        }else{
                            $save['way'] = 'wx';
                            $save['create_time'] = date('Y-m-d H:i:s',time());
                            $res = $mod->insert($save);
                        }
                        break;
                    case 'zfb':
                        if( !$zfbqrcode && !$zfbacccode){
                            $this->error('操作失败，二维码图片或账号不能为空');
                        }
                        if($zfbqrcode){
                            $save['qrcode'] = $zfbqrcode;
                        }
                        if($zfbacccode){
                            $save['number'] = $zfbacccode;
                        }
                        $save['status'] = $data['status_1'];
                        $save['payee'] = $data['status_2'];
                        $save['content'] = $data['contents'];
                        if( $zhifubao ){
                            $res = $mod->where('way','zfb')->update($save);
                        }else{
                            $save['way'] = 'zfb';
                            $save['create_time'] = date('Y-m-d H:i:s',time());
                            $res = $mod->insert($save);
                        }
                        break;
                    default:
                        $this->error('操作失败，请按照平台规则重试');
                        break;
                }
                /*dump($data);
                dump($save);
                dump($res);*/
                if( $res ){
                    // 管理员日志
                    $admin_info = '管理员'.session('admin_name').'修改了扫码充值设置';
                    admin_log(session('admin_name'), $admin_info, $request->ip());

                    $this->success('更新成功');
                }else{
                    $this->error('更新失败，稍后再试');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{

            $this->assign('weixin',$weixin);
            $this->assign('zhifubao',$zhifubao);
            return view();
        }
    }







    /**

        管理员设置

    */
    public function adminList(){
        $data = db('manage')->alias('m')
                ->join('acl a','m.u_group_id = a.id','left')
                ->order('m.id DESC')
                ->field('m.*,a.name AS group_name')
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function editAdmin(){
        $request = request();
        if($request->method()=='POST'){
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                if( !session('?admin_id') ){
                    $this->error('请先进行登录！','common/index');
                }
                $mod = db('manage');
                $data = input('post.');
                $action = input('post.action/s','');
                $admin_id = input('post.admin_id/d',0);
                if( !$admin_id || !$data['group'] || !$data['name'] ){
                    $this->error('缺少参数，请稍后再试！');
                }
                if( $data['pass'] != $data['pass2'] ){
                    $this->error('两次密码输入不一致！');
                }

                $manage = $mod->where('id', $admin_id)->field(true)->find();

                if( ($manage['u_group_id']!=1) && ($data['group']==1) ){
                    $this->error('不能创建超级管理员！');
                }

                switch ($action) {
                    case 'edit':
                        if( session('admin_name')!=$data['name'] ){
                            // $self = $mod->where('id',$admin_id)->value('user_name');
                            $self = $manage['user_name'];
                            $had = $mod->where('user_name',$data['name'])->count('id');
                            if( $had && ($self!=$data['name']) ){
                                $this->error('管理员账号不能重复');
                            }
                        }
                        $save = array(
                                'user_name'=>$data['name'],
                                'u_group_id'=>$data['group'],
                                'is_ban'=>$data['status'],
                                'content'=>$data['content']
                            );
                        if( $data['pass'] ){
                            $save['user_pass'] = set_pass($data['pass']);
                        }
                        $res = $mod->where('id', $admin_id)->update($save);
                        if( $res ){
                            session('admin_id',null);
                            session('admin_name',null);

                            $this->success('管理员信息修改成功，请重新登录');
                        }else{
                            $this->error('修改失败！请稍后重试');
                        }
                        break;
                    case 'add':
                        $had = $mod->where('user_name',$data['name'])->count('id');
                        if( $had ){
                            $this->error('管理员账号不能重复');
                        }
                        $add = array(
                            'user_name' => $data['name'],
                            'user_pass' => set_pass($data['pass']),
                            'u_group_id' => $data['group'],
                            'is_ban' => $data['status'],
                            'content' => $data['content'],
                            'phone' => '',
                            'add_time' => time(),
                            'codeStr' => '',
                            'real_name' => $data['name'],
                            'last_log_ip' => '',
                            'limit_ip' => null,
                            'qq' => ''
                        );
                        $res = $mod->insert($add);
                        if( $res ){
                            $this->success('添加管理员成功','index/adminList');
                        }else{
                            $this->error('添加管理员失败，请稍后再试');
                        }
                        break;
                    default:
                        $this->error('操作失败！请稍后重试');
                        break;
                }

            }else{
                $this->error('不要重复提交数据！');
            }

        }else{
            $action = input('param.action/s','');
            $admin_id = input('param.admin_id/d', session('admin_id'));
            $data = db('manage')->where('id', $admin_id)->field(true)->find();

            if( $data['u_group_id']!=1 ){
                $acl = db('acl')->where('id','NEQ',1)->field('id,name')->order('id DESC')->select();
            }else{
                $acl = db('acl')->field('id,name')->order('id DESC')->select();
            }

            $this->assign('acl',$acl);
            $this->assign('data',$data);
            $this->assign('action',$action);
            $this->assign('admin_id',$admin_id);
            $this->assign('admin_name', session('admin_name'));
            return view();
        }
    }
    public function delAdmin(){
        if( $this->admin_group!=1 ){
            return ['status'=>-1,'message'=>'暂无该权限'];
        }

        $request = request();
        if($request->isAjax()){
            $aid = input('post.admin_id/d',0);
            if( !$aid ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            $mod = db('manage');
            $data = $mod->where('id',$aid)->field(true)->find();
            if( !$data ){
                return ['status'=>-1,'message'=>'数据错误！请稍后重试'];
            }
            if( $data['id']==1 ){
                return ['status'=>-1,'message'=>'不能删除系统默认管理员'];
            }
            $res = $mod->where('id',$aid)->delete();
            if( $res ){
                return ['status'=>1,'message'=>'删除成功'];
            }else{
                return ['status'=>1,'message'=>'删除失败，请稍后再试'];
            }
        }else{
            $this->error('当前页面不存在','index');
        }
    }

    // 权限组管理
    public function adminAcl(){
        $data = db('acl')
                ->order('id DESC')
                ->field(true)
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function editAcl(){
        if( $this->admin_group!=1 ){
            $this->error('暂无该权限','index/index');
        }

        $request = request();
        if($request->method()=='POST'){
            $data = input('post.');
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                $gid = input('post.gid/d',0);
                $name = input('post.name/s','');
                $remark = input('post.remark/s','');
                $action = input('post.action/s','');
                $acl_mod = input('post.acl_mod/a',[]);
                if( !$name ){
                    $this->error('权限组名称不能为空');
                }

                switch ($action) {
                    case 'add':
                            $add = array(
                                    'name'=>$name,
                                    'value'=>json_encode($acl_mod),
                                    'remark'=>$remark,
                                    'add_time'=>time(),
                                );
                            $res = db('acl')->insert($add);
                            if( $res ){
                                $this->success('添加成功','index/adminAcl');
                            }else{
                                $this->error('添加失败，请稍后再试','index/adminAcl');
                            }
                        break;
                    case 'edit':
                            if( !$gid ){
                                $this->error('数据解析错误，请按规则重试！');
                            }
                            $save = array(
                                    'name'=>$name,
                                    'value'=>json_encode($acl_mod),
                                    'remark'=>$remark,
                                );
                            $res = db('acl')->where('id',$gid)->update($save);
                            if( $res ){
                                cache("admin_acl{$gid}", null);

                                $this->success('修改成功','index/adminAcl');
                            }else{
                                $this->error('修改失败，请稍后再试','index/adminAcl');
                            }
                        break;
                    default:
                        # code...
                        break;
                }

            }else{
                $this->error('不要重复提交表单！');
            }

        }else{
            $data = config('rabc') ?: []; // 確保 $data 是一個陣列
            if(empty($data)) {
                $data = [
                    'admin' => [
                        'mod' => '系統管理',
                        'acl' => [
                            'system' => [
                                'title' => '系統設置',
                                'list' => [
                                    'index' => '基本設置',
                                    'webSet' => '網站設置',
                                    'adv' => '廣告管理',
                                    'nav' => '導航管理'
                                ]
                            ]
                        ]
                    ]
                ];
            }
            $gid = input('param.gid/d',0);
            $action = input('param.action/s','');

            switch ($action) {
                    case 'add':
                        break;
                    case 'edit':
                            // 当前管理员
                            /*$admin_acl = array(
                                'index'=>['index','webSet','adv','nav', 'offline','scanqrcode'],
                                'stock'=>['stockSet','rateSet','subaccount'],
                            );*/
                            $acl = db('acl')->where('id',$gid)->field(true)->find();
                            $acl['admin_acl'] = json_decode($acl['value'], true);
                            // dump($acl);die;
                            $this->assign('acl',$acl);
                        break;
                    default:
                        break;
                }

            $this->assign('gid',$gid);
            $this->assign('data',$data);
            $this->assign('action',$action);
            return view();
        }
    }
    public function delAcl(){
        if( $this->admin_group!=1 ){
            return ['status'=>-1,'message'=>'暂无该权限'];
        }

        $request = request();
        if($request->isAjax()){
            $gid = input('post.gid/d',0);
            if( !$gid ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            $mod = db('acl');
            $data = $mod->where('id',$gid)->field(true)->find();
            if( !$data ){
                return ['status'=>-1,'message'=>'数据错误！请稍后重试'];
            }
            if( $data['id']==1 ){
                return ['status'=>-1,'message'=>'不能删除系统默认权限组'];
            }
            $res = $mod->where('id',$gid)->delete();
            if( $res ){
                return ['status'=>1,'message'=>'删除成功'];
            }else{
                return ['status'=>1,'message'=>'删除失败，请稍后再试'];
            }
        }else{
            $this->error('当前页面不存在','index');
        }
    }
    /**

        管理员设置

    */

    // 今日注册
    public function jrzc(){
        $data = db('extend_data')
                ->where('type','jrzc')
                ->order('id DESC')
                 ->paginate(10, false, ['query'=>request()->param()]);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function extend_data(){
        $request = request();
        if($request->isAjax()){
            $data = input('post.',[]);
            $action = input('post.action/s','');
            if( !$data ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }

            cache('extend_data', null);
            switch ($action) {
                case 'delete':
                    $id = input('post.id/d',0);
                    if( !$id ){
                        return ['status'=>-1,'message'=>'缺少参数，请按规则重试！'];
                    }
                    $res = db('extend_data')->where('id',$id)->delete();
                    if( $res ){
                        return ['status'=>1,'message'=>'数据删除成功'];
                    }else{
                        return ['status'=>-1,'message'=>'数据删除失败，请稍后再试'];
                    }
                    break;
                default:
                    $add = array(
                            'field1'=>empty($data['field1'])?'':$data['field1'],
                            'field2'=>empty($data['field2'])?'':$data['field2'],
                            'field3'=>empty($data['field3'])?'':$data['field3'],
                            'field4'=>empty($data['field4'])?'':$data['field4'],
                            'field5'=>empty($data['field5'])?'':$data['field5'],
                            'type'=>$data['type']
                        );
                    $res = db('extend_data')->insert($add);
                    if( $res ){
                        return ['status'=>1,'message'=>'数据添加成功'];
                    }else{
                        return ['status'=>-1,'message'=>'数据添加失败，请稍后再试'];
                    }
                    break;
            }

        }else{
            $type = input('param.type/s','');

            $this->assign('type',$type);
            return view();
        }
    }
    // 今日充值
    public function jrcz(){
        $data = db('extend_data')
                ->where('type','jrcz')
                ->order('id DESC')
                ->paginate(10, false, ['query'=>request()->param()]);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 配资达人
    public function pzdr(){
        $data = db('extend_data')
                ->where('type','pzdr')
                ->order('id DESC')
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 今日股票配资
    public function jrgppz(){
        $data = db('extend_data')
                ->where('type','jrgppz')
                ->order('id DESC')
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }





    // 站内信
    public function innerMsg(){
        $request = request();
        if( $request->isAjax() ){
            $action = input('post.action/s','');
            if( !$action ){
                return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试'];
            }
            $res = false;
            switch ($action) {
                case 'one':
                    $user_name = input('post.uname/s','');
                    $title = input('post.onetitle/s','');
                    $info = input('post.oneinfo/s','');
                    if( !$user_name || !$info || !$title ){
                        return ['status'=>-2,'message'=>'信息填写不完整，请按照平台规则重试'];
                    }
                    $uid = db('members')->where('user_name',$user_name)->value('id');
                    if( $uid ){
                        $res = addInnerMsg($uid, $title, $info);
                    }
                    break;
                case 'all':
                    $title = input('post.alltitle/s','');
                    $info = input('post.allinfo/s','');
                    if( !$info || !$title ){
                        return ['status'=>-3,'message'=>'信息填写不完整，请按照平台规则重试'];
                    }
                    $uid_arr = db('members')->field('id')->select();
                    if( $uid_arr ){
                        $res = addInnerMsg($uid_arr, $title, $info);
                    }
                    break;
                case 'smsall':
                        $info = input('post.smsallinfo/s','');
                        if( !$info ){
                            return ['status'=>-3,'message'=>'信息填写不完整，请按照平台规则重试'];
                        }
                        $res = db('member_info')->where('phone', 'not null')->field('phone')->select();
                        $i = 0;
                        $level = 2000;
                        $temp_arr = $phone_arr = array();
                        foreach ($res as $v) {
                            if( strlen($v['phone'])==11 ){
                                $temp_arr[] = $v['phone'];
                            }
                        }

                        $msgconfig = get_sms_param();
                        $data['Account']     = $msgconfig['user'];
                        $data['Pwd']         = $msgconfig['password'];
                        $url="http://api.feige.ee/Account/Balance";

                        $curl = curl_init();
                        curl_setopt($curl, CURLOPT_URL, $url);
                        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); //在HTTP请求中包含一个"User-Agent: "头的字符串。
                        curl_setopt($curl, CURLOPT_HEADER, 0); //启用时会将头文件的信息作为数据流输出。
                        curl_setopt($curl, CURLOPT_POST, true); //发送一个常规的Post请求
                        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);//Post提交的数据包
                        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); //启用时会将服务器服务器返回的"Location: "放在header中递归的返回给服务器，使用CURLOPT_MAXREDIRS可以限定递归返回的数量。
                        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //文件流形式
                        $content = curl_exec($curl);
                        curl_close($curl);
                        unset($curl);

                        $sms_res = json_decode($content,true);

                        if( !$sms_res['Balance'] ){
                            return ['status'=>-1,'message'=>'发送失败，请检查短信相关配置信息'];
                        }
                        if( count($temp_arr)>$sms_res['Balance'] ){
                            return ['status'=>-2,'message'=>'发送失败，短信剩余条数不足！'];
                        }

                        foreach ($temp_arr as $k2 => $v2) {
                            $phone_arr[$i][] = $v2;
                            if( ($level-1)==$k2 ){
                                $i++;
                            }
                        }
                        foreach ($phone_arr as $v3) {
                            $result = sendsms_hx( implode(',',$v3), $info );
                        }

                        if( $result ){
                            return ['status'=>1,'message'=>'发送成功'];
                        }else{
                            return ['status'=>-5,'message'=>'发送失败，请检查短信相关配置信息'];
                        }
                    break;
                default:
                    return ['status'=>-4,'message'=>'数据解析错误，请按照平台规则重试'];
                    break;
            }

            if( $res ){
                return ['status'=>1,'message'=>'操作成功'];
            }else{
                return ['status'=>-5,'message'=>'操作失败，请稍后再试'];
            }

        }else{

            return view();
        }

    }

    // 后台限制ip
    public function verifyIp1(){
        $request = request();
        if($request->isAjax()){
            $data = input('post.');
            $action = input('post.action/s','');
            $phone = input('post.phone/s','');
            $admin_phone = $this->global['admin_phone'];
            if( !$admin_phone ){
                return ['status'=>-1,'message'=>'请先设置管理员手机号！'];
            }
            if( $phone!=$admin_phone ){
                return ['status'=>-2,'message'=>'管理员手机号码不正确！'];
            }
            switch ($action) {
                case 'sendcode':
                    $common_mod = new \app\index\controller\Common;
                    $res = $common_mod->user_verify();
                    return $res;
                    break;
                case 'verify':
                    if( verify_code($phone,$data['vcode']) ){
                        session('temp_verifyIp',time()+60*5);

                        return ['status'=>1,'message'=>'验证成功，即将跳转'];
                    }else{
                        return ['status'=>-4,'message'=>'短信验证码输入不正确，请重新输入'];
                    }
                    break;
                default:
                    break;
            }

        }else{
            if( session('?temp_verifyIp') && ( time()<session('temp_verifyIp') ) ){
                $this->redirect('Index/verifyIp2');
            }

            $admin_phone = substr_replace($this->global['admin_phone'],'****',3,4);

            $this->assign('admin_phone', $admin_phone);
            return view();
        }
    }
    public function verifyIp2(){
        $sum = 8;//允许最多IP
        $mod = db('verifyip');
        $request = request();
        if($request->isAjax()){
            if( !session('?temp_verifyIp') || ( time()>session('temp_verifyIp') ) ){
                return ['status'=>-1,'message'=>'管理员验证超时，请退出页面重新验证！'];
            }
            $content = input('post.content/s','');
            if( !$content ){
                return ['status'=>-2,'message'=>'IP列表不能为空！'];
            }
            $temp_arr = explode(',', trim($content,','));
            if( empty($temp_arr) ){
                return ['status'=>-3,'message'=>'IP列表格式不正确！'];
            }
            if( count($temp_arr)>$sum ){
                return ['status'=>-4,'message'=>'IP列表不能超过'.$sum.'条IP'];
            }

            $temp_add = array();
            $admin_name = session('admin_name');
            foreach ($temp_arr as $v) {
                if( empty($v) ){
                    return ['status'=>-3,'message'=>'IP列表格式不正确！'];
                }
                $temp_add[] = array(
                        'ip'=>$v,
                        'admin_name'=>$admin_name
                    );
            }

            $del = $mod->where('status',1)->delete();
            $res = $mod->insertAll($temp_add);
            if( $res ){
                cache('allow_ip', null);
                session('temp_verifyIp', null);

                return ['status'=>1,'message'=>'操作成功'];
            }else{
                return ['status'=>-4,'message'=>'操作失败，请稍后再试'];
            }
        }else{
            if( !session('?temp_verifyIp') || ( time()>session('temp_verifyIp') ) ){
                $this->redirect('Index/verifyIp1');
            }

            $ip_txt = '';
            $allow_ip = array();
            $ip = $mod->field('ip')->select();
            if( $ip ){
                foreach ($ip as $v) {
                    $allow_ip[] = $v['ip'];
                }

                $ip_txt = implode(',', $allow_ip);
            }

            $diff = $sum - count($allow_ip);

            $this->assign('diff',$diff);
            $this->assign('ip_txt',$ip_txt);
            return view();
        }
    }
    public function setipTip(){
        if( admin_verifyip(request()->ip()) ){

            // abort(404,'页面不存在');
            $this->error('当前页面不存在','index');
        }else{

            return view();
        }

    }

   public function codeList()
    {
        $data = db('manage')->alias('m')
            ->join('acl a','m.u_group_id = a.id','left')
            ->order('m.id DESC')
            ->field('m.*,a.name AS group_name')
            ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);
//        print_r($data);exit;
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }

	public function editCode(){
        $request = request();
        if($request->method()=='POST'){
            $token = input('post.__token__/s','');
            if( Validate::token('__token__','',['__token__'=>$token]) ){
                if( !session('?admin_id') ){
                    $this->error('请先进行登录！','common/index');
                }
                $mod = db('manage');
                $data = input('post.');
                $action = input('post.action/s','');
                $admin_id = input('post.admin_id/d',0);
                if (!$data['codeStr'])
                {
                    Loader::import('GoogleAuthenticator-master.PHPGangsta.GoogleAuthenticator',EXTEND_PATH);
                    $ga = new \PHPGangsta_GoogleAuthenticator();
                    $mySecret = $ga->createSecret();
                    $data['codeStr'] = $mySecret;
                }
                if( session('admin_name')!=$data['name'] ){
                    $admininfo = $mod->where('user_name',$data['name'])->find();
                    if( !$admininfo ){
                        $this->error('管理员账号不存在');
                    }
                }
                $save = array(
                    'codeStr'=>$data['codeStr'],
                    // 'limit_ip'=>$data['limit_ip']
                );
                $res = $mod->where('id', $admin_id)->update($save);
                if( $res ){
                    $this->success('管理员信息修改成功','index/codeList');
                }else{
                    $this->error('修改失败！请稍后重试');
                }

            }else{
                $this->error('不要重复提交数据！');
            }

        }else{
            $action = input('param.action/s','');
            $admin_id = input('param.admin_id/d', session('admin_id'));
            $data = db('manage')->where('id', $admin_id)->field(true)->find();
            if( $this->admin_group!=1 ){
                $acl = db('acl')->where('id','NEQ',1)->field('id,name')->order('id DESC')->select();
            }else{
                $acl = db('acl')->field('id,name')->order('id DESC')->select();
            }

            $this->assign('acl',$acl);
            $this->assign('data',$data);
            $this->assign('action',$action);
            $this->assign('admin_id',$admin_id);
            $this->assign('admin_name', session('admin_name'));
            return view();
        }
    }


}
