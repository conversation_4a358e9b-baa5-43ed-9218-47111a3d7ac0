<?php
namespace app\admin\controller;

use think\Controller;
use think\Request;
// use app\common\controller\Admin;

class Common extends Controller{
	protected function _initialize(){
		parent::_initialize();

    }

    public function _empty(){
        // echo '空操作！Admin CommonController';

        $this->error('页面未找到！');
    }
    
    public function index(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.');
            $vcode = $data['vcode'];
            if(captcha_check($vcode)){
                $mod = db('manage');
                $info = $mod->where('user_name',$data['uname'])->field(true)->find();
                if( !$info ){
                    return ['status'=>-1,'message'=>'无此管理员信息'];
                }
                if( $info['is_ban']==1 ){
                    return ['status'=>-1,'message'=>'该管理员已被禁用'];
                }
                $v_pass = v_pass($data['passwd'] , $info['user_pass']);
                if( !$v_pass ){
                    return ['status'=>-2,'message'=>'账号密码错误'];
                }
                $ip = $request->ip();
                $save = array(
                        'last_log_time'=>time(),
                        'last_log_ip'=>$ip
                    );
                $save_res = $mod->where('id',$info['id'])->update($save);
                admin_log($info['user_name'], '管理员'.$info['user_name'].'登录成功', $ip);

                session('admin_id' , $info['id']);
                session('admin_name' , $info['user_name']);
                session('admin_level' , $info['u_group_id']);

                return ['status'=>1,'message'=>'后台登录成功'];
            }else{
                return ['status'=>-1,'message'=>'图形验证码错误！'];
            }
        }else{
            return view();
        }
    }

    public function logout(){
        session('admin_id',null);
        session('admin_name',null);

        $this->success('注销成功',url('/'));
    }

    

}
