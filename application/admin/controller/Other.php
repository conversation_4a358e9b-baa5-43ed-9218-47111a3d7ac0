<?php

namespace app\admin\controller;

use think\Request;
use app\common\controller\Admin;
use \think\Validate;
use \think\Db;

class Other extends Admin{
	protected function _initialize(){
		parent::_initialize();
        $this->hola = 'Admin Other hola !!!';
    }

    public function pclunbo(){
        $data = db('slide')->where('type',1)->field(true)->order('order DESC')->select();

        $this->assign('data',$data);
        return view();
    }
    public function doSlide(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.');
            $action = input('post.action/s');
            $model = db('slide');
            switch ($action) {
                case 'edit':
                        $id = input('post.id/d');
                        if(!$id){
                            return ['status'=>-1,'message'=>'非法操作！'];
                        }
                        $save = array(
                                'link'=>$data['lianjie'],
                                'order'=>$data['shunxu'],
                                'image'=>$data['image'],
                                'type'=>$data['type']
                            );
                        // dump($id);
                        // dump($save);die;
                        $res = $model->where('id',$id)->update($save);
                    break;
                case 'delete':
                        $id = input('post.id/d');
                        if(!$id){
                            return ['status'=>-1,'message'=>'非法操作！'];
                        }
                        $res = $model->where('id',$id)->delete();
                    break;
                default:
                        $add = array(
                                'link'=>$data['lianjie'],
                                'order'=>$data['shunxu'],
                                'image'=>$data['image'],
                                'type'=>$data['type'],
                                'create_time'=>date('Y-m-d H:i:s',time())
                            );
                        // dump($add);die;
                        $res = $model->insert($add);
                    break;
            }
            if($res){
                return ['status'=>1,'message'=>'操作成功'];
            }else{
                return ['status'=>2,'message'=>'操作失败！'];
            }

        }else{
            $type = input('param.type/d');
            $action = input('param.action/s');
            $data = array();
            if($action=='edit'){
                $id = input('param.id/d');
                $data = db('slide')->where('id',$id)->field(true)->find();
            }

            $this->assign('type',$type);
            $this->assign('action',$action);
            $this->assign('data',$data);
            return view();
        }
    }
    public function waplunbo(){
        $data = db('slide')->where('type',2)->field(true)->order('order DESC')->select();

        $this->assign('data',$data);
        return view();
    }


    public function kefu(){
        $type = input('param.type/s');
        switch ($type) {
            case 'qq':
            case 'tel':
                    $data = array();
                    $action = input('param.action/s');
                    if($action=='edit'){
                        $id = input('param.id/d');
                        $data = db('kefu')->where('id',$id)->field(true)->find();
                    }
                    $this->assign('type',$type);
                    $this->assign('data',$data);
                    $this->assign('action',$action);
                    return view('add_kefu');
                break;
            default:
                    $data = db('kefu')->field(true)->order('order DESC')->select();
                    $qq_data = $tel_data = array();
                    foreach ($data as $k => $v) {
                        if($v['type']==1) $qq_data[] = $v;
                        if($v['type']==2) $tel_data[] = $v;
                    }
                    $this->assign('qq_data',$qq_data);
                    $this->assign('tel_data',$tel_data);
                    return view();
                break;
        }

    }
    public function doKefu(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.');
            $action = input('post.action/s');
            switch ($action) {
                case 'add':
                        $add = array(
                                'number'=>$data['haoma'],
                                'title'=>$data['mingcheng'],
                                'order'=>$data['shunxu'],
                                'is_show'=>$data['is_show'],
                                'type'=>$data['type']
                            );
                        $res = db('kefu')->insert($add);
                        if($res){
                            return ['status'=>1,'message'=>'添加成功'];
                        }else{
                            return ['status'=>-1,'message'=>'添加失败'];
                        }
                    break;
                case 'edit':
                        $id = input('post.id/d');
                        $save = array(
                                'number'=>$data['haoma'],
                                'title'=>$data['mingcheng'],
                                'order'=>$data['shunxu'],
                                'is_show'=>$data['is_show'],
                                'type'=>$data['type']
                            );
                        $res = db('kefu')->where('id',$id)->update($save);
                        if($res){
                            return ['status'=>1,'message'=>'修改成功'];
                        }else{
                            return ['status'=>-1,'message'=>'修改失败'];
                        }
                    break;
                case 'delete':
                        $id = input('post.id/d');
                        $res = db('kefu')->where('id',$id)->delete();
                        if($res){
                            return ['status'=>1,'message'=>'删除成功'];
                        }else{
                            return ['status'=>-1,'message'=>'删除失败'];
                        }
                    break;
                default:
                    break;
            }
            
        }else{
            abort(404,'页面不存在');
        }

    }




public function duanxin(){
        $request = request();
        if( $request->method()=='POST' ){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $data = input('post.');

                $save = array(
                        'user'=>$data['user'],
                        'password'=>$data['password'],
                        'sign'=>$data['sign'],
                    );

                $res = db('sms_set')->where('id',1)->update($save);
                if($res){
                    cache('sms_set',null);
                    $this->success('信息更新成功');
                }else{
                    $this->error('信息更新失败或未作更改');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{
            
            $msgconfig = get_sms_param();
            $data=array(
                    'action'=>'overage',
                    'userid'=>'',
                    'account'=>$msgconfig['user'],
                    'password'=>$msgconfig['password'],
                );
            $url='https://dx.ipyy.net/smsJson.aspx';
     
            $curl = curl_init();  
            curl_setopt($curl, CURLOPT_URL, $url);    
            curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); //在HTTP请求中包含一个"User-Agent: "头的字符串。        
            curl_setopt($curl, CURLOPT_HEADER, 0); //启用时会将头文件的信息作为数据流输出。   
            curl_setopt($curl, CURLOPT_POST, true); //发送一个常规的Post请求  
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);//Post提交的数据包  
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); //启用时会将服务器服务器返回的"Location: "放在header中递归的返回给服务器，使用CURLOPT_MAXREDIRS可以限定递归返回的数量。     
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //文件流形式
            $content = curl_exec($curl);  
            curl_close($curl);  
            unset($curl);

            $result = json_decode($content,true);

            // file_put_contents("send.txt", json_encode($result).' | '.PHP_EOL,FILE_APPEND);

            $result['Balance'] = $result['overage'];

            $this->assign('result',$result);
            $this->assign('msgconfig',$msgconfig);
            return view();
        }
    }
    public function duanxin_feige(){
        $request = request();
        if( $request->method()=='POST' ){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $data = input('post.');

                $save = array(
                        'user'=>$data['user'],
                        'password'=>$data['password'],
                        'sign'=>$data['sign'],
                    );

                $res = db('sms_set')->where('id',1)->update($save);
                if($res){
                    cache('sms_set',null);
                    $this->success('信息更新成功');
                }else{
                    $this->error('信息更新失败或未作更改');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{
            
            $msgconfig = get_sms_param();
            $data['Account']     = $msgconfig['user'];
            $data['Pwd']         = $msgconfig['password']; 
            $url="http://api.feige.ee/Account/Balance";
     
            $curl = curl_init();  
            curl_setopt($curl, CURLOPT_URL, $url);    
            curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); //在HTTP请求中包含一个"User-Agent: "头的字符串。        
            curl_setopt($curl, CURLOPT_HEADER, 0); //启用时会将头文件的信息作为数据流输出。   
            curl_setopt($curl, CURLOPT_POST, true); //发送一个常规的Post请求  
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);//Post提交的数据包  
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); //启用时会将服务器服务器返回的"Location: "放在header中递归的返回给服务器，使用CURLOPT_MAXREDIRS可以限定递归返回的数量。     
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //文件流形式
            $content = curl_exec($curl);  
            curl_close($curl);  
            unset($curl);

            $result = json_decode($content,true);

            // dump($info);die;

            $this->assign('result',$result);
            $this->assign('msgconfig',$msgconfig);
            return view();
        }
    }
    public function duanxin_mandao(){
        $request = request();
        if( $request->method()=='POST' ){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $data = input('post.');

                $save = array(
                        'user'=>$data['user'],
                        'password'=>$data['password'],
                        'sign'=>$data['sign'],
                    );

                $res = db('sms_set')->where('id',1)->update($save);
                if($res){
                    cache('sms_set',null);
                    $this->success('信息更新成功');
                }else{
                    $this->error('信息更新失败或未作更改');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{
            
            $msgconfig = get_sms_param();
            $type = 0;// type=0 漫道短信接口
            $uid2=$msgconfig['user']; //分配给你的账号
            $pwd2=strtoupper(md5($msgconfig['user'].$msgconfig['password']));//密码 

            $d = @file_get_contents("http://sdk2.zucp.net:8060/webservice.asmx/balance?sn={$uid2}&pwd={$pwd2}",false);
            preg_match('/<string.*?>(.*?)<\/string>/', $d, $matches);
            
            if($matches[1]<0){ 
                switch($matches[1]){
                    case -2:
                        $d="帐号/密码不正确或者序列号未注册";
                    break;
                    case -4:
                        $d="余额不足";
                    break;
                    case -6:
                        $d="参数有误";
                    break;
                    case -7:
                        $d="权限受限,该序列号是否已经开通了调用该方法的权限";
                    break;
                    case -12:
                        $d="序列号状态错误，请确认序列号是否被禁用";
                    break;
                    default:
                        $d="用户名或密码错误";
                    break;
                }
            }else{
                $d = $d;
            }

            $result['Balance'] = $d;

            $this->assign('result',$result);
            $this->assign('msgconfig',$msgconfig);
            return view();
        }
    }

    public function shiming(){
        $request = request();
        if( $request->method()=='POST' ){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $data = input('post.',[]);
                $save = array(
                        'dizhi'=>$data['dizhi'],
                        'mima'=>$data['mima'],
                        'xingming'=>$data['xingming'],
                        'zhengjian'=>$data['zhengjian'],
                        'status'=>$data['status'],
                    );

                $res = db('realname_set')->where('id',1)->update($save);
                if($res){
                    cache('realname_set',null);
                    $this->success('信息更新成功');
                }else{
                    $this->error('信息更新失败或未作更改');
                }
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{
            $data = get_realname_param();

            $this->assign('data',$data);
            return view();
        }
    }






}
