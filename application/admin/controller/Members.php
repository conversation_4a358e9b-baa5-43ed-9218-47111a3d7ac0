<?php

namespace app\admin\controller;

use think\Request;
use think\Db;
use think\Validate;
use app\common\controller\Admin;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\IOFactory;

class Members extends Admin{
	protected function _initialize(){
		parent::_initialize();

        $this->hola = 'Admin Members hola !!!';
    }

    public function index(){
    	$w = array();
        $mod = db('members');
    	$name = input('get.name/s','');
        $action = input('get.action/s','');
        $page_num = input ('get.page_num',10);
        $select = input('get.select/s','');
        $order = input('get.order/s','');
    	if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'recommend':
                    $recommend_id = $mod->where('user_name',$name)->value('id');
                    $w['m1.recommend_id'] = $recommend_id;
                    break;
                case 'domain':
                    $w['m1.reg_domain'] = ['like','%'.$name.'%'];
                    break;
                case 'real_name':
                    $w['m2.real_name'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }

        if($select && $order){
            if($order == 'desc'){
                switch ($select){
                    case 'account_money':
                        $o = 'account_money DESC';
                        break;
                    case 'interest_money':
                        $o = 'interest_money DESC';
                        break;
                    case 'money_freeze':
                        $o = 'money_freeze DESC';
                        break;
                    case 'last_log_time':
                        $o = 'last_log_time DESC';
                        break;
                    case 'id':
                        $o = 'id DESC';
                        break;
                }
            }else {
                switch ($select) {
                    case 'account_money':
                        $o = 'account_money';
                        break;
                    case 'interest_money':
                        $o = 'interest_money';
                        break;
                    case 'money_freeze':
                        $o = 'money_freeze';
                        break;
                    case 'last_log_time':
                        $o = 'last_log_time';
                        break;
                    case 'id':
                        $o = 'id';
                        break;
                }
            }
        }else{
            $o = 'id DESC';
        }
        
        $subQuery1 = db('member_login')
            ->field('uid, max(add_time) as add_time')
            ->where('is_success =1')
            ->group('uid')
            ->buildSql();

        $subQuery2 = Db::table($subQuery1. ' m4')
            ->join('member_login m5','m5.uid = m4.uid')
            ->field('m5.uid,m5.domain')
            ->where('m5.add_time = m4.add_time')
            ->buildSql();

        $data = $mod->alias('m1')
                ->join('member_info m2','m1.id = m2.uid','left')
        		->join('member_money m3','m1.id = m3.uid','left')
        		->join($subQuery2. ' m6','m1.id = m6.uid','left')
        		->field('m1.*,m2.real_name,m2.phone,m2.phone_status,m2.id_status,m3.account_money,m3.interest_money,m3.money_freeze,m6.domain')
        		->order($o)
        		->where($w)
        		->paginate($page_num, false, ['query'=>request()->param()]);

        $this->assign('select',$select);
        $this->assign('order',$order);
        $this->assign('page_num',$page_num);
        $this->assign('data',$data);
        $this->assign('name',$name);
        $this->assign('action',$action);
        $this->assign('page', $data->render());
        return view();
    }
    public function edit(){
    	$request = request();
        if( $request->method()=='POST' ){
        	if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
        		// sleep(2);
        		$uid = input('post.uid/d',0);
		    	if( !$uid ){
		    		$this->error('缺少数据，请返回重试！');
		    	}
		    	$action = input('post.action/d',0);
		    	if( !$action ){
		    		$this->error('错误操作，请返回重试！');
		    	}
		    	$data = input('post.',[]);
		    	$res = $res1 = $res2 = false;
		    	$save = $save1 = $save2 = array();
		    	switch ($action) {
		    		case 1:
		    				$m1 = db('members');
		    				$user_info = $m1->alias('m1')
                                        ->join('member_info m2','m1.id = m2.uid','left')
                                        ->where('m1.id',$uid)
                                        ->field('m1.*,m2.phone')
                                        ->find();
		    				if($user_info['phone'] != $data['user_phone']){
		    					$had_phone = db('member_info')->where(['phone'=>$data['user_phone']])->count('id');
		    					if($had_phone){
		    						$this->error('该手机号已被使用，不能修改！');
		    					}
		    					$save1['phone'] = $data['user_phone'];
		    				}
		    				if($user_info['is_ban'] != $data['is_ban']){
		    					$save2['is_ban'] = $data['is_ban'];
		    				}
		    				if($data['user_pass']){
		    					$save2['user_pass'] = set_pass($data['user_pass']);
		    				}
		    				if($data['pin_pass']){
		    					$save2['pin_pass'] = set_pass($data['pin_pass']);
		    				}
                            if($save1){
                                $res1 = db('member_info')->where('uid',$uid)->update($save1);
                            }
                            if($save2){
		    					$res2 = $m1->where('id',$uid)->update($save2);
                            }
                            if($res1 || $res2){
                                $adminlog = '成功修改会员 '.$user_info['user_name'].' 基本资料';
		    				    $res = true;
                            }else{
		    					$res = false;
		    				}
		    			break;
		    		case 2:
		    				$user_info = db('member_info')->alias('m1')
		    							->join('members m2','m1.uid = m2.id','left')
		    							->where('m1.uid',$uid)
		    							->field('m1.real_name,m1.id_card,m1.id_status,m2.user_name')
		    							->find();
		    				// dump($user_info);die;
		    				if($data['real_name']){
			    				if( ($user_info['real_name'] != $data['real_name']) || empty($user_info['real_name']) ){
		    						$save['real_name'] = $data['real_name'];
			    				}
		    				}
		    				if($data['id_card']){
		    					if( ($user_info['id_card'] != $data['id_card']) || empty($user_info['id_card']) ){
		    						$save['id_card'] = $data['id_card'];
								}
		    				}
		    				if( $user_info['id_status'] != $data['id_status'] ){
			    				if( $data['id_status'] == 1){
			    					if( !$data['id_card'] || !$data['real_name'] ){
			    						$this->error('修改实名状态需要填写完整身份信息！');
			    					}
			    				}
		    					$save['id_status'] = $data['id_status'];
                            }
                            if( $data['id_status']==0 ){
                                $save['id_card'] = '';
                                if( empty($data['real_name']) ){
                                    $save['real_name'] = '';
                                }
                            }
		    				// dump($save);die;
		    				if($save){
		    					$res = db('member_info')->where('uid',$uid)->update($save);
		    					$adminlog = '成功修改会员 '.$user_info['user_name'].' 实名认证信息';
		    				}else{
		    					$res = false;
		    				}
		    			break;
		    		default:
		    			# code...
		    			break;
		    	}
        		if($res){
        			admin_log(session('admin_name'),$adminlog);
        			$this->success('修改成功',session('history_url'));
        		}else{
        			$this->error('修改失败或者未作任何修改！');
        		}
        	}else{
        		$this->error('不要重复提交表单！','members/index');
        	}
        }else{
	    	$uid = input('param.uid/d',0);
	    	if( !$uid ){
	    		$this->error('缺少数据，请返回重试！');
	    	}
            $info = memberInfo($uid);

            $bank = db('member_bank')->where('uid',$uid)->field(true)->select();

	    	$this->assign('uid',$uid);
            $this->assign('info',$info);
	    	$this->assign('bank',$bank);
	    	setBackUrl();
	    	$this->assign('back_url', session('?history_url')?session('history_url'):url('members/index') );
	    	return view();
        }
    }
    public function delMember(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $uid = input('post.uid/d',0);
            if( !$uid ){
                return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试！'];
            }
            $res = db('members')->where('id',$uid)->delete();
            $res2 = db('member_info')->where('uid',$uid)->delete();
            $res3 = db('member_money')->where('uid',$uid)->delete();
            $res4 = db('member_bank')->where('uid',$uid)->delete();
            if($res){
                $admin_info = '管理员'.session('admin_name').'删除了会员信息，会员编号：'.$uid;
                admin_log(session('admin_name'), $admin_info, $ip);

                return ['status'=>1,'message'=>'ok'];
            }else{
                return ['status'=>-1,'message'=>'删除失败！请重试'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    public function delBank(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.');
            $id = input('post.id/d',0);
            if( !$id ){
                return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试！'];
            }
            $res = db('member_bank')->where('id',$id)->delete();
            if($res){
                return ['status'=>1,'message'=>'ok'];
            }else{
                return ['status'=>-1,'message'=>'删除失败！请重试'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    // 会员列表
    public function exportUser(){

        $xlsData = db('members')->alias('m1')
                    ->join('member_info m2','m1.id = m2.uid','left')
                    ->field('m1.*,m2.phone,m2.real_name')
                    ->select();

        //實例化 Spreadsheet
        $spreadsheet = new Spreadsheet();
        
        //設置內容
        $sheet = $spreadsheet->getActiveSheet();
        $key = ord("A");
        $letter =explode(',',"A,B,C,D,E,F");
        $arrHeader = array('用户名','手机号','真实姓名','注册域名','注册IP','注册时间');

        $sheet->getStyle('A1:F1')->getFont()->setBold(true);

        //填充表頭信息
        $lenth =  count($arrHeader);
        for($i = 0;$i < $lenth;$i++) {
            $sheet->setCellValue("$letter[$i]1","$arrHeader[$i]");
            // 設定表格的寬度
            $sheet->getColumnDimension($letter[$i])->setAutoSize(true);
        }

        //填充表格信息
        foreach($xlsData as $k=>$v){
            $k += 2;
            //表格內容
            $sheet->setCellValue('A'.$k,$v['user_name']);
            $sheet->setCellValue('B'.$k, $v['phone']);
            $sheet->setCellValue('C'.$k, $v['real_name']);
            $sheet->setCellValue('D'.$k, $v['reg_domain']);
            $sheet->setCellValue('E'.$k, $v['reg_ip']);
            $sheet->setCellValue('F'.$k, date('Y-m-d H:i',$v['reg_time']));
            // 表格高度
            $sheet->getRowDimension($k)->setRowHeight(20);
        }

        $outfile = "会员列表-".date('YmdHi').".xlsx";
        ob_end_clean();
        header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"); // 正確的 XLSX MIME 類型
        // header("Content-Type: application/octet-stream"); // 可以移除或保留
        // header("Content-Type: application/download"); // 可以移除
        header('Content-Disposition:inline;filename="'.$outfile.'"');
        header("Content-Transfer-Encoding: binary");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        
        $writer = new Xlsx($spreadsheet); // 或者 IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
    }

    public function infoCard(){
        $uid = input('param.uid/d',0);

        $data = memberInfo($uid);
        $bank = db('member_bank')->where('uid',$uid)->field(true)->select();
        $login = db('member_login')->where('uid',$uid)->field(true)->limit(10)->order('id DESC')->select();
        // 充值信息 单位（分）
        $member_recharge = db('member_recharge');
        $recharge_count1 = $member_recharge->where('uid',$uid)->where('status',1)->count('id');
        $recharge_count2 = $member_recharge->where('uid',$uid)->where('status',1)->sum('money');
        // 提现信息 单位（分）
        $member_withdraw = db('member_withdraw');
        $withdraw_count1 = $member_withdraw->where('uid',$uid)->where('status',1)->count('id');
        $withdraw_count2 = $member_withdraw->where('uid',$uid)->where('status',1)->sum('money');

        //配资保证金
        $baozhengjin = db('stock_borrow')->where('status',2)->where('uid',$uid)->sum('deposit_money');
        $ip_count = Db::query("SELECT  g.user_name as user_name,h.real_name as real_name,f.num as num FROM zh_members g 
    JOIN (SELECT e.uid as uid,count(*) as num FROM (SELECT d.uid as uid,d.ip as ip from zh_member_login d 
        JOIN (SELECT DISTINCT(a.ip) as ip_address FROM zh_member_login a JOIN zh_members b on a.uid = b.id WHERE b.id = $uid AND a.create_time> (SELECT DATE_ADD(NOW(), INTERVAL - 2 MONTH))) c on 
            d.ip = c.ip_address WHERE d.create_time> (SELECT DATE_ADD(NOW(), INTERVAL - 2 MONTH)) GROUP BY d.ip,d.uid) e GROUP by e.uid) f on g.id=f.uid JOIN zh_member_info h on f.uid = h.uid");

        $this->assign('ip_count',$ip_count);

        $this->assign('data',$data);
        $this->assign('bank',$bank);
        $this->assign('baozhengjin',$baozhengjin);
        $this->assign('login',$login);
        $this->assign('recharge_count1',$recharge_count1);
        $this->assign('recharge_count2',$recharge_count2);
        $this->assign('withdraw_count1',$withdraw_count1);
        $this->assign('withdraw_count2',$withdraw_count2);
        return view();
    }


    // 代理
    public function shezhidaili(){
        $request = request();
        if( $request->isAjax() ){
            $uid = input('post.uid/d',0);
            $data = input('post.');
            $mod = db('members');
            if( !$uid ){
                return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试！'];
            }

            switch ($data['action']) {
                case 'shezhi':
                        if( $data['status']==1 ){
                            $peizi = $data['peizi'] ? $data['peizi']: 0;
                            $chongzhi = $data['chongzhi'] ? $data['chongzhi']: 0;
                            $yuming = $data['yuming']? trim(rtrim($data['yuming'], ',')): '';
                            $save = array(
                                    'user_type'=>config('DAILIREN_TYPE'),
                                    'czpz_bl'=>$chongzhi.'|'.$peizi,
                                    'dl_yuming'=>$yuming
                                );
                        }else{
                            $save = array(
                                    'user_type'=>1,
                                    'czpz_bl'=>'',
                                    'dl_yuming'=>''
                                );
                        }
                        $res = $mod->where('id',$uid)->update($save);
                        if( $res ){
                            return ['status'=>1,'message'=>'操作成功'];
                        }else{
                            return ['status'=>-1,'message'=>'操作失败，请稍后再试'];
                        }
                    break;
                case 'shangji':
                        $sj_uid = input('post.sj_uid/d',0);
                        // if( !$sj_uid ){
                        //     return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试！'];
                        // }
                        // $minfo = $mod->where('id',$uid)->field(true)->find();
                        if( $uid==$sj_uid ){
                            return ['status'=>-1,'message'=>'不能设置自己为上级'];
                        }
                        $save = array(
                                'recommend_id'=>$sj_uid
                            );
                        $res = $mod->where('id',$uid)->update($save);
                        if( $res ){
                            return ['status'=>1,'message'=>'操作成功'];
                        }else{
                            return ['status'=>-1,'message'=>'操作失败，请稍后再试'];
                        }
                    break;
                default:
                        return ['status'=>1,'message'=>'数据解析错误，请按照平台规则重试！'];
                    break;
            }
        }else{
            $uid = input('param.uid/d',0);

            $mod = db('members');
            $minfo = $mod->where('id',$uid)->field(true)->find();
            $bili = explode('|', $minfo['czpz_bl']);

            $dailiren = $mod->where('user_type', config('DAILIREN_TYPE'))->field('id,user_name')->select();

            $this->assign('uid',$uid);
            $this->assign('minfo',$minfo);
            $this->assign('bili',$bili);
            $this->assign('dailiren',$dailiren);
            $this->assign('DAILIREN_TYPE',config('DAILIREN_TYPE'));
            return view();
        }
    }
    public function daili(){
        $w = array();
        $uname = input('get.uname/s','');
        $action = input('get.action/s','');
        $page_num = input ('get.page_num',10);
        if( $uname ){
            $w['m1.user_name'] = ['like','%'.$uname.'%'];
        }
        $w['user_type'] = config('DAILIREN_TYPE');

        $today_s = strtotime( date('Y-m-d',time()) );
        $today_e = strtotime( date('Y-m-d',time()) ) + 86400;

        $members = db('members');
        $member_recharge = db('member_recharge');
        $member_withdraw = db('member_withdraw');
        $stock_borrow = db('stock_borrow');
        $member_moneylog = db('member_moneylog');

        $data = $members->alias('m1')
                ->join('member_info m2','m1.id = m2.uid','left')
                ->field('m1.id,m1.user_name,m1.czpz_bl,m1.dl_yuming,m2.real_name')
                ->order('m1.id DESC')
                ->where($w)
                ->paginate($page_num, false, ['query'=>request()->param()]);

        $page = $data->render();
        $data = $data->all();
        foreach ($data as &$v) {
            //代理下会员记录
            $huiyuan_zong = $members->where('recommend_id', $v['id'])->field('id')->select();
            if($huiyuan_zong){
                $v['huiyuan_zong'] = count($huiyuan_zong);
                //代理下今日新增会员数量
                $v['jinri_zhuce'] = $members
                                    ->where('reg_time','between time',"{$today_s},{$today_e}")
                                    ->count('id');

                $chongzhi_zong = 0;//代理下会员总充值金额 单位（分）
                $tixian_zong = 0;//代理下会员总提现金额
                $peizi_zong = 0;//代理下会员配资金额总额
                foreach ($huiyuan_zong as $v2) {
                    $chongzhi_zong += $member_recharge->where(['uid'=>$v2['id'], 'way'=>'offline', 'status'=>1])->sum('money');
                    $tixian_zong += $member_withdraw->where(['uid'=>$v2['id'], 'status'=>1])->sum('money');

                    $temp_gainMoney = $stock_borrow
                                         ->where('uid', $v2['id'])
                                         ->where('status','EGT',2)
                                         ->field('sum(deposit_money) AS deposit_money,sum(borrow_money) AS borrow_money')
                                         ->find();
                    $peizi_zong += $temp_gainMoney['borrow_money']-$temp_gainMoney['deposit_money'];
                    unset($temp_gainMoney);
                }
                $v['chongzhi_zong'] = $chongzhi_zong;
                $v['tixian_zong'] = $tixian_zong;
                //代理下线会员充值返利总额
                $v['cz_fanli'] = $member_moneylog->where(['uid'=>$v['id'],'type'=>31])->sum('affect_money');

                $v['peizi_zong'] = $peizi_zong;
                //代理下线会员配资返利总额
                $v['pz_fanli'] = $member_moneylog->where(['uid'=>$v['id'],'type'=>32])->sum('affect_money');

            }else{
                $v['huiyuan_zong'] = 0;
                $v['jinri_zhuce'] = 0;
                $v['chongzhi_zong'] = 0;
                $v['tixian_zong'] = 0;
                $v['cz_fanli'] = 0;
                $v['peizi_zong'] = 0;
                $v['pz_fanli'] = 0;
            }
            //充值配资返佣比例
            $v['czpz_bl'] = explode('|', $v['czpz_bl']);

            unset($huiyuan_zong);
        }
        unset($v);

        $this->assign('action',$action);
        $this->assign('page_num',$page_num);
        $this->assign('data',$data);
        $this->assign('uname',$uname);
        $this->assign('page', $page);
        return view();
    }
    // 查看下线
    public function ckxx(){
        $rid = input('param.rid/d',0);

        $data = db('members')->alias('m1')
                 ->join('member_info m2','m1.id = m2.uid','left')
                 ->field('m1.id,m1.user_name,m1.reg_domain,m1.reg_time,m1.last_log_time,m2.real_name')
                 ->where('m1.recommend_id',$rid)
                 ->paginate(10, false, ['query'=>request()->param()]);
                 // ->select();

        $this->assign('data',$data);
        $this->assign('page',$data->render());
        return view();
    }
    // 查看下线情况
    public function ckqk(){
        $uid = input('param.uid/d',0);

        $member_recharge = db('member_recharge');
        $member_withdraw = db('member_withdraw');
        $stock_borrow = db('stock_borrow');
        $stock_addfinancing = db('stock_addfinancing');
        $stock_renewal = db('stock_renewal');
        $stock_addmoney = db('stock_addmoney');
        $stock_drawprofit = db('stock_drawprofit');

        $data = array(
                'cz_cs'=>0,'cz_zje'=>0,
                'tx_cs'=>0,'tx_zje'=>0,
                'pz_cs'=>0,'pz_bzj'=>0,'pz_glf'=>0,'pz_kuoda'=>0,'pz_yanqi'=>0,'pz_bukui'=>0,'pz_tiying'=>0
            );
        $glf1 = $glf2 = $glf3 = 0;

        if( $uid ){
            $data['cz_cs'] = $member_recharge->where('uid',$uid)->where('way','offline')->where('status',1)->count('id');//充值次数
            $data['cz_zje'] = $member_recharge->where('uid',$uid)->where('way','offline')->where('status',1)->sum('money');//充值总金额

            $data['tx_cs'] = $member_withdraw->where('uid',$uid)->where('status',1)->count('id');//提现次数
            $data['tx_zje'] = $member_withdraw->where('uid',$uid)->where('status',1)->sum('money');//提现总金额

            $data['pz_cs'] = $stock_borrow->where('uid',$uid)->where('status','EGT',2)->count('id');//配资次数
            $data['pz_bzj'] = $stock_borrow->where('uid',$uid)->where('status','EGT',2)->sum('deposit_money');//配资总保证金

            $glf1 = $stock_borrow->where('uid',$uid)->where('status','EGT',2)->sum('borrow_interest');//配资总管理费
            $glf2 = $stock_addfinancing->where('uid',$uid)->where('status',1)->sum('borrow_fee');//扩大总管理费
            $glf3 = $stock_renewal->where('uid',$uid)->where('status',1)->sum('borrow_fee');//延期总管理费
            $data['pz_glf'] = $glf1 + $glf2 + $glf3;//配资总管理费

            $data['pz_kuoda'] = $stock_addfinancing->where('uid',$uid)->where('status',1)->count('id');//扩大次数
            $data['pz_yanqi'] = $stock_renewal->where('uid',$uid)->where('status',1)->count('id');//延期次数
            $data['pz_bukui'] = $stock_addmoney->where('uid',$uid)->where('status',1)->count('id');//补亏次数
            $data['pz_tiying'] = $stock_drawprofit->where('uid',$uid)->where('status',1)->count('id');//提盈次数
        }

        $this->assign('data',$data);
        return view();
    }

    public function userInfoBankEdit()
    {
        $request = request();
        $mod = db('member_info');
        if( $request->isAjax() ){
            $data = input('post.');
            $id = $data['id'];
            unset($data['id']);
            $res = $mod->where('id',$id)->update($data);
            return ['status'=>1,'message'=>'操作成功！'];
        }else{
            $id = input('id');

            $info = $mod->where('id',$id)->find();
            $this->assign('data',$info);
            $this->assign('id',$id);
            return view();
        }
    }

    public function blacklist(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $uid = input('post.uid/d',0);
            if( !$uid ){
                return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试！'];
            }
            $res = db('members')->where('id',$uid)->update(['is_blacklist' => 1]);
            if($res){
                $admin_info = '管理员'.session('admin_name').'会员信息加入黑名单，会员编号：'.$uid;
                admin_log(session('admin_name'), $admin_info, $ip);
                return ['status'=>1,'message'=>'ok'];
            }else{
                return ['status'=>-1,'message'=>'操作失败！请重试'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    
    public function ipsummary(){
        $w = array();
        $w['is_success'] = 1;
        $action = input('get.action/s','');
        $name = input('get.name/s','');
        $page_num = input ('get.page_num',10);
        if( $name ){
            $w['ip'] = $name;
        }
        $data = db('member_login')
            ->field('ip,COUNT(*) as log_num,count(DISTINCT(uid)) as cum_num')
            ->group('ip')
            ->where($w)
            ->where('create_time > (SELECT DATE_ADD(NOW(), INTERVAL - 2 MONTH))')
            ->order('log_num DESC')
            ->paginate($page_num, false, ['query'=>request()->param()]);

        $this->assign('page_num',$page_num);
        $this->assign('data',$data);
        $this->assign('name',$name);
        $this->assign('action',$action);
        $this->assign('page', $data->render());
        return view();
    }

    public function relacus(){
        $ip = input('param.ip',0);
        $data = db('members')
            ->alias('a')
            ->join('member_info b','a.id = b.uid')
            ->join('member_login c','c.uid = b.uid')
            ->field('DISTINCT(a.user_name) as user_name,b.real_name as real_name')
            ->where('c.ip',$ip)
            ->where('c.create_time> (SELECT DATE_ADD(NOW(), INTERVAL - 2 MONTH))')
            ->where('c.is_success','=',1)
            ->select();
        $this->assign('data',$data);
        return view();
    }

    public function details(){
        $ip = input('param.ip',0);
        $data = db('members')
            ->alias('a')
            ->join('member_info b','a.id = b.uid')
            ->join('member_login c','c.uid = b.uid')
            ->field('a.user_name as user_name,b.real_name as real_name,c.create_time as login_time')
            ->where('c.ip',$ip)
            ->where('c.create_time> (SELECT DATE_ADD(NOW(), INTERVAL - 2 MONTH))')
            ->where('c.is_success','=',1)
            ->select();
        $this->assign('data',$data);
        return view();
    }


}
