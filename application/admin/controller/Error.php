<?php 
namespace app\admin\controller;

use think\Controller;

class Error extends Controller{
    protected function _initialize(){
        parent::_initialize();

        $this->assign('jump_url_404' , url('/') );
    }
    public function index(){
        // echo '空控制器 Admin Error';

        abort(404,'当前地址不存在，请在网站中手动点击链接 admin');
    }

    public function _empty(){
        // echo '空操作！Admin Error';

        abort(404,'当前地址不存在，请在网站中手动点击链接 admin_empty');
    }

}