<?php

namespace app\admin\controller;

use app\common\model\MemberWithdraw;
use app\common\model\RechargeOrder;
use app\common\model\WithdrawalOrder;
use think\Db;
use think\Validate;
use app\common\Services\Withdraw\GuantianService;
use think\Request;
use app\common\controller\Admin;
use think\Response;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\IOFactory;

class Money extends Admin
{
    /** @var GuantianService */
    protected $guantianService;

    protected function _initialize()
    {
        parent::_initialize();
        $this->hola = 'Admin Money hola !!!';

        $this->guantianService = new GuantianService();
    }

    /* 提现 */
    public function withdrawApply()
    {
        $w = array();
        $user_name = '';
        if (input('get.user_name')) {
            $user_name = input('get.user_name/s', '');
            $w['w.user_name'] = ['like', '%' . $user_name . '%'];
        }
        $w['w.status'] = 0;
        $data = model('Withdraw')->withdrawList($w)
            ->paginate(config('admin_rows'), false, ['query' => request()->param()]);
        // dump($data->toArray());die;

        $this->assign('user_name', $user_name);
        $this->assign('data', $data);
        $this->assign('page', $data->render());
        return view('withdrawApply');
    }

    public function withdrawDeal()
    {
        $request = request();
        $ip = $request->ip();
        if ($request->isAjax()) {
            $id = input('post.id/d', 0);
            $fee = round(input('post.fee/s', 0), 2);
            $account = round(input('post.account/s', 0), 2);
            $status = input('post.status/d', 0);
            $info = input('post.info/s', '');
            if (!$id) {
                return ['status' => -1, 'message' => '操作失败，请按照平台规则重试'];
            }
            $mod = db('member_withdraw');
            $data = $mod->alias('m1')
                ->join('member_info m2', 'm1.uid = m2.uid', 'left')
                ->join('member_bank bank', 'm1.bank_id = bank.id', 'inner')
                ->where('m1.id', $id)
                ->field('m1.*,m2.phone,bank.*')
                ->find();

            if ($data['status'] != 0) {
                return ['status' => '-1', 'message' => '该提现申请已被处理，请刷新页面后重试'];
            }

            if(($account * 100 + $fee * 100) != $data['money']){
                return ['status' => -1, 'message' => '操作失败，手续费加到账金额必须等于申请提现金额'];
            }

            // 添加该记录锁定
            if (cache("withdraw-{$id}")) {
                return ['status' => -1, 'message' => '该数据正在处理中，请稍后再试'];
            }
            cache("withdraw-{$id}", true, 60);
            // 开启事务
            db()->startTrans();
            Db::name('members')->lock(true);
            Db::name('member_info')->lock(true);
            Db::name('member_money')->lock(true);
            Db::name('member_moneylog')->lock(true);
            if ($status == 1) {
                if ($fee < 0 || $account <= 0) {
                    return ['status' => -2, 'message' => '操作失败，金额不能小于0'];
                }
                // 处理信息
                $deal_save = array(
                    'fee' => $fee * 100,
                    'account' => $account * 100,
                    'deal_time' => time(),
                    'deal_user' => session('admin_name'),
                    'deal_info' => $info,
                    'status' => MemberWithdraw::STATUS_PROCESSING
                );
                // 站内信
                $title = '提现审核成功，交易已在处理中，扣除冻结资金';
                $msg = '提现审核成功，交易已在处理中，扣除冻结金额。手续费' . $fee . '元，实际到账' . $account . '元。备注：' . $info;
                // 发送至客户短信
                $sms_text = '您的提现申请已通过';

                // 管理员日志
                $admin_info = '管理员' . session('admin_name') . '审核提现成功，提现编号：' . $id;
                // 扣除冻结资金
                $mlog = memberMoneyLog($data['uid'], $data['money'], $msg, 4);

                // 寫入提現訂單
                $withdrawalOrder = WithdrawalOrder::create([
                    'member_id' => $data['uid'],
                    'member_withdraw_id' => $id,
                    'order_no' => WithdrawalOrder::generateOrderNo(),
                    'amount' => $account,
                    'status' => WithdrawalOrder::STATUS_PENDING,
                    'account_name' => $data['account_name'],
                    'account_number' => $data['number'],
                    'bank_name' => $data['bank_name'],
                    'bank_branch' => $data['bank_address'],
                    'payment_provider' => WithdrawalOrder::PAYMENT_PROVIDER_GUANTIAN,
                    'payment_method' => WithdrawalOrder::PAYMENT_METHOD_BANK,
                ]);

                // 提现到冠天
                $result = $this->guantianService->withdraw($withdrawalOrder);

                if ($result['code'] != 1) {
                    db()->rollback();
                    cache("withdraw-{$id}", null);//结束运行 释放记录锁
                    return ['status' => -5, 'message' => '处理失败，请稍后再试'];
                }

                $withdrawalOrder->where('id', $withdrawalOrder->id)->update([
                    'transaction_no' => $result['order_no'],
                ]);
            } else {
                if (!$info) {
                    cache("withdraw-{$id}", null);//结束运行 释放记录锁
                    return ['status' => -3, 'message' => '如果拒绝审核，需要填写拒绝理由'];
                }
                // 处理信息
                $deal_save = array(
                    'deal_time' => time(),
                    'deal_user' => session('admin_name'),
                    'deal_info' => $info,
                    'status' => $status
                );
                // 站内信
                $title = '提现失败，返还冻结资金';
                $msg = '提现审核失败，返还冻结金额。备注：' . $info;
                // 发送至客户短信
                $sms_text = '您的提现申请未通过';

                // 管理员日志
                $admin_info = '管理员' . session('admin_name') . '审核提现未通过，提现编号：' . $id . '，备注：' . $info;
                // 返还冻结资金
                $mlog = memberMoneyLog($data['uid'], $data['money'], $msg, 5);
            }
            // var_dump(input('post.'),$fee,$account,$info,$admin_info);

            $deal_res = $mod->where('id', $id)->update($deal_save);

            if ($mlog && $deal_res) {
                $ctip = clearTip($data['uid'], 3);
                $inner_res = addInnerMsg($data['uid'], $title, $msg);//站内信
                $admin_res = admin_log(session('admin_name'), $admin_info, $ip);//管理员日志
                if ($data['phone']) {
                    sendsms_hx($data['phone'], $sms_text);
                }

                cache("withdraw-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status' => 1, 'message' => '处理成功'];
            } else {
                cache("withdraw-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status' => -5, 'message' => '处理失败，请稍后再试'];
            }

        } else {
            $id = input('param.id/d', 0);
            $data = model('Withdraw')->getWithdraw($id);

            // dump($data);
            $this->assign('id', $id);
            $this->assign('data', $data);
            return view('withdrawDeal');
        }
    }

    public function withdrawList()
    {
        $w = array();
        $user_name = input('get.user_name', '');
        $timestr = input('get.time/s', '');
        $status = input('get.status/d', 0);
        $page_num = input('get.page_num', 10);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['w.add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['w.user_name'] = ['like', '%' . $user_name . '%'];
        }
        if ($status) {
            $w['w.status'] = $status;
        } else {
            $w['w.status'] = ['neq', 0];
        }

        $data = model('Withdraw')->withdrawList($w)
            ->paginate($page_num, false, ['query' => request()->param()]);
        $this->assign('page_num', $page_num);
        $this->assign('user_name', $user_name);
        $this->assign('data', $data);
        $this->assign('timestr', $timestr);
        $this->assign('status', $status);
        $this->assign('page', $data->render());
        return view();
    }

    // 已处理提现记录表
    public function exportWithdraw()
    {
        $w = array();
        $user_name = input('get.user_name', '');
        $timestr = input('get.time/s', '');
        $status = input('get.status/d', 0);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['w.add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['w.user_name'] = ['like', '%' . $user_name . '%'];
        }
        if ($status) {
            $w['w.status'] = $status;
        } else {
            $w['w.status'] = ['neq', 0];
        }

        $xlsData = db('member_withdraw')->alias('w')
            ->join('member_info m1', 'w.uid = m1.uid', 'left')
            ->join('member_bank m2', 'w.bank_id = m2.id', 'left')
            ->where($w)
            ->field('w.*,m1.real_name,m2.bank_name,m2.bank_address,m2.bank_address,m2.province,m2.city')
            ->order('id DESC')
            ->select();

        $spreadsheet = new Spreadsheet();
        $objActSheet = $spreadsheet->getActiveSheet();
        $key = ord("A");
        $letter = explode(',', "A,B,C,D,E,F,G,H,I,J,K,L");
        $arrHeader = array('编号', '用户名', '真实姓名', '提现金额', '提现手续费', '所在地', '开户银行', '申请时间', '状态', '处理人', '处理时间', '备注');

        //填充表头信息
        $lenth = count($arrHeader);
        for ($i = 0; $i < $lenth; $i++) {
            $objActSheet->setCellValue("$letter[$i]2", "$arrHeader[$i]");
            // 设置表格的宽度
            $objActSheet->getColumnDimension($letter[$i])->setAutoSize(true);
        }

        $objActSheet->mergeCells('A1:L1');
        $objActSheet->getRowDimension(1)->setRowHeight(25);// 表格高度

        $a1_text = '时间范围：' . $timestr .
            ' 状态：' . str_replace([-1, 0, 1], ['未通过', '', '已通过'], $status) .
            ' 用户名：' . $user_name;
        $objActSheet->setCellValue('A1', $a1_text);
        $objActSheet->getStyle('A1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $objActSheet->getStyle('A2:L2')->getFont()->setBold(true);

        //填充表格信息
        foreach ($xlsData as $k => $v) {
            $k += 3;
            //表格内容
            $objActSheet->setCellValue('A' . $k, $v['id']);
            $objActSheet->setCellValue('B' . $k, $v['user_name']);
            $objActSheet->setCellValue('C' . $k, $v['real_name']);
            $objActSheet->setCellValue('D' . $k, ($v['money'] / 100));
            $objActSheet->setCellValue('E' . $k, ($v['fee'] / 100));
            $objActSheet->setCellValue('F' . $k, $v['province'] . $v['city']);
            $objActSheet->setCellValue('G' . $k, $v['bank_address']);
            $objActSheet->setCellValue('H' . $k, date('Y-m-d H:i', $v['add_time']));
            $objActSheet->setCellValue('I' . $k, str_replace([-1, 1], ['未通过', '已通过'], $v['status']));
            $objActSheet->setCellValue('J' . $k, $v['deal_user']);
            $objActSheet->setCellValue('K' . $k, date('Y-m-d H:i', $v['deal_time']));
            $objActSheet->setCellValue('L' . $k, $v['deal_info']);
            // 表格高度
            $objActSheet->getRowDimension($k)->setRowHeight(20);
        }

        $outfile = "已处理提现记录表-" . date('YmdHi') . ".xlsx";
        ob_end_clean();
        header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outfile . '"');
        header("Content-Transfer-Encoding: binary");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
    }
    /* 提现 */

    /* 充值 */
    public function rechargeApply()
    {
        $w = array();
        $user_name = '';
        if (input('get.user_name')) {
            $user_name = input('get.user_name/s', '');
            $w['r.user_name'] = ['like', '%' . $user_name . '%'];
        }
        $w['r.status'] = 0;
        $data = model('Recharge')->rechargeList($w)
            ->paginate(config('admin_rows'), false, ['query' => request()->param()]);
        // dump($data->toArray());die;

        $this->assign('user_name', $user_name);
        $this->assign('data', $data);
        $this->assign('page', $data->render());
        return view();
    }

    public function rechargeDeal()
    {
        $request = request();
        $ip = $request->ip();
        if ($request->isAjax()) {
            $id = input('post.id/d', 0);
            $status = input('post.status/d', 0);
            $info = input('post.info/s', '');
            if (!$id) {
                return ['status' => -1, 'message' => '操作失败，请按照平台规则重试'];
            }
            $mod = db('member_recharge');
            $data = $mod->where('id', $id)->field(true)->find();
            if ($data['status'] != 0) {
                return ['status' => '-2', 'message' => '该充值订单已被处理，请刷新页面后重试'];
            }

            // 添加该记录锁定
            if (cache("recharge-{$id}")) {
                return ['status' => -1, 'message' => '该数据正在处理中，请稍后再试'];
            }
            cache("recharge-{$id}", true, 60 * 3);
            // 开启事务
            db()->startTrans();
            Db::name('members')->lock(true);
            Db::name('member_info')->lock(true);
            Db::name('member_money')->lock(true);
            Db::name('member_moneylog')->lock(true);
            if ($status == 1) {
                // 站内信
                $title = '充值订单通过';
                $msg = '充值订单通过，增加至可用余额。备注：' . $info;
                // 管理员日志
                $admin_info = '管理员' . session('admin_name') . '审核充值订单通过，订单编号：' . $id;
                // 增加资金记录
                $mlog = memberMoneyLog($data['uid'], $data['money'], $msg, 1);

                if ($data['way'] == 'offline') {
                    // 线下充值返利
                    $rebate = (int)$this->global['rebate_num'];//千分比
                    if ($rebate > 0) {
                        $rebate_money = $data['money'] * ($rebate / 1000);
                        $rebate_money = (int)$rebate_money;

                        if ($rebate_money > 0) {
                            $rebate_msg = '线下充值' . ($data['money'] / 100) . '元，返利' . ($rebate_money / 100) . '元，已增加至管理费余额';
                            $rebate_log = memberMoneyLog($data['uid'], $rebate_money, $rebate_msg, 10, 2);
                        }
                    }

                    // 线下充值代理返利
                    sendRebateForDL('', $data['uid'], $id, 5);
                }

                $xsrw = myXsrw($data['uid'], config('MY_XSRW')['CHONGZHI_JL']);
                file_put_contents('./xsrw.txt',
                    date("Y-m-d H:i ") . "uid=" . $data['uid'] . "&rw_id=" . config('MY_XSRW')['CHONGZHI_JL'] . "&xsrw:" . json_encode($xsrw) . "\r\n",
                    FILE_APPEND);
            } else {
                if (!$info) {
                    cache("recharge-{$id}", null);//结束运行 释放记录锁
                    return ['status' => -3, 'message' => '如果拒绝审核，需要填写拒绝理由'];
                }
                // 站内信
                $title = '充值订单审核未通过';
                $msg = '充值订单审核未通过。备注：' . $info;
                // 管理员日志
                $admin_info = '管理员' . session('admin_name') . '审核充值订单未通过，订单编号：' . $id . '，备注：' . $info;
                // 没有资金记录
                $mlog = true;
            }
            // 处理信息
            $deal_save = array(
                'deal_time' => time(),
                'deal_admin' => session('admin_name'),
                'deal_info' => $info,
                'deal_ip' => $ip,
                'status' => $status
            );
            $res = $mod->where('id', $id)->update($deal_save);
            if ($mlog && $res) {
                $ctip = clearTip($data['uid'], 1); 
                $inner_res = addInnerMsg($data['uid'], $title, $msg); 
                $admin_log = admin_log(session('admin_name'), $admin_info, $ip);//管理员日志

                cache("recharge-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status' => 1, 'message' => '处理成功'];
            } else {
                cache("recharge-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status' => -5, 'message' => '处理失败，请稍后再试'];
            }

        } else {
            $id = input('param.id/d', 0);
            $data = model('Recharge')->getRecharge($id);

            // dump($data);
            $this->assign('id', $id);
            $this->assign('data', $data);
            return view();
        }
    }

    public function rechargeList()
    {
        $w = array();
        $user_name = input('get.user_name', '');
        $timestr = input('get.time/s', '');
        $status = input('get.status/d', 0);
        $page_num = input('get.page_num', 10);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['r.add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['r.user_name'] = ['like', '%' . $user_name . '%'];
        }
        if ($status) {
            $w['r.status'] = $status;
        } else {
            $w['r.status'] = ['neq', 0];
        }

        $data = model('Recharge')->rechargeList($w)
            ->paginate($page_num, false, ['query' => request()->param()]);
        $this->assign('page_num', $page_num);
        $this->assign('user_name', $user_name);
        $this->assign('data', $data);
        $this->assign('status', $status);
        $this->assign('timestr', $timestr);
        $this->assign('page', $data->render());
        return view();
    }

    public function rechargeOrders()
    {
        $conditions = [];

        if (input('get.order_no')) {
            $conditions['withdraw_orders.order_no'] = ['like', '%' . input('get.order_no/s', '') . '%'];
        }

        if (input('get.transaction_no')) {
            $conditions['withdraw_orders.transaction_no'] = ['like', '%' . input('get.transaction_no/s', '') . '%'];
        }

        $data = RechargeOrder::alias('recharge_orders')
            ->join('members', 'members.id = recharge_orders.member_id', 'inner')
            ->join('member_info', 'member_info.uid = recharge_orders.member_id', 'inner')
            ->field('*,member_info.real_name,members.user_name')
            ->where($conditions)
            ->order('recharge_orders.id DESC')
            ->paginate(config('admin_rows'), false, ['query' => request()->param()]);

        $this->assign('conditions', request()->param());
        $this->assign('paymentProviderMap', RechargeOrder::$paymentProviderMap);
        $this->assign('statusMap', RechargeOrder::$statusMap);
        $this->assign('data', $data);
        $this->assign('page', $data->render());
        return view();
    }

    // 已处理充值记录表
    public function exportRecharge()
    {
        $w = array();
        $user_name = input('get.user_name', '');
        $timestr = input('get.time/s', '');
        $status = input('get.status/d', 0);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['r.add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['r.user_name'] = ['like', '%' . $user_name . '%'];
        }
        if ($status) {
            $w['r.status'] = $status;
        } else {
            $w['r.status'] = ['neq', 0];
        }

        $xlsData = db('member_recharge')->alias('r')
            ->join('member_info m1', 'r.uid = m1.uid', 'left')
            ->field('r.*,m1.real_name')
            ->order('id DESC')
            ->where($w)
            ->select();

        $spreadsheet = new Spreadsheet();
        $objActSheet = $spreadsheet->getActiveSheet();
        $key = ord("A");
        $letter = explode(',', "A,B,C,D,E,F,G,H,I,J,K,L");
        $arrHeader = array('编号', '用户名', '真实姓名', '充值金额', '充值方式', '对账订单号', '状态', '申请时间', '处理人', '处理时间', '操作IP', '备注');

        //填充表头信息
        $lenth = count($arrHeader);
        for ($i = 0; $i < $lenth; $i++) {
            $objActSheet->setCellValue("$letter[$i]2", "$arrHeader[$i]");
            // 设置表格的宽度
            $objActSheet->getColumnDimension($letter[$i])->setAutoSize(true);
        }

        $objActSheet->mergeCells('A1:L1');
        $objActSheet->getRowDimension(1)->setRowHeight(25);// 表格高度

        $a1_text = '时间范围：' . $timestr .
            ' 状态：' . str_replace([-1, 0, 1], ['未通过', '', '已通过'], $status) .
            ' 用户名：' . $user_name;
        $objActSheet->setCellValue('A1', $a1_text);
        $objActSheet->getStyle('A1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $objActSheet->getStyle('A2:L2')->getFont()->setBold(true);

        //填充表格信息
        foreach ($xlsData as $k => $v) {
            $k += 3;
            //表格内容
            $objActSheet->setCellValue('A' . $k, $v['id']);
            $objActSheet->setCellValue('B' . $k, $v['user_name']);
            $objActSheet->setCellValue('C' . $k, $v['real_name']);
            $objActSheet->setCellValue('D' . $k, ($v['money'] / 100));
            $objActSheet->setCellValue('E' . $k, str_replace(['offline', 'wx', 'zfb'], ['银行转账', '微信扫码', '支付宝扫码'], $v['way']));
            $objActSheet->setCellValue('F' . $k, $v['tran_id']);
            $objActSheet->setCellValue('G' . $k, str_replace([-1, 1], ['未通过', '已通过'], $v['status']));
            $objActSheet->setCellValue('H' . $k, date('Y-m-d H:i', $v['add_time']));
            $objActSheet->setCellValue('I' . $k, $v['deal_admin']);
            $objActSheet->setCellValue('J' . $k, date('Y-m-d H:i', $v['deal_time']));
            $objActSheet->setCellValue('K' . $k, $v['deal_ip']);
            $objActSheet->setCellValue('L' . $k, $v['deal_info']);
            // 表格高度
            $objActSheet->getRowDimension($k)->setRowHeight(20);
        }

        $outfile = "已处理充值记录表-" . date('YmdHi') . ".xlsx";
        ob_end_clean();
        header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outfile . '"');
        header("Content-Transfer-Encoding: binary");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
    }
    /* 充值 */


    // 资金明细
    public function fund()
    {
        $w = array();
        $timestr = input('get.time/s', '');
        $user_name = input('get.uname/s', '');
        $trade = input('get.trade/d', 0);
        $page_num = input('get.page_num', 10);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['user_name'] = array('like', '%' . $user_name . '%');
        }
        if ($trade) {
            $w['type'] = $trade;
        }

        $data = db('member_moneylog')
            ->alias('a')
            ->join('member_info m2', 'a.uid=m2.uid', 'left')
            ->field('a.*,m2.real_name')
            ->order('a.id DESC')
            ->where($w)
            ->paginate($page_num, false, ['query' => request()->param()]);

        $this->assign('page_num', $page_num);
        $this->assign('trade', $trade);
        $this->assign('trade_type', config('trade_type'));
        $this->assign('data', $data);
        $this->assign('timestr', $timestr);
        $this->assign('user_name', $user_name);
        $this->assign('page', $data->render());
        return view();
    }

    // 会员资金明细表
    public function exportFund()
    {
        $w = array();
        $timestr = input('get.time/s', '');
        $user_name = input('get.uname/s', '');
        $trade = input('get.trade/d', 0);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['user_name'] = array('like', '%' . $user_name . '%');
        }
        if ($trade) {
            $w['type'] = $trade;
        }
        $xlsData = db('member_moneylog')
            ->order('id DESC')
            ->where($w)
            ->select();
        $trade_type = config('trade_type');

        $spreadsheet = new Spreadsheet();
        $objActSheet = $spreadsheet->getActiveSheet();
        $key = ord("A");
        $letter = explode(',', "A,B,C,D,E,F,G,H,I");
        $arrHeader = array('编号', '用户名', '交易类型', '变动资金', '账户余额', '管理费余额', '冻结资金', '交易时间', '备注信息');

        //填充表头信息
        $lenth = count($arrHeader);
        for ($i = 0; $i < $lenth; $i++) {
            $objActSheet->setCellValue("$letter[$i]2", "$arrHeader[$i]");
            // 设置表格的宽度
            $objActSheet->getColumnDimension($letter[$i])->setAutoSize(true);
        }

        $objActSheet->mergeCells('A1:I1');
        $objActSheet->getRowDimension(1)->setRowHeight(25);// 表格高度
        $temp_trade = empty($trade) ? '' : $trade_type[$trade];
        $a1_text = '时间范围：' . $timestr .
            ' 交易类型：' . $temp_trade .
            ' 用户名：' . $user_name;
        $objActSheet->setCellValue('A1', $a1_text);
        $objActSheet->getStyle('A1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $objActSheet->getStyle('A2:I2')->getFont()->setBold(true);

        //填充表格信息
        foreach ($xlsData as $k => $v) {
            $k += 3;
            //表格内容
            $objActSheet->setCellValue('A' . $k, $v['id']);
            $objActSheet->setCellValue('B' . $k, $v['user_name']);
            $objActSheet->setCellValue('C' . $k, $trade_type[$v['type']]);
            $objActSheet->setCellValue('D' . $k, ($v['affect_money'] / 100));
            $objActSheet->setCellValue('E' . $k, ($v['account_money'] / 100));
            $objActSheet->setCellValue('F' . $k, ($v['interest_money'] / 100));
            $objActSheet->setCellValue('G' . $k, ($v['freeze_money'] / 100));
            $objActSheet->setCellValue('H' . $k, $v['create_time']);
            $objActSheet->setCellValue('I' . $k, $v['info']);
            // 表格高度
            $objActSheet->getRowDimension($k)->setRowHeight(20);
        }

        $outfile = "会员资金明细表-" . date('YmdHi') . ".xlsx";
        ob_end_clean();
        header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outfile . '"');
        header("Content-Transfer-Encoding: binary");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function transfer()
    {
        $request = request();
        $ip = $request->ip();
        if ($request->method() == 'POST') {
            if (Validate::token('__token__', '', ['__token__' => input('post.__token__/s')])) {
                $uname = input('post.uname/s', '');
                $money = input('post.money/d', 0);
                $type = input('post.type/d', 0);
                $content = input('post.content/s', '');
                if (!$uname || !$money || !$type) {
                    $this->error('缺少参数！请按照平台规则重试');
                }

                $user = db('members')->alias('m1')
                    ->join('member_money m2', 'm1.id = m2.uid', 'left')
                    ->where('m1.user_name', $uname)
                    ->field('m1.*,m2.account_money,m2.interest_money')
                    ->find();
                if (!$user) {
                    $this->error('无该会员信息，请核对后重新输入');
                }

                $uid = $user['id'];
                $money = $money * 100;//单位（分）
                $interest = $type == 1 ? 1 : 2;
                if ($money < 0) {
                    $info = '管理员扣费' . abs($money / 100) . '元。备注：' . $content;
                    $admin_info = '管理员' . session('admin_name') . '向' . $uname . '扣费' . abs($money / 100) . '元，备注：' . $content;

                    if ($type == 1) {
                        $account_money = $user['account_money'] - $money;
                        $interest_money = $user['interest_money'];
                    } else {
                        $account_money = $user['account_money'];
                        $interest_money = $user['interest_money'] - $money;
                    }
                } else {
                    if ($type == 1) {
                        $info = '管理员向您转账' . ($money / 100) . '元。备注：' . $content;
                        $admin_info = '管理员' . session('admin_name') . '向' . $uname . '转账' . ($money / 100) . '元，备注：' . $content;

                        $account_money = $user['account_money'] + $money;
                        $interest_money = $user['interest_money'];
                    } else {
                        $info = '管理员赠送管理费' . ($money / 100) . '元。备注：' . $content;
                        $admin_info = '管理员' . session('admin_name') . '向' . $uname . '赠送管理费' . ($money / 100) . '元，备注：' . $content;

                        $account_money = $user['account_money'];
                        $interest_money = $user['interest_money'] + $money;
                    }
                }

                // 开启事务
                db()->startTrans();
                Db::name('members')->lock(true);
                Db::name('member_info')->lock(true);
                Db::name('member_money')->lock(true);
                Db::name('member_moneylog')->lock(true);
                $add = array(
                    'uid' => $uid,
                    'user_name' => $uname,
                    'admin_name' => session('admin_name'),
                    'type' => $type,
                    'money' => $money,
                    'account_money' => $account_money,
                    'interest_money' => $interest_money,
                    'content' => $content,
                    'add_time' => time()
                );
                $transfer_res = db('transfer')->insert($add);
                $member_moneylog = memberMoneyLog($uid, $money, $info, 15, $interest);

                // var_dump($transfer_res,$info,$member_moneylog);
                if ($transfer_res && $member_moneylog) {
                    $admin_log = admin_log(session('admin_name'), $admin_info, $ip);//管理员日志

                    db()->commit();
                    $this->success('转账成功');
                } else {

                    db()->rollback();
                    $this->error('转账失败，请稍后重试');
                }
            } else {
                $this->error('不要重复提交表单！');
            }
        } else {

            return view();
        }
    }

    public function transList()
    {
        $w = array();
        $timestr = input('get.time/s', '');
        $user_name = input('get.uname/s', '');
        $type = input('get.type/d', 0);
        $page_num = input('get.page_num', 10);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['user_name'] = array('like', '%' . $user_name . '%');
        }
        if ($type) {
            $w['type'] = $type;
        }

        $data = db('transfer')
            ->where($w)
            ->order('id DESC')
            ->field(true)
            ->paginate($page_num, false, ['query' => request()->param()]);

        $this->assign('page_num', $page_num);
        $this->assign('data', $data);
        $this->assign('type', $type);
        $this->assign('timestr', $timestr);
        $this->assign('user_name', $user_name);
        $this->assign('page', $data->render());
        return view();
    }

    // 后台转账记录表
    public function exportTrans()
    {
        $w = array();
        $timestr = input('get.time/s', '');
        $user_name = input('get.uname/s', '');
        $type = input('get.type/d', 0);
        if ($timestr) {
            $time = explode(' - ', $timestr);
            $stime = strtotime($time[0]);
            $etime = strtotime($time[1] . ' 23:59:59');
            $w['add_time'] = array('between time', [$stime, $etime]);
        }
        if ($user_name) {
            $w['user_name'] = array('like', '%' . $user_name . '%');
        }
        if ($type) {
            $w['type'] = $type;
        }

        $xlsData = db('transfer')
            ->where($w)
            ->order('id DESC')
            ->field(true)
            ->select();

        $spreadsheet = new Spreadsheet();
        $objActSheet = $spreadsheet->getActiveSheet();
        $key = ord("A");
        $letter = explode(',', "A,B,C,D,E,F,G,H,I");
        $arrHeader = array('编号', '用户名', '转账类型', '转账金额', '账户余额', '管理费余额', '管理员', '转账时间', '备注信息');

        //填充表头信息
        $lenth = count($arrHeader);
        for ($i = 0; $i < $lenth; $i++) {
            $objActSheet->setCellValue("$letter[$i]2", "$arrHeader[$i]");
            // 设置表格的宽度
            $objActSheet->getColumnDimension($letter[$i])->setAutoSize(true);
        }

        $objActSheet->mergeCells('A1:I1');
        $objActSheet->getRowDimension(1)->setRowHeight(25);// 表格高度

        $a1_text = '时间范围：' . $timestr .
            ' 转账类型：' . str_replace([0, 1, 2], ['', '转至账户余额', '赠送管理费'], $type) .
            ' 用户名：' . $user_name;
        $objActSheet->setCellValue('A1', $a1_text);
        $objActSheet->getStyle('A1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $objActSheet->getStyle('A2:I2')->getFont()->setBold(true);

        //填充表格信息
        foreach ($xlsData as $k => $v) {
            $k += 3;
            //表格内容
            $objActSheet->setCellValue('A' . $k, $v['id']);
            $objActSheet->setCellValue('B' . $k, $v['user_name']);
            $objActSheet->setCellValue('C' . $k, str_replace([1, 2], ['转至账户余额', '赠送管理费'], $v['type']));
            $objActSheet->setCellValue('D' . $k, ($v['money'] / 100));
            $objActSheet->setCellValue('E' . $k, ($v['account_money'] / 100));
            $objActSheet->setCellValue('F' . $k, ($v['interest_money'] / 100));
            $objActSheet->setCellValue('G' . $k, $v['admin_name']);
            $objActSheet->setCellValue('H' . $k, $v['create_time']);
            $objActSheet->setCellValue('I' . $k, $v['content']);
            // 表格高度
            $objActSheet->getRowDimension($k)->setRowHeight(20);
        }

        $outfile = "后台转账记录表-" . date('YmdHi') . ".xlsx";
        ob_end_clean();
        header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        header("Content-Type: application/octet-stream");
        header("Content-Type: application/download");
        header('Content-Disposition:inline;filename="' . $outfile . '"');
        header("Content-Transfer-Encoding: binary");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Pragma: no-cache");
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function withdrawOrders()
    {
        $conditions = [];

        if (input('get.order_no')) {
            $conditions['withdraw_orders.order_no'] = ['like', '%' . input('get.order_no/s', '') . '%'];
        }

        if (input('get.transaction_no')) {
            $conditions['withdraw_orders.transaction_no'] = ['like', '%' . input('get.transaction_no/s', '') . '%'];
        }

        $data = WithdrawalOrder::alias('withdraw_orders')
            ->join('members', 'members.id = withdraw_orders.member_id', 'inner')
            ->join('member_info', 'member_info.uid = withdraw_orders.member_id', 'inner')
            ->field('*,member_info.real_name,members.user_name')
            ->where($conditions)
            ->order('withdraw_orders.id DESC')
            ->paginate(config('admin_rows'), false, ['query' => request()->param()]);

        $this->assign('conditions', request()->param());
        $this->assign('paymentProviderMap', WithdrawalOrder::$paymentProviderMap);
        $this->assign('paymentMethodMap', WithdrawalOrder::$paymentMethodMap);
        $this->assign('statusMap', WithdrawalOrder::$statusMap);
        $this->assign('data', $data);
        $this->assign('page', $data->render());
        return view();
    }
}
