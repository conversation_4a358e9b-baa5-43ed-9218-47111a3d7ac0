<?php

namespace app\admin\controller;

use think\Controller;
use think\Request;
use app\common\controller\Admin;

use \think\Validate;

class Future extends Admin{
    protected $category = 2;
	protected function _initialize(){
		parent::_initialize();
        $this->hola = 'Admin Future hola !!!';
    }
    // 配资设置
	public function stockSet(){
        $request = request();
        if( $request->method()=='POST' ){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $data = input('post.');
                // dump($data);die;
                foreach ($data as $k => $v) {
                    if( is_numeric($k) ){
                        db('future_global')->where('id',$k)->setField('text',$v);
                    }
                }

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'修改了期货配资设置';
                admin_log(session('admin_name'), $admin_info, $request->ip());

                cache('future_set',null);
                $this->success('更新成功');
            }else{
                $this->error('不要重复提交表单！');
            }
        }else{
            $data = db('future_global')->where('is_hide',0)->field(true)->order('order_sn desc')->select();
            $this->assign('data',$data);
            return view();
        }
    }
    // 利率设置
    public function rateSet(){
    	$request = request();
        if( $request->method()=='POST' ){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $type = input('post.type/d');
                $rate_arr = explode("\r\n", input('post.rate/s'));

                $rate = array();
                foreach ($rate_arr as $k => $v) {
                    $temp_arr = explode('|', trim($v));
                    if( !$v || empty($temp_arr[0]) || empty($temp_arr[1]) ){
                        $this->error('费率格式错误，如不清楚，请联系管理员！');
                    }
                    $rate[$temp_arr[0]] = $temp_arr[1];
                }

                $save = array(
                        'info'=>json_encode($rate)
                    );
                
                $res = db('future_rateset')->where('type',$type)->update($save);
                if($res){
                    $this->success('更新成功');
                }else{
                    $this->error('更新失败！');
                }

            }else{
                $this->error('不要重复提交表单！');
            }
        }else{
            $rate_set = db('future_rateset')->select();
            $data = array();
            foreach ($rate_set as $k => $v) {
                $temp_arr = json_decode($v['info'],1);
                $temp_str = '';
                // dump($temp_arr);
                if( is_array($temp_arr) ){
                    foreach ($temp_arr as $k2 => $v2) {
                        $temp_str .= $k2."|".$v2."\r\n";
                    }
                }
                // dump( rtrim($temp_str,"\r\n") );
                $data[$v['type']] = rtrim($temp_str,"\r\n");
                unset($temp_arr);
                unset($temp_str);
            }
            $this->assign('data',$data);
            return view();
        }
    }
    // 交易账户
    public function subaccount(){
        // 使用状态 1：未使用 2：使用中 3已终止
        $action = input('param.action/s');
        $status = input('param.status/d');
        $where = array('category'=>$this->category);
        if($action=='select'){
            if( $status ){
                $where['status'] = $status;
            }
        }
        $data = db('stock_account')
                ->where($where)
                ->field(true)
                ->order('id desc')
                ->paginate();
                
        // dump($action);
        $this->assign('status',$status);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function doAccount(){
        $request = request();
        if( $request->method()=='POST' ){
            if( $request->isAjax() ){
                $data = input('post.');
                $model = db('stock_account');
                switch ($data['action']) {
                    case 'add':
                            $had = $model->where('name',$data['mingcheng'])->value('id');
                            if($had){
                                return ['status'=>-1,'message'=>'已有该子账户！'];
                            }
                            $add = array(
                                    'name'=>$data['mingcheng'],
                                    'password'=>$data['mima'],
                                    'remark'=>$data['beizhu'],
                                    'add_time'=>time(),
                                    'category'=>$this->category
                                );
                            $res = $model->insert($add);
                            if($res){
                                return ['status'=>1,'message'=>'添加成功'];
                            }else{
                                return ['status'=>-1,'message'=>'添加失败'];
                            }
                        break;
                    case 'delete':
                            $res = $model->where('id',$data['id'])->delete();
                            if($res){
                                return ['status'=>1,'message'=>'删除成功'];
                            }else{
                                return ['status'=>-1,'message'=>'删除失败'];
                            }
                        break;
                    default:
                        break;
                }
            }
        }else{

            return view();
        }
    }

    // 申请中配资
    public function stockApply(){
        // dump( model('stock')->getStock(47)['end_time'] > time() );die;
        $w = array();
        $user_name = '';
        if( input('get.user_name') ){
            $user_name = input('get.user_name/s','');
            $w['m1.user_name'] = ['like','%'.$user_name.'%'];
        }
        $w['s.status'] = 0;
        $w['s.category'] = 2;
        $data = model('Stock')->stockList($w);

        $this->assign('user_name',$user_name);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 使用中配资
    public function stockList(){
        $w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }
        if( $type ){
            $w['s.type'] = $type;
        }

        $w['s.status'] = 2;
        $w['s.category'] = 2;
        $w['s.end_time'] = ['> time',time()];
        $data = model('Stock')->stockList($w);

        $this->assign('name',$name);
        $this->assign('type',$type);
        $this->assign('action',$action);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 已到期配资
    public function stockEnd(){
    	$w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }
        if( $type ){
            $w['s.type'] = $type;
        }

        $w['s.status'] = 2;
        $w['s.category'] = 2;
        $w['s.end_time'] = ['< time',time()];
        $data = model('Stock')->stockList($w);

        $this->assign('name',$name);
        $this->assign('type',$type);
        $this->assign('action',$action);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 已完成配资
    public function stockFinish(){
        $w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }
        if( $type ){
            $w['s.type'] = $type;
        }

        $w['s.status'] = 3;
        $w['s.category'] = 2;
        $data = model('Stock')->stockList($w);

        $this->assign('name',$name);
        $this->assign('type',$type);
        $this->assign('action',$action);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 未通过配资
    public function stockPass(){
        $w = array();
        $user_name = '';
        if( input('get.user_name') ){
            $user_name = input('get.user_name/s','');
            $w['m1.user_name'] = ['like','%'.$user_name.'%'];
        }
        $w['s.status'] = -1;
        $w['s.category'] = 2;
        $data = model('Stock')->stockList($w);

        $this->assign('user_name',$user_name);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function stockEdit(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $money = input('post.money/f',0);
            $content = input('post.info/s','');
            if( !$id || !$money ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }

            // 添加该记录锁定
            if( cache("stockEdit-{$id}") ){
                return ['status'=>-1,'message'=>'该数据正在处理中，请稍后再试'];
            }
            cache("stockEdit-{$id}", true, 60*3);

            $binfo = model('Stock')->getStock($id);
            // 开启事务
            db()->startTrans();
            if( $binfo ){
                if ( $binfo['status'] != 2 ){
                    cache("stockEdit-{$id}", null);//结束运行 释放记录锁
                    return ['status'=>-2,'message'=>'审核失败，该配资不在使用中！'];
                }
                // 单位（分）
                $money = $money*100;
                $res = settlementFinancing($money, $id, '期货结算处理');

                // 站内信
                $title = '期货结算处理';
                $msg = '期货结算处理成功，释放保证金。备注：'.$content;
                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'期货结算处理成功，配资编号：'.$id.'，剩余配资金额：'.($money/100).' 备注：'.$content;

                //释放配资子账户
                $stock_account = db('stock_account')
                                    ->where('name',$binfo['home_user'])
                                    ->update(['status'=>3]);

                if( $res ){
                    $inner_res = addInnerMsg($binfo['uid'], $title, $msg);//站内信
                    $admin_res = admin_log(session('admin_name'),$admin_info,$ip);//管理员日志
                    
                    cache("stockEdit-{$id}", null);//结束运行 释放记录锁
                    db()->commit();
                    return ['status'=>1,'message'=>'处理成功'];
                }else{

                    cache("stockEdit-{$id}", null);//结束运行 释放记录锁
                    db()->rollback();
                    return ['status'=>-1,'message'=>'处理失败，请稍后再试'];
                }

            }else{

                cache("stockEdit-{$id}", null);//结束运行 释放记录锁
                return ['status'=>-1,'message'=>'该配资记录不存在'];
            }

        }else{
            $id = input('param.id/d',0);
            $data = model('Stock')->getStock($id);

            $this->assign('id',$id);
            $this->assign('data',$data);
            return view();
        }

    }
    // 管理员手动延期
    public function adminRenewal(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $data = input('post.');
            $borrow_id = input('post.borrow_id/d',0);
            $action = input('post.action/s','');
            switch ($action) {
                case 'calculate':
                        if( !$borrow_id || !$data['type'] || !$data['duration'] ){
                            return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
                        }
                        switch ($data['type']) {
                            case 1:
                            case 2:
                            case 8:
                                    $fee = calculate_renewal($data['borrow_id'], $data['duration']);//单位（分）
                                    $temp = $this->getAddTime($data['type'], $data['end_time'], $data['duration']);
                                    $addEndTime = date('Y-m-d H:i', $temp);
                                break;
                            default:
                                    $fee = 0;
                                    $temp = $this->getAddTime(1, $data['end_time'], $data['duration']);
                                    $addEndTime = date('Y-m-d H:i', $temp);
                                break;
                        }

                        return ['status'=>1,'data'=>['fee'=>$fee,'addEndTime'=>$addEndTime] ];
                    break;
                case 'adminRenewal':
                        if( !$borrow_id || !$data['duration'] ){
                            return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
                        }
                        $stockInfo = db('stock_borrow')->where('id', $borrow_id)->field(true)->find();
                        if( $stockInfo['status'] != 2 ){
                            return ['status'=>-2,'message'=>'处理失败，该配资已不在使用中！'];
                        }

                        switch ($stockInfo['type']) {
                            case 1:
                            case 2:
                            case 8:
                                    $addEndTime = $this->getAddTime($stockInfo['type'], $stockInfo['end_time'], $data['duration']);
                                    $tempEndTime = date('Y-m-d H:i', $addEndTime);
                                break;
                            default:
                                    $addEndTime = $this->getAddTime(1, $stockInfo['end_time'], $data['duration']);
                                    $tempEndTime = date('Y-m-d H:i', $addEndTime);
                                break;
                        }

                        //增加续期
                        $stock_save['borrow_duration'] = $stockInfo['borrow_duration']+$data['duration'];
                        $stock_save['end_time'] = $addEndTime;
                        $stock_res = db("stock_borrow")->where('id', $borrow_id)->update($stock_save);
                        if( $stock_res ){
                            // 站内信
                            $title = '管理员手动延期';
                            $msg = '管理员已为子账号('.$stockInfo['home_user'].') 配资订单编号('.$stockInfo['id'].') 手动延期至'.$tempEndTime.'，备注信息：'.$data['content'];
                            
                            // 管理员日志
                            $admin_info = '管理员'.$this->admin_name.'已为子账号('.$stockInfo['home_user'].') 配资订单编号('.$stockInfo['id'].') 手动延期至'.$tempEndTime.'，备注信息：'.$data['content'];
                        
                            $inner_res = addInnerMsg($stockInfo['uid'], $title, $msg);//站内信
                            admin_log($this->admin_name, $admin_info, $ip);//管理员操作日志

                            return ['status'=>1,'message'=>'操作成功'];
                        }else{
                            return ['status'=>-2,'message'=>'操作失败，请稍后再试！'];
                        }
                    break;
                default:
                        return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！002'];
                    break;
            }

        }else{
            $id = input('param.id/d',0);
            $data = model('Stock')->getStock($id);

            $unit = '';
            $use_time = array();//使用期限
            switch ($data['type']) {
                case 1:
                    $unit = '天';
                    $use_time = explode( "|", $this->future['day_use_time']);
                    break;
                case 2:
                    $unit = '月';
                    $use_time = explode( "|", $this->future['month_use_time']);
                    break;
                case 8:
                    $unit = '月';
                    $use_time = explode( "|", $this->future['vip_use_time']);
                    break;
                default:
                    $unit = '天';
                    $use_time = [1,2,3,4,5,6,7];
                    break;
            }

            $this->assign('id',$id);
            $this->assign('data',$data);
            $this->assign('unit',$unit);
            $this->assign('use_time',$use_time);
            return view();
        }
    }

    // 申请中扩大
    public function addfinancing(){
        $w = array();
        $user_name = '';
        if( input('get.user_name') ){
            $user_name = input('get.user_name/s','');
            $w['m1.user_name'] = ['like','%'.$user_name.'%'];
        }
        $w['a.status'] = 0;
        $w['a.category'] = 2;
        $data = model('Addfinancing')->addfinancingList($w);
        // dump($data);die;
        $this->assign('user_name',$user_name);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 已处理扩大
    public function addfinancingList(){
        $w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }

        $w['a.status'] = ['neq',0];
        $w['a.category'] = 2;
        $data = model('Addfinancing')->addfinancingList($w);
        
        $this->assign('name',$name);
        $this->assign('data',$data);
        $this->assign('action',$action);
        $this->assign('page', $data->render());
        return view();
    }
    public function addfinancingEdit(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $status = input('post.status/d',0);
            $content = input('post.info/s','');
            if( !$id || !$status ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            
            $financingInfo = addFinancingCount($id);
            if( !$financingInfo ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！002'];
            }
            if( $financingInfo['status']!=0 ){
                return ['status'=>-1,'message'=>'该扩大记录已被处理！请返回后刷新数据'];
            }

            $money_log = array();//准备资金记录数据$money_log
            $member_money = array();//准备资金账户数据$member_money
            $minfo = memberInfo($financingInfo['uid']);
            $sumMoney = $financingInfo['money'] + $financingInfo['borrow_fee'];//总费用

            // 添加该记录锁定
            if( cache("addfinancing-{$id}") ){
                return ['status'=>-1,'message'=>'该数据正在处理中，请稍后再试'];
            }
            cache("addfinancing-{$id}", true, 60*3);
            // 开启事务
            db()->startTrans();
            if( $status==1 ){
                $stock_borrow = db("stock_borrow");
                $stockInfo = $stock_borrow
                                ->where('id', $financingInfo['borrow_id'])
                                ->field(true)
                                ->find();
                if( $stockInfo['status'] != 2 ){
                    return ['status'=>-2,'message'=>'审核失败，该配资不在使用中！'];
                }

                $stock_save['deposit_money'] = $financingInfo['sumDepositMoney'];
                $stock_save['borrow_money'] = $financingInfo['sumBorrowMoney'];
                // $stock_save['borrow_fee'] = array('exp', 'borrow_fee+' . $financingInfo['borrow_fee']);
                $stock_save['loss_warn'] = $financingInfo['sumLossWarn'];
                $stock_save['loss_close'] = $financingInfo['sumLossClose'];
                $stock_save['stock_addfinancing'] = $stockInfo['stock_addfinancing'] + $financingInfo['money'];//更新申请保证金记录
                $stock_res = $stock_borrow->where('id', $financingInfo['borrow_id'])->update($stock_save);

                $member_money['account_money'] = $minfo['account_money'];
                $member_money['interest_money'] = $minfo['interest_money'];
                $member_money['deposit_money'] = $minfo['deposit_money'] + $financingInfo['money'];

                $log_type = 1;
                $info = '扩大配资审核通过，扣除冻结资金';
                // 站内信
                $title = '扩大配资审核通过';
                $msg = '扩大配资审核通过，扣除冻结资金，备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的扩大申请已通过';

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'扩大配资审核通过，扩大编号：'.$id.'，总操盘资金：'.($financingInfo['sumBorrowMoney']/100).' 备注：'.$content;
            }else{
                if( !$content ){
                    cache("addfinancing-{$id}", null);//结束运行 释放记录锁
                    return ['status'=>-3,'message'=>'未通过备注信息不能为空！'];
                }

                $temp_borrow_interest = $financingInfo['dikou_interest'];//实际管理费抵扣金额
                $temp_interest_diff = $financingInfo['borrow_fee']-$temp_borrow_interest;//使用余额实际抵扣管理费金额
                $deposit_and_fee = $financingInfo['money']+$temp_interest_diff;//退回金额
                $member_money['account_money'] = $minfo['account_money']+$deposit_and_fee;//退回
                $member_money['interest_money'] = $minfo['interest_money']+$temp_borrow_interest;//退回

                $log_type = 2;
                $info = '扩大配资审核未通过，冻结资金已退回账户';
                // 站内信
                $title = '扩大配资审核未通过';
                $msg = '扩大配资审核未通过，冻结资金已退回账户，备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的扩大申请未通过';
                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'扩大配资审核未通过，扩大编号：'.$id.' 备注：'.$content;
            }
            $member_money['money_freeze'] = $minfo['money_freeze'] - $sumMoney;

            // 更新资金账户
            $member_money_res = db('member_money')
                                ->where('uid',$financingInfo['uid'])
                                ->update($member_money);

            // 资金记录数据$money_log
            $money_log = array(
                    'uid'=>$financingInfo['uid'],
                    'user_name'=>$financingInfo['user_name'],
                    'type'=>$log_type,
                    'affect_money'=>$sumMoney,
                    'account_money'=>$member_money['account_money'],
                    'interest_money'=>$member_money['interest_money'],
                    'freeze_money'=>$member_money['money_freeze'],
                    'info'=>$info
                );
            $addMoneyLog = $this->addMoneyLog($money_log, $ip);

            //更新stock_addfinancing状态
            $addfinancing = array(
                    'status'=>$status,
                    'verify_time'=>time(),
                    'content'=>$content
                );
            $addfinancing_res = db('stock_addfinancing')->where('id', $id)->update($addfinancing);

            // 推荐人返佣
            if( $status==1 && $minfo['recommend_id'] ){
                if( $financingInfo['type']==1 || $financingInfo['type']==2 || $financingInfo['type']==8 ){

                    $user_type = db('members')->where('id', $minfo['recommend_id'])->value('user_type');
                    if( $user_type==config('DAILIREN_TYPE') ){
                        sendRebateForDL( $minfo['recommend_id'], $minfo['id'], $id, 2 );
                    }else{
                        $this->sendRebate($minfo['recommend_id'], $minfo, $financingInfo['borrow_fee'], 2);
                    }

                }
            }

            if( $member_money_res && $addMoneyLog && $addfinancing_res ){//全部处理成功
                $ctip = clearTip($financingInfo['uid'],9);
                $inner_res = addInnerMsg($financingInfo['uid'], $title, $msg);//站内信
                admin_log(session('admin_name'), $admin_info, $ip);
                if( $minfo['phone'] ){
                    sendsms_hx($minfo['phone'], $sms_text);
                }

                if( $status==1 ){
                    $xsrw = myXsrw( $financingInfo['uid'] , config('MY_XSRW')['KDPEIZI_JL'] );
                            file_put_contents('./xsrw.txt',
                                    date("Y-m-d H:i ")."uid=".$financingInfo['uid']."&rw_id=".config('MY_XSRW')['KDPEIZI_JL']."&xsrw:".json_encode($xsrw)."\r\n",
                                    FILE_APPEND);
                }


                cache("addfinancing-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status'=>1,'message'=>'操作成功'];
            }else{

                cache("addfinancing-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status'=>-5,'message'=>'操作失败，请稍后再试！'];
            }

        }else{
            $id = input('param.id/d',0);
            $data = addFinancingCount($id);

            $this->assign('id',$id);
            $this->assign('data',$data);
            return view();
        }
    }

    public function renewalApply(){
        $w = array();
        $user_name = '';
        if( input('get.user_name') ){
            $user_name = input('get.user_name/s','');
            $w['m1.user_name'] = ['like','%'.$user_name.'%'];
        }
        $w['r.status'] = 0;
        $w['r.category'] = 2;
        $data = model('Renewal')->renewalList($w);
        // dump($data);die;
        $this->assign('user_name',$user_name);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    // 已处理延期
    public function renewalList(){
        $w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }

        $w['r.status'] = ['neq',0];
        $w['r.category'] = 2;
        $data = model('Renewal')->renewalList($w);
        
        $this->assign('name',$name);
        $this->assign('action',$action);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function renewalEdit(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $status = input('post.status/d',0);
            $content = input('post.info/s','');
            if( !$id || !$status ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            
            $renewalInfo = model('Renewal')->getRenewal($id);
            if( $renewalInfo['status']!=0 ){
                return ['status'=>-1,'message'=>'该延期记录已被处理！请返回后刷新数据'];
            }

            $money_log = array();//准备资金记录数据$money_log
            $member_money = array();//准备资金账户数据$member_money
            $minfo = memberInfo($renewalInfo['uid']);

            // 添加该记录锁定
            if( cache("renewal-{$id}") ){
                return ['status'=>-1,'message'=>'该数据正在处理中，请稍后再试'];
            }
            cache("renewal-{$id}", true, 60*3);
            // 开启事务
            db()->startTrans();
            if( $status==1 ){
                if( $renewalInfo['stock_status'] != 2 ){
                    return ['status'=>-2,'message'=>'审核失败，该配资不在使用中！'];
                }
                //增加续期
                $tempEndTime = $this->getAddTime($renewalInfo['type'], $renewalInfo['end_time'], $renewalInfo['borrow_duration']);
                $stock_save['borrow_duration'] = $renewalInfo['last_duration']+$renewalInfo['borrow_duration'];
                $stock_save['end_time'] = $tempEndTime;
                $stock_res = db("stock_borrow")
                                ->where('id', $renewalInfo['borrow_id'])
                                ->update($stock_save);

                $member_money['account_money'] = $minfo['account_money'];
                $member_money['interest_money'] = $minfo['interest_money'];

                $log_type = 1;
                $info = '申请延期审核通过，扣除冻结资金';
                // 站内信
                $title = '申请延期审核通过';
                $msg = '申请延期审核通过，扣除冻结资金，备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的延期申请已通过';

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'申请延期审核通过，延期编号：'.$id.'。备注：'.$content;
            }else{
                if( !$content ){
                    cache("renewal-{$id}", null);//结束运行 释放记录锁
                    return ['status'=>-3,'message'=>'未通过备注信息不能为空！'];
                }

                $temp_borrow_interest = $renewalInfo['dikou_interest'];//实际管理费抵扣金额
                $temp_interest_diff = $renewalInfo['borrow_fee']-$temp_borrow_interest;//使用余额实际抵扣管理费金额
                $deposit_and_fee = $temp_interest_diff;//退回金额
                $member_money['account_money'] = $minfo['account_money']+$deposit_and_fee;//退回
                $member_money['interest_money'] = $minfo['interest_money']+$temp_borrow_interest;//退回

                $log_type = 2;
                $info = '申请延期审核未通过，冻结资金已退回账户';
                // 站内信
                $title = '申请延期审核未通过';
                $msg = '申请延期审核未通过，冻结资金已退回账户，备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的延期申请未通过';
                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'申请延期审核未通过，延期编号：'.$id.' 备注：'.$content;

            }
            //退冻结费用
            $member_money['money_freeze'] = $minfo['money_freeze']-$renewalInfo['borrow_fee'];
            $member_money_res = db("member_money")
                                ->where('uid', $renewalInfo['uid'])
                                ->update($member_money);

            // 资金记录数据$money_log
            $money_log = array(
                    'uid'=>$renewalInfo['uid'],
                    'user_name'=>$renewalInfo['user_name'],
                    'type'=>$log_type,
                    'affect_money'=>$renewalInfo['borrow_fee'],
                    'account_money'=>$member_money['account_money'],
                    'interest_money'=>$member_money['interest_money'],
                    'freeze_money'=>$member_money['money_freeze'],
                    'info'=>$info
                );
            $addMoneyLog = $this->addMoneyLog($money_log, $ip);

            $renewal_save = array(
                            'status'=>$status,
                            'verify_time'=>time(),
                            'content'=>$content
                        );
            $renewal_res = db('stock_renewal')->where('id', $id)->update($renewal_save);

            // 推荐人返佣
            if( $status==1 && $minfo['recommend_id'] ){
                if( $renewalInfo['type']==1 || $renewalInfo['type']==2 || $renewalInfo['type']==8 ){

                    $user_type = db('members')->where('id', $minfo['recommend_id'])->value('user_type');
                    if( $user_type==config('DAILIREN_TYPE') ){
                        sendRebateForDL( $minfo['recommend_id'], $minfo['id'], $id, 3 );
                    }else{
                        $this->sendRebate($minfo['recommend_id'], $minfo, $renewalInfo['borrow_fee'], 3);
                    }
                    
                }
            }

            if( $member_money_res && $addMoneyLog && $renewal_save ){//全部处理成功
                $ctip = clearTip($renewalInfo['uid'],10);
                $inner_res = addInnerMsg($renewalInfo['uid'], $title, $msg);//站内信
                admin_log(session('admin_name'), $admin_info, $ip);

                if( $minfo['phone'] ){
                    sendsms_hx($minfo['phone'], $sms_text);
                }

                cache("renewal-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status'=>1,'message'=>'操作成功'];
            }else{

                cache("renewal-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status'=>-5,'message'=>'操作失败，请稍后再试！'];
            }

        }else{
            $id = input('param.id/d',0);
            $data = model('Renewal')->getRenewal($id);

            $data['addEndTime'] = $this->getAddTime($data['type'], $data['end_time'], $data['borrow_duration']);

            $this->assign('id',$id);
            $this->assign('data',$data);
            return view();
        }
    }
    /*  得到续期时间
    **  $type :1、天，2、月 8VIP
    **  $end_time :原过期时间
    **  $borrow_duration :申请时间段
    */
    private function getAddTime($type, $end_time, $borrow_duration){
        if( $type==1 ){
            $nowData = date("Y-m-d", $end_time);//以之前结束时间开始算
            $set_holidays = explode(',', $this->future['holidays']);
            $endYmd = getEndDay($nowData, $borrow_duration, $set_holidays);
            $addEndTime = $endYmd . " 14:45:00";//结束时间
            $addEndTime = strtotime($addEndTime);
        }else if( $type==2 || $type==8 ){
            $addEndTime = strtotime("+{$borrow_duration} month", $end_time);
        }

        return $addEndTime;
    }

    // 申请中终止
    public function stopfinancingApply(){
        $w = array();
        $user_name = '';
        if( input('get.user_name') ){
            $user_name = input('get.user_name/s','');
            $w['m1.user_name'] = ['like','%'.$user_name.'%'];
        }
        $w['s.status'] = 0;
        $w['s.category'] = 2;
        $data = model('Stopfinancing')->stopfinancingList($w);
        // dump($data);die;
        $this->assign('user_name',$user_name);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function stopfinancingEdit(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $money = input('post.money/f',0);
            $status = input('post.status/d',0);
            $content = input('post.info/s','');
            if( !$id || !$status ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            $info = model('Stopfinancing')->getStopfinancing($id);
            if( !$info ){
                return ['status'=>-1,'message'=>'数据不存在！请稍后重试'];
            }
            if( $info['status']!=0 ){
                return ['status'=>-1,'message'=>'该终止记录已被处理！请返回后刷新数据'];
            }

            $check = checkStopApply($info['borrow_id']);
            if( $check!==true ){
                return $check;
            }

            // 添加该记录锁定
            if( cache("stopfinancing-{$id}") ){
                return ['status'=>-1,'message'=>'该数据正在处理中，请稍后再试'];
            }
            cache("stopfinancing-{$id}", true, 60*3);
            // 开启事务
            db()->startTrans();
            if( $status==1 ){
                if ( $info['stock_status'] != 2 ){
                    return ['status'=>-2,'message'=>'审核失败，该配资不在使用中！'];
                }
                if( !$money ){
                    return ['status'=>-1,'message'=>'剩余配资金额不能为空'];
                }
                $money = $money*100;// 单位（分）
                $settlement_res = settlementFinancing($money, $info['borrow_id'], '终止配资');
                //释放配资子账户
                $stock_account = db('stock_account')->where('name',$info['home_user'])->update(['status'=>3]);

                // 站内信
                $title = '终止配资审核通过';
                $msg = '终止配资审核通过，备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的终止申请已通过';

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'终止配资审核通过，终止编号：'.$id.'，剩余配资金额：'.($money/100).' 备注：'.$content;
            }else{
                if( !$content ){
                    cache("stopfinancing-{$id}", null);//结束运行 释放记录锁
                    return ['status'=>-3,'message'=>'未通过 备注信息不能为空！'];
                }
                $settlement_res = true;
                // 站内信
                $title = '终止配资审核未通过';
                $msg = '终止配资审核未通过，备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的终止申请未通过';

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'终止配资审核未通过，终止编号：'.$id.' 备注：'.$content;
            }

            //更新stock_stopfinancing状态
            $stopData = array(
                    'status'=>$status,
                    'verify_time'=>time(),
                    'content'=>$content
                );
            $stopRet = db('stock_stopfinancing')->where("id",$id)->update($stopData);
            if( $settlement_res && $stopRet ){
                $ctip = clearTip($info['uid'],11);
                $inner_res = addInnerMsg($info['uid'], $title, $msg);//站内信
                $admin_res = admin_log(session('admin_name'),$admin_info,$ip);//管理员日志
                
                if( $info['phone'] ){
                    sendsms_hx($info['phone'], $sms_text);
                }
                
                cache("stopfinancing-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status'=>1,'message'=>'处理成功'];
            }else{
                cache("stopfinancing-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status'=>-1,'message'=>'处理失败，请稍后再试'];
            }

        }else{
            $id = input('param.id/d',0);
            $data = model('Stopfinancing')->getStopfinancing($id);

            $this->assign('id',$id);
            $this->assign('data',$data);
            return view();
        }
    }
    // 已处理终止
    public function stopfinancingList(){
        $w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s2.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }

        $w['s.status'] = ['neq',0];
        $w['s.category'] = 2;
        $data = model('Stopfinancing')->stopfinancingList($w);
        
        $this->assign('name',$name);
        $this->assign('action',$action);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }

    // 申请中补亏
    public function fillApply(){
        $w = array();
        $user_name = '';
        if( input('get.user_name') ){
            $user_name = input('get.user_name/s','');
            $w['m1.user_name'] = ['like','%'.$user_name.'%'];
        }
        $w['f.status'] = 0;
        $w['f.category'] = 2;
        $data = model('Fill')->fillList($w);
        // dump($data);die;
        $this->assign('user_name',$user_name);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function fillEdit(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $status = input('post.status/d',0);
            $content = input('post.info/s','');
            if( !$id || !$status ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            $binfo = model('Fill')->getFill($id);
            if( $binfo['status']!=0 ){
                return ['status'=>-1,'message'=>'该补亏记录已被处理！请返回后刷新数据'];
            }
            
            $minfo = memberInfo($binfo['uid']);//用户信息
            $money_log = array();//准备资金记录数据$money_log
            $member_money = array();//准备资金账户数据$member_money


            // 添加该记录锁定
            if( cache("fill-{$id}") ){
                return ['status'=>-1,'message'=>'该数据正在处理中，请稍后再试'];
            }
            cache("fill-{$id}", true, 60*3);
            // 开启事务
            db()->startTrans();
            if( $status==1 ){
                if( $binfo['stock_status'] != 2 ){
                    return ['status'=>-2,'message'=>'审核失败，该配资不在使用中！'];
                }
                $log_type = 1;
                $account_money = $minfo['account_money'];
                $info = '申请补亏审核通过，扣除冻结资金';
                // 站内信
                $title = '补亏审核通过';
                $msg = $info;
                // 发送至客户短信
                $sms_text = '您的补亏申请已通过';

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'审核补亏成功，补亏编号：'.$id;

                // 添加补亏金到stock_borrow
                $stock_res = db('stock_borrow')
                                ->where('id',$binfo['borrow_id'])
                                ->setInc('stock_addmoney', $binfo['money']);
            }else{
                if( !$content ){
                    cache("fill-{$id}", null);//结束运行 释放记录锁
                    return ['status'=>-3,'message'=>'未通过备注信息不能为空！'];
                }
                $log_type = 2;
                $account_money = $minfo['account_money'] + $binfo['money'];
                $member_money['account_money'] = $account_money;//退回

                $info = '申请补亏审核未通过，冻结资金已退回账户';
                // 站内信
                $title = '补亏审核未通过';
                $msg = $info.' 备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的补亏申请未通过';
                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'审核补亏未通过，补亏编号：'.$id.' 备注：'.$content;
            }
            $member_money['money_freeze'] = $minfo['money_freeze'] - $binfo['money'];
            $member_moneylog = db('member_money')->where('uid',$binfo['uid'])->update($member_money);

            $money_log = array(
                    'uid'=>$binfo['uid'],
                    'user_name'=>$minfo['user_name'],
                    'type'=>$log_type,
                    'affect_money'=>$binfo['money'],
                    'account_money'=>$account_money,
                    'interest_money'=>$minfo['interest_money'],
                    'freeze_money'=>$member_money['money_freeze'],
                    'info'=>$info
                );
            $addMoneyLog = $this->addMoneyLog($money_log, $ip);

            //更新状态及添加保证金
            $save = array(
                    'status'=>$status,
                    'verify_time'=>time(),
                    'content'=>$content
                );
            $fill_res = db('stock_addmoney')->where("id",$id)->update($save);
            if( $member_moneylog && $addMoneyLog ){//全部处理成功
                $ctip = clearTip($binfo['uid'],8);
                $inner_res = addInnerMsg($binfo['uid'], $title, $msg);//站内信
                $admin_res = admin_log(session('admin_name'),$admin_info,$ip);//管理员日志

                if( $minfo['phone'] ){
                    sendsms_hx($minfo['phone'], $sms_text);
                }

                if( $status==1 ){
                    $xsrw = myXsrw( $binfo['uid'] , config('MY_XSRW')['BUKUI_JL'] );
                            file_put_contents('./xsrw.txt',
                                    date("Y-m-d H:i ")."uid=".$binfo['uid']."&rw_id=".config('MY_XSRW')['BUKUI_JL']."&xsrw:".json_encode($xsrw)."\r\n",
                                    FILE_APPEND);
                }

                cache("fill-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status'=>1,'message'=>'处理成功'];
            }else{
                cache("fill-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status'=>-1,'message'=>'处理失败，请稍后再试'];
            }

        }else{
            $id = input('param.id/d',0);
            $data = model('Fill')->getFill($id);

            $this->assign('id',$id);
            $this->assign('data',$data);
            return view();
        }
    }
    public function fillList(){
        $w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }

        $w['f.status'] = ['neq',0];
        $w['f.category'] = 2;
        $data = model('Fill')->fillList($w);
        
        $this->assign('name',$name);
        $this->assign('action',$action);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }

    // 申请中提盈
    public function drawprofitApply(){
        $w = array();
        $user_name = '';
        if( input('get.user_name') ){
            $user_name = input('get.user_name/s','');
            $w['m1.user_name'] = ['like','%'.$user_name.'%'];
        }
        $w['d.status'] = 0;
        $w['d.category'] = 2;
        $data = model('Drawprofit')->drawprofitList($w);
        // dump($data);die;
        $this->assign('user_name',$user_name);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function drawprofitEdit(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $status = input('post.status/d',0);
            $content = input('post.info/s','');
            if( !$id || !$status ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            $binfo = model('Drawprofit')->getDrawprofit($id);
            if( $binfo['status']!=0 ){
                return ['status'=>-1,'message'=>'该提盈记录已被处理！请返回后刷新数据'];
            }


            // 添加该记录锁定
            if( cache("drawprofit-{$id}") ){
                return ['status'=>-1,'message'=>'该数据正在处理中，请稍后再试'];
            }
            cache("drawprofit-{$id}", true, 60*3);
            // 开启事务
            db()->startTrans();
            if( $status==1 ){
                if ( $binfo['stock_status'] != 2) {
                    return ['status'=>-2,'message'=>'审核失败，该配资不在使用中！'];
                }
                if( $binfo['type']==3 ){
                    // 免息配资提取盈利收取盈利提成
                    $freeSet = explode('|',$this->future['free_set']);
                    $rate = $freeSet[2];
                    $gainMoney = $binfo['money'] * ($rate / 100);
                    $info = "提盈审核通过，申请金额".($binfo['money']/100)."元，免息配资盈利的{$freeSet[2]}%归您, 实际到账金额".($gainMoney/100)."元";
                }else{
                    $gainMoney = $binfo['money'];
                    $info = '申请提取盈利审核通过，添加金额'.($gainMoney/100).'元';
                }
                $member_moneylog = memberMoneyLog($binfo['uid'], $gainMoney, $info, 1);
                //更新已提盈的资金到stock_borrow
                $stock_res = db('stock_borrow')
                                ->where('id',$binfo['borrow_id'])
                                ->setInc('stock_drawprofit', $gainMoney);
                // 站内信
                $title = '提盈审核通过，资金增加至余额';
                $msg = $info;
                // 发送至客户短信
                $sms_text = '您的提盈申请已通过';

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'审核提盈通过，提盈编号：'.$id;
            }else{
                if( !$content ){
                    cache("drawprofit-{$id}", null);//结束运行 释放记录锁
                    return ['status'=>-3,'message'=>'未通过 备注信息不能为空！'];
                }
                $member_moneylog = true;
                $info = '申请提取盈利审核未通过';
                // 站内信
                $title = '提盈审核未通过';
                $msg = $info.' 备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的提盈申请未通过';

                // 管理员日志
                $admin_info = '管理员'.session('admin_name').'审核提盈未通过，提盈编号：'.$id.' 备注：'.$content;
            }
            // stock_drawprofit存储信息
            $save = array(
                    'status'=>$status,
                    'verify_time'=>time(),
                    'content'=>$content
                );
            $drawprofit_res = db('stock_drawprofit')->where('id',$binfo['id'])->update($save);
            if( $member_moneylog && $drawprofit_res){//全部处理成功
                $ctip = clearTip($binfo['uid'],7);
                $inner_res = addInnerMsg($binfo['uid'], $title, $msg);//站内信
                $admin_res = admin_log(session('admin_name'),$admin_info,$ip);//管理员日志
                if( $binfo['phone'] ){
                    sendsms_hx($binfo['phone'], $sms_text);
                }
                if( $status==1 ){
                    $xsrw = myXsrw( $binfo['uid'] , config('MY_XSRW')['TIYING_JL'] );
                            file_put_contents('./xsrw.txt',
                                    date("Y-m-d H:i ")."uid=".$binfo['uid']."&rw_id=".config('MY_XSRW')['TIYING_JL']."&xsrw:".json_encode($xsrw)."\r\n",
                                    FILE_APPEND);
                }

                cache("drawprofit-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status'=>1,'message'=>'处理成功'];
            }else{

                cache("drawprofit-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status'=>-1,'message'=>'处理失败，请稍后再试'];
            }
        }else{
            $id = input('param.id/d',0);
            $data = model('Drawprofit')->getDrawprofit($id);

            $this->assign('id',$id);
            $this->assign('data',$data);
            return view();
        }
    }
    public function drawprofitList(){
        $w = array();
        $name = input('get.name/s','');
        $type = input('get.type/d',0);
        $action = input('get.action/s','');
        if( $name ){
            switch ($action) {
                case 'user_name':
                    $w['m1.user_name'] = ['like','%'.$name.'%'];
                    break;
                case 'home_user':
                    $w['s.home_user'] = ['like','%'.$name.'%'];
                    break;
                default:
                    # code...
                    break;
            }
        }

        $w['d.status'] = ['neq',0];
        $w['d.category'] = 2;
        $data = model('Drawprofit')->drawprofitList($w);
        
        $this->assign('name',$name);
        $this->assign('action',$action);
        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }

    /**
     * 发送返利给推荐人
     * 返利金额
     * $recommender 推荐人id
     * $investor 被推荐人信息
     * $borrow 配资信息
     * $type 1申请配资 2扩大配资 3申请延期
     */
    private function sendRebate($recommender,$investor,$interest=0,$type=1){
        $rebate = (int)$this->future['rebate'];
        if( $rebate>0 ){
            switch ($type) {
                case 1:
                    // $interest = $borrow['borrow_interest'];
                    $rebate_money = intval( $interest * ($rebate/100) );
                    $info = "您推荐的用户".$investor['user_name']."配资通过，配资管理费：".($interest/100)."元，您获得返利：".($rebate_money/100)."元";
                    $res = memberMoneyLog($recommender, $rebate_money, $info, 6);
                    break;
                case 2:
                    $rebate_money = intval( $interest * ($rebate/100) );
                    $info = "您推荐的用户".$investor['user_name']."扩大配资通过，配资管理费：".($interest/100)."元，您获得返利：".($rebate_money/100)."元";
                    $res = memberMoneyLog($recommender, $rebate_money, $info, 6);
                    break;
                case 3:
                    $rebate_money = intval( $interest * ($rebate/100) );
                    $info = "您推荐的用户".$investor['user_name']."申请延期通过，延期管理费：".($interest/100)."元，您获得返利：".($rebate_money/100)."元";
                    $res = memberMoneyLog($recommender, $rebate_money, $info, 6);
                    break;
                default:
                    # code...
                    break;
            }
        }
        return true;
    }
    public function sendFinancingUser(){
        $request = request();
        $ip = $request->ip();
        if( $request->isAjax() ){
            $id = input('post.borrow_id/d',0);
            $status = input('post.status/d',0);
            $content = input('post.info/s','');

            if( !$id || !$status ){
                return ['status'=>-1,'message'=>'数据解析错误，请按规则重试！'];
            }
            if ($status == -1 && empty($content)) {
                return ['status'=>-3,'message'=>'拒绝审核时备注信息不能为空！'];
            }

            $binfo = model('Stock')->getStock($id);
            if( !$binfo ){
                return ['status'=>-3,'message'=>'该配资记录不存在或已被处理！'];
            }
            if( $binfo['status']!=0 ){
                return ['status'=>-3,'message'=>'该配资记录已被处理！请返回后刷新数据'];
            }

            $money_log = array();//准备资金记录数据$money_log
            $member_money = array();//准备资金账户数据$member_money
            $minfo = memberInfo($binfo['uid']);
            $sumMoney = $binfo['deposit_money']+$binfo['borrow_interest'];//被冻结资金
            
            $member_money['money_freeze'] = $minfo['money_freeze']-$sumMoney;
            $money_log['uid'] = $binfo['uid'];
            $money_log['user_name'] = $minfo['user_name'];
            $money_log['affect_money'] = $sumMoney;
            $money_log['account_money'] = $minfo['account_money'];
            $money_log['interest_money'] = $minfo['interest_money'];

            // 添加该记录锁定
            if( cache("sendFinancingUser-{$id}") ){
                return ['status'=>-1,'message'=>'该数据正在处理中，请稍后再试'];
            }
            cache("sendFinancingUser-{$id}", true, 60*3);
            // 开启事务
            db()->startTrans();
            if( $status==2 ){
                // 自動查找該配資申請對應的子帳戶
                $stock_account_mod = db('stock_account');
                $stock_account = $stock_account_mod
                                    ->where(['borrow_id'=>$id,'status'=>1])
                                    ->field(true)
                                    ->find();
                if( !$stock_account ){
                    cache("sendFinancingUser-{$id}", null);
                    db()->rollback();
                    return ['status'=>-2,'message'=>'该配资申请对应的子账号不存在或已被使用，请联系管理员！'];
                }

                $stock_account_id = $stock_account['id'];
                    
                $member_money['deposit_money'] = $minfo['deposit_money'] + $binfo['deposit_money'];
                $money_log['type'] = 1;
                $money_log['freeze_money'] = $member_money['money_freeze'];
                $money_log['info'] = '期货配资审核通过，扣除冻结资金';

                // 更新子账户
                $account_save['uid'] = $binfo['uid'];
                $account_save['borrow_id'] = $id;
                $account_save['status'] = 2;
                $account_res = $stock_account_mod->where('id',$stock_account_id)->update($account_save);

                // 站内信
                $title = '期货配资审核通过';
                $msg = '期货配资审核通过，扣除冻结资金，备注：'.$content;
                // 发送至客户短信
                $sms_text = '您的客户端交易账号为：'.$stock_account['name'].' 密码：'.$stock_account['password'];

                $admin_info = '管理员'.session('admin_name').'期货配资审核通过，配资编号：'.$binfo['id'];
            }else{
                // 審核拒絕時，刪除已創建的子帳戶
                $stock_account_mod = db('stock_account');
                $rejected_account = $stock_account_mod
                                    ->where(['borrow_id'=>$id,'status'=>1])
                                    ->find();
                if ($rejected_account) {
                    $stock_account_mod->where('id', $rejected_account['id'])->delete();
                }

                $temp_borrow_interest = $binfo['dikou_interest'];//实际管理费抵扣金额
                $temp_interest_diff = $binfo['borrow_interest']-$temp_borrow_interest;//使用余额实际抵扣管理费金额
                $deposit_and_fee = $binfo['deposit_money']+$temp_interest_diff;//退回金额
                $member_money['account_money'] = $minfo['account_money']+$deposit_and_fee;//退回
                $member_money['interest_money'] = $minfo['interest_money']+$temp_borrow_interest;//退回

                $money_log['type'] = 2;
                $money_log['freeze_money'] = $member_money['money_freeze'];
                $money_log['account_money'] = $member_money['account_money'];
                $money_log['interest_money'] = $member_money['interest_money'];
                $money_log['info'] = '期货配资审核未通过，冻结资金已退回账户';
                // var_dump($temp_borrow_interest,$temp_interest_diff,$deposit_and_fee,$member_money,$money_log);die;

                // 站内信
                $title = '期货配资审核未通过';
                $msg = '期货配资审核未通过，冻结资金已退回账户，备注：'.$content;
                // 发送至客户短信
                $sms_text = '申请方案审核未通过';

                $admin_info = '管理员'.session('admin_name').'期货配资审核未通过，配资编号：'.$binfo['id'].'，备注信息：'.$content;
            }
            // 更新资金账户
            $member_money_res = db('member_money')
                                ->where('uid',$binfo['uid'])
                                ->update($member_money);
            // 更新资金记录
            $money_log_res = $this->addMoneyLog($money_log, $ip);

            // 更新stock_borrow数据
            $stock_save['status'] = $status;
            $stock_save['home_user'] = $status==2 ? $stock_account['name'] : '';
            $stock_save['home_pws'] = $status==2 ? $stock_account['password'] : '';
            $stock_save['verify_time'] = time();
            $stock_save['content'] = $content;
            $stock_save['stock_usable_money'] = $status==2 ? $binfo['borrow_money'] : 0;
            $stock_res = db('stock_borrow')->where('id',$id)->update($stock_save);

            // 推荐人返佣
            if( $status==2 && $minfo['recommend_id'] ){
                if ( $binfo['type']==1 || $binfo['type']==2 || $binfo['type']==8 ) {

                    $user_type = db('members')->where('id', $minfo['recommend_id'])->value('user_type');
                    if( $user_type==config('DAILIREN_TYPE') ){
                        sendRebateForDL( $minfo['recommend_id'], $minfo['id'], $id, 1 );
                    }else{
                        $this->sendRebate($minfo['recommend_id'], $minfo, $binfo['borrow_interest']);
                    }
                    
                }
            }

            if( $member_money_res && $money_log_res && $stock_res ){//全部处理成功
                $ctip = clearTip($binfo['uid'],6);
                $inner_res = addInnerMsg($binfo['uid'], $title, $msg);
                admin_log(session('admin_name'), $admin_info, $ip);
                if( $minfo['phone'] ){
                    sendsms_hx($minfo['phone'], $sms_text);
                }

                if( $status==2 ){
                    $xsrw = myXsrw( $binfo['uid'] , config('MY_XSRW')['PEIZI_JL'] );
                            file_put_contents('./xsrw.txt',
                                    date("Y-m-d H:i ")."uid=".$binfo['uid']."&rw_id=".config('MY_XSRW')['PEIZI_JL']."&xsrw:".json_encode($xsrw)."\r\n",
                                    FILE_APPEND);
                }

                cache("sendFinancingUser-{$id}", null);//结束运行 释放记录锁
                db()->commit();
                return ['status'=>1,'message'=>'操作成功'];
            }else{

                cache("sendFinancingUser-{$id}", null);//结束运行 释放记录锁
                db()->rollback();
                return ['status'=>-5,'message'=>'操作失败，请稍后再试！'];
            }
            // var_dump($content,$member_money,$member_money_res,$money_log_res,$stock_res,$stock_save);die;

        }else{
            $borrow_id = input('param.id/d',0);
            // $data = stock_info($borrow_id);
            $data = model('Stock')->getStock($borrow_id);

            // 查找該配資申請對應的子帳戶
            $sub_account = db('stock_account')
                                ->where(['borrow_id'=>$borrow_id,'status'=>1])
                                ->field('id,name,password')
                                ->find();

            $this->assign('borrow_id',$borrow_id);
            $this->assign('sub_account',$sub_account);
            $this->assign('data',$data);
            return view('sendFinancingUser');
        }
    }
    // 添加资金记录
    // $uid,$user_name='',$log_type=0,$affect_money=0,$account_money=0,$interest_money=0,$money_freeze=0,$info='',$ip=''
    protected function addMoneyLog($money_log=array(),$ip=''){
        $log = array(
                    'uid'=>$money_log['uid'],
                    'user_name'=>$money_log['user_name'],
                    'type'=>$money_log['type'],
                    'affect_money'=>$money_log['affect_money'],
                    'account_money'=>$money_log['account_money'],
                    'interest_money'=>$money_log['interest_money'],
                    'freeze_money'=>$money_log['freeze_money'],
                    'info'=>$money_log['info'],
                    'add_time'=>time(),
                    'add_ip'=>$ip
                );
        $res = db('member_moneylog')->insert($log);
        if($res){
            return true;
        }else{
            return false;
        }
    }


}
