<?php

namespace app\admin\controller;

use think\Request;
use app\common\controller\Admin;
use \think\Validate;
use \think\Db;

class Article extends Admin{
	protected function _initialize(){
		parent::_initialize();
        $this->hola = 'Admin Other hola !!!';
    }

    public function lists(){
        $mod = db('article');
        $w = array();
        $parent_id = input('param.parent_id/d',0);
        if( $parent_id ){
            $w['a.parent_id'] = $parent_id;
        }

        $data = $mod->alias('a')
                ->join('category c','a.parent_id = c.id','left')
                ->field('a.*,c.name AS category_name')
                ->order('a.sort_order DESC,a.add_time DESC')
                ->where($w)
                ->paginate(config('admin_rows'), false, ['query'=>request()->param()]);
                // ->select();
        $category = $mod->alias('a')
                    ->join('category c','a.parent_id = c.id','left')
                    ->field('a.parent_id,c.name')
                    ->group('a.parent_id')
                    ->order('c.sort_order DESC,c.id DESC')
                    ->select();

        $this->assign('data',$data);
        $this->assign('parent_id',$parent_id);
        $this->assign('category',$category);
        $this->assign('page', $data->render());
        return view();
    }
    public function addArticle(){
        $request = request();
        if($request->method()=='POST'){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $data = input('post.',[]);
                $action = input('post.action/s','');
                $content = input('post.contents/s','');
                $art_time = input('post.art_time/s','');

                $add_time =  $art_time ? strtotime($art_time) : time();

                switch ($action) {
                    case 'edit':
                        $id = input('post.id/d',0);
                        if( !$id ){
                            $this->error('数据解析错误，请按照平台规则重试！');
                        }
                        $save = array(
                                'title'=>$data['title'],
                                'info'=>$data['info'],
                                'keyword'=>$data['keyword'],
                                'content'=>$content,
                                'writer'=>session('admin_name'),
                                'parent_id'=>$data['parent_id'],
                                'is_hide'=>$data['is_hide'],
                                'sort_order'=>$data['sort_order'],
                                'add_time'=>$add_time
                            );
                        $res = db('article')->where('id',$id)->update($save);
                        break;
                    default:
                        $add = array(
                                'title'=>$data['title'],
                                'info'=>$data['info'],
                                'keyword'=>$data['keyword'],
                                'content'=>$content,
                                'writer'=>session('admin_name'),
                                'parent_id'=>$data['parent_id'],
                                'is_hide'=>$data['is_hide'],
                                'sort_order'=>$data['sort_order'],
                                'add_time'=>$add_time
                            );
                        $res = db('article')->insert($add);
                        break;
                }
                if( $res ){
                    $this->success('操作成功','article/lists');
                }else{
                    $this->error('操作失败，请稍后再试！');
                }
            }else{
                $this->error('不要重复提交数据！');           
            }
        }else{
            $id =  input('param.id/d',0);
            $action = input('param.action/s','');
            $data = array();

            if( $action=='edit' ){
                $data = db('article')->where('id',$id)->field(true)->find();
            }

            $tree = getCategoryTree();
            $this->assign('id',$id);
            $this->assign('action',$action);
            $this->assign('tree',$tree);
            $this->assign('data',$data);
            return view();
        }
    }
    public function delArticle(){
        $request = request();
        if($request->isAjax()){
            $id = input('post.id/d');
            if( !$id ){
                return ['status'=>-1,'message'=>'参数错误，请稍后再试'];
            }
            $res = db('article')->where('id',$id)->delete();
            if( $res ){
                return ['status'=>1,'message'=>'删除成功'];
            }else{
                return ['status'=>-1,'message'=>'删除失败，请稍后重试'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }

    
    public function category(){
        $data = db('category')
                ->where('parent_id',0)
                ->field('id,name,parent_id AS pid,sort_order,create_time,is_hidden,type_nid')
                ->order('sort_order DESC')
                ->select();
        foreach ($data as $k => &$v) {
            $v['has'] = $this->_typeSon($v['id']);
        }
        unset($v);
        
        $this->assign('data',$data);
        return view();
    }
    public function addCategoty(){
        $request = request();
        if($request->method()=='POST'){
            if( Validate::token('__token__','',['__token__'=>input('post.__token__/s')]) ){
                $mod = db('category');
                $data = input('post.');
                $action = input('post.action/s','');
                $type_nid = input('post.jiancheng/s','');
                if( !$type_nid ){
                    $this->error('栏目简称不能为空！');
                }

                switch ($action) {
                    case 'edit':
                            $id = input('post.id/d',0);
                            if( !$id ){
                                $this->error('数据解析错误，请按照平台规则重试！');
                            }
                            $temp_nid = $mod->where('id',$id)->value('type_nid');
                            if( $temp_nid!=$type_nid ){
                                $had = $mod->where('type_nid',$type_nid)->count('id');
                                if( $had ){
                                    $this->error('栏目简称不能重复！请返回重试');
                                }
                            }
                            $save = array(
                                    'name'=>$data['mingcheng'],
                                    'parent_id'=>$data['subcate'],
                                    'content'=>empty($data['contents'])?'':$data['contents'],
                                    'sort_order'=>$data['paixu'],
                                    'type_set'=>$data['shuxing'],
                                    'type_nid'=>$data['jiancheng'],
                                    'is_hidden'=>empty($data['is_hidden'])?2:1,
                                );
                            $res = $mod->where('id',$id)->update($save);
                        break;
                    default:
                            $had = $mod->where('type_nid',$type_nid)->count('id');
                            if( $had ){
                                $this->error('栏目简称不能重复！请返回重试');
                            }
                            $add = array(
                                    'name'=>$data['mingcheng'],
                                    'parent_id'=>$data['subcate'],
                                    'content'=>empty($data['contents'])?'':$data['contents'],
                                    'sort_order'=>$data['paixu'],
                                    'type_set'=>$data['shuxing'],
                                    'type_nid'=>$data['jiancheng'],
                                    'is_hidden'=>empty($data['is_hidden'])?2:1,
                                    'create_time'=>date('Y-m-d H:i:s',time())
                                );
                            $res = $mod->insert($add);
                        break;
                }
                if($res){
                    $this->success('操作成功','article/category');
                }else{
                    $this->error('操作失败，请稍后再试！');
                }

            }else{
                $this->error('不要重复提交数据！');                
            }
        }else{
            $id =  input('param.id/d',0);
            $action = input('param.action/s','');
            $data = array();

            if( $action=='edit' ){
                $data = db('category')->where('id',$id)->field(true)->find();
            }

            $tree = getCategoryTree();
            $this->assign('id',$id);
            $this->assign('action',$action);
            $this->assign('tree',$tree);
            $this->assign('data',$data);
            return view('addCategoty');
        }

    }
    public function delCategory(){
        $request = request();
        if($request->isAjax()){
            $id = input('post.id/d');
            if($id <= 0){
                return ['status'=>-1,'message'=>'参数错误，请稍后再试'];
            }
            $has = db('category')->where('parent_id',$id)->field('id')->find();
            if($has){
                return ['status'=>-2,'message'=>'删除失败，该栏目下包含有子栏目，不能删除！'];
            }
            $res = db('category')->where('id',$id)->delete();
            if($res){
                return ['status'=>1,'message'=>'删除分类成功'];
            }else{
                return ['status'=>-3,'message'=>'删除分类失败！'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }



    public function getCategotyList(){
        $type_id = input('post.type_id/d');
        if($type_id <= 0){
            return ['status'=>'-1','message'=>'数据错误！请重试'];
        }
        $data = db('category')->where('parent_id',$type_id)
                ->field('id,name,parent_id,sort_order,is_hidden,type_nid,create_time')
                ->order('sort_order desc')
                ->select();
        $html = '';
        foreach ($data as &$v) {
            $v['has'] = $this->_typeSon($v['id']);
            $temp_zs = $v['has']===true?'<i class="zhanshi"></i>':'';
            $temp_level = $this->_typeLeve($v['id']);

            $html .= '<tr id="list_'.$v['id'].'" data-id="'.$v['id'].'" parentid="'.$type_id.'" class="leve_'.$temp_level.'">
                        <td>'.$v['id'].'</td>
                        <td class="zsyc">
                            '.$temp_zs.$v['name'].'
                        </td>
                        <td>'.$v['type_nid'].'</td>
                        <td>'.$v['sort_order'].'</td>
                        <td>'.str_replace([1,2], ['显示','隐藏'], $v['is_hidden']).'</td>
                        <td>'.$v['create_time'].'</td>
                        <td>
                            <a href="'.url('article/addCategoty',['action'=>'edit','id'=>$v['id']] ).'" class="layui-bg-blue ft15">编辑</a>
                            &nbsp;
                            <a href="javascript:;" onclick="delConfirm('.$v['id'].')" class="layui-bg-red ft15">删除</a>
                        </td>
                    </tr>';
            unset($temp_zs);
            unset($temp_level);
        }
        unset($v);
        return ['status'=>1,'data'=>$html];
    }
    private function _typeSon($pid){
        $condition['parent_id'] = intval($pid);
        $val = db('category')->field('id')->where($condition)->find();
        if($val){
            return true;
        }else{
            return false;
        }
    }
    private $typeleve=1;
    private $typeleve_default=1;
    private function _typeLeve($typeid){
        static $level=0;
        $condition['id'] = intval($typeid);
        $v = db('category')->field('parent_id')->where($condition)->find();
        if($v['parent_id']>0){
            $this->typeleve++;
            $this->_typeLeve($v['parent_id']);
        }else{
            $level = $this->typeleve;
            $this->typeleve = $this->typeleve_default;
        }
        return $level;
    }




}
