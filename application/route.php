<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
/*return [
    '__pattern__' => [
        'name' => '\w+',
    ],
    '[hello]'     => [
        ':id'   => ['index/hello', ['method' => 'get'], ['id' => '\d+']],
        ':name' => ['index/hello', ['method' => 'post']],
    ],
];*/

use think\Route;

Route::get([
        /*'trial'     => 'stock/trial',
        'free'      => 'stock/free',
        'day'       => 'stock/day',
        'month'     => 'stock/month',
        'vip'       => 'stock/vip',
		'order'     => 'stock/order',*/

        'stock_trial'     => 'stock/trial',
        'stock_free'      => 'stock/free',
        'stock_day'       => 'stock/day',
        'stock_month'     => 'stock/month',
        'stock_vip'       => 'stock/vip',
        'stock_order'     => 'stock/order',

        'future_free'      => 'future/free',
        'future_day'       => 'future/day',
        'future_month'     => 'future/month',
        'future_order'     => 'future/order',

		'login'     => 'common/login',
		'register'  => 'common/register',
        'logout'    => 'common/logout',
        'findpwd'   => 'common/findpwd',
        'hetong'    => 'common/hetong',
        'zcxy'      => 'common/zcxy',
        'download'  => 'common/download',
        'xsrw'      => 'common/xsrw',
        'tuiguang'  => 'common/tuiguang',
        'anquan'    => 'common/anquan',
        'bangzhu'    => 'common/bangzhu',
	]);
Route::post([
        'createOrder'      =>'stock/createOrder',

        'tryFinancingApply'=>'sotck/tryFinancingApply',
		'login'            =>'common/login',
		'register'         =>'common/register',
        'findpwd'          => 'common/findpwd',
	]);

Route::get('cms/:nid$','Article/column');
Route::get('article/:aid$','Article/detial',[],['aid'=>'\d+']);

// Route::domain('m','wap');