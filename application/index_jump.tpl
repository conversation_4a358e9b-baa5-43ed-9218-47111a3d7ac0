<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Index Jump</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    {load href="/static/js/jquery183.js" /}
</head>
<style type="text/css">
body, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, form, fieldset, input, button, textarea, p, th, td {
    margin: 0;
    padding: 0;
}
a,a:hover {
    text-decoration: none;
}
body{
    background: #f5f5f5;
}
.main{
    width: 100%;
    height: 170px;
    background: #fff;
    margin-top: 150px;
    padding: 22px 0 0;
    text-align: center;
}
.main .title{
    font-size: 36px;
    font-weight: 300;
}
.main .cwtit{
    color: #ff6b6b;
}
.main .zqtit{
    color: #46C37B;
}
.main .title i.cw,i.zq{
    width: 33px;
    display: inline-block;
    height: 33px;
    background: url(/static/img/H/cw.png) no-repeat;
    position: relative;
    top: 4px;
    left: 5px;
}
.main .title i.zq{
    background: url(/static/img/H/zq.png) no-repeat;
}
.main .txt{
    margin-top: 10px;
    font-size: 14px;
    color: #333333;
}
.main .txt a{
    color: #5c90d2;
}
.main .bom{
    margin-top: 25px;
}
.main .bom a{
    display: inline-block;
    line-height: 32px;
    width: 108px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
}
.main .bom a i{
    display: inline-block;
    width: 13px;
    height: 12px;
    position: relative;
    top: 1px;
    left: -3px;
}
.main .bom .a1{
    border: 1px solid #34a263;
    background-color: #46c37b;
}
.main .bom .a1 i{
    background: url(/static/img/H/a1.png) no-repeat;
}
.main .bom .a2{
    background-color: #f3b760;
    border: 1px solid #efa231;
}
.main .bom .a2 i{
    background: url(/static/img/H/a2.png) no-repeat -1px 0px;
}
.main .bom .a3{
    background-color: #f5f5f5;
    border: 1px solid #e9e9e9;
    color: #555555;
}
.main .bom .a3 i{
    left: -2px;
    background: url(/static/img/H/a3.png) no-repeat 0px 0px;
}
</style>
<body>
    <div class="main">
        <div class="title">
            {if condition="$code eq 0"}
            <i class="cw"></i>
            <span class='cwtit'>{$msg}</span>
            {else/}
            <i class="zq"></i>
            <span class='zqtit'>{$msg}</span>
            {/if}
        </div>

        <p class="txt">页面将自动<a href="{$url}" id="href"> 跳转</a>，等待时间：<b id="wait">{$wait}</b>秒</p>
    
        <div class="bom">
            <a href="{$url}" class="a1"><i></i>立即跳转</a>
            <a href="javascript:;" class="a2" onclick="c()"><i></i>停止跳转</a>
            <a href="{:url('/index')}" class="a3"><i></i>返回首页</a>
        </div>
    </div>
<script type="text/javascript">
(function(){
    var wait = document.getElementById('wait'),
        href = document.getElementById('href').href;
        interval = setInterval(function(){
        var time = --wait.innerHTML;
        if(time <= 0) {
            location.href = href;
            clearInterval(interval);
        };
    }, 1000);
})();
function c(){
    clearInterval(interval);
}
</script>
</body>
</html>