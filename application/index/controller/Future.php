<?php
namespace app\index\controller;

use app\common\controller\Common;

class Future extends Common{
    protected $category = 2;
    protected function _initialize(){
        parent::_initialize();

        // 当前时间是否可申请
        $couldApplyToday = couldApplyToday();
        $this->assign('couldApplyToday',$couldApplyToday);
        
        // 1按天 2按月 3免息 4体验 8VIP
    }
    public function trial(){
        abort(404,'页面不存在');//暂时关闭

        $try_set = explode( "|", $this->future['try_set']);
        
        $this->assign('try_set',$try_set);
        return view();
    }
    public function free(){
        $money_range = explode( "|", $this->future['free_money_range']);//资金范围
        $free_loss = explode( "|", $this->future['free_loss']);//亏损比例
        $free_set = explode( "|", $this->future['free_set']);//

        $this->assign('moneyRange',$money_range);
        $this->assign('free_loss',$free_loss);
        $this->assign('free_set',$free_set);
        return view();
    }
    public function day(){
        $money_range = explode( "|", $this->future['day_money_range']);//资金范围
        $day_loss = explode( "|", $this->future['day_loss']);//亏损比例
        $use_day = explode( "|", $this->future['day_use_time']);//使用期限
        $rateInfo = $this->future_rate[1];
        // dump( $use_day );
        $i = 0;
        $rateArr = array();
        foreach($rateInfo as $k=>$v){
            $rateArr[$i]['multiple'] = $k;
            $rateArr[$i]['rate'] = $v;
            if($i % 3 == 0){
                $rateArr[$i]['ml0'] = 1;
            }
            $i++;
        }
        
        $this->assign('moneyRange',$money_range);
        $this->assign('day_loss',$day_loss);
        $this->assign('use_day',$use_day);
        $this->assign('rateArr',$rateArr);
        return view();
    }
    public function month(){
        $money_range = explode( "|", $this->future['month_money_range']);//资金范围
        $use_month = explode( "|", $this->future['month_use_time']);//使用期限
        $month_loss = explode( "|", $this->future['month_loss']);//亏损比例
        $rateInfo = $this->future_rate[2];
        // dump( $rateInfo );die;
        $i = 0;
        $rateArr = array();
        foreach($rateInfo as $k=>$v){
            $rateArr[$i]['multiple'] = $k;
            $rateArr[$i]['rate'] = $v;
            if($i % 3 == 0){
                $rateArr[$i]['ml0'] = 1;
            }
            $i++;
        }
        
        $this->assign('moneyRange',$money_range);
        $this->assign('month_loss',$month_loss);
        $this->assign('use_month',$use_month);
        $this->assign('rateArr',$rateArr);
        return view();
    }
    public function vip(){
        abort(404,'页面不存在');//暂时关闭

        $vip_range = explode( "|", $this->future['vip_money_range']);//资金范围
        $use_vip = explode( "|", $this->future['vip_use_time']);//使用期限
        $vip_loss = explode( "|", $this->future['vip_loss']);//亏损比例
        $rateInfo = $this->future_rate[8];
        // dump( $rateInfo );die;
        $i = 0;
        $rateArr = array();
        foreach($rateInfo as $k=>$v){
            $rateArr[$i]['multiple'] = $k;
            $rateArr[$i]['rate'] = $v;
            if($i % 3 == 0){
                $rateArr[$i]['ml0'] = 1;
            }
            $i++;
        }
        
        $this->assign('moneyRange',$vip_range);
        $this->assign('vip_loss',$vip_loss);
        $this->assign('use_vip',$use_vip);
        $this->assign('rateArr',$rateArr);
        return view();
    }





    // 弹框展示确认信息
    public function order(){
        $fee = $warnMoney = $closeMoney = 0;
        $unit = null;
        $data = input('get.',[]);
        $type = input('get.type/d',0);
        $money = input('get.money/d',0);
        $multiple = input('get.multiple/d',0);
        $duration = input('get.duration/d',0);
        $startDate = input('get.startDate/d',0);
        $depositMoney = input('get.depositMoney/d',0);
        $minfo = memberInfo($this->uid);

        switch ($type) {
            case 1:
                $rateInfo = $this->future_rate[$type];
                $rate = (float)$rateInfo[$data['multiple']];
                $fee = $money * ($rate/100) * $duration;
                $unit = '天';
                $loss = explode('|', $this->future['day_loss']);
                $warnMoney = $money + ( $depositMoney * ($loss[0]/100) );//警戒线
                $closeMoney = $money + ( $depositMoney * ($loss[1]/100) );//平仓线
                $borrowMoney = $depositMoney+$money;//总操盘资金
                break;
            case 2:
                $rateInfo = $this->future_rate[$type];
                $rate = (float)$rateInfo[$data['multiple']];
                $fee = $money * ($rate/100) * $duration;
                $unit = '月';
                $loss = explode('|', $this->future['month_loss']);
                $warnMoney = $money + ( $depositMoney * ($loss[0]/100) );
                $closeMoney = $money + ( $depositMoney * ($loss[1]/100) );
                $borrowMoney = $depositMoney+$money;
                break;
            case 3:
                $fee = 0;
                $unit = '天';
                $loss = explode('|', $this->future['free_loss']);
                $warnMoney = $money + ( $depositMoney * ($loss[0]/100) );
                $closeMoney = $money + ( $depositMoney * ($loss[1]/100) );
                $borrowMoney = $depositMoney+$money;
                break;
            case 4:
                $try_set = explode( "|", $this->future['try_set']);
                $fee = 0;
                $unit = '天';
                $warnMoney = 0;
                $closeMoney = 0;
                $borrowMoney = $try_set[3];
                break;
            case 8:
                $rateInfo = $this->future_rate[$type];
                $rate = (float)$rateInfo[$data['multiple']];
                $fee = $money * ($rate/100) * $duration;
                $unit = '月';
                $loss = explode('|', $this->future['vip_loss']);
                $warnMoney = $money + ( $depositMoney * ($loss[0]/100) );
                $closeMoney = $money + ( $depositMoney * ($loss[1]/100) );
                $borrowMoney = $depositMoney+$money;
                break;
            default:
                break;
        }
        // 资金是否足够
        $enough = $diff_money = 0;
        $borrow_fee = $depositMoney+$fee;
        $account_money = $minfo['account_money']/100;//账户余额
        $interest_money = $minfo['interest_money']/100;//管理费余额
        if( $type==3 ){
            if( $depositMoney>$account_money ){
                $diff_money = $depositMoney-$account_money;
            }else{
                $enough = 1;
            }
        }else{
            if( $interest_money>=$fee ){
                if( $depositMoney>$account_money ){
                    $diff_money = $depositMoney-$account_money;
                }else{
                    $enough = 1;
                }
            }else{
                
                $temp_diff_interest = $fee - $interest_money; 
                if( ($depositMoney+$temp_diff_interest)>$account_money ){
                    $diff_money = ($depositMoney+$temp_diff_interest)-$account_money;
                }else{
                    $enough = 1;
                }
            }
        }
        // var_dump($borrow_fee,$depositMoney,$fee,$account_money,$interest_money,$diff_money);die;

        $this->assign('fee',$fee);
        $this->assign('unit',$unit);
        $this->assign('data',$data);
        $this->assign('type',$type);
        $this->assign('money',$money);
        $this->assign('multiple',$multiple);
        $this->assign('duration',$duration);
        $this->assign('startDate',$startDate);
        $this->assign('depositMoney',$depositMoney);
        $this->assign('borrowMoney',$borrowMoney);
        $this->assign('warnMoney',$warnMoney);
        $this->assign('closeMoney',$closeMoney);
        $this->assign('minfo',$minfo);

        $this->assign('enough',$enough);
        $this->assign('diff_money',$diff_money);
        return view();
    }
    public function createOrder(){
        if( !session('?uid') ){
            return [ 'status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $money = input('post.money/d');
            $multiple = input('post.multiple/d');
            $duration = input('post.duration/d');
            $type = input('post.type/d');
            $startDate = input('post.startDate/d');
            $depositMoney = input('post.depositMoney/d');
            if( !$money || !$multiple || !$duration || !$type || !$startDate || !$depositMoney ){
                return [ 'status'=>-2,'message'=>'申请数据错误，请按照申请规则重试！' ];
            }
            if( !in_array($type, array(1,2,3,4,8)) ){
                return [ 'status'=>-3,'message'=>'申请类型出错，请稍后再试！' ];
            }
            if($type == 3){
                $rate = 0;
            }else{
                $rateInfo = $this->future_rate[$type];
                if( empty($rateInfo[$multiple]) ){//不在设置的倍率范围内
                    return [ 'status'=>-4,'message'=>'杠杆倍数选择错误，请稍后再试！' ];
                }
                $rate = (float)$rateInfo[$multiple];
            }

            $stockSql = db("stock_borrow");
            $uidCount = $stockSql
                        ->where(['uid'=>$this->uid,'status'=>0,'category'=>$this->category])
                        ->count('id');
            if($uidCount > 0){
                return ['status'=>-5,'message'=>'您当前已提交过期货配资申请，正在审核中，不能再次申请'];
            }
            //使用中的配资次数限制
            $stockCount = $stockSql
                            ->where(['uid'=>$this->uid,'status'=>2,'category'=>$this->category])
                            ->count('id');
            if($stockCount >= (int)$this->future['stock_count']){
                return ['status'=>-5,'message'=>"您当前使用中的期货配资已达到{$this->future['stock_count']}次限制,结算后可再次申请"];
            }
            switch ($type) {
                case 1:
                    $money_range = explode('|', $this->future['day_money_range']);
                    $use_time = explode('|', $this->future['day_use_time']);
                    // $loss_warn_rate = explode( "|", $this->future['day_loss']);//亏损比例
                    $fee = round($rate*$duration*$money/100,2);
                    break;
                case 2:
                    $money_range = explode('|', $this->future['month_money_range']);
                    $use_time = explode('|', $this->future['month_use_time']);

                    $fee = round($rate*$duration*$money/100,2);
                    break;
                case 3:
                    $money_range = explode('|', $this->future['free_money_range']);
                    $use_time = array( explode('|', $this->future['free_set'])[1] );
                    $fee = 0;
                    // var_dump($use_time);die;
                    break;
                case 8:
                    $money_range = explode('|', $this->future['vip_money_range']);
                    $use_time = explode('|', $this->future['vip_use_time']);

                    $fee = round($rate*$duration*$money/100,2);
                    break;
                default:
                        return ['status'=>-1,'message'=>'申请数据错误，请按照申请规则重试！'];
                    break;
            }
            if( $depositMoney < $money_range[0] ){
                return ['status'=>-6,'message'=>'保证金不得低于'.$money_range[0].'元！'];
            }
            if( $depositMoney > $money_range[1] ){
                return ['status'=>-6,'message'=>'保证金不得高于'.$money_range[1].'元！'];
            }
            if( $depositMoney % $money_range[2] != 0 ){
                return ['status'=>-6,'message'=>'保证金必须是'.$money_range[2].'的整数倍！'];
            }
            if( !in_array($duration, $use_time) ){
                return ['status'=>-7,'message'=>'不符合使用期限规则，请按照申请规则重试！'];
            }
            $minfo = memberInfo($this->uid);
            /*$sumFee = $depositMoney + $fee;//总费用
            $account_money = memberInfo($this->uid)['sum_money'] / 100;
            if( $sumFee > $account_money ){
                return ['status'=>-8,'message'=>'账户资金不足，还需'.($sumFee-$account_money).'元'];
            }*/
            // 资金是否足够
            $enough = $diff_money = 0;
            $account_money = $minfo['account_money']/100;//账户余额
            $interest_money = $minfo['interest_money']/100;//管理费余额
            if( $type==3 ){
                if( $depositMoney>$account_money ){
                    $diff_money = $depositMoney-$account_money;
                    return ['status'=>-8,'message'=>'账户余额不足，还需'.$diff_money.'元'];
                }
            }else{
                if( $interest_money>=$fee ){
                    if( $depositMoney>$account_money ){
                        $diff_money = $depositMoney-$account_money;
                        return ['status'=>-8,'message'=>'账户余额不足，还需'.$diff_money.'元'];
                    }
                }else{
                    
                    $temp_diff_interest = $fee - $interest_money;
                    if( ($depositMoney+$temp_diff_interest)>$account_money ){
                        $diff_money = ($depositMoney+$temp_diff_interest)-$account_money;
                        return ['status'=>-8,'message'=>'账户余额不足，还需'.$diff_money.'元'];
                    }
                }
            }
            // 资金是否足够 end

            $res = $this->applyFinancing($type,$money,$depositMoney,$duration,$multiple,$rate,$startDate);
            if($res){
                return ['status'=>1,'message'=>'配资申请成功，请等待工作人员审核'];
            }else{
                return ['status'=>-9,'message'=>'配资申请失败，请稍后再试！'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    public function tryFinancingApply(){
        if( !session('?uid') ){
            return [ 'status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $couldApplyToday = couldApplyToday();
            if( $couldApplyToday!=1 ){
                return ['status'=>-2,'message'=>'请在交易时间的06:00:00至14:45:00之间申请免费体验'];
            }
            //是否体验过
            $uidCount = db('stock_borrow')
                         ->where('status','neq',-1)
                         ->where(['type'=>4, 'uid'=>$this->uid, 'category'=>$this->category])
                         ->count('id');
            if( $uidCount ){
                return ['status'=>-3,'message'=>'您已体验过，不能再次申请'];
            }
            
            $minfo = memberInfo($this->uid);
            $try_set = explode( "|", $this->future['try_set']);
            // 资金是否足够
            $diff_money = 0;
            $try_deposit_money = $try_set[0];
            $account_money = $minfo['account_money']/100;//账户余额
            $interest_money = $minfo['interest_money']/100;//管理费余额
            if( $try_deposit_money>$account_money ){
                $diff_money = $try_deposit_money-$account_money;
                return ['status'=>-3,'message'=>'账户余额不足，还需'.$diff_money.'元，请先进行充值'];
            }
            // 资金是否足够 end

            $res = $this->applyFinancing(4, $try_set[2], $try_set[0], $try_set[1], 0,0,1);
            if( $res ){
                return ['status'=>1,'message'=>'配资申请成功，请等待工作人员审核'];
            }else{
                return ['status'=>-9,'message'=>'配资申请失败，请稍后再试！'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    /**
     *  添加申请
     *  $type:1按天，2按月 3免息
     *  $money:配资金额
     *  $depositMoney:保证金金额
     *  $borrow_duration:使用期限
     *  $multiple:配资倍数
     *  //$borrow_fee:服务费
     *  $rate:利率（分）
     *  $startDate 开始时间
     *  $category 类型 股票/期货
     */
    protected function applyFinancing($type,$money,$depositMoney,$duration,$multiple,$rate,$startDate){
        // var_dump($type,$money,$depositMoney,$duration,$multiple,$rate,$startDate);die;
        if($startDate == 1){
            $start_time = time();
        }else if($startDate == 2){
            //开始时间为下个交易日早晨8:00
            $holidays =  explode(',',$this->future['holidays']);
            $nowDateTime = new \DateTime();
            $start_time = strtotime( getNextWorkDay($nowDateTime,$holidays)." 06:00" );
        }
        $startCalcDate = date("Y-m-d",$start_time - 24 * 3600);
        switch ($type) {
            case 1:
                $loss = explode( "|", $this->future['day_loss']);//亏损比例
                $borrow_interest = round($rate*$duration*$money/100,2);

                $holidays = explode( ',', $this->future['holidays']);
                $endYmd = getEndDay($startCalcDate,$duration,$holidays);
                $useEndTime = $endYmd." 14:45:00";//结束时间
                $end_time = strtotime($useEndTime);
                break;
            case 2:
                $loss = explode( "|", $this->future['month_loss']);//亏损比例
                $borrow_interest = round($rate*$duration*$money/100,2);
                $endTimeStamp = strtotime("+{$duration} month", strtotime($startCalcDate));
                $endDate = date('Y-m-d', $endTimeStamp) . " 14:45:00";
                $end_time = strtotime($endDate);
                break;
            case 3:
                $loss = explode( "|", $this->future['free_loss']);//亏损比例
                $borrow_interest = 0;

                $holidays = explode( ',', $this->future['holidays']);
                $endYmd = getEndDay($startCalcDate,$duration,$holidays);
                $useEndTime = $endYmd." 14:45:00";//结束时间
                $end_time = strtotime($useEndTime);
                break;
            case 4:
                $holidays = explode( ',', $this->future['holidays']);
                $endYmd = getEndDay($startCalcDate,$duration,$holidays);
                $useEndTime = $endYmd." 14:45:00";//结束时间
                $end_time = strtotime($useEndTime);
                break;
            case 8:
                $loss = explode( "|", $this->future['vip_loss']);//亏损比例
                $borrow_interest = round($rate*$duration*$money/100,2);
                $endTimeStamp = strtotime("+{$duration} month", strtotime($startCalcDate));
                $endDate = date('Y-m-d', $endTimeStamp) . " 14:45:00";
                $end_time = strtotime($endDate);
                break;
            default:
                return false;
                break;
        }

        // 资金单位（分）
        if( $type==4 ){//免费配资单独设置
            $try_set = explode( "|", $this->future['try_set']);
            $depositMoney_fen = $try_set[0]*100;//保证金
            $multiple = 0;
            $borrow_money_fen = $try_set[3]*100;
            $borrow_interest_fen = 0;
            $sumFee_fen = $depositMoney_fen+$borrow_interest_fen;//总费用
            $loss_warn_fen = 0;
            $loss_close_fen = 0;
        }else{
            $depositMoney_fen = $depositMoney*100;//保证金
            $borrow_money_fen = ($money+$depositMoney)*100;//操盘总资金
            $borrow_interest_fen = $borrow_interest*100;//管理费
            $sumFee_fen = ($depositMoney+$borrow_interest)*100;//总费用
            $loss_warn_fen = ($money+$depositMoney*$loss[0]/100)*100;//亏损警告线
            $loss_close_fen = ($money+$depositMoney*$loss[1]/100)*100;//亏损平仓线
        }
        // var_dump($depositMoney_fen,$borrow_money_fen,$borrow_interest_fen,$sumFee_fen,$loss_warn_fen);die;

        // 开启事务
        db()->startTrans();

        $stockData = array();
        $stockData['order_id'] = orderNumber();
        $stockData['uid'] = $this->uid;
        $stockData['type'] = $type;
        $stockData['category'] = $this->category;
        $stockData['status'] = 0;
        $stockData['deposit_money'] = $depositMoney_fen;
        $stockData['multiple'] = $multiple;
        $stockData['borrow_money'] = $borrow_money_fen;
        $stockData['borrow_interest'] = $borrow_interest_fen;//利息，计算全部
        $stockData['repayment_type'] = $type;
        $stockData['borrow_duration'] = $duration;
        $stockData['loss_warn'] = $loss_warn_fen;
        $stockData['loss_close'] = $loss_close_fen;
        $stockData['rate'] = $rate;
        $stockData['add_time'] = $start_time;
        $stockData['end_time'] = $end_time;
        // var_dump($stockData);die;
        $stockRes = db('stock_borrow')->insertGetId($stockData);
        if($stockRes){
            $info = '您申请了期货配资，申请总费用'.($sumFee_fen/100).'元';
            $moneylog = updateMoneyLogWithInterest($this->uid,$sumFee_fen,$depositMoney_fen,$borrow_interest_fen,$info,7,1,$stockRes);
            // var_dump($moneylog);die;
        }else{
            db()->rollback();
            return '申请期货配资失败，请稍后再试！008';
        }

        if($stockRes && $moneylog){
            // 自動創建子帳戶
            $subAccount = createSubAccount($this->uid, $stockRes, $this->category);
            if (!$subAccount) {
                db()->rollback();
                return '申请期货配资失败，子账户创建失败，请稍后再试！';
            }

            $peiziTypeTxt = str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $type);
            $adminSms = "会员".$this->uname."申请了".$peiziTypeTxt."方案，申请总费用".($sumFee_fen/100)."元，已自动创建子账户：".$subAccount['name'];
            sendsms_to_admin($adminSms);

            addTip($this->uid,6);
            db()->commit();
            return true;
        }else{
            db()->rollback();
            return '申请期货配资失败，请稍后再试！009';
        }
        
    }
    // 提取盈利
    public function drawprofitApply(){
        if( !session('?uid') ){
            return ['status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $borrow_id = input('post.id/d',0);
            $money = input('post.money/d',0);
            if( !$borrow_id || !$money ){
                return ['status'=>-2,'message'=>'请求数据有误！请按照平台规则重试' ];
            }
            //提取收益配置
            $sy_set = explode("|", $this->future['sy_set']);//使用期限
            if( $money<$sy_set[0] ){
                return ['status'=>-3,'message'=>'提盈金额不得小于'.$sy_set[0].'元'];
            }
            $stockSql = db("stock_borrow");
            $stockInfo = $stockSql
                         ->where("id",$borrow_id)
                         ->field('uid,status,borrow_money,type,category')
                         ->find();
            if( $stockInfo['status']!=2 ){
                return ['status'=>-4,'message'=>'该配资不在使用中，不能申请提取盈利'];
            }

            $drawprofitSql = db("stock_drawprofit");
            $drawprofit_in = $drawprofitSql
                                 ->where(['uid'=>$this->uid,'borrow_id'=>$borrow_id,'status'=>0,'category'=>$this->category])
                                 ->field(true)
                                 ->find();
            if($drawprofit_in){
                return ['status'=>-5,'message'=>'您当前已申请过提取盈利，正在审核中，不能再次申请'];
            }
            if($sy_set[2] > 0){
                $drawprofit_ok = $drawprofitSql->field(true)
                                    ->where(['uid'=>$this->uid,'borrow_id'=>$borrow_id,'status'=>1])
                                    ->order('id desc')
                                    ->find();
                if($drawprofit_ok){
                    $nowData = date("Ymd");
                    $dataCount = $nowData-date("Ymd",$drawprofit_ok['verify_time']);
                    if($dataCount < $sy_set[2]){
                        return ['status'=>-6,'message'=>'距离上次提盈时间未超过'.$sy_set[2].'天，不能提取盈利'];
                    }
                }
            }
            //添加申请
            $add = array(
                    'uid'=>$this->uid,
                    'borrow_id'=>$borrow_id,
                    'money'=>$money*100,
                    'borrow_money'=>$stockInfo['borrow_money'],
                    'add_time'=>time(),
                    'category'=>$this->category
                );
            $res = $drawprofitSql->insert($add);
            if( $res ){
                addTip($this->uid,7,$add['money']);

                $adminSms = "会员".$this->uname."申请了提盈方案";
                sendsms_to_admin($adminSms);

                return ['status'=>1,'message'=>'提盈申请成功，请等待工作人员审核'];
            }else{
                return ['status'=>-1,'message'=>'提盈申请失败，请稍后再试'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    // 补亏申请
    public function fillApply(){
        if( !session('?uid') ){
            return ['status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $borrow_id = input('post.id/d',0);
            $money = input('post.money/s',0);
            if( !$borrow_id || !$money ){
                return ['status'=>-1,'message'=>'数据解析错误，请按平台规则重试！' ];
            }
            $fill_set = explode('|',$this->future['fill_set']);
            if( $money<$fill_set[0] ){
                return ['status'=>-1,'message'=>'补亏金额不能小于'.$fill_set[0].'元' ];
            }
            if( $money%$fill_set[1] != 0){
                return ['status'=>-1,'message'=>'补亏金额必须是'.$fill_set[1].'的整倍数' ];
            }
            $stockSql = db("stock_borrow");
            $stockInfo = $stockSql
                         ->where("id",$borrow_id)
                         ->field('uid,status,borrow_money,type,category')
                         ->find();
            if( $stockInfo['status']!=2 ){
                return ['status'=>-2,'message'=>'该配资不在使用中，不能申请提取盈利'];
            }
            $addMoneySql = db("stock_addmoney");
            //当前是否正在申请
            $addMoneyState = $addMoneySql
                                 ->where(['borrow_id'=>$borrow_id,'uid'=>$this->uid,'status'=>0,'category'=>$this->category])
                                 ->count('id');
            if($addMoneyState){
                return ['status'=>-3,'message'=>'您已申请补充亏损，正在审核中，不能重复申请。'];
            }
            //余额
            $account_money = (memberInfo($this->uid)['account_money']) / 100;
            if( $account_money<$money ){
                return ['status'=>-4,'message'=>'余额不足，请先充值'];
            }

            // 开启事务
            db()->startTrans();
            //冻结金额，增加记录
            // 资金单位（分）
            $money = $money*100;
            $info = '申请补充亏损，冻结金额';
            $member_moneylog = memberMoneyLog( $this->uid, $money, $info, 11 );
            $add = array(
                    'uid'=>$this->uid,
                    'borrow_id'=>$borrow_id,
                    'money'=>$money,
                    'add_time'=>time(),
                    'category'=>$this->category
                );
            $res = $addMoneySql->insert($add);
            if( $member_moneylog && $res ){
                addTip($this->uid,8,$add['money']); 

                $adminSms = "会员".$this->uname."申请了补亏方案";
                sendsms_to_admin($adminSms);

                db()->commit();
                return ['status'=>1,'message'=>'申请成功，请等待工作人员审核'];
            }else{
                db()->rollback();
                return ['status'=>-5,'message'=>'申请失败，请稍后再试'];
            }

        }else{
            abort(404,'页面不存在');
        }
    }
    public function addfinancingApply(){
        if( !session('?uid') ){
            return ['status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $borrow_id = input('post.id/d',0);
            $deposit_money = input('post.money/d',0);
            if( !$borrow_id || !$deposit_money ){
                return ['status'=>-1,'message'=>'数据解析错误，请按平台规则重试！' ];
            }

            $now = time();
            $closeTime = mktime(14,45);
            if($now > $closeTime){
                return ['status'=>-2,'message'=>'为了避免计息，14:45以后，不能申请，请明天再来' ];
            }
            $kd_set = explode( "|", $this->future['kd_set']);
            if($deposit_money < $kd_set[0]){
                return ['status'=>-3,'message'=>'扩大金额不得小于'.$kd_set[0].'元' ];
            }
            if($deposit_money > $kd_set[1]){
                return ['status'=>-3,'message'=>'扩大金额不得大于'.$kd_set[1].'元' ];
            }
            if($deposit_money % $kd_set[2] != 0 ){
                return ['status'=>-3,'message'=>'扩大金额必须是'.$kd_set[2].'的整倍数' ];
            }
            $stock_addfinancing = db("stock_addfinancing");
            $had = $stock_addfinancing->where(['borrow_id'=>$borrow_id, 'status'=>0,'uid'=>$this->uid,'category'=>$this->category])->count('id');
            if( $had ){
                return ['status'=>-3,'message'=>'您当前已有扩大配资申请，不能再次申请' ];
            }
            $binfo = model('admin/Stock')->getStock($borrow_id);
            if( !$binfo ){
                return ['status'=>-4,'message'=>'当前申请的配资不存在，不能扩大配资' ];
            }
            if($binfo['status']!=2){
                return ['status'=>-4,'message'=>'您当前申请的配资不在使用中，不能扩大配资' ];
            }
            if( $now < $binfo['add_time'] ){
                return ['status'=>-4,'message'=>'只能在配资期限内扩大配资' ];
            }
            if( $binfo['type']==3 ){
                return ['status'=>-4,'message'=>'免息配资不能扩大配资' ];
            }
            if( $binfo['type']==4 ){
                return ['status'=>-4,'message'=>'免费体验不能扩大配资' ];
            }

            // 申请扩大校验当前配资是否已到期，是否有未处理的延期记录
            if( $now > $binfo['end_time'] ){
                return ['status'=>-4,'message'=>'该配资期限已到，如需扩大请先延期配资' ];
            }
            $renewalSearch = array( 'borrow_id'=>$borrow_id, 'status'=>0 );
            $renewalCount = db("stock_renewal")->where($renewalSearch)->count('id');
            if( $renewalCount ){
                return ['status'=>-5,'message'=>'您当前有未处理的续期申请，请等待管理员审核' ];
            }
            // 利息 单位（分）
            $borrow_fee = calculate_rate($borrow_id,$deposit_money);
            $deposit_money = $deposit_money * 100;
            $sumFee = $deposit_money+$borrow_fee;//总费用
            $minfo = memberInfo($this->uid);
            // 检查资金是否充足
            if( $minfo['interest_money']>=$borrow_fee ){
                if( $deposit_money>$minfo['account_money'] ){
                    return ['status'=>-61,'message'=>"帐户金额不足，您申请追加配资所需费用为".($sumFee/100)."元"];
                }
            }else{
                $temp_diff_interest = $borrow_fee - $minfo['interest_money'];
                if( ($deposit_money+$temp_diff_interest) > $minfo['account_money'] ){
                    return ['status'=>-62,'message'=>"帐户金额不足，您申请追加配资所需费用为".($sumFee/100)."元"];
                }
            }

            // 开启事务
            db()->startTrans();
            $add = array(
                    'uid'=>$this->uid,
                    'borrow_id'=>$borrow_id,
                    'money'=>$deposit_money,
                    'borrow_fee'=>$borrow_fee,
                    'last_deposit_money'=>$binfo['deposit_money'],
                    'last_borrow_money'=>$binfo['borrow_money'],
                    'add_time'=>time(),
                    'category'=>$this->category
                );
            $res = $stock_addfinancing->insertGetId($add);

            $info = '您申请了扩大配资，申请总费用'.($sumFee/100).'元';
            $member_moneylog = updateMoneyLogWithInterest($this->uid,$sumFee,$deposit_money,$borrow_fee,$info,7,2,$res);

            if( $res && $member_moneylog ){
                addTip($this->uid,9,$sumFee); 

                $adminSms = "会员".$this->uname."申请了扩大方案";
                sendsms_to_admin($adminSms);

                db()->commit();
                return ['status'=>1,'message'=>'申请成功，请等待工作人员审核'];
            }else{
                db()->rollback();
                return ['status'=>-1,'message'=>'申请失败，请稍后再试'];
            }

        }else{
            abort(404,'页面不存在');
        }
    }
    public function renewalApply(){
        if( !session('?uid') ){
            return ['status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $borrow_id = input('post.id/d',0);
            $duration = input('post.duration/d',0);
            if( !$borrow_id || !$duration ){
                return ['status'=>-1,'message'=>'数据解析错误，请按平台规则重试！'];;
            }
            $stock_renewal = db("stock_renewal");
            $renewalCount = $stock_renewal->where(['borrow_id'=>$borrow_id, 'status'=>0,'uid'=>$this->uid,'category'=>$this->category])->count('id');
            if( $renewalCount ){
                return ['status'=>-2,'message'=>'您当前已有增加延期申请，不能再次申请' ];
            }
            $binfo = model('admin/Stock')->getStock($borrow_id);
            if( !$binfo ){
                return ['status'=>-3,'message'=>'当前申请的配资不存在，不能申请续期' ];
            }
            if( $binfo['status']!=2 ){
                return ['status'=>-3,'message'=>'您当前申请的配资不在使用中，不能申请续期' ];
            }
            if( $binfo['type']==3 ){
                return ['status'=>-4,'message'=>'免息配资不能申请续期' ];
            }
            if( $binfo['type']==4 ){
                return ['status'=>-4,'message'=>'免费体验不能申请续期' ];
            }

            // 如有未处理的扩大配资记录，禁止申请延期
            $expendSearch = array( 'borrow_id'=>$borrow_id, 'status' =>0, 'category'=>$this->category);
            $expendCount = db('stock_addfinancing')->where($expendSearch)->count('id');
            if( $expendCount ){
                return ['status'=>-5,'message'=>'您当前有未处理的扩大配资申请，请等待管理员审核' ];
            }
            // 计算利息
            $borrow_fee = calculate_renewal($borrow_id,$duration);//单位（分）

            $minfo = memberInfo($this->uid);
            // 检查资金是否充足
            if( $borrow_fee>$minfo['interest_money'] ){
                $temp_diff_interest = $borrow_fee - $minfo['interest_money'];
                if( $temp_diff_interest > $minfo['account_money'] ){
                    return ['status'=>-5,'message'=>"帐户金额不足，您申请延期所需费用为".($borrow_fee/100)."元"];
                }
            }

            // 开启事务
            db()->startTrans();
            $add = array(
                    'uid'=>$this->uid,
                    'borrow_id'=>$borrow_id,
                    'borrow_fee'=>$borrow_fee,
                    'borrow_duration'=>$duration,
                    'add_time'=>time(),
                    'category'=>$this->category
                );
            $res = $stock_renewal->insertGetId($add);

            $deposit_money = 0;
            $info = '申请配资续期，扣除利息'.($borrow_fee/100).'元';
            $member_moneylog = updateMoneyLogWithInterest($this->uid,$borrow_fee,$deposit_money,$borrow_fee,$info,7,3,$res);
            
            if( $res && $member_moneylog ){
                addTip($this->uid,10,$borrow_fee); 

                $adminSms = "会员".$this->uname."申请了延期方案";
                sendsms_to_admin($adminSms);

                db()->commit();
                return ['status'=>1,'message'=>'申请成功，请等待工作人员审核'];
            }else{
                db()->rollback();
                return ['status'=>-1,'message'=>'申请失败，请稍后再试'];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    public function stopfinancingApply(){
        if( !session('?uid') ){
            return ['status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $borrow_id = input('post.id/d',0);
            if( !$borrow_id ){
                return ['status'=>-1,'message'=>'数据解析错误，请按平台规则重试！'];
            }
            $stock_stopfinancing = db("stock_stopfinancing");
            $stopfinancingCount = $stock_stopfinancing
                                 ->where(['borrow_id'=>$borrow_id, 'status'=>0,'uid'=>$this->uid,'category'=>$this->category])
                                 ->count('id');
            if( $stopfinancingCount ){
                return ['status'=>-2,'message'=>'您当前已有申请提前终止，不能再次申请'];;
            }
            $binfo = model('admin/Stock')->getStock($borrow_id);
            if( !$binfo ){
                return ['status'=>-3,'message'=>'当前申请的配资不存在，不能申请终止' ];
            }
            if( $binfo['status']!=2 ){
                return ['status'=>-3,'message'=>'您当前申请的配资不在使用中，不能申请终止' ];
            }

            $now = time();
            if( $binfo['type']!=4 && $binfo['type']!=3 ){
                if( $binfo['end_time']<$now ){
                    $timediff = $now - $binfo['end_time'];
                    $days = ceil(round( $timediff/86400, 2 ));
                    
                    return ['status'=>-4,'message'=>'您当前申请的配资已经逾期，需要先申请延期（'.$days.'）天再申请终止' ];
                }
            }

            $add = array(
                    'uid'=>$this->uid,
                    'borrow_id'=>$borrow_id,
                    'add_time'=>$now,
                    'category'=>$this->category
                );
            $res = $stock_stopfinancing->insert($add);
            if( $res ){
                addTip($this->uid,11); 

                $adminSms = "会员".$this->uname."申请了终止方案";
                sendsms_to_admin($adminSms);

                return ['status'=>1,'message'=>'申请成功，请等待工作人员审核'];
            }else{
                return ['status'=>-1,'message'=>'申请失败，请稍后再试'];
            }          
        }else{
            abort(404,'页面不存在');
        }
    }
    // 计算扩大配资利息
    public function calculate_rate(){
        if( !session('?uid') ){
            return ['status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $money = input('post.money/d',0);
            if( !$id || !$money ){
                return ['status'=>-1,'message'=>'数据解析错误，请按平台规则重试！','fee'=>0];;
            }
            $res = calculate_rate($id,$money);//单位（分）
            $fee = $res / 100;
            if( $res ){
                return ['status'=>1,'fee'=>$fee];
            }else{
                return ['status'=>-1,'fee'=>0];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    public function calculate_renewal(){
        if( !session('?uid') ){
            return ['status'=>-1,'message'=>'请先进行登录！' ];
        }
        $request = request();
        if( $request->isAjax() ){
            $borrow_id = input('post.id/d',0);
            $duration = input('post.duration/d',0);
            if( !$borrow_id || !$duration ){
                return ['status'=>-1,'message'=>'数据解析错误，请按平台规则重试！','fee'=>0];;
            }
            $res = calculate_renewal($borrow_id,$duration);//单位（分）
            $fee = $res / 100;
            if( $res ){
                return ['status'=>1,'fee'=>$fee];
            }else{
                return ['status'=>-1,'fee'=>0];
            }
        }else{
            abort(404,'页面不存在');
        }
    }




}