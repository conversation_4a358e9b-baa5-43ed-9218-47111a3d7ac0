<?php 
namespace app\index\controller;

use think\Controller;
class Error extends controller{
    protected function _initialize(){
        parent::_initialize();

        $this->assign('jump_url_404' , url('/') );
    }
    
    public function index(){
        // echo '空控制器 index controller Error.php';

        abort(404,'当前地址不存在，请在网站中手动点击链接');
    }

    public function _empty(){
        // echo '空操作！index controller Error.php _empty()';

        abort(404,'当前地址不存在，请在网站中手动点击链接');
    }

}