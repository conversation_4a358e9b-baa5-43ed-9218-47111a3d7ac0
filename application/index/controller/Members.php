<?php
namespace app\index\controller;

use app\common\controller\Member;

class Members extends Member{
    protected $invite_link = null;//推广链接
    protected $invite_qrcode = null;//二维码推广链接
    protected function _initialize(){
        parent::_initialize();

        $this->assign('empty_data','<td colspan="10" style="border-bottom:0px"><div style="text-align:center;padding:30px 0 20px;">暂无记录</div></td>');
        $this->invite_link = request()->domain().url('common/register',['invite'=>$this->uname]);
        $this->assign('R',request()->action());//当前所在菜单

        $this->invite_qrcode = request()->domain().'/wap.php/common/register/invite/'.$this->uname.'.html';

        // 新增期货配资
        $category = input('param.c/d',1);
        $this->assign('category',$category);
        $this->stock = $category==1? $this->stock: $this->future;
    }

    public function index(){
        $bank = db('member_bank')->where('uid',$this->uid)->field('id')->select();

        $qiandao = qiandao($this->uid);

        $this->assign('bank',$bank);
        $this->assign('qiandao',$qiandao);
        return view();
    }

    public function real(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $realname_set = get_realname_param();
            if( $realname_set['status']==1 ){
                if( empty($data['name']) ){
                    return ['status'=>-1,'message'=>'填写用户姓名！'];
                }
                if( empty($data['number']) || (strlen($data['number'])<15||strlen($data['number'])>18) ){
                    return ['status'=>-2,'message'=>'身份证号码填写错误！'];
                }
                $minfo_mod = db('member_info');
                $had = $minfo_mod->where('id_card',$data['number'])->find();
                if( $had ){
                    return ['status'=>-3,'message'=>'身份证号码已使用，不能重复使用！'];
                }
                $param = array(
                        $realname_set['xingming'] => $data['name'],
                        $realname_set['zhengjian'] => $data['number'],
                        'appkey' => $realname_set['mima']
                    );
                $res = realname_request($realname_set['dizhi'],$param);
                if( $res ){
                    $save = array(
                            'real_name'=>$data['name'],
                            'id_status'=>1,
                            'id_card'=>$data['number'],
                            'sex'=>$data['sex'],
                            'province'=>$data['province']
                        );
                    $save_res = $minfo_mod->where('uid',$this->uid)->update($save);
                    if($save_res){
                        $xsrw = myXsrw( $this->uid , config('MY_XSRW')['SHIMING_JL'] );
                        file_put_contents('./xsrw.txt',
                                date("Y-m-d H:i ")."uid=".$this->uid."&rw_id=".config('MY_XSRW')['SHIMING_JL']."&xsrw:".json_encode($xsrw)."\r\n",
                                FILE_APPEND);

                        return ['status'=>1,'message'=>'认证成功'];
                    }else{
                        return ['status'=>-6,'message'=>'认证信息保存失败，请稍后再试！'];
                    }
                }else{
                    return ['status'=>-5,'message'=>'认证失败，请填写正确身份信息！'];
                }

            }else{
                return ['status'=>-1,'message'=>'自动实名认证未开启，请联系工作人员进行手动审核'];
            }

        }else{
            $minfo = $this->minfo;
            $minfo['realname_display'] = mb_substr($minfo['real_name'], 0,1).'***';
            $minfo['idcard_display'] = substr_replace($minfo['id_card'],'*****',9,5);

            $this->assign('minfo',$minfo);
    	    return view();
        }
    }

    public function bank(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $number = input('post.number/s','');
            $mod = db('member_bank');
            $band_bank = (int)$this->global['band_bank'];
            $had = $mod->where('number',$number)->find();
            if( $had ){
                return ['status'=>-1,'message'=>'该银行卡已被使用！'];
            }
            $count = $mod->where('uid',$this->uid)->count('id');
            if( $count>=$band_bank ){
                return ['status'=>-2,'message'=>'银行卡绑定已上限，最多绑定'.$band_bank.'张！'];
            }
            if( empty($data['otherbank']) ){
                $bank_name = config('bank')[$data['bankCode']];
            }else{
                $bank_name = $data['otherbank'];
            }
            $add = array(
                    'uid'=>$this->uid,
                    'bank_name'=>$bank_name,
                    'bank_code'=>$data['bankCode'],
                    'bank_address'=>$data['address'],
                    'number'=>$number,
                    'province'=>$data['province'],
                    'city'=>$data['city'],
                    'add_time'=>time(),
                    'add_ip'=>$request->ip(),
                );

            $res = $mod->insert($add);
            if($res){
                $xsrw = myXsrw( $this->uid , config('MY_XSRW')['BDYHK_JL'] );
                        file_put_contents('./xsrw.txt',
                                date("Y-m-d H:i ")."uid=".$this->uid."&rw_id=".config('MY_XSRW')['BDYHK_JL']."&xsrw:".json_encode($xsrw)."\r\n",
                                FILE_APPEND);

                return ['status'=>1,'message'=>'添加成功'];
            }else{
                return ['status'=>-3,'message'=>'添加失败！请稍后再试'];
            }
        }else{
            $data = db('member_bank')->where('uid',$this->uid)->field(true)->select();

            foreach ($data as &$v) {
                $v['number_display'] = formatBankCardNo($v['number']);
            }
            unset($v);

            $this->assign('data',$data);
            $this->assign('id_status',$this->minfo['id_status']);
            $this->assign('bank_list',config('bank'));
            return view();
        }
    }

    public function pass(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $minfo = $this->minfo;
            if( !v_pass($data['oldPwd'],$minfo['user_pass']) ){
                return ['status'=>-1,'message'=>'原密码不正确！请重新输入'];
            }
            if( $data['oldPwd']==$data['newPwd'] ){
                return ['status'=>-2,'message'=>'新密码不能和原密码一致！'];
            }
            $save = array(
                    'user_pass'=>set_pass($data['newPwd'])
                );
            $res = db('members')->where('id',$this->uid)->update($save);
            if( $res ){
                session('uid',null);
                session('uname',null);
                return ['status'=>1,'message'=>'修改成功，请返回重新登录'];
            }else{
                return ['status'=>-1,'message'=>'修改失败！请稍后再试'];
            }
        }else{

            return view();
        }
    }

    public function paypass(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.',[]);
            $minfo = $this->minfo;
            $action = input('post.action/s','');
            switch ($action) {
                case 'set':
                    if( captcha_check($data['vcode']) ){
                        if( $data['pin']!=$data['pinConfirm'] ){
                            return ['status'=>-1,'message'=>'两次密码输入不一致，请重新输入'];
                        }
                        $set = array('pin_pass'=>set_pass($data['pin']));
                        $res = db('members')->where('id',$this->uid)->update($set);
                    }else{
                        return ['status'=>-3,'message'=>'图像验证码输入错误'];
                    }
                    break;
                case 'update':
                    if( verify_code($minfo['phone'],$data['scode']) ){
                        if( $data['pin']!=$data['pinConfirm'] ){
                            return ['status'=>-1,'message'=>'两次密码输入不一致，请重新输入'];
                        }

                        $mod = db('members');
                        $oldPwd = $mod->where('id', $this->uid)->value('pin_pass');
                        if( v_pass($data['pin'], $oldPwd) ){
                            return ['status'=>-1,'message'=>'修改失败！密码不能和上次支付密码一样'];
                        }
                        $save = array('pin_pass'=>set_pass($data['pin']));
                        $res = $mod->where('id',$this->uid)->update($save);
                    }else{
                        return ['status'=>-3,'message'=>'短信验证码输入错误'];
                    }
                    break;
                default:
                    break;
            }
            if( $res ){
                return ['status'=>1,'message'=>'操作成功'];
            }else{
                return ['status'=>-1,'message'=>'操作失败！请稍后再试'];
            }
        }else{
            $minfo = $this->minfo;

            $this->assign('minfo',$minfo);
            $this->assign('id_status',$minfo['id_status']);
            return view();
        }
    }

    public function invite(){
        $this->assign('link',$this->invite_link);
        return view();
    }
    public function inviteUser(){
        $w = array('m1.recommend_id'=>$this->uid);
        $timestr = input('get.time/s','');
        if( $timestr ){
            $time = explode( ' - ',input('get.time') );
            $w['m1.reg_time'] = array( 'between time',[$time[0],$time[1]] );
        }

        $data = db('members')->alias('m1')
                ->join('member_info m2','m1.id = m2.uid','left')
                ->field('m1.id,m1.user_name,m1.reg_time,m1.last_log_time,m2.real_name')
                ->order('id DESC')
                ->where($w)
                ->paginate(10, false, ['query'=>request()->param()]);

        $page = $data->render();
        $data = $data->all();
        foreach ($data as &$v) {
            $v['user_name_display'] = substr_replace($v['user_name'],'****',3,4);
            $v['real_name_display'] = empty($v['real_name']) ? '未实名': mb_substr($v['real_name'], 0,1).'***';
        }
        unset($v);

        $this->assign('timestr',$timestr);
        $this->assign('data',$data);
        $this->assign('page',$page);
        return view();
    }

    public function qrcode(){
        header('Content-Type: image/png');
        ob_clean();
        import('phpqrcode', EXTEND_PATH, '.php');
        $qrcode = new \QRcode();
        $value = $this->invite_qrcode;//二维码内容
        $errorCorrectionLevel = 'Q';//容错级别
        $matrixPointSize = 4;
        $res = $qrcode::png($value, false , $errorCorrectionLevel, $matrixPointSize, 2);
        die;
    }

    public function mrecord(){
        $w = array('uid'=>$this->uid);
        $timestr = input('get.time/s','');
        if( $timestr ){
            $timestr = input('get.time/s');
            $time = explode( ' - ',input('get.time') );
            $w['create_time'] = array( 'between time',[$time[0],$time[1]] );
        }
        $data = db('member_moneylog')
                ->where($w)
                ->order('id DESC')
                ->paginate(10, false, ['query'=>request()->param()]);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        $this->assign('timestr',$timestr);
        $this->assign('trade_type',config('trade_type'));
        return view();
    }

    public function withdraw(){
        $request = request();
        $withdraw_set = explode('|', $this->global['withdraw_set']);
        if( $request->isAjax() ){
            $money = input('post.money/d',0);
            $bank_id = input('post.bank/d',0);
            $pin = input('post.pin/s','');
            $pin_pass = $this->minfo['pin_pass'];
            if( !v_pass($pin,$pin_pass) ){
                return ['status'=>-1,'message'=>'提现失败！支付密码不正确'];
            }
            if( !$money || !$bank_id ){
                return ['status'=>-1,'message'=>'操作失败！请按平台规则重试'];
            }
            if( $money < $withdraw_set[0] ){
                return ['status'=>-2,'message'=>'提现失败！提现金额不能小于'.$withdraw_set[0].'元'];
            }
            if( $money > ($this->minfo['account_money']/100) ){
                return ['status'=>-2,'message'=>'提现失败！提现金额不能大于可提款金额'];
            }
            $mod = db('member_withdraw');
            $had = $mod->where(['uid'=>$this->uid,'status'=>0])->value('id');
            if( $had ){
                return ['status'=>-3,'message'=>'您当前有未审核提现申请，请等待工作人员审核'];
            }
            $stime = strtotime(date('Y-m-d'));
            $etime = strtotime(date('Y-m-d').' 23:59:59');
            $count = $mod
                    ->where('uid',$this->uid)
                    ->whereTime('add_time', 'between', [$stime, $etime])
                    ->count('id');
            if( $count >= $withdraw_set[1] ){
                return ['status'=>-5,'message'=>'提现失败！每天最多申请提现'.$withdraw_set[1].'次'];
            }
            // 开启事务
            db()->startTrans();
            // 资金记录
            $money = $money * 100;//单位（分）
            $mlog = memberMoneyLog($this->uid,$money,'申请提现成功，资金已冻结，请等待工作人员审核',3);

            $add = array(
                    'uid'=>$this->uid,
                    'user_name'=>$this->uname,
                    'money'=>$money,
                    'bank_id'=>$bank_id,
                    'add_time'=>time(),
                    'add_ip'=>$request->ip()
                );
            $res = $mod->insert($add);
            if( $mlog && $res ){
                addTip($this->uid,3);

                $adminSms = "会员".$this->uname."申请了提现";
                sendsms_to_admin($adminSms);

                db()->commit();
                return ['status'=>1,'message'=>'申请提现成功，请等待工作人员审核'];
            }else{
                db()->rollback();
                return ['status'=>-6,'message'=>'提现失败！请稍后再试'];
            }
        }else{
            $bank = db('member_bank')->where('uid',$this->uid)->field(true)->select();

            foreach ($bank as &$v) {
                $v['number_display'] = substr($v['number'], -5);
            }
            unset($v);

            $is_set_pass = $this->minfo['pin_pass'] ? 1 : 0;//设置支付密码

            $this->assign('bank',$bank);
            $this->assign('is_set_pass',$is_set_pass);
            $this->assign('withdraw_set',$withdraw_set);
            return view();
        }
    }

    public function recharge(){
        $request = request();
        if( $request->isAjax() ){
            $way = input('post.way/s','');
            $money = round(input('post.money/s',0),2);
            $account = input('post.account/s','');
            $tran_id = input('post.tran_id/s','');
            $mod = db('member_recharge');
            $had = $mod->where('status',0)->where('uid',$this->uid)->count('id');
            if( $had ){
                return ['status'=>-1,'message'=>'当前有未审核的充值订单，请等待工作人员处理'];
            }
            if( !$way ){
                return ['status'=>-2,'message'=>'提交失败，请按照平台规则重试！'];
            }
            if( $money<=0 ){
                return ['status'=>-3,'message'=>'充值金额不能小于0'];
            }
            /*if( !$account ){
                return ['status'=>-4,'message'=>'提交失败！付款账户信息不能为空'];
            }*/
            $add = array(
                'uid'=>$this->uid,
                'user_name'=>$this->uname,
                'money'=>$money*100,//单位（分）
                'way'=>$way,
                'pay_account'=>$account,
                'tran_id'=>$tran_id,
                'add_time'=>time(),
                'add_ip'=>$request->ip(),
            );
            $res = $mod->insert($add);
            if( $res ){
                addTip($this->uid,1,$money*100);
                return ['status'=>1,'message'=>'充值订单提交成功'];
            }else{
                return ['status'=>-5,'message'=>'充值订单提交失败，请稍后再试'];
            }

        }else{
            $where = [
                'way' => 'offline',
                'status' => 1,
            ];

            $offline = db('recharge_set')->where($where)->field(true)->select();//线下银行卡
//            $offline = db('recharge_set')->where('way','offline')->field(true)->find();//线下银行卡
            $weixin = db('recharge_set')->where('way','wx')->field(true)->find();//微信扫码
            $zhifubao = db('recharge_set')->where('way','zfb')->field(true)->find();//支付宝扫码
            if($offline){
                $offline_status = $offline[0]['status'];
            }else{
                $offline_status = 0;
            }
            $uid = session('uid');
            $userInfo = db('members')->where('id',$uid)->find();//线下银行卡
            if ($userInfo['is_blacklist'] == 1)
            {
                $offline_status = 0;
            }

            $this->assign('offline',$offline);
            $this->assign('offline_status',$offline_status);
            $this->assign('offline',$offline);
            $this->assign('weixin',$weixin);
            $this->assign('zhifubao',$zhifubao);
            return view();
        }
    }

    // 配资
    public function stock(){
        $model = model('admin/Stock');
        $data = $model::stockListByUid($this->uid,5);

        $renewal= db('stock_renewal');
        $addfinancing= db('stock_addfinancing');
        foreach ($data as &$v) {
            switch ($v['status']) {
                case -1:
                    $v['status_class'] = 'shibai';
                    break;
                case 2:
                    $v['status_class'] = 'caopanzhong';
                    break;
                case 3:
                    $v['status_class'] = 'yijieshu';
                    break;
                default:
                    $v['status_class'] = 'daishenhe';
                    break;
            }

            $w = array('borrow_id'=>$v['id'], 'uid'=>$v['uid'], 'status'=>1);
            $sum1 = $addfinancing->where($w)->sum('borrow_fee');
            $sum2 = $renewal->where($w)->sum('borrow_fee');

            switch ($v['type']) {
                case 1://day
                case 2://month
                case 8://vip
                    $v['totalinterest'] = $v['borrow_interest']+$sum1+$sum2;
                    break;
                case 3:
                case 4:
                    $v['totalinterest'] = 0;
                    break;
                default:
                    break;
            }
        }
        unset($v);

        $this->assign('data',$data);
        $this->assign('page', $data->render());
        return view();
    }
    public function drawprofit(){
        $id = input('param.id/d',0);
        $sy_set = explode('|',$this->stock['sy_set']);

        $this->assign('id',$id);
        $this->assign('sy_set',$sy_set);
        return view();
    }
    public function stock_account(){
        $id = input('param.id/d',0);
        $data = model('admin/Stock')->getStock($id);

        $this->assign('id',$id);
        $this->assign('data',$data);
        return view();
    }
    public function fill(){
        $id = input('param.id/d',0);
        $fill_set = explode('|',$this->stock['fill_set']);

        // $this->assign('data',$data);
        $this->assign('id',$id);
        $this->assign('minfo',$this->minfo);
        $this->assign('fill_set',$fill_set);
        return view();
    }
    public function addfinancing(){
        $id = input('param.id/d',0);

        $this->assign('id',$id);
        $this->assign('kd_set', explode( "|", $this->stock['kd_set']) );
        return view();
    }
    public function renewal(){
        $id = input('param.id/d',0);
        $type = input('param.type/d',0);

        $unit = '';
        $use_time = array();//使用期限
        switch ($type) {
            case 1:
                $unit = '天';
                // $use_time = explode( "|", $this->stock['day_use_time']);
                $use_time = range(1,30);
                break;
            case 2:
                $unit = '月';
                $use_time = explode( "|", $this->stock['month_use_time']);
                break;
            case 8:
                $unit = '月';
                $use_time = explode( "|", $this->stock['vip_use_time']);
                break;
            default:
                break;
        }

        $this->assign('id',$id);
        $this->assign('unit',$unit);
        $this->assign('use_time',$use_time);
        return view();
    }
    public function stopfinancing(){
        $id = input('param.id/d',0);

        $this->assign('id',$id);
        return view();
    }
    // 配资

    public function msg(){
        $request = request();
        if( $request->isAjax() ){
            $id = input('post.id/d',0);
            $action = input('post.action/s','');
            $mod = db('inner_msg');
            switch ($action) {
                case 'all':
                    $res = $mod->where( ['uid'=>$this->uid,'status'=>0] )->update(['status'=>1]);
                    break;
                case 'one':
                    if( !$id ){
                        return ['status'=>-1,'message'=>'数据解析错误，请按照平台规则重试'];
                    }
                    $res = $mod->where('id',$id)->update(['status'=>1]);
                    break;
            }
            if( $res ){
                return ['status'=>1,'message'=>'处理成功'];
            }else{
                return ['status'=>-1,'message'=>'处理失败，请稍后重试'];
            }
        }else{
            $data = db('inner_msg')
                        ->where('uid',$this->uid)
                        ->where('create_time >= DATE_ADD(NOW(),INTERVAL -1 MONTH)')
                        ->order('id DESC')
                        ->paginate(10, false, ['query'=>request()->param()]);

            $this->assign('data',$data);
            $this->assign('page', $data->render());
            return view();
        }
    }

    public function qiandao(){
        $request = request();
        if( $request->isAjax() ){
            $data = input('post.');
            if( qiandao($this->uid) ){
                $money = (int)$this->global['qd_money'];
                if( !$money ){
                    return ['status'=>-1,'message'=>'签到失败，签到功能已关闭'];
                }
                $add = array(
                        'uid'=>$this->uid,
                        'ip'=>$request->ip(),
                        'add_time'=>time(),
                        'money'=>$money*100
                    );
                // 开启事务
                db()->startTrans();
                $info = '签到成功，赠送管理费'.$money.'元';
                $res = db('member_qd')->insert($add);
                $mlog = memberMoneyLog($this->uid,$money*100,$info,8,2);

                if( $res&&$mlog ){
                    db()->commit();
                    return ['status'=>1,'message'=>'签到成功，赠送管理费'.$money.'元'];
                }else{
                    db()->rollback();
                    return ['status'=>-1,'message'=>'签到失败，请稍后重试'];
                }
            }else{
                return ['status'=>-1,'message'=>'签到失败'];
            }

        }else{

            abort(404,'当前地址不存在');
        }
    }






}
