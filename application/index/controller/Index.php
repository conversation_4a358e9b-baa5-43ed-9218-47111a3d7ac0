<?php

namespace app\index\controller;

use app\common\controller\Common;

class Index extends Common
{
    protected function _initialize()
    {
        parent::_initialize();

//        if( isMobile() ){
        Header("HTTP/1.1 301 Moved Permanently");
        $red = request()->domain() . '/wap.php';
        $this->redirect($red, 301);
//        }

    }

    public function index()
    {

        $banner = getBanner()['pc'];//pc轮播
        $friend = getFriend(2, 10);//合作伙伴

        $noticeList = getArticleList(73, 8);
        $gnywList = getArticleList(86, 8);
        $gjywList = getArticleList(87, 8);
        $pzbkList = getArticleList(69, 8);
        $gshqList = getArticleList(75, 8);

        $this->assign('noticeList', $noticeList);
        $this->assign('gnywList', $gnywList);
        $this->assign('gjywList', $gjywList);
        $this->assign('pzbkList', $pzbkList);
        $this->assign('gshqList', $gshqList);
        $this->assign('banner', $banner);
        $this->assign('friend', $friend);

        //免息配资
        $free_set = explode('|', $this->stock['free_set']);
        $this->assign("free_set", $free_set);
        $free_money_range = explode('|', $this->stock['free_money_range']);
        $this->assign("free_money_range", $free_money_range);
        //按天配资
        $day_money_range = explode('|', $this->stock['day_money_range']);
        $this->assign("day_money_range", $day_money_range);
        $day_use_time = explode('|', $this->stock['day_use_time']);
        $this->assign("day_use_time", $day_use_time);

        //按月配资
        $month_money_range = explode('|', $this->stock['month_money_range']);
        $this->assign("month_money_range", $month_money_range);
        $month_use_time = explode('|', $this->stock['month_use_time']);
        $this->assign("month_use_time", $month_use_time);
        //vip配资
        $vip_use_time = explode('|', $this->stock['vip_use_time']);
        $this->assign("vip_use_time", $vip_use_time);
        $vip_money_range = explode('|', $this->stock['vip_money_range']);
        $this->assign("vip_money_range", $vip_money_range);

        //免费
        $try_set = explode('|', $this->stock['try_set']);
        $this->assign("try_set", $try_set);

        $day_max_multiple = getmax_multiple(1);
        $month_max_multiple = getmax_multiple(2);
        $vip_max_multiple = getmax_multiple(8);
        $this->assign('day_max_multiple', $day_max_multiple);
        $this->assign('month_max_multiple', $month_max_multiple);
        $this->assign('vip_max_multiple', $vip_max_multiple);
        $this->extend_data();
        return view();
    }

    private function extend_data()
    {
        // 今日注册 充值
        if (cache('extend_data')) {
            $extend_data = cache('extend_data');
        } else {
            $temp = db('extend_data')->field(true)->order('id DESC')->select();
            $extend_data = array();
            foreach ($temp as $v) {
                switch ($v['type']) {
                    case 'jrzc':
                        $extend_data['jrzc'][] = $v;
                        break;
                    case 'jrcz':
                        $extend_data['jrcz'][] = $v;
                        break;
                    default:
                        break;
                }
            }
            cache('extend_data', $extend_data);
        }
        $this->assign('extend_data', $extend_data);
    }


}
