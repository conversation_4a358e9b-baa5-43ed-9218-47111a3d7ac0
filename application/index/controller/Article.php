<?php
namespace app\index\controller;

use app\common\controller\Common;

class Article extends Common
{
    protected function _initialize(){
        parent::_initialize();
    }

    public function column(){
        $type_nid = input('param.nid/s','');
        if( !$type_nid ){
            $this->error('缺少参数！请按照平台规则重试1');
        }
        $category = db('category');
        $data = $category->where('type_nid',$type_nid)->field(true)->find();
        if( !$data ){
            $this->error('该栏目内容未找到，请按照平台规则重试');
        }
        $type_set = $data['type_set'];
        if( $type_set==1 ){
            $web_title = $data['name'];
            $left_menu = $category
                            ->where(['parent_id'=>$data['parent_id'], 'type_set'=>1, 'is_hidden'=>1])
                            ->field('id,name,type_nid')
                            ->order('sort_order DESC')
                            ->select();
            foreach ($left_menu as &$v) {
                $v['type_url'] = url("cms/{$v['type_nid']}");
            }
            unset($v);

            $this->assign('data',$data);
            $this->assign('type_nid',$type_nid);
            $this->assign('web_title',$web_title);
            $this->assign('left_menu',$left_menu);
            return view('column');
        }else if( $type_set==2 ){
            $web_title = $data['name'];
            $left_menu = $category
                            ->where(['parent_id'=>$data['parent_id'], 'type_set'=>2, 'is_hidden'=>1])
                            ->field('id,name,type_nid')
                            ->order('sort_order DESC')
                            ->select();
            foreach ($left_menu as &$v) {
                $v['type_url'] = url("cms/{$v['type_nid']}");
            }
            unset($v);

            $list = db('article')
                        ->where('parent_id',$data['id'])
                        ->where('is_hide',1)
                        ->field('id,title,info,add_time')
                        ->order('sort_order DESC,add_time DESC')
                        ->paginate(10, false, ['query'=>request()->param()]);
                        // ->select();

            $page = $list->render();
            $list = $list->all();
            foreach ($list as $k2 => &$v2) {
                $v2['article_url'] = url("article/{$v2['id']}");
            }
            unset($v2);

            // dump($list);die;
            $this->assign('data',$data);
            $this->assign('list',$list);
            $this->assign('page', $page);
            $this->assign('type_nid',$type_nid);
            $this->assign('left_menu',$left_menu);
            $this->assign('web_title',$web_title);
            return view('article');
        }else{
            $this->error('该栏目内容未找到，请按照平台规则重试');
        }
    }

    public function detial(){
        $aid = input('param.aid/d',0);
        if( !$aid ){
            $this->error('缺少参数！请按照平台规则重试2');
        }
        $mod = db('article');
        $data = $mod->alias('a')
                ->join('category c','a.parent_id = c.id','left')
                ->where('a.id',$aid)
                ->field('a.*,c.name AS category_name,c.type_nid')
                ->find();
        if( !$data ){
            $this->error('该文章内容未找到，请按照平台规则重试');
        }
        $data['category_url'] = url("cms/{$data['type_nid']}");
        $prev = $mod->where('add_time','LT',$data['add_time'])
                    ->where(['parent_id'=>$data['parent_id'], 'is_hide'=>1])
                    ->field('id,title')
                    ->order('sort_order DESC,add_time DESC')
                    ->find();
        $next = $mod->where('add_time','GT',$data['add_time'])
                    ->where(['parent_id'=>$data['parent_id'], 'is_hide'=>1])
                    ->field('id,title')
                    ->order('sort_order DESC,add_time DESC')
                    ->find();
        if( $prev ){
            $prev['article_url'] = url("article/{$prev['id']}");
        }
        if( $next ){
            $next['article_url'] = url("article/{$next['id']}");
        }
        // SEO元素
        $this->global['web_keywords'] = $data['keyword'];
        $this->global['web_descript'] = $data['info'];
        $this->assign('global',$this->global);
        // dump($this->global);
        // dump($prev);
        // dump($next);
        $this->assign('data',$data);
        $this->assign('prev',$prev);
        $this->assign('next',$next);
        return view();
    }

}
