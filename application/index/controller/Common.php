<?php
namespace app\index\controller;

use think\Config;
use think\Controller;
use think\Request;
use app\common\controller\Common AS Com;
use NeteaseSms\NeteaseSmsSender;

class Common extends Com{
    protected function _initialize(){
        parent::_initialize();

    }

    public function login(){
        if( session('?uid') ){
            $this->redirect('members/index');
        }
        $request = request();
        if($request->method()=='POST'){
            if( $request->isAjax() ){
                $data = input('post.');
           
                    $u_info = db('members')->where('user_name',$data['uname'])
                            ->field('id,user_name,user_pass,is_ban')
                            ->find();
                    if( !$u_info ){
                        return ['status'=>-1,'message'=>'无该用户信息！'];
                    }
                    if( $u_info['is_ban']==1 ){
                        return ['status'=>-1,'message'=>'该账户已冻结！有问题联系在线客服'];
                    }

                    $v_pass = v_pass($data['pass'] , $u_info['user_pass']);
                    if( $v_pass ){
                        $this->_loginlog($u_info['id'],true);
                    }else{
                        $stime = strtotime( date("Y-m-d", time()) );
                        $etime = strtotime( date("Y-m-d", time()).' 23:59:59' );
                        $login_count = db('member_login')
                                ->where(['uid'=>$u_info['id'],'is_success'=>0])
                                ->whereTime('add_time','between',[$stime,$etime])
                                ->count('id');
                        if( $login_count>10 ){
                            return ['status'=>-2,'message'=>'今天登陆错误次数过多，请明天再试！'];
                        }
                        $this->_loginlog($u_info['id'],false);

                        return ['status'=>-3,'message'=>'密码输入错误！请重试'];
                    }

                    session('uid',$u_info['id']);
                    session('uname',$u_info['user_name']);
                    return ['status'=>'1','message'=>'登录成功'];
              
            }
        }else{

            return view();
        }
    }
    public function _loginlog($uid,$res=false){
        if( !$uid ){
            return false;
        }
        $request = request();
        $add = array(
                'uid'=>$uid,
                'ip'=>$request->ip(),
                'domain'=>$request->domain(),
                'add_time'=>time(),
                'is_success'=>$res?1:0
            );
        $result1 = db('member_login')->insert($add);
        if($res){
            $save = array(
                    'last_log_ip'=>$request->ip(),
                    'last_log_time'=>time()
                );
            $result2 = db('members')->where('id',$uid)->update($save);
        }
        return true;
    }
    public function register(){
        if( session('?uid') ){
            $this->redirect('members/index');
        }
        $request = request();
        if($request->method()=='POST'){
            if( $request->isAjax() ){
                $data = input('post.');
                $phone = input('post.uname/s','');
                $mod = db('members');
                $had = $mod->where('user_name',$data['uname'])->count('id');
                if($had){
                    return ['status'=>-1,'message'=>'该用户名已被使用！'];
                }
                $had2 = db('member_info')->where('phone',$phone)->count('id');
                if($had2){
                    return ['status'=>-1,'message'=>'该手机号已被使用2！'];
                }
                
                if( verify_code($phone,$data['vcode']) ){
                //if(true){
                    $server_name = $_SERVER['SERVER_NAME'];

                    $add['user_name'] = $data['uname'];
                    //$add['user_phone'] = $data['uname'];
                    $add['user_pass'] = set_pass($data['pass']);
                    $add['reg_time'] = time();
                    $add['reg_ip'] = $request->ip();
                    $add['reg_domain'] = $server_name;
                    $add['last_log_ip'] = $request->ip();
                    $add['last_log_time'] = time();
                    if($data['recommend']){
                        $had_recommend = $mod->where('user_name',$data['recommend'])->value('id');
                        if($had_recommend){
                            $add['recommend_id'] = $had_recommend;
                        }else{
                            return ['status'=>-3,'message'=>'无此推荐人信息！请核对推荐人或推广链接'];
                        }
                    }else{
                        $dl_yuming = dl_yuming($server_name);
                        if( $dl_yuming ){
                            $add['recommend_id'] = $dl_yuming;
                        }
                    }

                    $temp_uid = $mod->insertGetId($add);
                    if($temp_uid){
                        $minfo = array(
                                'uid'=>$temp_uid,
                                'phone'=>$phone,
                                'phone_status'=>1,
                            );
                        $mmoney = array(
                                    'uid'=>$temp_uid,
                                    'create_time'=>date('Y-m-d H:i:s',time())
                                );
                        $member_info = db('member_info')->insert($minfo);
                        $member_money = db('member_money')->insert($mmoney);

                        $xsrw = myXsrw( $temp_uid , config('MY_XSRW')['ZHUCE_JL'] );
                        file_put_contents('./xsrw.txt',
                                date("Y-m-d H:i ")."uid=".$temp_uid."&rw_id=".config('MY_XSRW')['ZHUCE_JL']."&xsrw:".json_encode($xsrw)."\r\n",
                                FILE_APPEND);
                        
                        session('uid',$temp_uid);
                        session('uname',$data['uname']);
                        return ['status'=>1,'message'=>'恭喜注册成功'];
                    }else{
                        return ['status'=>-2,'message'=>'注册失败，请稍后再试！'];
                    }
                }else{
                    return ['status'=>-1,'message'=>'短信验证码输入有误！'];
                }
            }
        }else{
            $invite = input('param.invite/s','');

            $this->assign('invite',$invite);
            return view();
        }
    }
    public function logout(){
        session('uid',null);
        session('uname',null);
        
        $this->success('账号退出成功', 'common/login');
    }
    public function findpwd(){
        if( session('?uid') ){
            $this->redirect('members/index');
        }

        $request = request();
        if( $request->isAjax() ){
            $data = input('post.');
            $phone = input('post.phone/s','');
            $action = input('post.action/s','');

            $temp_uid = db('member_info')->where('phone', $phone)->value('uid');
            if( $temp_uid ){
            }else{
                return ['status'=>-1,'message'=>'该手机号未绑定用户信息！'];
            }

            switch ($action) {
                case 'sendsms':
                    return $this->user_verify();
                    break;
                case 'findpwd':
                    if( captcha_check($data['vcodeyc']) ){
                        if( verify_code($phone, $data['vcode']) ){
                            if( $data['pass']!=$data['pass2'] ){
                                return ['status'=>-2,'message'=>'两次密码输入不一致！'];
                            }
                            $newp = set_pass($data['pass']);
                            $res = db('members')->where('id',$temp_uid)->update(['user_pass'=>$newp]);
                            if( $res ){
                                return ['status'=>1,'message'=>'修改成功，请重新登录'];
                            }else{
                                return ['status'=>-3,'message'=>'修改失败，请重试'];
                            }
                        }else{
                            return ['status'=>-3,'message'=>'短信验证码输入错误'];
                        }
                    }else{
                        return ['status'=>-3,'message'=>'图形验证码输入错误'];
                    }
                    break;
                default:
                    # code...
                    break;
            }

        }else{

            return view();
        }
    }

    public function user_verify(){
        $request = request();
        if( $request->isAjax() ){
            $uname = input('post.uname/s','');
            $phone = input('post.phone/s','');
            $source = input('post.source/s','');
            if( strlen($phone) != 11){
                return ['status'=>-1,'message'=>'手机号码格式不正确！'];
            }
            if( $source=='register' ){
                $had = db('member_info')->where('phone',$phone)->find();
                if($had){
                    return ['status'=>-5,'message'=>'该手机号已被使用！'];
                }
            }
            $mod = db('send_log');
            $send_cound = $mod->where('phone',$phone)
                            ->whereTime('send_time','between',[time()-60*10,time()])
                            ->count('id');
            if($send_cound > 10){
                return ['status'=>-2,'message'=>'发送短信次数过于频繁，请稍后再试！'];
            }
            $round = rand_number(); // 生成随机验证码

            // 获取网易易盾短信配置
            $netease_config = Config::get('netease_sms_config');
            
            // 檢查關鍵配置是否存在且不為空或未被替換
            if (empty($netease_config['secretId']) || 
                empty($netease_config['secretKey']) || 
                empty($netease_config['businessId']) || 
                empty($netease_config['templateId'])) {
                trace('網易易盾短信配置不完整或 .env 未正確加載', 'error');
                return ['status' => -4, 'message' => '短信服务配置错误，请联系管理员'];
            }

            // 實例化 NeteaseSmsSender
            $neteaseSms = new NeteaseSmsSender(
                $netease_config['secretId'],
                $netease_config['secretKey'],
                $netease_config['businessId'],
                $netease_config['apiUrl'],
                $netease_config['version'],
                $netease_config['paramType']
            );

            // 發送短信
            $params = ['code' => (string)$round]; // 验证码参数
            $result = $neteaseSms->sendWithParam($phone, $netease_config['templateId'], $params);

            $send_success = false;
            $message = '短信发送失败'; // 默认失败消息
            $raw_response_for_trace = isset($result['raw_curl_result']) ? $result['raw_curl_result'] : json_encode($result);

            if (isset($result['sdk_error']) && $result['sdk_error'] === true) {
                // SDK 內部錯誤 (例如 cURL 錯誤或無效響應)
                trace('NeteaseSms SDK Error: ' . $result['msg'] . ' Raw: ' . $raw_response_for_trace, 'error');
                $message = $result['msg'];
            } elseif (isset($result['code']) && $result['code'] == 200) {
                $send_success = true;
                $message = '短信发送成功';
            } else {
                // API 返回的業務錯誤
                $error_msg_from_api = isset($result['msg']) ? $result['msg'] : '未知API错误';
                trace('NeteaseSms API Error: Code - ' . (isset($result['code']) ? $result['code'] : 'N/A') . ', Msg - ' . $error_msg_from_api . ' Raw: ' . $raw_response_for_trace, 'error');
                $message = '短信发送失败: ' . $error_msg_from_api;
            }
            
            $add = array(
                    'code'=>$round,
                    'phone'=>$phone,
                    'send_time'=>time(),
                    'ip'=>$request->ip(),
                    'is_success'=> $send_success ? 1 : 0
                );
            $mod->insert($add);

            if($send_success){
                return ['status'=>1,'message'=>$message];
            }else{
                return ['status'=>-3,'message'=>$message];
            }
        }else{
            abort(404,'页面不存在');
        }
    }
    
    public function hetong(){
        // $uid = input('param.uid/d',0);
        $category = input('param.c/d',1);
        $borrow_id = input('param.id/d',0);

        $stock = array();
        if( $borrow_id ){
            $stock = model('admin/Stock')->getStock($borrow_id);

            $w = array('borrow_id'=>$stock['id'], 'uid'=>$stock['uid'], 'status'=>1);
            $sum1 = db('stock_addfinancing')->where($w)->sum('borrow_fee');
            $sum2 = db('stock_renewal')->where($w)->sum('borrow_fee');
            $stock['sumInterest_display'] = $stock['borrow_interest'] + $sum1 + $sum2;
        }

        $this->assign('stock',$stock);
        if( $category==1 ){
            $this->assign('data',hetong(1));
            return view();
        }else{
            $this->assign('data',hetong(2));
            return view('qihuohetong');
        }
    }

    public function zcxy(){
        $this->assign('data',hetong(3));
        return view();
    }

    public function download(){
        return view();
    }

    public function xsrw(){
        // $a = myXsrw(17,3);
        // dump($a);
        // die;
        $xsrw_on = $this->global['xsrw_on'];
        $xsrw_money = explode('|', $this->global['xsrw_money']);
        $shiming_1 = $peizi_1 = $chognzhi_1 = $yhk_1 = $kdpz_1 = $bukui_1 = $tiying_1 = false;

        if( $this->uid ){
            $mod = db('xsrw');
            //实名认证
            $shiming = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['SHIMING_JL']])->count('id');
            if( $shiming ){
                $shiming_1 = true;
            }
            //首次配资
            $peizi = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['PEIZI_JL']])->count('id');
            if( $peizi ){
                $peizi_1 = true;
            }
            //首次充值
            $chognzhi = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['CHONGZHI_JL']])->count('id');
            if( $chognzhi ){
                $chognzhi_1 = true;
            }
            //首次绑定银行卡
            $yhk = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['BDYHK_JL']])->count('id');
            if( $yhk ){
                $yhk_1 = true;
            }
            // 首次扩大配资奖励
            $kdpz = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['KDPEIZI_JL']])->count('id');
            if( $kdpz ){
                $kdpz_1 = true;
            }
            // 首次补亏
            $bukui = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['BUKUI_JL']])->count('id');
            if( $bukui ){
                $bukui_1 = true;
            }
            // 首次提盈
            $tiying = $mod->where(['uid'=>$this->uid, 'rw_id'=>config('MY_XSRW')['TIYING_JL']])->count('id');
            if( $tiying ){
                $tiying_1 = true;
            }
        }

        $this->assign('shiming_1',$shiming_1);
        $this->assign('peizi_1',$peizi_1);
        $this->assign('chognzhi_1',$chognzhi_1);
        $this->assign('yhk_1',$yhk_1);
        $this->assign('kdpz_1',$kdpz_1);
        $this->assign('bukui_1',$bukui_1);
        $this->assign('tiying_1',$tiying_1);
        $this->assign('money',$xsrw_money);
        return view();
    }

    public function tuiguang(){

        return view();
    }

    public function anquan(){

        return view();
    }

    public function bangzhu(){

        return view();
    }


    /**
     * 自动续期
     * 业务逻辑
     * 用户允许自动延期配资
     * 交易日晚上延期(周末节假日不处理)
     * 未延期成功发送消息提醒用户和管理员
     * 按天配资延期一天、按月配资延期一月
     * 延期手续费按照最新的利率设置收取（期货、股票）
     */
    public function auto_renewal(){
        $now = time();

        $checkDayStr = date('Y-m-d ',time());
        $stime1 = strtotime($checkDayStr."16:00:00");// 16:01:00
        $etime1 = strtotime($checkDayStr."16:02:00");
       
        if( ($now>=$stime1) && ($now<=$etime1) ){
            api_auto_renewal();
        }else{
            echo "time_out";
        }

        $stime2 = strtotime($checkDayStr."16:05:00");// 16:06:00
        $etime2 = strtotime($checkDayStr."16:40:00");

        if( ($now>=$stime2) && ($now<=$etime2) ){
            api_auto_renewal();
        }else{
            echo "time_out<br>";
        }
    }
    
    




}
