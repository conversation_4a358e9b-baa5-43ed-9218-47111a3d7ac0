<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

	{load href="/static/js/jquery172.js" /}
	{load href="/static/layer-v3.1.1/layer/layer.js" /}
	{load href="/static/css/H/peizi.css" /}
</head>
<body>
	<div class="confirm-apply layui-layer-wrap">
		<div class="title">确认信息</div>
		<div class="meta">
            <div class="item clearfix">
                <div class="left">
                    配资保证金
                </div>
                <div class="middle">
                    <span class="number-middle text-red">{$data.depositMoney}</span>
                    元
                </div>
                <div class="right">
                    总操盘资金 <span class="text-red">{$data.depositMoney + $data.money}</span>
                    元
                </div>
            </div>
            <div class="item clearfix">
                <div class="left">
                    利息
                </div>
                <div class="middle">
                    <span class="number-middle text-red">{$fee}</span>
                    元
                </div>
                <div class="right">
                    {if condition="$type eq 3"}
                    固定倍率,到期结束，不可续期
                    {elseif condition="$type eq 4"/}
                    不收取利息
                    {else /}
                    本次管理费在配资成功后一次性收取
                    {/if}
                </div>
            </div>
            <div class="item clearfix">
                <div class="left">
                    配资周期
                </div>
                <div class="middle"><span class="number-middle text-red j-duration2">{$duration}</span>
                    {$unit}
                </div>
                <!-- <div class="right">
                    配资周期灵活选择，到期自动续约，资金不足自动停止。
                </div> -->
                <div class="left">
                    开始交易时间：
                </div>
                <div class="right middle" style="width: 150px;">
	                <span class="text-red">
						{:str_replace([1,2], ['立即生效','下个交易日'], $startDate)}
	                </span>
                </div>
            </div>
            <div class="item clearfix">
                <div class="left">
                    亏损警戒线：
                </div>
                <div class="middle">
                	<span class="text-red">{$warnMoney}</span>
                	元
                </div>
                <div class="left">
                    亏损平仓线：
                </div>
                <div class="right middle" style="width: 150px;">
                    <span class="text-red">{$closeMoney}</span>
                    元
                </div>
            </div>
            <div class="item clearfix" style="height: auto;
                                              line-height: 23px;
                                              padding-top: 16px;
                                              padding-bottom: 10px;">
                <div class="left">
                    需准备资金：
                </div>
                <div class="middle">
                    <span class="text-red">{$depositMoney + $fee}</span>
                    元
                </div>
                <div class="right" style="font-size: 15px;">
                    账户余额 <span class="text-red j-account-money">{$minfo.account_money / 100}</span> 元
                    管理费 <span class="text-red j-account-money">{$minfo.interest_money / 100}</span> 元

                    {if condition="$enough neq 1"}
                    <span class="j-money-tip">，本次支付还差 <span class="text-red">{$diff_money}</span>
                    元
					{/if}
                    </span>
                    <a href="/members/recharge" target="_blank" class="fr" style="font-size: 15px;">马上充值</a>
                </div>
            </div>
        </div>
        <div class="operate-group">
            <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">返回修改</a>
            <a href="javascript:;" class="btn btn-primary" onclick="applyFinancing()">确认配资</a>
        </div>
	</div>
</body>

<script type="text/javascript">
function applyFinancing(){
	var enough = {$enough};
	var diff_money = '{$diff_money}';
	var money = {$money}; //配资金额
    var depositMoney = {$depositMoney}; //本金
    var multiple = {$multiple}; //倍数
    var type = {$type}; //天
    var duration = {$duration}; //周期
    var startDate = {$startDate};
    if( enough != 1){
        return layer.msg('当前余额不足，还需'+diff_money+'元', { icon: 2,anim: 6,shade: [0.3, '#000'] });
    }
    if(depositMoney < 1) {
        return layer.msg('本金不能为空', { icon: 2,anim: 6,shade: [0.3, '#000'] });
    }
    if(multiple < 1) {
        return layer.msg('请选择配资倍数', { icon: 2,anim: 6,shade: [0.3, '#000'] });
    }
    if(duration < 1) {
        return layer.msg('请选择配资周期', { icon: 2,anim: 6,shade: [0.3, '#000'] });
    }
    var la_load = layer.load(0,{
    		shade: [0.1,'#000'] 
    	});
    $.ajax({
        url: "{:url('future/createOrder')}",
        type: "post",
        dataType: "json",
        data: {
            "money": money,
            'depositMoney': depositMoney,
            'multiple': multiple,
            'duration': duration,
            "type": type,
            "startDate": startDate
        },
        success: function(d) {
            if (d.status == 1) {
            	layer.msg(d.message, { icon: 1,shade: [0.1, '#000'] });
                setTimeout(function(){ 
						parent.location.href = "{:url('members/stock')}";
					}, 1200);
            } else {
            	layer.close(la_load);
                return layer.msg(d.message, { icon: 2,anim: 6,shade: [0.1, '#000'] });
            }
        }
    });

}
function cancel(){
	//注意：parent 是 JS 自带的全局对象，可用于操作父页面
	var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
	parent.layer.close(index);
}
</script>
</html>