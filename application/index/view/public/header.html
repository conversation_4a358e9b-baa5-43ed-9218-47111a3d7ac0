<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	
	<meta name="keywords" content="{$global.web_keywords}" />
	<meta name="description" content="{$global.web_descript}" />

	{load href="/static/css/H/style.css" /}
	{load href="/static/css/H/extend.css" /}
	{load href="/static/js/jquery183.js" /}
	{load href="/static/layer-v3.1.1/layer/layer.js" /}
	{load href="/static/js/H/common.js" /}
</head>
<body>

<div class="header">
	<div class="top">
		<div class="wrapper clearfix">
			
			<div class="tel-num">
			    欢迎访问{$global.web_name}，易记网站：<span class="red1" style='font-weight: bold;'>{$global.txt_url}</span>，财富热线：<span class="red1" style='font-weight: bold;'>{$kefu.tel.0.number}</span> 
			</div>
			<div class="quick-link">
				{if condition="$uid GT 0"}
				<a href="{:url('members/index')}">我的账户</a>
				<a href="{:url('common/logout')}">退出</a>
				{else /}
				<a href="{:url('common/register')}" class="header-reg">免费注册</a>
				<a href="{:url('common/login')}">立即登录</a>
				
				{/if}

				
				<a href="/bangzhu">帮助中心</a>
				<a href="{:url('cms/gsjj')}">关于我们</a>
				<a href="/anquan">安全保障</a>
				<a href="{:url('cms/xszn')}">新手指南</a>
			</div>

			<div style="clear:both;"></div>
		</div>
	</div>
	<div class="top2">
		<div class="wrapper clearfix">
			<div class="logo">
				<img src="{$global.logo}" alt="{$global.web_name}logo">
			</div>
			<div class="nav clearfix">
				


				<ul class="fr navigation-list">
					{volist name="navigation" id="vo"}
						<li class="nav1">
							<a href="{$vo.url}" class="item item-{$vo.id}">{$vo.name}</a>
							{php} if( !empty($vo['children']) ){ {/php}
							<div class="nav2">
								{volist name="$vo.children" id="vo"}
									<a href="{$vo.url}">{$vo.name}</a>
								{/volist}
							</div>
							{php} } {/php}
						</li>
					{/volist}
				</ul>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
$('.nav1').each(function(index, el) {
    $(this).mouseover(function () {
        $(this).find('div.nav2').show();
    }).mouseout(function () {
        $(this).find('div.nav2').hide();
    });
});

$(".navigation-list>li a").each(function () {
    if ($($(this))[0].href == String(window.location)){
        $(this).parent().addClass('active');
        $(this).parents("li").addClass('active');
    }
});



</script>