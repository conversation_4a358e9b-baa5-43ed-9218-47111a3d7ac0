{include file="public/header" /}
<title>{$data.title} - {$global.web_name}</title>
{load href="/static/css/H/article.css" /}

<style type="text/css">
</style>

<div class="news-container clearfix">
        <div class="bread-crumb">
            <a href="{:url('/index')}" style="color: #333;">首页</a>
            &gt;
            <a href="{$data.category_url}">{$data.category_name}</a>
            &gt;
            <a>{$data.title}</a>
        </div>
        <div class="news-article">
            <h1 class="title">{$data.title}</h1>
            <div class="meta clearfix">
                <div class="date"><i class="icon icon-clock"></i>{:date('Y-m-d H:i', $data.add_time)}</div>
                <div class="share"></div>
            </div>
            <div class="article-body">
                {:htmlspecialchars_decode($data.content)}
            </div>
            <hr>
            <div class="pager">
                {empty name="$prev"}
                <a class="prev">上一篇: 没有了</a>
                {else /}
                <a class="prev" href="{$prev.article_url}">上一篇: {$prev.title}</a>
                {/empty}

                {empty name="$next"}
                <a class="next">下一篇: 没有了</a>
                {else /}
                <a class="next" href="{$next.article_url}">下一篇: {$next.title}</a>
                {/empty}
            </div>
        </div>        
    </div>   


<script type="text/javascript">

</script>
{include file="public/footer" /}