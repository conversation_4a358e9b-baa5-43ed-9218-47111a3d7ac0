{include file="public/header" /}
<title>{$web_title} - {$global.web_name}</title>
{load href="/static/css/H/article.css" /}

<style type="text/css">
.news-list { padding: 15px 30px 60px; }
</style>
<div class="news-container clearfix">
    <div class="bread-crumb">
        <a href="{:url('/index')}" style="color: #333;">首页</a>
        &gt;
        <a>{$data.name}</a>
    </div>
    <div class="news-menu">
        <ul>
            {volist name="left_menu" id="vo"}
            <li class=' {if condition="$vo.type_nid EQ $type_nid"}active{/if} ' >
                {if condition="$vo.type_nid EQ $type_nid"}
                <a href="javascript:;">{$vo.name}</a>
                {else /}
                <a href='{$vo.type_url}'>{$vo.name}</a>
                {/if}
            </li>
            {/volist}
         </ul>
    </div>
    <div class="page-container">
        <div class="page-hd">
            <h3>{$data.name}</h3>
        </div>
        <div class="page-main">
            <div class="news-list clearfix">
                {:htmlspecialchars_decode($data.content)}
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

</script>
{include file="public/footer" /}