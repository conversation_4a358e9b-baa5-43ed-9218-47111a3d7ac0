{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/underscore-min.js" /}
{load href="/static/js/jquery172.js" /}
{load href="/static/css/M/user.css" /}

<style type="text/css">
.stock-main .box_word{ margin-top: 6px; }
#use_time{ width: 108px;height: 26px; }
</style>

<div class="stock-main">
    <div class="box_prompt">
        利息根据当前平台设置的利率重新算， 收取费用的公式和申请配资一样
    </div>
    
    <p style="text-align: center;margin-top:20px;">当前账户余额：<span>{$minfo.account_money/100}</span>元</p>
    
    <div class="box_word">
        续期时间：
        <select id="use_time">
        {volist name="use_time" id="vo"}
        <option value="{$vo}">{$vo}{$unit}</option>
        {/volist}
        </select>
    </div>

    <p style="text-align: center;margin-top: 6px;">手续费：<span id='j-fee'>&nbsp;</span></p>

    <div class="operate-group">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消续期</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认续期</a>
    </div>
    
</div>

<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
$(function(){
    $("#use_time")
        .on("change", _.debounce(function (e) {
            $("#j-fee").text("计算中...")
            var duration = parseInt( $(this).val() );
            $.ajax({
                url: "{:url('stock/calculate_renewal')}",
                type: "post",
                dataType: "json",
                data: {id:id,duration:duration},
                success: function(d,e,x){
                    // console.log(d);
                    $("#j-fee").text(d.fee);
                }
            });
        }, 300)).trigger('change');
})
function apply(){
    var duration = $("#use_time").val();
    if( !id || !duration ){
        return layer.msg('数据不能为空，请按照平台规则重试！', { icon: 2,anim: 6 });
    }

    if( category==1 ){
        var url = "{:url('stock/renewalApply')}";
    }else{
        var url = "{:url('future/renewalApply')}";
    }
	// console.log(money ,yu,);return;
	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	$.ajax({
        url: url,
        type: "post",
        dataType: "json",
        data: {id:id,duration:duration},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );
            	// var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){
					// parent.layer.close(index);
                    parent.location.reload();
				}, 999);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}

function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}

layui.use('layer', function(){
    layer = layui.layer;
});
</script>