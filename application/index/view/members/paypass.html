{extend name="members/common" /}

{block name="title"}支付密码{/block}

{block name="main"}

<div class="space-right">
    <h2><strong>
        {if condition='$minfo.pin_pass'}
        修改支付密码
        {else /}
        设置支付密码
        {/if}
        </strong>
    </h2>
    <div class="formbox">
        {if condition='$id_status eq 1'}

            {if condition='$minfo.pin_pass'}
            <table>
                <tbody>
                    <tr>
                        <th>新密码：</th>
                        <td>
                            <input id="pin" class="inp" type="password" autocomplete="off">
                                <span style="color:red" id="in">*</span>
                        </td>
                    </tr>
                    <tr>
                        <th>确认新密码：</th>
                        <td>
                            <input id="pinConfirm" class="inp" type="password" autocomplete="off">
                                <span style="color:red" id="ic">*</span>
                        </td>
                    </tr>
                    <tr>
                        <th>绑定手机号：</th>
                        <td>
                            <input class="inp btn-disabled" value="{:substr_replace($minfo.phone,'****',3,4)}" readonly="" >
                            <!-- <span>{:substr_replace($minfo.phone,'****',3,4)}</span> -->
                        </td>
                    </tr>
                    <tr>
                        <th>短信验证码：</th>
                        <td style="position: relative;">
                            <input id="scode" class="inp" maxlength="6" type="text" autocomplete="off">
                            <button id="sendSMS" style="background: #e33333;
                                            color: #fff;
                                            height: 30px;
                                            width: 100px;
                                            cursor: pointer;
                                            position: absolute;">发送验证码</button>
                        </td>
                    </tr>

                    <tr>
                        <th></th>
                        <td>
                            <button id="updatePin" type="button" class="btn-b">确定</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            {else /}

            <table>
                <tbody>
                    <tr>
                        <th>输入密码：</th>
                        <td>
                            <input id="pin" class="inp" type="password" autocomplete="off">
                                <span style="color:red" id="in">*</span>
                        </td>
                    </tr>
                    <tr>
                        <th>确认密码：</th>
                        <td>
                            <input id="pinConfirm" name="newPwdConfirm" class="inp" type="password" autocomplete="off">
                                <span style="color:red" id="ic">*</span>
                        </td>
                    </tr>
                    <tr>
                        <th>验证码：</th>
                        <td style="position: relative;">
                            <input id="vcode" class="inp" type="text" maxlength="4" autocomplete="off">
                            <img src='{:captcha_src()}' title="点击换一个" class="jym" style="
                                                                width: 98px; 
                                                                position: absolute;
                                                                top: 10px;
                                                                cursor: pointer;" onclick="this.src='{:captcha_src()}'+'?cd='+Math.random()" />
                                <!-- <span style="color:red" id="ic">*</span> -->
                        </td>
                    </tr>
                    <tr>
                        <th></th>
                        <td>
                            <button id="setPin" type="button" class="btn-b">确定</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            {/if}

        {else /}

            <div style="text-align: center;line-height: 100px;font-size: 18px;color:#F60;">
                您的身份信息未绑定，<a href="{:url('members/real')}">去绑定</a>
            </div>

        {/if}

    </div>

</div>

{/block}

{block name="script"}
<script type="text/javascript">
$('#setPin').on('click', function(event) {
    var pin = $('#pin').val();
    var pinConfirm = $('#pinConfirm').val();
    var vcode = $('#vcode').val();

    if ( !pin || !pinConfirm ) {
        return layer.msg( '请输入密码',{icon : 2,anim:6} );
    }
    if ( pin.length<6 || pin.length>16 ) {
        return layer.msg( '密码必须为6-16位的英文或数字组合',{icon : 2,anim:6} );
    }
    if ( pin != pinConfirm ) {
        return layer.msg( '两次密码输入不一致，请重新输入',{icon : 2,anim:6} );
    }
    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/paypass")}',
        type: "post",
        dataType: "json",
        data: {pin:pin,pinConfirm:pinConfirm,vcode:vcode,action:'set'},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        layer.close(la_load);
                        window.location.href = '{:url("members/index")}';
                    },1000);
                return;
            }else{
                $('.jym').trigger('click');
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });

});

$('#updatePin').on('click', function(e) {
    var pin = $('#pin').val();
    var pinConfirm = $('#pinConfirm').val();
    var scode = $('#scode').val();
    if ( !pin || !pinConfirm ) {
        return layer.msg( '请输入密码',{icon : 2,anim:6} );
    }
    if ( pin.length<6 || pin.length>16 ) {
        return layer.msg( '密码必须为6-16位的英文或数字组合',{icon : 2,anim:6} );
    }
    if ( pin != pinConfirm ) {
        return layer.msg( '两次密码输入不一致，请重新输入',{icon : 2,anim:6} );
    }
    if ( !scode ) {
        return layer.msg( '请输入短信验证码',{icon : 2,anim:6} );
    }
    // console.log(oldPwd,newPwd,newPwdConfirm );
    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/paypass")}',
        type: "post",
        dataType: "json",
        data: {pin:pin,pinConfirm:pinConfirm,scode:scode,action:'update'},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        layer.close(la_load);
                        window.location.href = '{:url("members/index")}';
                    },999);
                return;
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });
});


$('#sendSMS').on('click', function(event) {
    var pin = $('#pin').val();
    var pinConfirm = $('#pinConfirm').val();
    if ( !pin || !pinConfirm ) {
        return layer.msg( '请输入密码',{icon : 2,anim:6} );
    }
    if ( pin.length<6 || pin.length>16 ) {
        return layer.msg( '密码必须为6-16位的英文或数字组合',{icon : 2,anim:6} );
    }
    if ( pin != pinConfirm ) {
        return layer.msg( '两次密码输入不一致，请重新输入',{icon : 2,anim:6} );
    }

    var phone = "{$minfo.phone}";
    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url: '{:url("Common/user_verify")}',
        type: "post",
        dataType: "json",
        data: {phone:phone},
        beforeSend:function(){
            txtObj.text('验证码发送中').attr("disabled",true).addClass('btn-disabled');
        },
        success: function(d,e,x){  
            layer.close(la_load);
            if(d.status==1){
                myInterval();
                return layer.msg( d.message,{icon:1} );
            }else{
                //启用按钮
                txtObj.text('重新发送').removeAttr("disabled").removeClass('btn-disabled');
                return layer.msg( d.message,{icon:5} );
            }
        }
    });
});
var txtObj = $('#sendSMS');//文字DOM Obj
var InterValObj; //timer变量，控制时间 
var count = 90; //间隔函数，1秒执行 
var curCount;//当前剩余秒数
function myInterval() {
    curCount = count;
    //设置button效果，开始计时
    txtObj.text(curCount + "秒后可重新发送"); 
    InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次 
} 
function SetRemainTime() {
    if (curCount == 0) {         
        window.clearInterval(InterValObj);//停止计时器 
        //启用按钮
        txtObj.removeAttr("disabled").removeClass('btn-disabled');
        txtObj.text("重新发送"); 
    }else{
        curCount--; 
        txtObj.text(curCount + "秒后重新发送"); 
    } 
}

</script>
{/block}
