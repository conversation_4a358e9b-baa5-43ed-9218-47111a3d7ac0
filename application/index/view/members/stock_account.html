{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/M/user.css" /}

<style type="text/css">
.stock-main {
    padding: 30px 70px;
}
table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    text-align: center;
}
.sqyanqi table {
    margin: 0 auto;
    border-left: solid 1px #ddd;
    border-bottom: solid 1px #ddd;
}
.sqyanqi table td, .sqyanqi table th {
    width: 120px;
    padding: 5px 10px;
    border: solid 1px #ddd;
    text-align: center;
    line-height: 27px;
}
</style>

<div class="stock-main">
    <div class="box_prompt">
        温馨提示：请妥善保管您的账户密码，切勿告诉他人。
    </div>
    
    <div class="sqyanqi" style="margin: 25px 0 6px;">
        <table>
            <tbody>
                <tr>
                    <th>子账号：</th>
                    <td>{$data.home_user}</td>
                </tr>
                <tr>
                    <th>交易密码：</th>
                    <td>{$data.home_pws}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="operate-group">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">返回</a>
        <a href="{:url('common/download')}" class="btn btn-primary" target="_blank" >下载交易软件</a>
    </div>
    
</div>

<script type="text/javascript">

function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}

layui.use('layer', function(){
    layer = layui.layer;
});
</script>