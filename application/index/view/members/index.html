{extend name="members/common" /}

{block name="title"}用户中心{/block}

{block name="main"}
<div class="space-right">
    <h2></notempty><strong>我的账户</strong></h2>
    <div class="ms-c3 clearfix">
        <div class="ms-c3-l">
            <span>账户余额：</span>
            <br />
            <strong>{:number_format($minfo.account_money/100,2)}</strong>元
            <a href="{:url('members/mrecord')}" style="color: #069">查询资金明细</a>
        </div>
        <p>
            <a href="{:url('members/recharge')}" class="s1">充值</a>
            <a href="{:url('members/withdraw')}" class="s2">提现</a>
            {if condition="$qiandao"}
            <a href="javascript:;" onclick="qd()" class="s3">签到</a>
            {else /}
            <a href="javascript:;" class="s3 btn-disabled" style="background-color: #b7b7b7;">签到</a>
            {/if}
        </p>
    </div>
    <div class="ms-c8">
        <dl>
            <dd><h5>账户余额</h5><strong>{:number_format($minfo.account_money/100,2)}</strong>元</dd>
            <dd><h5>冻结资金</h5><strong>{:number_format($minfo.money_freeze/100,2)}</strong>元</dd>
            <dd class="last"><h5>赠送管理费余额</h5><strong>{:number_format($minfo.interest_money/100,2)}</strong>元</dd>
            <dt><h5>总资产</h5>
                <strong>
                    {php}
                        echo number_format( ($minfo['account_money']+$minfo['money_freeze']+$minfo['interest_money'])/100 , 2 )
                    {/php}
                </strong>元
            </dt>
        </dl>
    </div>
    <div class="ms-c3-b">
        <dl>
            <dt class="{if condition='$minfo.id_status eq 1'}s1-green{else /}s1{/if}">
                <h4>实名认证<br/><span></span></h4>

                {if condition='$minfo.id_status eq 1'}
                <span class="orange">已认证</span>
                <a href="{:url('members/real')}">查看</a>
                {else /}
                <span class="red">未认证</span>
                <a href="{:url('members/real')}">去认证</a>
                {/if}
            </dt>
        </dl>
        <dl>
            <dt class="s2-green">
                <h4>绑定手机<br/><span>{:substr_replace($minfo.phone,'****',3,4)}</span></h4>
                <span class="orange">已绑定</span>
            </dt>   
        </dl>
        <dl>
            <dt class=" {if condition='$bank'}s3-green{else /}s3{/if}">
                <h4>绑定银行卡</h4>
                {if condition='$bank'}
                <span class="orange">已绑定</span>
                <a href="{:url('members/bank')}">查看</a>
                {else /}
                <span class="red">未绑定</span>
                <a href="{:url('members/bank')}" data="bind">去绑定</a>
                {/if}
            </dt>
        </dl>
    </div>
    <div  class="ms-c4">
        <table>
            <tr>
                <th>银行卡管理：</th>
                <td>余额提款到银行卡需要先绑定一张您名下的银行卡</td>
                <td class="r"><a href="{:url('members/bank')}">去绑定</a></td>
            </tr>
            <tr>
                <th>登录密码：</th>
                <td>登录时需要输入的密码</td>
                <td class="r"><a href="{:url('members/pass')}">修改</a></td>
            </tr>
            <tr>
                <th>支付密码：</th>
                <td>提现时需要输入的密码</td>
                <td class="r">
                    <a href="{:url('members/paypass')}">
                    {if condition='$minfo.pin_pass'}
                    修改
                    {else /}
                    设置
                    {/if}
                    </a>
                </td>
            </tr>
            <tr>
                <th>注册时间：</th>
                <td>{:date('Y年m月d日',$minfo.reg_time)}</td>
                <td class="r"></td>
            </tr>
        </table>
    </div>
</div>
{/block}

{block name="script"}
<script type="text/javascript">
var qiandao = "{$qiandao}";
function qd(){
    if( qiandao ){
        var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
        $.ajax({
            url: '{:url("members/qiandao")}',
            type: "post",
            dataType: "json",
            // data: {},
            success: function(d,e,x){
                if(d.status==1){
                    layer.msg(d.message,{icon:1});
                    var t1 = setTimeout(function(){
                            // layer.close(la_load);
                            window.location.href = '{:url("members/index")}';
                        },1000);
                    return;
                }else{
                    layer.close(la_load);
                    return layer.msg(d.message,{icon:2});
                }
            }
        });
    }
}
</script>
{/block}
