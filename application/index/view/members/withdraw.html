{extend name="members/common" /}

{block name="title"}提现{/block}

{block name="main"}

<div class="space-right">
    <h2><strong>提现</strong><span>提现到指定银行卡</span></h2>
    <div class="ms-c6">
        <div class="ms-c6-m" style="padding-top: 10px">
        

            {if condition="$minfo.id_status NEQ 1"}

            <div style="text-align: center;line-height: 100px;font-size: 18px;color:#F60;">
                您的身份信息未绑定，<a href="{:url('members/real')}">去绑定</a>
            </div>

            {else /}

                {empty name="bank"}
                <div style="text-align: center;line-height: 100px;font-size: 18px;color:#F60;">
                   您的银行卡未绑定，<a href="{:url('members/bank')}">去绑定</a>
                </div>

                {else /}

                <div class="formbox">
                        <table class="withdraw">
                            <tbody>
                                <tr>
                                    <th>可提款金额：</th>
                                    <td>
                                        <strong class="red">{:number_format($minfo.account_money/100,2)}</strong> 元
                                    </td>
                                </tr>
                                <tr>
                                    <th>提款金额：</th>
                                    <td>
                                        <input id="money" placeholder='请输入整数' class="inp" type="text"> 元
                                    </td>
                                </tr>
                                <tr>
                                    <th>提现银行卡：</th>
                                    <td>
                                        <select id="bank" class="sel">
                                            <option value="">请选择</option>
                                            {volist name="bank" id="vo"}
                                            <option value="{$vo.id}">{$vo.bank_name}&nbsp;({$vo.number_display})</option>
                                            {/volist}
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <th>支付密码：</th>
                                    <td>
                                        {eq name="is_set_pass" value="1"}
                                            <input id="pin" placeholder='请输入支付密码' maxlength="16" class="inp" type="password">
                                        {else /}
                                            <a href="{:url('members/paypass')}" style="color: #F00;">设置支付密码</a>后才可以进行提现~
                                        {/eq}
                                    </td>
                                </tr>
                                <tr>
                                    <th></th>
                                    <td>
                                        <button id="withdraw" class="btn-b" type="button">提交</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                </div>
                <div class="ms-c6-dl clearfix">
                    <dl>
                        <dt>最快<strong>秒</strong>到账</dt>
                        <dd>工作时间内提款秒到账(节假日无休)</dd>
                    </dl>
                    <dl>
                        <dt>提款<strong>0</strong>手续费</dt>
                        <dd>提款产生的银行手续费全免</dd>
                    </dl>
                    <dl>
                        <dt>支持银行多达<strong>几十</strong>家</dt>
                        <dd>建议您使用大型银行提款，到账最快</dd>
                    </dl>
                    <p>
                        <span style='display: block;line-height: 28px;'>1、每天最多申请提现{$withdraw_set.1}次；</span>
                        <span style='display: block;line-height: 28px;'>2、禁止洗钱、信用卡套现、虚假交易等行为；</span>
                    </p>
                </div>
                
                {/empty}

            {/if}

        </div>
    </div>

</div>

{/block}

{block name="script"}
<script type="text/javascript">
var withdraw_min = parseFloat("{$withdraw_set.0}");
var is_set_pass = parseInt("{$is_set_pass}");

$('#withdraw').on('click', function(event) {
    var balance = parseInt("{$minfo.account_money}")/100;
    var money = parseFloat($('#money').val());
    var bank = $('#bank').val();
    var pin = $('#pin').val();
    if ( is_set_pass != 1){
        return layer.msg( '设置支付密码后才可以进行提现',{icon : 2,anim:6} );
    }
    if ( !money ) {
        return layer.msg( '请输入提现的金额',{icon : 2,anim:6} );
    }
    if ( money < withdraw_min) {
        return layer.msg( '提现金额不能小于'+withdraw_min+'元',{icon : 2,anim:6} );
    }
    if ( money > balance) {
        return layer.msg( '提现金额不能大于可提款金额',{icon : 2,anim:6} );
    }
    if( !/^\d+$/.test(money) ){
        return layer.msg( '提现金额必须是整数',{icon : 2,anim:6} );
    }
    if ( !bank ) {
        return layer.msg( '请选择提现银行卡',{icon : 2,anim:6} );
    }
    if ( !pin ) {
        return layer.msg( '请输入支付密码',{icon : 2,anim:6} );
    }
    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/withdraw")}',
        type: "post",
        dataType: "json",
        data: {money:money,bank:bank,pin:pin},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        // layer.close(la_load);
                        window.location.href = '{:url("members/index")}';
                    },999);
                return;
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });

});
</script>
{/block}
