{extend name="members/common" /}

{block name="title"}登录密码修改{/block}

{block name="main"}

<div class="space-right">
    <h2><strong>登录密码修改</strong></h2>
    <div class="formbox">
        <form method="post">
            <table>
                <tbody>
                    <tr>
                        <th>原密码：</th>
                        <td>
                            <input id="oldPwd" name="oldPwd" class="inp" type="password" autocomplete="off">
                                <span style="color:red" id="io">*</span>
                        </td>
                    </tr>
                    <tr>
                        <th>新密码：</th>
                        <td>
                            <input id="newPwd" name="newPwd" class="inp" type="password" autocomplete="off">
                                <span style="color:red" id="in">*</span>
                        </td>
                    </tr>
                    <tr>
                        <th>确认新密码：</th>
                        <td>
                            <input id="newPwdConfirm" name="newPwdConfirm" class="inp" type="password" autocomplete="off">
                                <span style="color:red" id="ic">*</span>
                        </td>
                    </tr>
                    <tr>
                        <th></th>
                        <td>
                            <button id="dosubmit" type="button" class="btn-b">确定</button>
                        </td>
                    </tr>
                </tbody>
            </table>
    </div>

</div>

{/block}

{block name="script"}
<script type="text/javascript">
$('#dosubmit').on('click', function(e) {
    var oldPwd = $('#oldPwd').val();
    var newPwd = $('#newPwd').val();
    var newPwdConfirm = $('#newPwdConfirm').val();

    if ( !oldPwd ) {
        return layer.msg( '请先输入原密码',{icon : 2,anim:6} );
    }
    if ( !newPwd ) {
        return layer.msg( '请输入新密码',{icon : 2,anim:6} );
    }
    if ( newPwd.length<6 || newPwd.length>16 ) {
        return layer.msg( '密码必须为6-16位的英文或数字组合',{icon : 2,anim:6} );
    }
    if ( newPwd != newPwdConfirm ) {
        return layer.msg( '新密码和确认密码输入不一致，请重新输入',{icon : 2,anim:6} );
    }
    // console.log(oldPwd,newPwd,newPwdConfirm );
    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/pass")}',
        type: "post",
        dataType: "json",
        data: {oldPwd:oldPwd,newPwd:newPwd,newPwdConfirm:newPwdConfirm},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        layer.close(la_load);
                        window.location.href = '{:url("common/login")}';
                    },999);
                return;
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });
});

</script>
{/block}
