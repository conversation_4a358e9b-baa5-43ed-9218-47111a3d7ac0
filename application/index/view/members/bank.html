{extend name="members/common" /}

{block name="title"}银行卡管理{/block}

{block name="main"}

<div class="space-right">
    <h2><strong>银行卡管理</strong></h2>
    <div class="ms-c6">
        <div class="ms-c6-m" style="padding-top: 10px">
            {eq name="$id_status" value="1"}
            
            <div class="formbox">
                <table>
                    <tbody>
                        <tr>
                            <th>您认证的银行卡：</th>
                            <td>
                                {volist name="data" id="vo" empty="未绑定银行卡"}
                                <div class="card">
                                    <div class="bank-logo" style="width:115px;font-size: 18px;">
                                        <!-- <img src="" alt="{$vo.bank_name}"> -->
                                        {$vo.bank_name}
                                    </div>
                                    <span>已绑定</span>
                                    <div class="clearfix"></div>
                                    <div class="inpbox">
                                        <i>{$vo.number_display}</i>
                                    </div>
                                </div>
                                {/volist}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <h4 style="text-indent: 20px;">添加银行卡</h4>
                <span style="color:red;display: block;text-indent: 25px;">
                    注意：仅限同名添加{$global.band_bank}张银行卡
                </span>
                <span style="color:red;margin-bottom: 15px;display: block;text-indent: 25px;">
                    温馨提示：为了您的账户资金安全，银行卡绑定仅限实名认证本人的银行帐号，否则将无法提现
                </span>
                <!-- <form id="addMemberBank" method="POST" onsubmit="return false;"> -->
                    <table>
                        <tbody>
                            <tr>
                                <th>帐户名：</th>
                                <td>{$minfo.real_name|default='未实名'}</td>
                            </tr>
                            <tr>
                                <th>银行：</th>
                                <td>
                                    <div class="sbox" style="float:left;">
                                        <div>
                                            <input class="inp" readonly="readonly" name="bank" value="选择银行" style="width:165px;">
                                        </div>
                                        <div class="s-option" style="width:175px;">
                                            <ul>
                                                <li data-sign="">选择银行</li>
                                                {volist name="bank_list" id="vo"}
                                                <li data-sign="{$key}">{$vo}</li>
                                                {/volist}
                                            </ul>
                                        </div>
                                    </div>
                                    <input class="inp c-inp" style='margin-top:3px;display: none;'  type='text' name='otherbank' id='otherbank' Placeholder='请填写银行名称'/>
                                    <font style="margin-top:10px;" id="bankCode_error_msg" color="red">*</font>
                                    
                                    <input id="bankCode" name="bankCode" value="" type="hidden">
                                </td>
                            </tr>
                            <tr>
                                <th>开户行所在地：</th>
                                <td>
                                    <select class="sel" id="selProvince" name="province" onchange="changeCity()">
                                        <option value="">请选择</option>
                                            
                                    </select>
                                    <select class="sel" id="selCity" name="city">
                                        <option value="">请选择</option>
                                    </select>
                                    <font id="province_error_msg" color="red">*</font>
                                </td>
                            </tr>
                            <tr>
                                <th>开户行：</th>
                                <td>
                                    <div class="kh-box">
                                        <input class="inp c-inp" maxlength="20" name="address" value="" placeholder='请输入开户行名称' type="text">
                                        <font id="bankCardNumer_error_msg" color="red">*</font>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>银行卡：</th>
                                <td>
                                    <div class="kh-box">
                                        <input class="inp c-inp" maxlength="19" id="number" name="number" value="" placeholder='请输入银行卡号' type="text">
                                        <font id="bankCardNumer_error_msg" color="red">*</font>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th></th>
                                <td>
                                    <button id="saveWithdrawBank" type="button" class="btn-b">提交</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                <!-- </form> -->
            </div>

            {else/}
            <div style="text-align: center;line-height: 100px;font-size: 18px;color:#F60;">
                您的身份信息未绑定，<a href="{:url('members/real')}">去绑定</a>
            </div>
            {/eq}
    
        </div>
    </div>
</div>

{/block}

{block name="script"}
{load href="/static/js/area2.js" /}
<script type="text/javascript">
$('#saveWithdrawBank').click(function() {
    var bankCode = $('input[name=bankCode]').val();
    var otherbank = $.trim($("#otherbank").val());
    var province = $('select[name=province]').val();
    var city = $('select[name=city]').val();
    var number = $('input[name=number]').val();
    var address = $('input[name=address]').val();

    if( !bankCode ){
        return layer.msg( '请选择银行',{icon : 2,anim:6} );
    }
    if( bankCode=="other" && !otherbank ){
        return layer.msg( '请输入其他银行名称',{icon : 2,anim:6} );
    }
    if ( !province || !city ) {
        return layer.msg( '请选择开户行所在地',{icon : 2,anim:6} );
    }
    if( !(/^[\u4e00-\u9fa5]+$/.test(address)) ){
        return layer.msg( '开户行必须是中文',{icon : 2,anim:6} );
    }
    if ( !number) {
        return layer.msg( '银行卡号不能为空',{icon : 2,anim:6} );
    }
    if( !(/^\d{16}|\d{19}$/.test(number)) ){
        return layer.msg( '银行卡号一般为16位或19位数字',{icon : 2,anim:6} );
    }

    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/bank")}',
        type: "post",
        dataType: "json",
        data: {bankCode:bankCode,otherbank:otherbank,province:province,city:city,address:address,number:number},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        layer.close(la_load);
                        // window.location.href = member_index;//页面刷新
                        window.location.href = '{:url("members/bank")}';
                    },999);
                return;
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });
    
});

$(function() {
    $(".sbox .inp").click(function() {
        $(this).parents(".sbox").children(".s-option").toggleClass("show");
    });
    $(".s-option li").click(function() {
        $(this).parents(".sbox").children().children(".inp").val($(this).html());
        $("#bankCode").val( $(this).data('sign') );
        $(this).parents(".s-option").toggleClass("show");
        
        if( $(this).data('sign')=="other" ){
            $("#otherbank").show();
        }else{
            $("#otherbank").hide().val("");
        }
    });

    addProvince();//加载页面时候添加省份下拉框里面的信息
});


</script>
{/block}
