{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/M/user.css" /}

<style type="text/css">
</style>

<div class="stock-main">
    <div class="box_prompt">
        配资用户的可用资金大于总操盘资金，且只能提取盈利部分，提取盈利最少为{$sy_set.0}元，<br>
        以{$sy_set.1}元单位为递增，即可申请提取盈利。
    </div>
    
    <div class="box_word">
        提取金额：<input type="text" value="{$sy_set.0}" class="draw-number" id="money" autocomplete="off">&nbsp;元
    </div>

    <div class="operate-group">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消提盈</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认提盈</a>
    </div>
    
</div>

<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
var min = parseFloat('{$sy_set.0}');
var yu = parseFloat('{$sy_set.1}');
function apply(){
    if( !id ){
        return layer.msg('数据不能为空，请按照平台规则重试！', { icon: 2,anim: 6 });
    }
    var money = parseFloat($('#money').val());
    if( isNaN(money) || money<=0 ){
        return layer.msg('提盈金额错误，请按照平台规则重试！', { icon: 2,anim: 6 });
    }
    if( money<min ){
        return layer.msg('提盈金额不得小于'+min+'元', { icon: 2,anim: 6 });
    }
    if( money%yu != 0 ){
        return layer.msg('提盈金额必须是'+yu+'的整倍数', { icon: 2,anim: 6 });
    }

    if( category==1 ){
        var url = "{:url('stock/drawprofitApply')}";
    }else{
        var url = "{:url('future/drawprofitApply')}";
    }

	// console.log(money ,yu,);return;
	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	$.ajax({
        url: url,
        type: "post",
        dataType: "json",
        data: {id:id,money:money},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );
            	// var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){
					// parent.layer.close(index);
                    parent.location.reload();
				}, 999);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}

function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}

layui.use('layer', function(){
    layer = layui.layer;
});
</script>