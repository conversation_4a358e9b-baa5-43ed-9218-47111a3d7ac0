{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/underscore-min.js" /}
{load href="/static/js/jquery172.js" /}
{load href="/static/css/M/user.css" /}

<style type="text/css">
.stock-main .box_word{ margin-top: 6px; }
</style>

<div class="stock-main">
    <div class="box_prompt">
        1. 扩大配资产生的利息费，需要一次性付清。 <br>
        2. 按月配资以30天为一个月计算，不满一个月的部分，按照占30天的百分比计算。
    </div>
    
    <p style="text-align: center;margin-top:20px;">当前账户余额：<span>{$minfo.account_money/100}</span>元</p>
    
    <div class="box_word">
        扩大金额：<input type="text" class="draw-number" id="money" value="{$kd_set.0}" autocomplete="off">&nbsp;元
    </div>

    <p style="text-align: center;margin-top: 6px;">手续费：<span id='j-fee'>&nbsp;</span></p>

    <div class="operate-group">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消扩大</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认扩大</a>
    </div>
    
</div>

<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
var min = parseInt('{$kd_set.0}');
var max = parseInt('{$kd_set.1}');
var yu = parseInt('{$kd_set.2}');
$(function(){
    $("#money")
        .on("keyup", _.debounce(function (e) {
            $("#j-fee").text("计算中...");
            var money = parseInt( $(this).val() );
            $.ajax({
                url: "{:url('stock/calculate_rate')}",
                type: "post",
                dataType: "json",
                data: {id:id,money:money},
                success: function(d,e,x){
                    // console.log(d);
                    $("#j-fee").text(d.fee);
                }
            });
        }, 300)).trigger('keyup');
})
function apply(){
    if( !id ){
        return layer.msg('数据不能为空，请按照平台规则重试！', { icon: 2,anim: 6 });
    }
    var money = parseInt( $('#money').val() );    
    if( isNaN(money) || money<=0 ){
        return layer.msg('扩大金额错误，请按照平台规则重试！', { icon: 2,anim: 6 });
    }
    if( money<min ){
        return layer.msg('扩大金额不得小于'+min+'元', { icon: 2,anim: 6 });
    }
    if( money>max ){
        return layer.msg('扩大金额不得大于'+max+'元', { icon: 2,anim: 6 });
    }
    if( money%yu != 0 ){
        return layer.msg('扩大金额必须是'+yu+'的整倍数', { icon: 2,anim: 6 });
    }

    if( category==1 ){
        var url = "{:url('stock/addfinancingApply')}";
    }else{
        var url = "{:url('future/addfinancingApply')}";
    }
    // console.log(yu);return;
	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	$.ajax({
        url: url,
        type: "post",
        dataType: "json",
        data: {id:id,money:money},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );
            	// var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){
					// parent.layer.close(index);
                    parent.location.reload();
				}, 999);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}

function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}

layui.use('layer', function(){
    layer = layui.layer;
});
</script>