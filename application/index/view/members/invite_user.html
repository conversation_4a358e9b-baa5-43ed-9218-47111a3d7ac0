{extend name="members/common" /}

{block name="title"}推广用户{/block}

{block name="main"}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
<div class="space-right">
    <h2><strong>推广用户</strong></h2>
    <div class="ms-c6">
        <div class="ms-c6-t">
            <ul>
                <li class="">
                    <a href="{:url('members/invite')}">我要推广</a>
                </li>
                <li class="current">
                    <a>推广用户</a>
                </li>                       
            </ul>
        </div>
        <div class="ms-c6-m" style="padding: 0 10px 20px;">
            <!-- <div class="ms-c6-ts">
                <div class="formbox">
                    <div class="ms-c6-b">
                    </div>
                </div>
            </div> -->
            <div class="search-box">
                <form class="layui-form layui-form-pane" action="{:url('members/inviteUser')}" id="subform">
                    <div class="layui-inline">
                        <label class="layui-form-label">注册时间</label>
                        <div class="layui-input-inline" style="width: 226px;">
                            <input type="text" value="{$timestr}" class="layui-input" id="test6" placeholder="开始 到 结束" autocomplete="off">
                        </div>
                    </div>
                    <input type="hidden" name="time" value="">
                </form>
            </div>

            <div class="ms-c6-table clearfix">
                <table>
                    <tbody>
                        <tr>
                            <th style="">用户名</th>
                            <th style="">真实姓名</th>
                            <th>上次登录时间</th>
                            <th>注册时间</th>
                        </tr>
                        {volist name="data" id="vo" empty="$empty_data"}
                        <tr>
                            <td>{$vo.user_name_display}</td>
                            <td>{$vo.real_name_display}</td>
                            <td>{:date('Y-m-d H:i', $vo.last_log_time)}</td>
                            <td>{:date('Y-m-d H:i', $vo.reg_time)}</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
                <div class="pager2">
                    {$page}
                </div>
            </div>

        </div>


    </div>
</div>

{/block}

{block name="script"}
{load href="/static/laydate-v5.0.9/laydate.js" /}
<script type="text/javascript">
laydate.render({
    elem: '#test6'
    ,range: true
    // ,format: 'yyyy年M月d日'
    ,done: function(value, date, endDate){
        $('input[name=time]').val(value);
        $('#subform').submit();
    }
});
</script>
{/block}
