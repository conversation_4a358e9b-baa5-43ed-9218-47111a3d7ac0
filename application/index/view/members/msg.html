{extend name="members/common" /}

{block name="title"}站内信息{/block}

{block name="main"}
<style type="text/css">
.space-right .yd_btn{
    color: #fff;
    padding: 5px 15px;
    line-height: 30px;
    border-radius: 3px;
    background-color: #cf2d36;
    cursor:pointer;
}
.inner-main{
    border: 1px solid #eee;
    margin: 15px 0 0;
    padding: 0 15px;
    min-height: 380px;
}
.df_l .no,.df_l .yes {
    width: 8px;
    height: 8px;
    border-radius: 100%;
    background-color: #cf2d36;
    display: -webkit-inline-box;
    margin-right: 7px;
}
.df_l .yes{ background-color: #b7b7b7 !important; }
.df_l{ float: left; }
.time_r{
    float: right;
    color: #6b6767;
}
ul .msg{
    margin: 15px 0;
}
body .demo-class .layui-layer-btn0{
    border-color: #cf2d36;
    background-color: #cf2d36;
    color: #ffffff;
}
</style>
<div class="space-right">
    <h2></notempty><strong>站内信息</strong></h2>
    <button class="yd_btn" onclick="setAllRead()">全部标为已读</button>

    <div class="ms-c6">
        <div class="inner-main clearfix">
            <ul style="cursor:pointer;">

                {volist name="data" id="vo" empty="$empty_data"}
                <li class="msg clearfix" onclick="readThis(this)" innerid="{$vo.id}" status="{$vo.status}">
                    <div class="df_l">
                        {eq name="vo.status" value="0"}
                        <span class="status no"></span>
                        {else/}
                        <span class="status yes"></span>
                        {/eq}
                        <span class="content" title="{$vo.title}" info="{$vo.msg}">{:cnsubstr($vo.msg,57)}</span>
                    </div>
                    <div class="time_r">{:date('Y-m-d H:i',$vo.send_time)}</div> 
                </li>
                {/volist}
            </ul>
        </div>
    
        <div class="pager2">
            {$page}
        </div>
    </div>
</div>
<div id="hide" style="display:none;">
    <div id="content" style="padding:10px 20px;">
    </div>
</div>
{/block}

{block name="script"}
<script type="text/javascript">
function readThis(obj){
    var id = $(obj).attr('innerid');
    var status = $(obj).attr('status');
    $('#content').html('').text( $(obj).find('.content').attr('info') );
    var title = $(obj).find('.content').attr('title');
    var la_load = layer.open({
        title: title,
        type: 1,
        skin: 'demo-class',
        btn: ['确定'],
        area: ['500px', '300px'],
        shade: [0.15,'#000'],
        content: $('#hide').html() //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
    });

    if( status==0 ){
        $.ajax({
            url: '{:url("members/msg")}',
            type: "post",
            dataType: "json",
            data: {id:id,action:'one'},
            success: function(d,e,x){
                if(d.status==1){
                    $(obj).attr('status',1).find('.status').addClass('yes');
                }else{
                    layer.close(la_load);
                    return layer.msg(d.message,{icon:2});
                }
            }
        });
    }

}
function setAllRead(){
    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/msg")}',
        type: "post",
        dataType: "json",
        data: {action:'all'},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        // layer.close(la_load);
                        parent.location.reload();
                    },999);
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });

}
</script>
{/block}
