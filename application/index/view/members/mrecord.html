{extend name="members/common" /}

{block name="title"}资金明细{/block}

{block name="main"}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
<style>
.search-box{
    padding: 20px 0;
}
.layui-form-label{
    padding: 9px 15px 0 0;
}
</style>
<div class="space-right">
    <h2><strong>资金记录</strong></h2>
   
    <div class="ms-c6">
        <!-- <div class="ms-c6-t">
            <ul id="accountLogTypes">
                <li class="current">
                    <a href="">全部</a>
                </li>
            </ul>
        </div> -->
        <div id="accountLogList">
            <div class="ms-c6-m" style="padding: 0 10px 20px;border-top: none;">
                <div class="search-box">
                    <form class="layui-form layui-form-pane" action="{:url('members/mrecord')}" id="subform">
                        <div class="layui-inline">
                            <label class="layui-form-label">时间范围</label>
                            <div class="layui-input-inline" style="width: 226px;">
                                <input type="text" value="{$timestr}" class="layui-input" id="test6" placeholder="开始 到 结束" autocomplete="off">
                            </div>
                        </div>
                        <input type="hidden" name="time" value="">
                    </form>
                </div>

                <div class="ms-c6-table clearfix">
                    <table>
                        <tbody>
                            <tr>
                                <th style="width:16%">交易时间</th>
                                <th style="width:15%">类型</th>
                                <th>影响金额</th>
                                <th>账户余额</th>
                                <th>管理费余额</th>
                                <th>冻结资金</th>
                                <th style="width:25%">备注</th>
                            </tr>
                            {volist name="data" id="vo" empty="$empty_data"}
                            <tr>
                                <td>{:date('Y-m-d H:i',$vo.add_time)}</td>
                                <td>{$trade_type[$vo.type]}</td>
                                <td>{$vo.affect_money / 100}</td>
                                <td>{$vo.account_money / 100}</td>
                                <td>{$vo.interest_money / 100}</td>
                                <td>{$vo.freeze_money / 100}</td>
                                <td title="{$vo.info}">{:cnsubstr($vo.info,15)}</td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                    <div class="pager2">
                        {$page}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="script"}
{load href="/static/laydate-v5.0.9/laydate.js" /}
<script type="text/javascript">
laydate.render({
    elem: '#test6'
    ,range: true
    // ,format: 'yyyy年M月d日'
    ,done: function(value, date, endDate){
        $('input[name=time]').val(value);
        $('#subform').submit();
    }
});
</script>
{/block}
