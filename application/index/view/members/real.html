{extend name="members/common" /}

{block name="title"}实名认证{/block}

{block name="main"}
<div class="space-right">
    <h2><strong>实名认证</strong></h2>

    {eq name="minfo.id_status" value="1"}
    
    <div class="prompt-box">
        <p><strong style='padding:0;'>已通过实名认证！</strong></p>
        <p>
            <br>如果还没有完成银行卡绑定，您可以选择
                <a href="{:url('members/bank')}" style="color:#F60;">绑定银行卡</a>
        </p>
    </div>
    <div class="formbox">
        <table>
            <tbody>
                <tr>
                    <th>真实姓名：</th>
                    <td>{$minfo.realname_display}</td>
                </tr>
                <tr>
                    <th>证件类型：</th>
                    <td>身份证</td>
                </tr>
                <tr>
                    <th>证件号码：</th>
                    <td>{$minfo.idcard_display}</td>
                </tr>
                <tr>
                    <th>认证状态：</th>
                    <td>
                        <strong style="color:gray">已认证</strong></td>
                </tr>
                <!--tr>
                    <th>认证申请时间：</th>
                    <td>1970-01-01 08:00:00</td>
                </tr-->
            </tbody>
        </table>
    </div>


    {else/}

    <div class="ms-c6">
        <div class="formbox">
            <table>
                <tbody>
                    <tr>
                        <th>真实姓名：</th>
                        <td><input value="" name="realName" class="inp" id="name" type="text"></td>
                    </tr>
                    <tr>
                        <th>身份证号码：</th>
                        <td>
                            <input value="" name="identityCard" class="inp" id="number" type="text"/>
                        
                            <input value="" id="province" type="hidden"/>
                            <input value="" id="birthday" type="hidden"/>
                            <input value="" id="age" type="hidden"/>
                            <input value="" id="sex" type="hidden"/>
                        </td>
                    </tr>

                    <tr>
                        <th>&nbsp;</th>
                        <td>
                            <button id="submitBtn" class="btn-b" onclick="doSubmit();" type="button" style="margin:20px 0;width:180px;">确定</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="ms-c6-b">
                <dl>
                    <dt>
                        <font color="red">温馨提示：</font>
                    </dt>
                    <dd>认证后身份信息不可修改；且之后仅限添加本人的银行卡！</dd>
                </dl>
            </div>
        </div>
    </div>

    {/eq}
    

</div>
{/block}

{block name="script"}
{load href="/static/js/M/idvalidate.js" /}
<script type="text/javascript">
function doSubmit() {
    var name = $('#name').val();
    var number = $('#number').val();
    if ( name == '') {
        return layer.msg( '请输入您的真实姓名！',{icon : 2,anim:6} );
    }
    var reg=/^[\u2E80-\u9FFF]+$/;//Unicode编码中的汉字范围
    if(!reg.test(name)){
        return layer.msg( '真实姓名输入错误',{icon : 2,anim:6} );
    }
    if (!IdCardValidate( number )) {
        return layer.msg( '身份证号码不正确！',{icon : 2,anim:6} );
    } else {
        setInfo( number , 'sex', 'birthday', 'province', 'age');
    }
    var sex = $('#sex').val();
    var province = $('#province').val();

    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/real")}',
        type: "post",
        dataType: "json",
        data: {name:name,number:number,sex:sex,province:province},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        layer.close(la_load);
                        // window.location.href = member_index;//页面刷新
                        window.location.href = '{:url("members/real")}';
                    },999);
                return;
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });

}

</script>
{/block}
