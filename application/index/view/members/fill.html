{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/M/user.css" /}

<style type="text/css">
.stock-main .box_word{ margin-top: 6px; }

</style>

<div class="stock-main">
    <div class="box_prompt">
        温馨提示：补充亏损最小额度{$fill_set.0}，金额是{$fill_set.1}的整倍数，注：追加保证金没有放大资金的效果。 
        如需放大资金，请申请扩大配资。
    </div>

    <p style="text-align: center;margin-top:20px;">当前账户余额：<span>{$minfo.account_money/100}</span>元</p>

    <div class="box_word">
        补亏金额：<input type="text" value="{$fill_set.0}" class="draw-number" id="money" autocomplete="off">&nbsp;元
    </div>

    <div class="operate-group">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消补亏</a>
        <a href="javascript:;" class="btn btn-primary" onclick="sub()" >确认补亏</a>
    </div>
    
</div>

<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
var min = parseFloat("{$fill_set.0}");
var yu = parseFloat("{$fill_set.1}");
var account_money = parseFloat("{$minfo.account_money/100}");
function sub(){
    var money = parseFloat($('#money').val());
    if( !id ){
        return layer.msg( '数据解析错误，请按照平台规则重试！',{icon : 2,anim:6} );
    }
    if( !money || money<min ){
        return layer.msg( '补亏金额不能小于'+min+'元',{icon : 2,anim:6} );
    }
    if( money%yu != 0 ){
        return layer.msg('补亏金额必须是'+yu+'的整倍数', { icon: 2,anim: 6 });
    }
    if( account_money<money ){
        return layer.msg( '余额不足，请先充值',{icon : 2,anim:6} );
    }

    if( category==1 ){
        var url = "{:url('stock/fillApply')}";
    }else{
        var url = "{:url('future/fillApply')}";
    }

    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: url,
        type: "post",
        dataType: "json",
        data: {id:id,money:money},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        // layer.close(la_load);
                        // window.location.href = '{:url("members/index")}';
                        parent.location.reload();
                    },999);
                return;
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });

}
function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}

layui.use('layer', function(){
    layer = layui.layer;
});
</script>