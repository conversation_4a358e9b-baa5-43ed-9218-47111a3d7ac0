{extend name="members/common" /}

{block name="title"}用户中心{/block}

{block name="main"}
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<style type="text/css">
.table{font-size:14px}
.table td{height:40px;padding-left: 40px;border: 1px solid #eee;padding-right:20px;text-align: left}
.table th{width:80px;height:40px;text-align: right;border: 1px solid #eee;padding-right:20px}
.recharge td{text-align: left;padding-left: 40px!important}
.ms-c6-m h3{
    color: #000;
    line-height: 60px;
}
.formbox{
    margin: 0 0 10px;
}
</style>
<div class="space-right">
    <h2><strong>账户充值</strong><span></span></h2>
    <div class="ms-c6">
        <div class="ms-c6-m" style="padding: 0 10px 20px;">

            {eq name="$minfo.id_status" value="1"}

            <h3>请选择支付方式</h3>
            <div class="ms-c6-pay clearfix">
                <ul>
                    {if condition="$offline_status EQ 1"}
                    {volist name="offline" id="vo" key="key"}
                    <li way="offline">银行转账{$key}</li>
                    {/volist}
                    {/if}
                    {if condition="$weixin.status EQ 1 || $weixin.payee EQ 1"}
                    <li way="wx">微信支付</li>
                    {/if}
                    {if condition="$zhifubao.status EQ 1 || $zhifubao.payee EQ 1"}
                    <li way="zfb">支付宝支付</li>
                    {/if}
                </ul>
            </div>

            {if condition="$offline_status EQ 1"}
            {volist name="offline" id="vo" key="key"}
            <div class="pay_block" style="display: none">
                <table class="table">
                    <tr>
                        <th>收款人：</th>
                        <td>{$vo.payee}</td>
                    </tr>
                    <tr>
                        <th>开户行：</th>
                        <td>{$vo.open_bank}</td>
                    </tr>
                    <tr>
                        <th>银行名称：</th>
                        <td>{$vo.bank_name}</td>
                    </tr>
                    <tr>
                        <th>银行账户：</th>
                        <td>{$vo.number}</td>
                    </tr>

                    <tr>
                        <th>充值说明：</th>
                        <td>
                            <br>
                            {:htmlspecialchars_decode($vo.content)}
                        </td>
                    </tr>
                </table>
            </div>
            {/volist}
            {/if}

            {if condition="$weixin.status EQ 1 || $weixin.payee EQ 1"}
            <div class="pay_block" style="display: none">
                <table class="table" style="width:100%">
                    {if condition="$weixin.status EQ 1"}
                    <tr id="wxPicEnable">
                        <th style="width:120px">二维码</th>
                        <td><img src="{$weixin.qrcode}" width="155"/></td>
                    </tr>
                    {/if}
                    {if condition="$weixin.payee EQ 1"}
                    <tr id="wxAccEnable">
                        <th style="width:120px">微信账号</th>
                        <td>
                            <input id="wxacccode" value="{$weixin.number}" type="text" readonly>
                        </td>
                    </tr>
                    {/if}
                    <tr>
                        <th>说明</th>
                        <td>
                            {:htmlspecialchars_decode($weixin.content)}
                        </td>
                    </tr>
                </table>
            </div>
            {/if}

            {if condition="$zhifubao.status EQ 1 || $zhifubao.payee EQ 1"}
            <div class="pay_block" style="display: none">
                <table class="table" style="width:100%">
                    {if condition="$zhifubao.status EQ 1"}
                    <tr id="zfbPicEnable">
                        <th style="width:120px">二维码</th>
                        <td><img id="zfbcode" src="{$zhifubao.qrcode}" width="180"/></td>
                    </tr>
                    {/if}
                    {if condition="$zhifubao.payee EQ 1"}
                    <tr id="zfbAccEnable">
                        <th style="width:120px">支付宝账号</th>
                        <td>
                            <input id="zfbacccode" value="{$zhifubao.number}" type="text" readonly>
                        </td>
                    </tr>
                    {/if}
                    <tr>
                        <th>说明</th>
                        <td>
                            {:htmlspecialchars_decode($zhifubao.content)}
                        </td>
                    </tr>
                </table>
            </div>
            {/if}


            <h3>请填写充值金额</h3>
            <div class="formbox">
                <form id="chargeForm" action="" onsubmit="return false;">
                    <input name="way" type="hidden" value="" />
                    <table class="recharge" align="left">
                        <!-- <tr>
                            <th>可用余额：</th>
                            <td><strong class="c-red">0.00</strong>元</td>
                        </tr> -->
                        <tr>
                            <th>已付金额：</th>
                            <td>
                                <input type="text" id="money" placeholder='请输入充值金额' style="height:25px" class="inp" maxlength="10" autocomplete="off"/>
                                元
                            </td>
                        </tr>
                        <tr>
                            <th>付款账号：</th>
                            <td>
                                <input type="text" id="account" placeholder='请输入付款账号' style="height:25px" class="inp" maxlength="20" autocomplete="off"/>
                            </td>
                        </tr>

                        <!--<tr>
                            <th>订单流水号：</th>
                            <td>
                                <input type="text" id="tran_id" placeholder='请输入订单流水号' value="" style="height:25px" class="inp" maxlength="20" autocomplete="off"/>

                            </td>
                        </tr>-->
                        <tr>
                            <th></th>
                            <td>
                                <button id="addBankBtn" class="btn-b" type="submit">提 交</button>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div class="ms-c6-b" id="banks_desc">
                <dl>
                    <dt>温馨提示：</dt>
                    <!-- <dd>1.为了您资金安全，请您充值前先实名认证和绑定银行卡号。</dd> -->

                    {:get_ad(22)}

                </dl>
            </div>

            {else/}
            <div style="text-align: center;line-height: 100px;font-size: 18px;color:#F60;">
                您的身份信息未绑定，<a href="{:url('members/real')}">去绑定</a>
            </div>
            {/eq}


        </div>

    </div>
</div>

{/block}

{block name="script"}
<script type="text/javascript">
$('#addBankBtn').on('click', function(event) {
    var money = parseFloat($('#money').val());
    var account = $('#account').val();
    var tran_id = $('#tran_id').val();
    var way = $('input[name=way]').val();

    if( isNaN(money) || money<=0 ){
        return layer.msg('充值金额不能小于0', { icon: 2,shade: [0.1, '#000'] });
    }
    if( !account ){
        return layer.msg('付款账号信息不能为空', { icon: 2,shade: [0.1, '#000'] });
    }
    // console.log(money,account,number,way);return
    var la_load = layer.load(0,{
                shade: [0.15,'#000']
            });
    $.ajax({
        url: '{:url("members/recharge")}',
        type: "post",
        dataType: "json",
        data: {money:money,account:account,tran_id:tran_id,way:way},
        success: function(d,e,x){
            if(d.status==1){
                layer.msg(d.message,{icon:1});
                var t1 = setTimeout(function(){
                        // layer.close(la_load);
                        window.location.href = '{:url("members/index")}';
                    },999);
                return;
            }else{
                layer.close(la_load);
                return layer.msg(d.message,{icon:2});
            }
        }
    });

});


$('.ms-c6-pay li').each(function(index, el) {
    $(this).on('click', function(event) {
        $(this).addClass("selected").siblings('li').removeClass("selected");
        // console.log(index);
        var way = $(this).attr('way');
        $('input[name=way]').val(way);
        $('.pay_block').eq(index).show().siblings('.pay_block').hide();
    });
});

$(".ms-c6-pay li").first().click();

</script>
{/block}
