{extend name="members/common" /}

{block name="title"}我的配资{/block}

{block name="main"}
<div class="space-right">
    <h2></notempty><strong>我的配资</strong></h2>

    <div style="min-height: 450px;">
        {volist name="data" id="vo" empty="$empty_data"}
        <div class="pz_mlist">
            <div class="table_top">
                <div class="operate_state">
                    {eq name="vo.category" value="1"}股票{else/}期货{/eq}配资(编号：{$vo.id})
                    <span class="state_all {$vo.status_class}"></span>
                </div>
                <a href="{:url('common/hetong',['id'=>$vo.id,'c'=>$vo.category])}" class="wqht" target="_blank">查看网签合同</a>
            </div>
            <table class="table_user" cellpadding="0" cellspacing="0" width="100%">
                <tbody>
                    <tr>
                        <th>类型</th>
                        <th>保证金</th>
                        <th>借入资金</th>
                        <th>总操盘资金</th>
                        <th>总利息</th>
                        <th>警戒线</th>
                        <th>平仓线</th>
                        <th>操盘时间</th>
                    </tr>
                    <tr>
                        <td width="8%">{:str_replace([1,2,3,4,8], ['按天','按月','免息','体验','VIP'], $vo.type)}</td>
                        <td width="11%">{$vo.deposit_money/100}</td>
                        <td width="12%">{php}echo ($vo['borrow_money']-$vo['deposit_money'])/100;{/php}</td>
                        <td width="12%">{$vo.borrow_money/100}</td>
                        <td width="11%">{$vo.totalinterest/100}</td>
                        <td width="12%">{$vo.loss_warn/100}</td>
                        <td width="12%">{$vo.loss_close/100}</td>
                        <td width="22%">
                            {:date('Y/m/d',$vo.add_time)} ~ {:date('Y/m/d',$vo.end_time)}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="user_oper tradeAct clearfix">
                <ul>
                    {if condition="$vo.status==2"}
                        <li>
                            <a class="menu_white btn-stop-financing" onclick="addIframe('查看交易密码','{:url("members/stock_account",["id"=>$vo.id])}');">查看交易密码</a>
                        </li>
                        
                        {if condition="($vo.type==1) OR ($vo.type==2) OR ($vo.type==8)"}
                        <li>
                            <a class="menu_white" onclick="addIframe('扩大配资', '{:url("members/addfinancing",["id"=>$vo.id,'c'=>$vo.category])}' )">扩大配资</a>
                        </li>
                        <li>
                            <a class="menu_white btn-renewal" onclick="addIframe('申请延期', '{:url("members/renewal",["id"=>$vo.id,"type"=>$vo.type,'c'=>$vo.category])}'  );">申请延期</a>
                        </li>
                        {/if}

                        {if condition="$vo.type NEQ 4"}
                        <li>
                            <a class="menu_white btn-add-deposit" onclick="addIframe('补充亏损','{:url("members/fill",["id"=>$vo.id,'c'=>$vo.category])}');">补充亏损</a>
                        </li>
                        <li>
                            <a class="menu_white btn-draw-profit" onclick="addIframe('提取盈利','{:url("members/drawprofit",["id"=>$vo.id,'c'=>$vo.category])}');">提取收益</a>
                        </li>
                        {/if}

                        <li>
                            <a class="menu_white btn-stop-financing" onclick="addIframe('终止操盘', '{:url("members/stopfinancing",["id"=>$vo.id,'c'=>$vo.category])}');">终止操盘</a>
                        </li>
                        
                    {/if}
                </ul>
            </div>                            
        </div>
        {/volist}

    </div>

    <div class="pager2">
        {$page}
    </div>
</div>
{/block}

{block name="script"}
<script type="text/javascript">
function addIframe(title,url){
    layer.open({
        type: 2,
        title: title,
        maxmin: false,
        shadeClose: false,
        area : ['680px' , '338px'],
        content: url,
        end: function(){
            // location.reload();
        }
    });
}
</script>
{/block}
