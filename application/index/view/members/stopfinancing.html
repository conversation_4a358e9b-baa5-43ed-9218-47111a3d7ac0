{__NOLAYOUT__}
{load href="/static/layui-v2.4.5/layui/css/layui.css" /}
{load href="/static/layui-v2.4.5/layui/layui.js" /}

{load href="/static/js/jquery172.js" /}
{load href="/static/css/M/user.css" /}

<style type="text/css">
.stock-main .box_prompt{padding: 8px 28px;}
</style>

<div class="stock-main">
    <p style="font-size: 16px;margin-bottom: 20px;">您确定要申请终止操盘吗？</p>
    
    <div class="box_prompt">
        1、请确保您的交易账户已经卖出，否则我们有权把您持有进行强制处理（不保证平仓价）
        <br>
        2、提前申请终止操盘，剩余配资利息不予退回。
    </div>

    <br>

    <div class="operate-group">
        <a href="javascript:;" class="btn btn-cancel" onclick="cancel()">取消终止</a>
        <a href="javascript:;" class="btn btn-primary" onclick="apply()">确认终止</a>
    </div>
    
</div>

<script type="text/javascript">
var id = parseInt('{$id}');
var category = parseInt('{$category}');
function apply(){
    if( !id ){
        return layer.msg('数据不能为空，请按照平台规则重试！', { icon: 2,anim: 6 });
    }
    // console.log(id);return;
    
    if( category==1 ){
        var url = "{:url('stock/stopfinancingApply')}";
    }else{
        var url = "{:url('future/stopfinancingApply')}";
    }

	var la_load = layer.load(0,{
    			shade: [0.2,'#000']
    		});
	$.ajax({
        url: url,
        type: "post",
        dataType: "json",
        data: {id:id},
        success: function(d,e,x){  
            // console.log(d,e,x);
            if(d.status==1){
            	layer.msg( d.message ,{icon : 1} );
            	// var index = parent.layer.getFrameIndex(window.name);
				setTimeout(function(){
					// parent.layer.close(index);
                    parent.location.reload();
				}, 999);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message,{icon : 2} );
            }
        }
    });

}

function cancel(){
    //注意：parent 是 JS 自带的全局对象，可用于操作父页面
    var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
    parent.layer.close(index);
}

layui.use('layer', function(){
    layer = layui.layer;
});
</script>