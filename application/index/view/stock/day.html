{include file="public/header" /}
<title>按天配资 - {$global.web_name}</title>

{load href="/static/css/H/peizi.css" /}

<div class="pz_banner">
    <div class="banner_img">
        {:get_ad(27)}
    </div>
</div>

<div class="peizi_bg newallt"> 
    <div class="peizi_img">
        <h3 style="color:#282828; font-size: 16px; padding-bottom: 4px; border-bottom: 1px solid #ddd;font-weight: bold;margin: 16px 0px;">
            股票配资流程 
            <img src="/static/img/H/peiziimgxjt.jpg">
        </h3>
        <img src="/static/img/H/peiziimg2.jpg">

        <h3 style="color:#282828;font-size: 16px;padding-bottom: 4px;border-bottom: 1px solid #ddd;font-weight: bold;margin: 16px 0px;">
            股票配资优势 
            <img src="/static/img/H/peiziimgxjt.jpg">
        </h3>
        <img src="/static/img/H/peiziimg112.jpg">

        <h3 style="color:#282828; font-size: 16px; padding-bottom: 4px; border-bottom: 1px solid #ddd;font-weight: bold;margin: 16px 0px;">
            选择配资方案 
            <img src="/static/img/H/peiziimgxjt.jpg">
        </h3>

        <div style="width:100%; margin:0; padding:0; clear:both;">
            <div style="width:495px; padding-left:32px; float:left; border:1px solid #c2c2c2; display:block;min-height: 552px;">
                <h3 style="color:#ff6f06;font-size: 16px; font-weight: bold;margin: 16px 0px;">
                    第一步：
                    <span style="font-weight: 400" id='first-step'>输入投资本金</span>
                </h3>
                <p>
                    <input id="deposit_money" style="height: 50px; line-height: 50px; width: 400px; text-align: center; font-size: 18px;" onkeyup="value=value.replace(/[^\d]/g,'')" placeholder="最少{:getMoneyFormt($moneyRange.0)}，最多{:getMoneyFormt($moneyRange.1)}" type="text" maxlength="8">
                    <em style="font-size: 24px; font-style: normal; color: #666; margin-left: 10px;">元</em>
                </p>
                <p id="first-remark" style="color:#8e8e8e; margin: 10px 0; font-size: 13px;">
                    备注：不小于{:getMoneyFormt($moneyRange.0)}，不大于{:getMoneyFormt($moneyRange.1)}，且为{:getMoneyFormt($moneyRange.2)}的整数倍
                </p>

                <h3 style="color:#ff6f06;font-size: 16px; margin-top: 20px; font-weight: bold;">
                    第二步：
                    <span style="font-weight: 400">选择获得资金（选择杠杆，资金放大2-10倍）</span>
                </h3>
                <ul class="chooseUlLi" id='pz_sel' style="margin: 28px 0 0 0!important">
                    <!-- <li>
                        <a class="active" href="javascript:">
                            <i></i><strong>1</strong>倍
                            <p style="font-size: 0.75em;margin: -5px 0px;">
                                日利率 %                                            
                            </p>
                            <p><span class="times" rate="1" data-times="2">6000</span>元</p>
                        </a>
                    </li> -->
					
					{volist name="rateArr" id="vo"}
						<li>
                            <a href="javascript:" class="{eq name='key' value='0'}active{/eq} ">
                                <i></i>
                                <strong>{$vo.multiple}</strong>倍
                                <p style="font-size: 0.75em;margin: -5px 0px;">
                                    日利率{$vo.rate}%                                            
                                </p>
                                <p>
                                	<span class="times" data-rate="{$vo.rate}" data-multiple="{$vo.multiple}">0</span>元
                                </p>
                            </a>
                        </li>  
					{/volist}

                </ul>
            </div>
            <div style="width:520px; height:552px; padding-left:32px; float:right; border:1px solid #c2c2c2; display:block;">
                <h3 style="color:#ff6f06;font-size: 16px; font-weight: bold;margin: 16px 0px;">
                    第三步：
                    <span style="font-weight: 400">
                        选择你的配资周期
                    </span>
                </h3>
                <p style="font-size: 16px;color:#6a6a6a;">
                    期限：
                        <select id="tradecycle">
                            <!-- <option value="1">1个自然</option> -->

							{volist name="use_day" id="vo"}
                            <option value="{$vo}">{$vo}个交易日</option>
							{/volist}
                        </select>
                        <span style="margin-left:25px;">&nbsp;</span>
                    账户管理费：
                        <em id="accountManageFees" style='font-style: normal;color: #ff6f06'>0</em>元 / 交易日
                </p>
                <p style="color:#8e8e8e; margin: 10px 0; font-size: 13px;">
                    备注：按交易日收取管理费
                </p>
                <h3 style="color:#ff6f06;font-size: 16px; font-weight: bold;margin: 16px 0px;">
                    第四步：
                    <span style="font-weight: 400">
                        选择开始交易时间
                    </span>
                </h3>
                
                <p>
                	{eq name="couldApplyToday" value="2"}
                    <a class="isstartBtn active" data-startDate="2" href="javascript:void(0);" style="margin-right: 15px;padding: 3px 25px;display: inline-block;border: 1px solid #ccc;background: #FFF;color: #999;font-size: 13px;">下个交易日</a>
                	{else/}
                    <a class="isstartBtn active" data-startDate="1" href="javascript:void(0);" style="margin-right: 15px;padding: 3px 25px;display: inline-block;border: 1px solid #ccc;background: #FFF;color: #999;font-size: 13px;">立即生效</a>
                    <a class="isstartBtn" data-startDate="2" href="javascript:void(0);" style="margin-right: 15px;padding: 3px 25px;display: inline-block;border: 1px solid #ccc;background: #FFF;color: #999;font-size: 13px;">下个交易日</a>
					{/eq}
                </p>
                <h3 style="color:#ff6f06;font-size: 16px; font-weight: bold;margin: 16px 0px;">
                    第五步：
                    <span style="font-weight: 400">
                        确认配资信息
                    </span>
                </h3>
                <table class="table tableborder tabletree tablemony" style="width:95%;">
                    <tbody>
                        <tr>
                            <th width="100">配资须知</th>
                            <td>
                                盈利全归您 
                            </td>
                        </tr>
                        <tr>
                            <th>总配资资金</th>
                            <td class="colorfe5911"><em id="totalAmount">0</em>元 <small id="tAtips" style="color:#555">0(本金)+0(配资资金)</small></td>
                        </tr>
                        <tr>
                            <th>亏损警戒线</th>
                            <td class="colorfe5911">
                                <em id="warnLine">0</em>元
                                <small id='ksjjx' style="color:#555">
                                	(警戒线=配资资金+本金×{$day_loss.0}%)
                                </small>
                            </td>
                        </tr>
                        <tr>
                            <th>亏损平仓线</th>
                            <td class="colorfe5911">
                                <em id="outLine">0</em>元
                                <small id='kspcx' style="color:#555">
                                	(平仓线=配资资金+本金×{$day_loss.1}%)
                                </small>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <p>&nbsp;</p>
            </div>

            <div class="listgz margin-bottom15" style="padding-top: 30px;border-bottom:1px solid #ddd; clear:both; margin-top:40px">
                <p style="color:#ff6f06; text-align: center; margin: 10px 0; font-size: 16px;">
                    备注： 您需支付的金额为：保证金 <span id="bzjOK">0</span> + <span id="glfOK">0.00</span>元（管理费） = <span id="zjeOK">0</span> 元
                </p>

                <p style="padding: 10px;">如您不清楚规则，或有其他疑问，请联系在线客服或客服QQ：{$kefu.qq.0.number} </p>
                <p style="padding: 10px;">
                	<label>
                		<input id="agreement" type="checkbox" checked="checked">我已阅读并同意
                	</label>
                	<a href="/hetong" target="_blank" id="showsp" title="配资合作协议" style='color:#6a6a6a'>《配资合作协议》</a>
                </p>
                <p style="padding: 10px;"><input id="goPay" class="btn btnorg btnorg-sizebig radius5px" type="button" value="我要配资" onclick="return false;" style="height: 40px; line-height: 40px;"></p>
            </div>

            <div class="ctrlexp margin-bottom25 margin-left15">
            </div>
        </div>
    </div>

</div>
<script type="text/javascript">
$(function(){
})

var uid = "{$uid}";
var dayStockSet = {
        warnLine: {$day_loss[0]},
        closeLine: {$day_loss[1]}
    },
    type = 1,
    duration = {$use_day[0]},
    multiple = {$rateArr[0]['multiple']},
    rate = {$rateArr[0]['rate']},
    depositMoney = {$moneyRange[0]},
    gainMoney = 0,
    warnMoney = 0,
    totalRate = 0,
    closeMoney = 0,
    beishu = {$moneyRange[2]},
    borrow_money_list;
    borrow_money_list = $("#pz_sel").find("li");
// 第一步
$("#deposit_money").on('keyup',function(){
	depositMoney = isNaN(parseInt($(this).val())) ? 0 : parseInt($(this).val());

	calc_borrow_money(depositMoney);
	updateText( depositMoney, multiple, duration, rate );
}).on('blur', function(event) {
	// console.log( $(this).val() , {$moneyRange[0]} );
	if ( $(this).val() > {$moneyRange[1]} ){
        $(this).val( {$moneyRange[1]} );    
    }
    if( $(this).val() < {$moneyRange[0]} ){
        $(this).val( {$moneyRange[0]} );    
        calc_borrow_money( {$moneyRange[0]} );
    }

    depositMoney = parseInt( $(this).val() );
    updateText( depositMoney, multiple, duration, rate );
});
// 第二步
$("#pz_sel li").on("click",function(){
    $(this).find('a').addClass("active").parent().siblings().find('a').removeClass('active');
    //倍率
    multiple = $(this).find(".times").data("multiple");
    //天利率
    rate = parseFloat( $(this).find(".times").data("rate") );
    updateText(depositMoney, multiple, duration, rate);
})
// 第三步
$("#tradecycle").on("change",function(){
    duration = parseInt( $(this).val() );
    updateText(depositMoney, multiple, duration, rate);
})
// 第四步
$('.isstartBtn').on('click',function(event) {
    $(this).addClass('active').siblings().removeClass('active');
});
// 1.保证金 2.倍数 3.期限 4.利率
function updateText(depositMoney, multiple, duration, rate) {
	// console.log(depositMoney, multiple, duration, rate);
    if( !$("#deposit_money").val() ){ return; }
	// 配资金额计算
    gainMoney = depositMoney * multiple;
    // 警戒线 = 配资金额 + 保证金 * 警告线率
    warnMoney = gainMoney + (depositMoney * dayStockSet.warnLine / 100);
    // 平仓线 = 配资金额 + 保证金 * 平仓线率
    closeMoney = gainMoney + (depositMoney * dayStockSet.closeLine / 100);

    // 利息计算
    // 总利息 = 配资金额 * 利率(转成百分比) * 天数
    totalRate = (gainMoney * (rate / 100) * duration).toFixed(2);
    // 每天利息 = 配资金额 * 利率(转成百分比)
    oneRate = (gainMoney * (rate / 100)).toFixed(2);

    $("#accountManageFees").text(oneRate);// 账户管理费/日

    $("#totalAmount").text( depositMoney+gainMoney );//总配资资金
    var zcpzj = depositMoney+'(本金)+'+gainMoney+'(配资资金)';
    $("#tAtips").text(zcpzj);

    $("#warnLine").text(warnMoney);
    $("#outLine").text(closeMoney);
    //备注
    $('#bzjOK').text(depositMoney);
    $('#glfOK').text(totalRate);
    $('#zjeOK').text( parseFloat(depositMoney) + parseFloat(totalRate) );
}
function calc_borrow_money( deposit_money ){
    borrow_money_list.each(function(item){
    	// console.log(item);return
        var itemMultiple = parseInt( $(this).find(".times").data("multiple") );
        $(this).find(".times").text( itemMultiple*deposit_money );
  
        if((itemMultiple * deposit_money) < 10){
            $(this).find(".times").html(0);
        }
    })
}
$('#goPay').on('click',  function(event) {
    if( !uid ){
        var t1 = setTimeout(function(){
                        window.location.href = '{:url("common/login")}';//页面刷新
                    },1500);
        layer.msg('请先进行登录！', { icon: 2,anim: 6,shade: [0.3, '#000'] });
        return false;
    }
	if( !$('#deposit_money').val() ){
		return layer.msg('请输入本金', { icon: 2,anim: 6,shade: [0.3, '#000'] });
	}
	if( (depositMoney%beishu) != 0 ){
		return layer.msg('您的本金金额必须是'+beishu+'的整数倍', { icon: 2,anim: 6,shade: [0.3, '#000'] });
	}
    if( !$("#agreement").is(":checked") ){
		return layer.msg('请阅读并同意配资协议', { icon: 2,anim: 6,shade: [0.3, '#000'] });
    }
	$('.isstartBtn').each(function(index, val) {
        if($(this).hasClass('active')){
            startDate = $(this).data('startdate');
        }
    });
	
	var buildQuery = "{:url('stock/order')}"+"?depositMoney="+depositMoney+"&money="+gainMoney+"&multiple="+multiple+"&duration="+duration+"&type="+type+"&startDate="+startDate;
	layer.open({
		title: false,
		type: 2,
		area: ['790px', '420px'],
		fixed: true,//固定
		maxmin: false,//最大最小化
		resize: false,//允许拉伸
        Boolean:false,//滚动条
		content: buildQuery
	});

	/*var param = {
			depositMoney:depositMoney,
			money:gainMoney,
			multiple:multiple,
			duration:duration,
			type:type,
			startDate:startDate
		};
	var la_load = layer.load(0,{
			shade: [0.2,'#000']
		});
	$.ajax({
        url: "{:url('/canOrder')}",
        type: "post",
        dataType: "json",
        data: param,
        success: function(d,e,x){  
            if(d.status==1){
            	layer.close(la_load);        	
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message, { icon: 2,anim: 6,shade: [0.3, '#000'] });
            }
        }
    });*/

	
});
</script>


{include file="public/footer" /}