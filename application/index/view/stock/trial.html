{include file="public/header" /}
<title>免费体验 - {$global.web_name}</title>

{load href="/static/css/H/peizi.css" /}
<style type="text/css">
body{ background-color: #ffffff; }
</style>

<div class="pz_banner">
    <div class="banner_img">
        {:get_ad(25)}
    </div>
</div>

<div class="wrapper bar">
    <div class="borBox" id="experienceBox">
        <dl id="textBox">
            <dt>每个新用户只有一次体验机会（机不可失）</dt>
            <dd>{$global.web_name}出<b class="font1">{$try_set[2]}</b>元<span>（完全免费）</span></dd>
            <dd>您交<b class="font1">{$try_set[0]}</b>元<span>（体验费，体验结束全额退还）</span></dd>
            <dd>总计<b class="font1">{$try_set[3]}</b>元<span>（由您操盘）</span></dd>
            <dd>交易<b class="font1">{$try_set[1]}</b>天<span>（第二个交易日14:30前必须卖出股票）</span></dd>
            <dd>盈利全归你，超额亏损算我们</dd>
        </dl>
        <div class="ruleText">
            <p>只需支付{$try_set[0]}元就可以立刻获得{$try_set[3]}元体验操盘帐号</p>
            <a class="btn btnBg1" id="subBtn">免费体验</a>
        </div>
    </div>
</div>


<script type="text/javascript">
var uid = "{$uid}";
var type = 4;
var depositMoney = parseFloat("{$try_set.0}");
var gainMoney = parseFloat("{$try_set.2}");
$('#subBtn').on('click', function(event) {
    if( !uid ){
        var t1 = setTimeout(function(){
                        window.location.href = '{:url("common/login")}';//页面刷新
                    },1500);
        layer.msg('请先进行登录！', { icon: 2,anim: 6,shade: [0.3, '#000'] });
        return false;
    }
	var param = {type:type};
	var la_load = layer.load(0,{
			shade: [0.2,'#000']
		});
	$.ajax({
        url: "{:url('tryFinancingApply')}",
        type: "post",
        dataType: "json",
        data: param,
        success: function(d,e,x){  
            if(d.status==1){
            	// layer.close(la_load);
                layer.msg(d.message, { icon: 1,shade: [0.1, '#000'] });
                setTimeout(function(){
                        parent.location.href = "{:url('members/stock')}";
                    }, 1190);
            }else{
            	layer.close(la_load);
            	return layer.msg( d.message, { icon: 2,anim: 6,shade: [0.3, '#000'] });
            }
        }
    });

	/*var buildQuery = "{:url('/order')}"+"?depositMoney="+depositMoney+"&money="+gainMoney+"&multiple=1&duration=2&type="+type+"&startDate=1";
    layer.open({
        title: false,
        type: 2,
        area: ['800px', '420px'],
        fixed: true,//固定
        maxmin: false,//最大最小化
        resize: false,//允许拉伸
        Boolean:false,//滚动条
        content: buildQuery
    });*/

});
</script>


{include file="public/footer" /}