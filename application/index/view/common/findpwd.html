{include file="public/header" /}
<title>找回密码 - {$global.web_name}</title>
{load href="/static/css/H/loginregister.css" /}
<!--[if lt IE 9]>
    {load href="/static/js/H/html5.min.js" /}
    {load href="/static/js/H/respond.min.js" /}
<![endif]-->
<!--[if lt IE 10]>
    {load href="/static/js/H/placeholder.js" /}
<![endif]-->

    <div class="log_reg_box">
        <div class="log_reg_main">
            <div class="log_reg_form clearfix">
                <div class="login_reg_title">
                    <a class="on">找回密码</a>
                </div>
                <div class="form">
                    <!-- <form action="/" class="reg_form"> -->
                        <ul>
                            <li>
                                <input type="text" name="phone" maxlength="11" class="login_text" placeholder="请输入已绑定手机号" />
                            </li>
                            <li>
                                <input type="text" id="vcodeyc" class="login_yzm" maxlength="4"  placeholder="请输入验证码" autocomplete="off" />
                                <img src='{:captcha_src()}' title="点击换一个" style="width:123px;" class="jym"  onclick="this.src='{:captcha_src()}'+'?cd='+Math.random()" />
                            </li>
                            <li>                                
                                <input type="text" name="vcode" maxlength="6" class="login_yzm"  placeholder="请输入验证码" autocomplete="off" />
                                <button href="javascript:;" class="" id="sendSMS">点击发送验证码</button>
                            </li>
                            <li>
                                <input type="password" name="pass"  class="login_pass"  placeholder="请输入密码" />
                            </li>
                            <li>
                                <input type="password" name="pass2"  class="login_pass"  placeholder="请再次输入密码" />
                            </li>

                            <li>
                                <input type="submit" id="j-submit-btn" onclick="findpwd()" value="确定提交">
                            </li>           
                        </ul>                 
                    <!-- </form> -->
                </div>
            </div>
        </div>
    </div>


<script type="text/javascript">
function findpwd(){
    var phone = $('input[name=phone]').val();
    var vcodeyc = $('#vcodeyc').val();
    var vcode = $('input[name=vcode]').val();
    var pass = $('input[name=pass]').val();
    var pass2 = $('input[name=pass2]').val();

    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(phone)) ){  
        return layer.msg( '手机号码有误，请重新输入',{icon:2,anim:6} );
    }
    if( !vcodeyc ){
        return layer.msg( '请输入图形验证码',{icon:2,anim:6} );
    }
    if( !vcode ){
        return layer.msg( '短信验证码不能为空',{icon:2,anim:6} );
    }
    if( pass.length<6 || pass.length>16 ){
        return layer.msg( '密码不得小于6位或者大于16位',{icon:2,anim:6} );
    }
    if( pass != pass2 ){
        return layer.msg( '两次密码输入不一致',{icon:2,anim:6} );
    }

    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url: '{:url("common/findpwd")}',
        type: "post",
        dataType: "json",
        data: {phone:phone,vcode:vcode,vcodeyc:vcodeyc,pass:pass,pass2:pass2,action:'findpwd'},
        success: function(d,e,x){  
            if(d.status==1){
                layer.msg( d.message,{icon:1} );
                var t1 = setTimeout(function(){
                        window.location.href = '{:url("common/login")}';
                    },1500);
            }else{
                $('.jym').trigger('click');
                layer.close(la_load);
                return layer.msg( d.message,{icon:2} );
            }
        }
    });
}

var txtObj = $('#sendSMS');//文字DOM Obj
var InterValObj; //timer变量，控制时间 
var count = 90; //间隔函数，1秒执行 
var curCount;//当前剩余秒数
function myInterval() {
    curCount = count;
    //设置button效果，开始计时
    txtObj.text(curCount + "秒后重新发送"); 
    InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次 
} 
$('#sendSMS').on('click', function(event) {
    var vcodeyc = $('#vcodeyc').val();
    var phone = $('input[name=phone]').val();
    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(phone)) ){  
        return layer.msg( '手机号码有误，请重新输入',{icon:2,anim:6} );
    }
    if( !vcodeyc ){
        return layer.msg( '请先输入图形验证码',{icon:2,anim:6} );
    }

    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url: '{:url("common/findpwd")}',
        type: "post",
        dataType: "json",
        data: {phone:phone,action:'sendsms'},
        beforeSend:function(){
            txtObj.text('验证码发送中').attr("disabled",true).addClass('btn-disabled');
        },
        success: function(d,e,x){  
            layer.close(la_load);
            if(d.status==1){
                myInterval();
                return layer.msg( d.message,{icon:1} );
            }else{
                //启用按钮
                txtObj.text('重新发送').removeAttr("disabled").removeClass('btn-disabled');
                return layer.msg( d.message,{icon:5} );
            }
        }
    });
});
function SetRemainTime() {
    if (curCount == 0) {         
        window.clearInterval(InterValObj);//停止计时器 
        //启用按钮
        txtObj.removeAttr("disabled").removeClass('btn-disabled');
        txtObj.text("重新发送"); 
    }else{
        curCount--; 
        txtObj.text(curCount + "秒后重新发送"); 
    } 
} 
</script>
{include file="public/footer" /}