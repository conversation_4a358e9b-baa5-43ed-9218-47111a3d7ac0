{include file="public/header" /}
<title>软件下载 - {$global.web_name}</title>
{load href="/static/css/H/download.css" /}

<style type="text/css">
.layui-layer img{ max-width: 150px; }
</style>

<div class="bg">
    <div class="wrapper clearfix">
        <div class="dleft"></div>

        <div class="dright">
            <a href="javascript:void(0)" id="win" class="down-block">
                <i></i>
                <span>Win版下载</span>
            </a>
            <a href="javascript:void(0)" id="android" class="down-block">
                <i></i>
                <span>Android版下载</span>
            </a>
            <a href="javascript:void(0)" id="ios" class="down-block">
                <i></i>
                <span>IOS版下载</span>
            </a>
        </div>
    </div>
</div>

<div id='andrqrcode' style="display:none;">{:get_ad(19)}</div>
<div id='iosqrcode' style="display:none;">{:get_ad(19)}</div>
<script type="text/javascript">
$("#win").mouseover(function (){
    var ms1 = '<b style="color:black;font-size:16px;">下载说明</b><p style="color:black;font-size:14px;">安装前请退出360杀毒软件！</p>';
    indexpc = layer.tips(
        ms1,
        '#win', {
        tips: [2, '#fff'],
        time: 0
    });
}).mouseout(function (){  
    layer.close(indexpc);
});
$("#android").mouseover(function (){
    var ms2 = $('#andrqrcode').html();
    indexios = layer.tips(
        ms2,
        '#android', {
        tips: [2, '#fff'],
        time:0
    });
}).mouseout(function (){  
    layer.close(indexios);
});
$("#ios").mouseover(function (){
    var ms3 = $('#iosqrcode').html();
    indexios = layer.tips(
        ms3,
        '#ios', {
        tips: [2, '#fff'],
        time:0
    });
}).mouseout(function (){  
    layer.close(indexios);
});
</script>
{include file="public/footer" /}