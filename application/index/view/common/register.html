{include file="public/header" /}
<title>注册 - {$global.web_name}</title>
{load href="/static/css/H/loginregister.css" /}
<!--[if lt IE 9]>
    {load href="/static/js/H/html5.min.js" /}
    {load href="/static/js/H/respond.min.js" /}
<![endif]-->
<!--[if lt IE 10]>
    {load href="/static/js/H/placeholder.js" /}
<![endif]-->

    <div class="log_reg_box">
        <div class="log_reg_main">
            <div class="log_reg_form clearfix">
                <div class="login_reg_title">
                    <a ><span></span>新用户注册</a>
                    <span>市场有风险，投资需谨慎</span>
                </div>
                <div class="form">
                    <!-- <form action="/" class="reg_form"> -->
                        <ul>
                            <li>
                                <input type="text" name="uname" maxlength="11" class="login_text" placeholder="请输入11位中国大陆手机号" />
                            </li> 
                          <li>                                
                                <input type="text" name="vcode" maxlength="6" class="login_yzm"  placeholder="请输入验证码" autocomplete="off" />
                                <button href="javascript:;" class="" id="sendSMS">点击发送验证码</button>
                            </li>
                            <li>
                                <input type="password" name="pass"  class="login_pass"  placeholder="请输入密码" />
                            </li>
                            <li>
                                <input type="password" name="pass2"  class="login_pass"  placeholder="请再次输入密码" />
                            </li>
                            <li>
                                {if condition='$invite'}
                                <input type="text" value="{$invite}" readonly="" name="recommend" maxlength="11" class="login_tuijian btn-disabled" placeholder="请输入推荐人,如果没有，可以不填写" />
                                {else /}
                                <input type="text" name="recommend" maxlength="11" class="login_tuijian" placeholder="请输入推荐人,如果没有，可以不填写" />
                                {/if}
                            </li>
                            <li style="line-height: 12px;">
                                <input type="checkbox" checked="" name="agree" class="fleft" /> 
                                <span class="white" style="font-size: 14px;">
                                    &nbsp;&nbsp;注册代表已同意《<a style="color: #0096fd;" href="javascript:;" onclick="zcxy()">{:hetong(3)['name']}</a>》
                                </span>  
                            </li>
                            <li>
                                <input type="submit" id="j-submit-btn" onclick="reg()" value="同意协议并注册">
                            </li>           
                        </ul>                 
                    <!-- </form> -->
                </div>
            </div>
        </div>
    </div>

{load href="/static/js/H/loginregister.js" /}
<script type="text/javascript">
var regUrl = '{:url("Common/register")}';
var smsUrl = '{:url("Common/user_verify")}';
var member_index = '{:url("members/index")}';

var zcxyt = "{:hetong(3)['name']}";
function zcxy(){
    layer.open({
        type: 2,
        title: zcxyt,
        maxmin: true,
        shadeClose: false,
        area : ['866px' , '470px'],
        content: '{:url("common/zcxy")}',
        end: function(){
        }
    });
}
</script>
{include file="public/footer" /}