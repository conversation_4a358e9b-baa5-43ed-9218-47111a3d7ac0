{include file="public/header" /}
<title>登录 - {$global.web_name}</title>
{load href="/static/css/H/loginregister.css" /}
<!--[if IE]>
<style type="text/css">
.log_reg_form{ background-color: #000;filter:alpha(opacity:40); opacity:0.4;  -moz-opacity:0.4;-khtml-opacity: 0.4;}
.login_reg_title a.on{ background-color: #79858B; filter:alpha(opacity:40); opacity:0.4;  -moz-opacity:0.4;-khtml-opacity: 0.4;}
</style>
<![endif]-->
<!--[if lt IE 9]>
    {load href="/static/js/H/html5.min.js" /}
    {load href="/static/js/H/respond.min.js" /}
<![endif]-->
<!--[if lt IE 10]>
    {load href="/static/js/H/placeholder.js" /}
<![endif]-->

    <div class="log_reg_box login_box" onkeydown="keyEnter()">
        <div class="log_reg_main">
            <div class="log_reg_form clearfix">
                <div class="login_reg_title">
                    <a >登录</a>
                    <a class="on" href="{:url('common/register')}">注册</a>
                </div>
                <div class="form">
                    <!-- <form action="/"  class="log_form"> -->
                        <ul>
                            <li>
                                <input type="text" name="uname" maxlength="11" class="login_text" placeholder="请输入11位中国大陆手机号" />
                            </li> 
                            <li>
                                <input type="password" name="pass"  class="login_pass"  placeholder="请输入密码" />
                            </li>
                            <li>
                                <input type="text" name="vcode" id="vcodeyc" class="login_yzm" maxlength="4"  placeholder="请输入验证码" autocomplete="off" />
                                <img src='{:captcha_src()}' title="点击换一个" class="jym"  onclick="this.src='{:captcha_src()}'+'?cd='+Math.random()" />
                            </li>
                            <li>
                                <div class="fleft">
                                    <span class="white fontsize14">没有账号 <a href="{:url('common/register')}" class="red">立即注册</a></span>              
                                </div>
                                <a href="/member/common/getPassWord.html" class="fright white">忘记密码</a>
                            </li>
                            <li>
                                <input type="submit" id="j-submit-btn" onclick="login()" value="立即登录">
                            </li>           
                        </ul> 
                    <!-- </form> -->
                </div>
            </div>
        </div>
    </div>

{load href="/static/js/H/loginregister.js" /}
<script type="text/javascript">
var ent = true;
function keyEnter(){
    if (event.keyCode==13){
        if(ent){
            login();
        }
        ent = false;
    }
}

var loginUrl = '{:url("Common/login")}';
var member_index = '{:url("members/index")}';
</script>


{include file="public/footer" /}