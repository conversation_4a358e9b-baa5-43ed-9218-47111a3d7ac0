<?php

if (!function_exists('environment')) {
    function environment($env = null)
    {
        if (!$env) {
            return config('app_env');
        }

        if (is_array($env)) {
            return in_array(config('app_env'), $env);
        }

        return config('app_env') == $env;
    }
}

if (!function_exists('randomStr')) {
    function randomStr($length)
    {
        $str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $len = strlen($str) - 1;
        $randstr = '';
        for ($i = 0; $i < $length; $i++) {
            $num = mt_rand(0, $len);
            $randstr .= $str[$num];
        }
        return $randstr;
    }
}

if (!function_exists('generateSubAccountName')) {
    /**
     * 生成隨機子帳戶名稱
     * @param int $length 帳戶名長度，默認8位
     * @return string
     */
    function generateSubAccountName($length = 8)
    {
        $prefix = 'PZ'; // 配資前綴
        $randomPart = randomStr($length - 2);
        return $prefix . $randomPart;
    }
}

if (!function_exists('generateSubAccountPassword')) {
    /**
     * 生成隨機子帳戶密碼
     * @param int $length 密碼長度，默認8位
     * @return string
     */
    function generateSubAccountPassword($length = 8)
    {
        // 包含數字和字母，確保密碼強度
        $str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $len = strlen($str) - 1;
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $num = mt_rand(0, $len);
            $password .= $str[$num];
        }
        return $password;
    }
}

if (!function_exists('createSubAccount')) {
    /**
     * 創建子帳戶
     * @param int $uid 用戶ID
     * @param int $borrow_id 配資申請ID
     * @param int $category 類型 1:股票 2:期貨
     * @return array|false 成功返回子帳戶信息，失敗返回false
     */
    function createSubAccount($uid, $borrow_id, $category = 1)
    {
        $maxAttempts = 10; // 最大嘗試次數，避免帳戶名重複

        for ($attempt = 0; $attempt < $maxAttempts; $attempt++) {
            $accountName = generateSubAccountName();

            // 檢查帳戶名是否已存在
            $exists = db('stock_account')->where('name', $accountName)->count();
            if ($exists > 0) {
                continue; // 如果存在，重新生成
            }

            $password = generateSubAccountPassword();

            $accountData = [
                'uid' => $uid,
                'borrow_id' => $borrow_id,
                'name' => $accountName,
                'password' => $password,
                'remark' => '配資申請時自動創建',
                'status' => 1, // 未使用
                'add_time' => time(),
                'category' => $category
            ];

            $accountId = db('stock_account')->insertGetId($accountData);

            if ($accountId) {
                return [
                    'id' => $accountId,
                    'name' => $accountName,
                    'password' => $password,
                    'uid' => $uid,
                    'borrow_id' => $borrow_id,
                    'category' => $category
                ];
            }
        }

        return false; // 創建失敗
    }
}

