---
trigger: always_on
---

# 專案 .roorules 檔案

本專案使用 `.roorules` 檔案來提供專案特定的指令，這些指令將自動附加到 Roo 的自訂指令中，以確保在專案上下文中的所有互動都遵循特定的規則和標準。

`.roorules` 檔案應放置在專案的根目錄下。

修改此檔案會更新 Roo 的提示快取，建議在對話之間更新。

## .roorules Folder 詳細內容

對於更複雜的專案，可以將規則組織到多個檔案中，並在專案根目錄下建立一個名為 `.roorules/` 的資料夾。

### 01-tech-stack

#### 技術棧偏好

在本專案中，主要使用以下技術棧進行開發：

-   **後端框架**: **ThinkPHP 5.0.x** (目前專案版本為 5.0.25)。在編寫後端程式碼時，請遵循 ThinkPHP 5.0 的最佳實踐和規範。
-   **程式語言**: **PHP 5.4.0 以上** (ThinkPHP 5.0 最低要求)。建議使用 PHP 7.x 或更高版本以獲得更好的性能和語言特性，但需注意專案的實際運行環境相容性。

### 02-project-structure

#### 專案結構 (ThinkPHP 5.0)

-   **應用程式目錄**: `application/`
    -   **模組 (Modules)**: 例如 `admin`, `index`, `common`, `wap` 等，每個模組通常包含自己的控制器、模型、視圖等。
        -   控制器 (Controllers): 位於 `application/[module_name]/controller/` 目錄下。
        -   模型 (Models): 位於 `application/[module_name]/model/` 目錄下。
        -   視圖 (Views): 位於 `application/[module_name]/view/` 目錄下。
        -   邏輯層/服務層 (Logic/Service): 可以在 `application/[module_name]/logic/` 或 `application/[module_name]/service/` (自訂)。
    -   **公共模組**: `application/common/` 可用於存放公共模型、函數庫等。
    -   **設定檔 (Configuration)**:
        -   應用程式級設定: `application/config.php`, `application/database.php`, `application/route.php`, `application/tags.php`。
        -   模組級設定: `application/[module_name]/config.php`。
        -   額外設定: `application/extra/` 目錄下的設定檔。
    -   **命令 (Commands)**: 定義在 `application/command.php` 中，或在模組內建立 `command` 目錄並定義。
    -   **路由 (Routes)**: 主要定義在 `application/route.php`，也可在模組設定檔或單獨的路由檔案中定義。
    -   **公共函數庫**: `application/common.php`。
-   **核心框架目錄**: `thinkphp/`
-   **公開存取目錄**: `public/`
-   **執行階段目錄**: `runtime/` (包含日誌、快取等)
-   **擴展目錄**: `extend/`

### 03-code-style

#### 程式碼風格和模式

請遵循以下程式碼風格和常用模式：

-   **ThinkPHP 5.0 程式碼風格**:
    -   建議遵循 PSR-2 編碼風格指南。
    -   控制器方法應保持簡潔，將複雜的業務邏輯移至模型、邏輯層 (Logic) 或服務層 (Service)。
    -   使用 ThinkPHP 5.0 的模型 (Model) 和資料庫操作類 (Db) 進行資料庫互動。
    -   使用命名空間 (Namespaces) 組織程式碼。
    -   避免在視圖檔案中寫入過多複雜邏輯，將邏輯移至控制器或輔助函數。
    -   善用 ThinkPHP 5.0 的輔助函數。

### 04-best-practices

#### 最佳實踐和建議

-   **資料庫操作**:
    -   使用 ThinkPHP 5.0 的模型進行 ORM 操作，或使用 `Db` 類進行更底層的資料庫操作。
    -   注意防範 SQL 注入，使用預處理語句或框架提供的安全方法。
    -   對於複雜查詢，合理使用查詢建構器。
-   **依賴管理**:
    -   使用 Composer ([`composer.json`](composer.json:0), [`composer.lock`](composer.lock:0)) 管理專案的外部依賴項。
    -   定期更新依賴項，以獲取最新的功能和安全補丁。
-   **測試**:
    -   ThinkPHP 5.0 支援 PHPUnit 進行單元測試和整合測試。
    -   測試控制器、模型、邏輯/服務層等核心組件。
-   **安全性**:
    -   注意防範常見的 Web 安全漏洞，例如 XSS、CSRF、SQL 注入等。
    -   使用 ThinkPHP 5.0 提供的安全功能，例如輸入過濾 (`input` 輔助函數的過濾參數)、CSRF 驗證 (Token)、驗證器 (Validate) 等。
    -   妥善處理使用者輸入，避免直接在查詢中使用未經處理的使用者資料。
-   **性能優化**:
    -   注意 N+1 查詢問題，使用模型的關聯預載入 (with) 功能。
    -   對於耗時的操作，考慮使用 ThinkPHP 5.0 的隊列 (Queue) 功能 (需安裝相應擴展)。
    -   使用快取 (Cache) 來減少資料庫查詢或重複計算。
    -   優化路由和控制器邏輯。
    -   使用 ThinkPHP 5.0 的除錯工具和日誌分析應用程式性能。
-   **錯誤處理和日誌記錄**:
    -   使用 ThinkPHP 5.0 提供的錯誤和異常處理機制。
    -   合理使用日誌記錄 (`Log` 類)，記錄應用程式運行時的錯誤和重要事件。
    -   配置日誌驅動和級別以便於管理和監控日誌。
    -   **詳細規範**:
        -   **錯誤類型**: 區分不同類型的錯誤（例如：驗證錯誤、業務邏輯錯誤、系統錯誤、第三方服務錯誤）。
        -   **錯誤響應**: API 應返回一致的錯誤響應格式，包含錯誤碼、錯誤訊息和可選的詳細資訊。
        -   **日誌級別**: 根據錯誤的嚴重程度使用不同的日誌級別（例如：`info`, `warning`, `error`, `sql` 等）。
        -   **日誌內容**: 日誌應包含足夠的上下文資訊，例如請求 URL、參數、用戶 ID (如果適用)、錯誤追蹤等，以便於問題排查。
        -   **敏感資訊**: 避免在日誌中記錄敏感資訊，例如密碼、API 金鑰等。
-   **邏輯/服務層職責**:
    -   邏輯層 (Logic) 或服務層 (Service) 應專注於處理特定的業務領域邏輯。
    -   一個類別應具有單一職責，避免過於龐大和複雜。
    -   可以依賴於模型或其他服務。
    -   方法應具有清晰的輸入和輸出。

#### 工具使用建議

-   **文檔搜索**:
    -   在需要查詢 PHP 和 ThinkPHP 相關文檔時，建議優先查閱官方文檔或可靠的社群資源。
    -   若使用 `context7` 工具獲取文檔，**建議先透過 [`composer.json`](composer.json:0) 和 [`composer.lock`](composer.lock:0) 確認框架或套件的確切版本**，然後再明確指定查詢的版本以獲取最相關的資訊。
    -   例如需要查詢 ThinkPHP 5.0 文件時，可以這樣提問：`use context7 搜尋 ThinkPHP 5.0 關於模型操作的文件`

### 05-php-features

#### PHP 特性應用

在開發過程中，根據專案實際運行的 PHP 版本，可以考慮使用相應的 PHP 新特性，以提高程式碼的可讀性、可維護性和性能。ThinkPHP 5.0 最低支援 PHP 5.4，但若環境允許，使用更高版本 PHP 會帶來更多優勢。

-   **PHP 5.4+**:
    -   短陣列語法 `[]`。
    -   Traits。
-   **PHP 5.5+**:
    -   `finally` 關鍵字。
    -   `list()` 用於 `foreach`。
-   **PHP 5.6+**:
    -   常數表達式。
    -   可變參數函數 `...`。
-   **PHP 7.0+**:
    -   純量類型提示 (Scalar Type Hints)。
    -   回傳類型宣告 (Return Type Declarations)。
    -   Null 合併運算子 `??`。
    -   太空船運算子 `<=>`。
    -   匿名類 (Anonymous Classes)。
-   **PHP 7.1+**:
    -   可為 Null 的類型 (Nullable Types)。
    -   `void` 回傳類型。
    -   類別常數可見性修飾符。
-   **PHP 7.4+**:
    -   類型化屬性 (Typed Properties)。
    -   箭頭函數 (Arrow Functions)。
    -   Null 合併指派運算子 `??=`。
    -   陣列解構中的展開運算子。
-   **(若專案升級至 PHP 8.x)**:
    -   命名參數 (Named Arguments)。
    -   聯合類型 (Union Types)。
    -   建構函數屬性提升 (Constructor Property Promotion)。
    -   Match 表達式 (Match Expression)。
    -   Nullsafe 運算子 (Nullsafe Operator)。
    -   Attributes。
    -   列舉 (Enums) (PHP 8.1+)。

### 06-important-files

#### 重要檔案位置（請勿修改）

為了專案的安全性和穩定性，請指示 Roo **不要讀取或修改** 以下檔案或目錄：

-   **環境變數檔案**: `.env` (如果專案使用 phpdotenv 等套件)。
-   **Composer 鎖檔案**: [`composer.lock`](composer.lock:0)。
-   **ThinkPHP 核心框架目錄**: `thinkphp/`。
-   **執行階段目錄**: `runtime/` 及其子目錄 (例如 `runtime/log/`, `runtime/cache/`, `runtime/temp/`)，除非明確指示進行日誌查看或快取清理等操作。
-   **Vendor 目錄**: `vendor/` (由 Composer 管理)。
-   **資料庫檔案**: 如果使用 SQLite，請勿直接修改 `.sqlite` 資料庫檔案。

請確保 Roo 在執行任何操作時都遵守這些限制，以防止意外修改或暴露敏感資訊。

### 07-api-design-guidelines

#### API 設計規範

-   **RESTful 風格**: API 設計應遵循 RESTful 風格，使用 HTTP 方法（GET, POST, PUT, PATCH, DELETE）來表示資源的操作。
-   **URL 設計**: 使用名詞複數形式表示資源集合，使用名詞單數形式表示單個資源。URL 應簡潔明了，避免使用動詞。
-   **請求和回應格式**: 預設使用 JSON 格式進行請求和回應。
-   **狀態碼**: 使用標準的 HTTP 狀態碼表示請求的結果（例如：200 OK, 201 Created, 204 No Content, 400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found, 422 Unprocessable Entity, 500 Internal Server Error）。
-   **錯誤回應**: 遵循統一的錯誤回應格式，包含易於理解的錯誤碼、錯誤訊息和可選的詳細資訊。
-   **版本控制**: 考慮在 API URL 中包含版本號（例如：`/api/v1/...`），以便未來進行版本升級。
-   **分頁、過濾和排序**: 對於資源集合，應支援分頁、過濾和排序功能，並使用標準的查詢參數來實現。
-   **輸入驗證**: 使用 ThinkPHP 的驗證器 (Validate) 對 API 輸入參數進行嚴格驗證。
