<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// [ 应用入口文件 ]

// 定义应用目录
// define('APP_PATH', __DIR__ . '/../application/');

// define('BIND_MODULE', 'wap');

// 加载框架引导文件
// require __DIR__ . '/../thinkphp/start.php';

/* 
--------------------------------------------------------
*/

// 定义项目路径
define('APP_PATH', __DIR__ . '/../application/');

// 加载框架基础文件
require __DIR__ . '/../thinkphp/base.php';

// 绑定当前入口文件到wap模块
\think\Route::bind('wap');

// 关闭wap模块的路由
\think\App::route(false);

// 执行应用
\think\App::run()->send();
