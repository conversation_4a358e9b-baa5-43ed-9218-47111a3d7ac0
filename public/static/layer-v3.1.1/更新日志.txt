当前版本对上个版本无缝兼容，可直接覆盖升级。

# v3.1.1 2017.12.05

* 优化自动获取路径代码，以解决部分浏览器下的某些场景无法自动加载样式问题

---

# v3.1.0 2017.09.13

* 紧急修复在最新版 Chrome（61.0.3163.79）下遮罩层出现的奇异花屏现象 
* 修复 layer.js 未设置btnAlign时，包裹按钮的 div.layui-layer-btn 出现一多余 class
* 对默认按钮颜色、Tips层、Prompt层、Tab层等进行了样式调整，以便更显大气，且更符合layui风格
* 增加maxHeight参数，用于设定弹层的最大高度 
* 去除 type:2 时如果content未填写而弹出 layer 官网的默认设置

---

# v3.0.3 2017.03.06

* 增加参数 isOutAnim，用于控制是否显示关闭动画，true 或者 false
* 撤销自动ready处理机制，因为在初始时与Ajax同步等使用场景存在耦合等问题
* 修复Firefox下对默认层设置了高度，按钮被溢出的bug
* photos层的出场动画不再是随机，而是采用默认动画（也可以通过参数 anim 来自定义）
* 去除photos层的默认关闭动画（觉得觉得不闪眼的话，也可以通过参数isOutAnim开启）

---

# v3.0.2 2017.02.25

* 新增 resizing 回调，用于监听窗口的调整大小
* 增加自动ready处理机制，即如果你页面一打开就执行弹层，无需放入layer.ready中
* 对moveEnd回调返回一个参数为当前容器的DOM对象
* 添加一个amd条件,避免 define出错（Merge pull request #42 from wangyateng/master）
* 修复tab,photos,prompt弹层默认success回调被覆盖导致功能不正常的问题（来自github用户 @waychan23 的友情提醒）
* 完善多按钮场景换行的间距（之前是紧贴在一起，略不雅观）
* 弹出图片层的动画时间改为800ms
* 修复按方向键切换图片层过快时重复弹出的bug
* 修复与animate.css可能存在的动画冲突

---

# v3.0 2016.11.07
* 新增拖动弹层右下角可调节弹层尺寸的功能，由参数resize控制，默认开启。对loading、tips层无效
* offset参数新增 t、r、b、l、lt、lb、rt、rb、的赋值，可快捷实现上、右、下、左、左上角、左下角、右上角、右下角的边缘定位
* 新增btnAlign参数，支持三个参数：l/c/r，分别用于控制按钮居左、居中、居右的快捷设置（默认r，即居右）
* 点击最小化时，自动定位到页面左下角，在出现多个最小化时，会依次排列，这是一次较为重大的完善。
* 新增关闭层的过度动画，可通过anim: -1统一取消动画

* 重写拖拽核心代码，性能大幅度提升
* layer.config核心调整
* layer.config中的extend参数只提供加载一个css皮肤文件（详见文档）
* 处理在head标签中执行layer弹层无效的情况
* 瘦身layer.ready方法
* 修改iframe层的loading动画
* 捕获页类型的弹层在关闭时，会根据所捕获元素的初始display值，进行不同处理，即不再强制隐藏。

* 剔除moveType参数，只提供默认的一种拖拽风格
* 剔除语义欠佳的fix参数，改为fixed取代
* 剔除语义欠佳的shift参数，改为anim取代，不过仍然对shift参数兼容

* 完善tips层细节
* 将prompt层改成页面层结构，即当你弹出prompt层时，再弹出msg，不会将prompt销毁。
* prompt层的textarea模式支持area参数来设定宽高
* layer.css大面积改善
* 降解IE6的fixed支持，不过仍然对ie6兼容（话说现在全浏览器兼容的组件已经不多了，layer后续会整理出不兼容ie6/7的版本）

* 修改图片超出屏幕后，重新计算大小的算法（Merge pull request from 390029659/master）
* 修复iframe层在用于iPhone设备时，无法触发滚动的Bug



