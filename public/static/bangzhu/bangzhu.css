* {
	PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
H1 {
	MARGIN: 0px auto; FONT-FAMILY: Arial, Lucida, Verdana, "����", Helvetica, sans-serif; FONT-SIZE: 20px
}
BODY > DIV {
	MARGIN-LEFT: auto; MARGIN-RIGHT: auto
}
H1 {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
H2 {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
H3 {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
H4 {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
H5 {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
H6 {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
P {
	/*TEXT-ALIGN: left; PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px*/
}
UL {
	PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
LI {
	PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
INPUT {
	VERTICAL-ALIGN: middle
}
.f12 {
	FONT-SIZE: 12px
}
.f13 {
	FONT-SIZE: 13px
}
.f14 {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 14px
}
.f15 {
	FONT-SIZE: 15px
}
.f16 {
	FONT-SIZE: 16px
}
.f18 {
	FONT-SIZE: 18px
}
.f20 {
	FONT-SIZE: 20px
}
.fs {
	FONT-SIZE: 16px
}
.f22 {
	FONT-SIZE: 22px
}
.f28 {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 28px
}
.f26 {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 24px
}
.f32 {
	LINE-HEIGHT: 95px; FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 34px
}
A:link {
	 TEXT-DECORATION: none
}
A:visited {
	TEXT-DECORATION: none
}
A:active {
	 TEXT-DECORATION: none
}
A:hover {
	 TEXT-DECORATION: none
}
.color_top {
	COLOR: #b5b5b5
}
#head_top {
	WIDTH: 100%; BACKGROUND: url(../images/head_bg.jpg); HEIGHT: 30px
}
.head_bg {
	WIDTH: 100%; BACKGROUND: url(../images/head_top_bg.jpg); HEIGHT: 71px
}
.head_width {
	LINE-HEIGHT: 30px; MARGIN: 0px auto; WIDTH: 100%; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 30px; COLOR: #b4b4b4; FONT-SIZE: 12px
}
.head_width UL {
	WIDTH: 980px
}
.head_width UL LI {
	LINE-HEIGHT: 30px; FLOAT: left; HEIGHT: 30px
}
.head_tel {
	MARGIN: 0px auto; WIDTH: 22px; BACKGROUND: url(../images/tel.png) no-repeat; FLOAT: left; HEIGHT: 30px
}
#banner {
	BACKGROUND-COLOR: #cccccc; WIDTH: 100%; FLOAT: left; HEIGHT: 400px
}
.line_width {
	WIDTH: 100%; MARGIN-BOTTOM: 10px; HEIGHT: 1px
}
.line_width SPAN {
	WIDTH: 958px; HEIGHT: 6px
}
.sina_width {
	PADDING-RIGHT: 5px; MARGIN-LEFT: 5px
}
.ffs {
	FLOAT: right; PADDING-TOP: 7px !important
}
.font_left {
	MARGIN-LEFT: 10px
}
.mianico {
	LINE-HEIGHT: 30px; WIDTH: 20px; HEIGHT: 18px
}
.mianico_h {
	PADDING-RIGHT: 5px; FLOAT: left; PADDING-TOP: 5px
}
.mianico_h1 {
	PADDING-RIGHT: 5px; FLOAT: left
}
.mianico_h2 {
	PADDING-RIGHT: 5px; FLOAT: left; PADDING-TOP: 8px
}
.mian_font {
	LINE-HEIGHT: 30px; FLOAT: left; HEIGHT: 30px
}
.mian_line {
	WIDTH: 2px; PADDING-RIGHT: 10px; FLOAT: left; HEIGHT: 27px; MARGIN-LEFT: 10px
}
.main_kuang_left {
	LINE-HEIGHT: 28px; PADDING-LEFT: 30px !important; WIDTH: 343px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: left; HEIGHT: 200px; PADDING-TOP: 10px
}
.main_kuang_right {
	WIDTH: 570px; PADDING-RIGHT: 10px; FLOAT: right; HEIGHT: 200px
}
.font_f {
	LINE-HEIGHT: 45px; COLOR: #009add
}
.lan {
	MARGIN-TOP: 5px; COLOR: #1566ac
}
.main_txt {
	WIDTH: 600px
}
.main_txt UL {
	WIDTH: 280px; FLOAT: left
}
.main_txt UL LI {
	TEXT-ALIGN: right; WIDTH: 280px
}
.fya {
	LINE-HEIGHT: 25px; FONT-FAMILY: "Microsoft Yahei"
}
.fya1 {
	FONT-FAMILY: "Microsoft Yahei"
}
.ff {
	COLOR: #1566ac; TEXT-DECORATION: underline
}
.head_width UL LI A:link {
	COLOR: #b4b4b4; TEXT-DECORATION: none
}
.head_width UL LI A:visited {
	COLOR: #b4b4b4; TEXT-DECORATION: none
}
.head_width UL LI A:active {
	COLOR: #b4b4b4; TEXT-DECORATION: none
}
.head_width UL LI A:hover {
	COLOR: #dddada; TEXT-DECORATION: underline
}
.main_kuang {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; WIDTH: 958px; HEIGHT: 190px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.main_kuang_li {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; WIDTH: 958px; HEIGHT: 610px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.main_kuang_li1 {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; WIDTH: 958px; HEIGHT: 510px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.main_kuang_li3 {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; WIDTH: 958px; HEIGHT: 710px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.main_kuang_w {
	WIDTH: 958px; HEIGHT: 200px
}
LABEL {
	CURSOR: pointer
}
.democode {
	LINE-HEIGHT: 24px; MARGIN: 30px auto 0px; WIDTH: 400px
}
.democode H2 {
	HEIGHT: 28px; COLOR: #3366cc; FONT-SIZE: 14px
}
.agree {
	MARGIN: 40px auto; WIDTH: 400px; COLOR: #3366cc; FONT-SIZE: 16px; FONT-WEIGHT: 800
}
.mainlist {
	PADDING-BOTTOM: 10px; PADDING-LEFT: 10px; PADDING-RIGHT: 10px; PADDING-TOP: 10px
}
.mainlist LI {
	LINE-HEIGHT: 28px; HEIGHT: 28px; FONT-SIZE: 12px
}
.mainlist LI SPAN {
	MARGIN: 0px 5px 0px 0px; FONT-FAMILY: "����"; COLOR: #ddd; FONT-SIZE: 12px; FONT-WEIGHT: 400
}
.btnbox {
	TEXT-ALIGN: center; BACKGROUND: #ecf9ff; HEIGHT: 30px; PADDING-TOP: 10px
}
#windownbg {
	POSITION: absolute; WIDTH: 100%; DISPLAY: none; BACKGROUND: #000; HEIGHT: 100%; TOP: 0px; LEFT: 0px
}
#windown-box {
	BORDER-BOTTOM: #e9f3fd 5px solid; POSITION: fixed; TEXT-ALIGN: left; BORDER-LEFT: #e9f3fd 5px solid; BACKGROUND: #fff; BORDER-TOP: #e9f3fd 5px solid; BORDER-RIGHT: #e9f3fd 5px solid; _position: absolute
}
#windown-title {
	BORDER-BOTTOM: #cccccc 1px solid; POSITION: relative; BORDER-LEFT: #cccccc 1px solid; BACKGROUND: url(../images/tipbg.png) repeat-x 0px 0px; HEIGHT: 30px; OVERFLOW: hidden; BORDER-TOP: #cccccc 1px solid; BORDER-RIGHT: #cccccc 1px solid
}
#windown-title H2 {
	POSITION: relative; COLOR: #666; FONT-SIZE: 14px; TOP: 5px; LEFT: 10px
}
#windown-close {
	POSITION: absolute; TEXT-INDENT: -10em; WIDTH: 10px; BACKGROUND: url(../images/tipbg.png) no-repeat 100% -49px; HEIGHT: 16px; OVERFLOW: hidden; TOP: 8px; CURSOR: pointer; RIGHT: 10px
}
#windown-content-border {
	BORDER-BOTTOM: #cccccc 1px solid; POSITION: relative; BORDER-LEFT: #cccccc 1px solid; PADDING-BOTTOM: 5px; PADDING-LEFT: 5px; PADDING-RIGHT: 0px; BORDER-TOP: #cccccc 1px solid; TOP: -1px; BORDER-RIGHT: #cccccc 1px solid; PADDING-TOP: 5px
}
#windown-content IMG {
	DISPLAY: block
}
#windown-content IFRAME {
	DISPLAY: block
}
#windown-content .loading {
	POSITION: absolute; MARGIN-TOP: -8px; MARGIN-LEFT: -8px; TOP: 50%; LEFT: 50%
}
.tome {
	BACKGROUND-IMAGE: url(../images/pro_bg.jpg)
}
.time {
	TEXT-ALIGN: right; PADDING-BOTTOM: 2px; LINE-HEIGHT: 24px; WIDTH: 560px; PADDING-RIGHT: 10px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: right; HEIGHT: 40px; PADDING-TOP: 48px
}
.pro {
	WIDTH: 965px; MARGIN-BOTTOM: 2px; FLOAT: right; HEIGHT: 264px
}
.main_kan {
	WIDTH: 961px; HEIGHT: 260px
}
.main_kan UL {
	WIDTH: 240px; FLOAT: left; HEIGHT: 260px
}
.main_kan UL LI {
	WIDTH: 240px
}
.main_kan_h {
	TEXT-ALIGN: center; HEIGHT: 88px; PADDING-TOP: 35px
}
.pro_list {
	BACKGROUND-IMAGE: url(../images/touzhi.jpg); MARGIN-TOP: 5px; WIDTH: 979px; FLOAT: right; HEIGHT: 113px
}
.pro_list2 {
	BACKGROUND-IMAGE: url(../images/touzhi-det.jpg); MARGIN-TOP: 5px; WIDTH: 979px; FLOAT: right; HEIGHT: 113px
}
.pro_ok {
	BACKGROUND-IMAGE: url(../images/pro_bg.jpg); WIDTH: 965px; MARGIN-BOTTOM: 10px; HEIGHT: 264px
}
.yahao {
	TEXT-ALIGN: center; LINE-HEIGHT: 75px; MARGIN-TOP: 3px; PADDING-LEFT: 55px; WIDTH: 100px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: left; COLOR: #ffffff; FONT-SIZE: 22px
}
.yahao_right {
	TEXT-ALIGN: right; LINE-HEIGHT: 75px; MARGIN-TOP: 5px; PADDING-LEFT: 55px; WIDTH: 800px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: left; FONT-SIZE: 16px
}
.yahao_div {
	WIDTH: 979px; FLOAT: left
}
.yahao_div_left {
	MARGIN-LEFT: 20px !important
}
.yahao_div UL {
	BACKGROUND-COLOR: #ffffff; WIDTH: 979px
}
.yahao_div UL LI {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; WIDTH: 956px; FLOAT: left; HEIGHT: 67px; BORDER-RIGHT: #dadada 1px solid
}
.yahao_div UL LI A:link {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #fff; WIDTH: 956px; FLOAT: left; HEIGHT: 67px; BORDER-RIGHT: #dadada 0px solid
}
.yahao_div UL LI A:visited {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #fff; WIDTH: 956px; FLOAT: left; HEIGHT: 67px; BORDER-RIGHT: #dadada 0px solid
}
.yahao_div UL LI A:hover {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #f0f4f7; WIDTH: 956px; FLOAT: left; HEIGHT: 67px; BORDER-RIGHT: #dadada 0px solid
}
.yahao_div_bg {
	BACKGROUND-COLOR: #ffffff; WIDTH: 956px; FLOAT: left; HEIGHT: 67px
}
.logo {
	MARGIN: 0px auto;
	HEIGHT: 71px;
}
.moreinfo {
	MARGIN-TOP: 25px; PADDING-LEFT: 680px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: left; FONT-SIZE: 16px; TEXT-DECORATION: underline
}
.new_list {
	BACKGROUND-IMAGE: url(../images/newzhi.jpg); MARGIN-TOP: 15px; WIDTH: 979px; FLOAT: right; HEIGHT: 82px
}
.new_list_ok {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; WIDTH: 956px; FLOAT: left; HEIGHT: 340px; BORDER-RIGHT: #dadada 1px solid
}
.new_list_ok UL {
	PADDING-LEFT: 40px; WIDTH: 420px; FLOAT: left; PADDING-TOP: 10px
}
.new_list_ok UL LI {
	WIDTH: 420px; FONT-FAMILY: "Microsoft Yahei"; COLOR: #737272
}
.new_list_ok_list {
	LINE-HEIGHT: 55px; WIDTH: 340px; FLOAT: left; HEIGHT: 55px
}
.meiti_list {
	LINE-HEIGHT: 55px; WIDTH: 280px; FLOAT: left; HEIGHT: 55px
}
.new_list_ok_list2 {
	WIDTH: 340px; FLOAT: left
}
.new_list_ok_list1 {
	LINE-HEIGHT: 55px; FLOAT: left; HEIGHT: 55px
}
.new_list_ok_list3 {
	LINE-HEIGHT: 35px; FLOAT: left; HEIGHT: 35px
}
.ico_left {
	WIDTH: 13px; HEIGHT: 13px; MARGIN-RIGHT: 13px
}
.gay {
	COLOR: #737272
}
.gay_line {
	BORDER-BOTTOM: #cccccc 1px dashed; LINE-HEIGHT: 35px; HEIGHT: 35px
}
.gay_hight {
	HEIGHT: 105px
}
.gay_more {
	LINE-HEIGHT: 45px; PADDING-LEFT: 10px; HEIGHT: 45px
}
.u {
	TEXT-DECORATION: underline
}
.meiti {
	BACKGROUND-IMAGE: url(../images/meiti_bg.jpg); MARGIN-TOP: 20px; WIDTH: 123px; FLOAT: left; HEIGHT: 76px; MARGIN-RIGHT: 15px
}
.meiti_d {
	PADDING-LEFT: 1px; PADDING-TOP: 1px
}
.line {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; LINE-HEIGHT: 41px; BACKGROUND-COLOR: #fff; MARGIN-TOP: 15px; WIDTH: 956px; FLOAT: left; HEIGHT: 41px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.lineone {
	BORDER-BOTTOM: #dadada 1px solid; BACKGROUND-COLOR: #fff; MARGIN-TOP: 15px; WIDTH: 756px; FLOAT: left; HEIGHT: 20px
}
.lineone1 {
	BORDER-BOTTOM: #dadada 1px solid; BACKGROUND-COLOR: #fff; MARGIN-TOP: 1px; WIDTH: 753px; FLOAT: left; HEIGHT: 1px
}
.line1 {
	LINE-HEIGHT: 25px; WIDTH: 256px; FLOAT: left; HEIGHT: 25px
}
.line_left {
	TEXT-ALIGN: center; LINE-HEIGHT: 45px; BACKGROUND-COLOR: #ebebeb; WIDTH: 80px; FLOAT: left; HEIGHT: 41px; BORDER-RIGHT: #dadada 1px solid
}
.linebouut {
	BORDER-BOTTOM: #dadada 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #dadada 1px solid; PADDING-BOTTOM: 3px; BACKGROUND-COLOR: #f8f8f8; PADDING-LEFT: 8px; WIDTH: 62px; PADDING-RIGHT: 8px; HEIGHT: 25px; MARGIN-LEFT: 10px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid; PADDING-TOP: 3px
}
#foot {
	MARGIN-TOP: 15px; WIDTH: 100%;  FLOAT: left; 
}
.foot_list {
	MARGIN: 0px auto; WIDTH: 956px; HEIGHT: 156px; PADDING-TOP: 5px
}
.foot_list UL {
	MARGIN-TOP: 10px; WIDTH: 237px; FLOAT: left
}
.foot_list UL LI {
	LINE-HEIGHT: 25px; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 25px; COLOR: #c2c2c2
}
.foot_list UL LI A:link {
	COLOR: #b4b4b4; TEXT-DECORATION: none
}
.foot_list UL LI A:visited {
	COLOR: #b4b4b4; TEXT-DECORATION: none
}
.foot_list UL LI A:active {
	COLOR: #b4b4b4; TEXT-DECORATION: none
}
.foot_list UL LI A:hover {
	COLOR: #dddada; TEXT-DECORATION: none
}
.mianico {
	BACKGROUND-IMAGE: url(../images/mianico.jpg); LINE-HEIGHT: 35px; WIDTH: 13px; FLOAT: left; HEIGHT: 14px; MARGIN-RIGHT: 3px
}
.le {
	MARGIN-TOP: 3px; MARGIN-RIGHT: 2px
}
.us_h {
	LINE-HEIGHT: 26px; MARGIN-TOP: 10px; HEIGHT: 26px
}
.weixin {
	MARGIN-RIGHT: 13px
}
.weixin1 {
	MARGIN-RIGHT: 3px
}
.kftel {
	TEXT-ALIGN: right
}
.foot_list {
	BORDER-BOTTOM: #a6a6a6 1px dashed; WIDTH: 956px
}
.fein {
	TEXT-ALIGN: center; LINE-HEIGHT: 50px; MARGIN: 0px auto; WIDTH: 956px; HEIGHT: 50px; COLOR: #a6a6a6
}
.logo_left {
	WIDTH: 371px; FLOAT: left; HEIGHT: 71px
}
.title_main {
	WIDTH: 597px; FLOAT: right
}
.title_main UL {
	TEXT-ALIGN: right; WIDTH: 597px
}
.title_main UL LI {
	TEXT-ALIGN: right; LINE-HEIGHT: 50px; WIDTH: 597px; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 50px; FONT-SIZE: 26px
}
.title_main UL LI SPAN {
	TEXT-ALIGN: right; PADDING-LEFT: 12px; PADDING-RIGHT: 18px
}
.head_width_left {
	PADDING-LEFT: 10%
}
.list_1 {
	PADDING-LEFT: 16px; WIDTH: 532px; FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 18px; PADDING-TOP: 10px
}
.list_2 {
	TEXT-ALIGN: center; LINE-HEIGHT: 55px; WIDTH: 100px; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 55px; FONT-SIZE: 12px
}
.list_2i {
	LINE-HEIGHT: 52px
}
.gen-1 {
	LINE-HEIGHT: 110%
}
.ts-1 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 944px; HEIGHT: 69px; MARGIN-LEFT: auto; BORDER-TOP: medium none; MARGIN-RIGHT: auto; BORDER-RIGHT: medium none; PADDING-TOP: 5px
}
.ts-1-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 1px; PADDING-LEFT: 1px; WIDTH: 211px; PADDING-RIGHT: 1px; HEIGHT: 67px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 1px; LEFT: 0px
}
.ts-1-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 1px; LINE-HEIGHT: 50px; PADDING-LEFT: 1px; WIDTH: 107px; PADDING-RIGHT: 1px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 1px; LEFT: 190px
}
.gen-2 {
	VERTICAL-ALIGN: middle
}
.ts-1-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: right; BORDER-LEFT: medium none; PADDING-BOTTOM: 1px; LINE-HEIGHT: 50px; PADDING-LEFT: 1px; WIDTH: 150px; PADDING-RIGHT: 1px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 1px; LEFT: 252px
}
.ts-1-4 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 1px; LINE-HEIGHT: 50px; PADDING-LEFT: 1px; WIDTH: 177px; PADDING-RIGHT: 1px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 1px; LEFT: 382px
}
.ts-1-5 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 1px; LINE-HEIGHT: 50px; PADDING-LEFT: 1px; WIDTH: 73px; PADDING-RIGHT: 1px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 1px; LEFT: 541px
}
.ts-1-6 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 1px; LINE-HEIGHT: 50px; PADDING-LEFT: 1px; WIDTH: 173px; PADDING-RIGHT: 1px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 1px; LEFT: 646px
}
.ts-1-7 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 1px; PADDING-LEFT: 1px; WIDTH: 121px; PADDING-RIGHT: 1px; HEIGHT: 67px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 1px; LEFT: 821px
}
.st {
	MARGIN-TOP: 5px
}
.topads {
	BORDER-BOTTOM: #e6e6fa 2px solid; BORDER-LEFT: #e6e6fa 2px solid; MARGIN-TOP: 3px; OVERFLOW: hidden; BORDER-TOP: #e6e6fa 2px solid; BORDER-RIGHT: #e6e6fa 2px solid
}
.topads:hover {
	BORDER-BOTTOM: #b0e0e6 2px solid; BORDER-LEFT: #b0e0e6 2px solid; MARGIN-TOP: 3px; BORDER-TOP: #b0e0e6 2px solid; BORDER-RIGHT: #b0e0e6 2px solid
}
TEXTAREA {
	MARGIN: 5px 0px
}
.bottomAds {
	BORDER-BOTTOM: white 1px solid; BORDER-LEFT: white 1px solid; MARGIN: 10px 3px 0px; BORDER-TOP: white 1px solid; BORDER-RIGHT: white 1px solid
}
.bottomAds_quote {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; PADDING-BOTTOM: 5px; LINE-HEIGHT: 150%; MARGIN: 0px 5px; PADDING-LEFT: 5px; WIDTH: 700px; PADDING-RIGHT: 5px; FONT-FAMILY: Verdana, ����; FONT-SIZE: 12px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid; PADDING-TOP: 5px
}
.bottomAds_quote LEGEND {
	FONT-FAMILY: Microsoft Yahei; COLOR: green; FONT-SIZE: 16px
}
.bottomAds_quote P {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 150%; TEXT-INDENT: 24px; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
.ads {
	MARGIN: 5px; WIDTH: 350px; FLOAT: left
}
.ads_content {
	MARGIN: 8px 5px 8px 10px; WIDTH: 305px; FLOAT: right
}
.cnt {
	TEXT-ALIGN: center; LINE-HEIGHT: 40px; WIDTH: 240px; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 40px
}
.nairong {
	WIDTH: 220px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: right; COLOR: #666666
}
.ran {
	COLOR: #009add
}
.green {
	COLOR: #57a940
}
.cheng {
	COLOR: #e08c06
}
.hei {
	COLOR: #444444
}
.reds {
	COLOR: #bf0908
}
.hei1 {
	
}
.linetai {
	BORDER-BOTTOM: #cccccc 1px dashed; TEXT-ALIGN: right; WIDTH: 495px; MARGIN-LEFT: 65px
}
#sddm {
	Z-INDEX: 30; PADDING-BOTTOM: 0px; MARGIN: 0px auto; PADDING-LEFT: 0px; WIDTH: 480px; PADDING-RIGHT: 0px; HEIGHT: 30px; PADDING-TOP: 0px
}
#sddm LI {
	PADDING-BOTTOM: 0px; LIST-STYLE-TYPE: none; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; FONT: bold 12px arial; FLOAT: left; LIST-STYLE-IMAGE: none; PADDING-TOP: 0px
}
#sddm LI A {
	TEXT-ALIGN: center; PADDING-BOTTOM: 4px; MARGIN: 0px 1px 0px 0px; PADDING-LEFT: 10px; WIDTH: 60px; PADDING-RIGHT: 10px; DISPLAY: block; BACKGROUND: #5970b2; COLOR: #fff; TEXT-DECORATION: none; PADDING-TOP: 4px
}
#sddm LI A:hover {
	BACKGROUND: #49a3ff
}
#sddm DIV {
	BORDER-BOTTOM: #5970b2 1px solid; POSITION: absolute; BORDER-LEFT: #5970b2 1px solid; PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; BACKGROUND: #eaebd8; VISIBILITY: hidden; BORDER-TOP: #5970b2 1px solid; BORDER-RIGHT: #5970b2 1px solid; PADDING-TOP: 0px
}
#sddm DIV A {
	POSITION: relative; TEXT-ALIGN: left; PADDING-BOTTOM: 5px; MARGIN: 0px; PADDING-LEFT: 10px; WIDTH: auto; PADDING-RIGHT: 10px; DISPLAY: block; FONT: 12px arial; WHITE-SPACE: nowrap; BACKGROUND: #eaebd8; COLOR: #2875de; TEXT-DECORATION: none; PADDING-TOP: 5px
}
#sddm DIV A:hover {
	BACKGROUND: #49a3ff; COLOR: #fff
}
.ds {
	BACKGROUND-IMAGE: url(../images/weixingnew.jpg); BORDER-BOTTOM: 0px; BORDER-LEFT: 0px; WIDTH: 18px; DISPLAY: block; FLOAT: left; HEIGHT: 15px; BORDER-TOP: 0px; CURSOR: pointer; BORDER-RIGHT: 0px
}
.dsbig {
	BACKGROUND-IMAGE: url(../images/weixin.png); BORDER-BOTTOM: 0px; BORDER-LEFT: 0px; WIDTH: 30px; DISPLAY: block; FLOAT: left; HEIGHT: 26px; BORDER-TOP: 0px; CURSOR: pointer; BORDER-RIGHT: 0px
}
.feat-area {
	POSITION: relative; MIN-WIDTH: 100%; HEIGHT: 400px; OVERFLOW: hidden
}
.slider {
	POSITION: absolute; MIN-WIDTH: 1000px; WIDTH: 100%; DISPLAY: none; HEIGHT: 330px; OVERFLOW: hidden; TOP: 0px; LEFT: 0px
}
.slider LI {
	POSITION: absolute; WIDTH: 100%; DISPLAY: none; HEIGHT: 330px; OVERFLOW: hidden; TOP: 0px; LEFT: 0px
}
.slider LI A {
	TEXT-INDENT: -9999em; DISPLAY: block; HEIGHT: 400px; OVERFLOW: hidden
}
.gain-cont A {
	COLOR: #fff; TEXT-DECORATION: underline
}
.gain-cont A:visited {
	COLOR: #fff; TEXT-DECORATION: underline
}
.gain-cont A:hover {
	TEXT-DECORATION: none
}
.gain-cont {
	Z-INDEX: 5; POSITION: relative; PADDING-BOTTOM: 0px; PADDING-LEFT: 35px; PADDING-RIGHT: 35px; COLOR: #fff; PADDING-TOP: 0px
}
.gain-cont .light-fc {
	FONT-FAMILY: Tahoma, Geneva, sans-serif; COLOR: #fc8026
}
.gain-cont EM {
	FONT-FAMILY: Tahoma, Geneva, sans-serif; COLOR: #fc8026; FONT-SIZE: 20px
}
.gain-cont H2 {
	FONT-SIZE: 18px; FONT-WEIGHT: normal; PADDING-TOP: 15px
}
.gain-cont .f {
	LINE-HEIGHT: 50px; FONT-FAMILY: Tahoma, Geneva, sans-serif; HEIGHT: 50px; COLOR: #fc8026; FONT-SIZE: 34px; OVERFLOW: hidden
}
.gain-cont .f EM {
	FONT-SIZE: 40px
}
.gain-cont .reg {
	PADDING-BOTTOM: 5px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; HEIGHT: 46px; PADDING-TOP: 10px
}
.gain-cont .reg A {
	TEXT-ALIGN: center; LINE-HEIGHT: 46px; DISPLAY: block; BACKGROUND: #fc8026; HEIGHT: 46px; FONT-SIZE: 18px; TEXT-DECORATION: none
}
.gain-cont .tar {
	TEXT-ALIGN: right; PADDING-BOTTOM: 25px; MARGIN-TOP: 7px
}
.gain {
	POSITION: absolute; WIDTH: 320px; HEIGHT: 270px; OVERFLOW: hidden; TOP: -600px; RIGHT: 0px
}
.gain .opacity {
	Z-INDEX: 1; POSITION: absolute; WIDTH: 320px; BACKGROUND: #000; HEIGHT: 270px; TOP: 0px; LEFT: 0px; opacity: 0.5
}
.gain-cont A {
	COLOR: #fff; TEXT-DECORATION: underline
}
.gain-cont A:visited {
	COLOR: #fff; TEXT-DECORATION: underline
}
.gain-cont A:hover {
	TEXT-DECORATION: none
}
.gain-cont {
	Z-INDEX: 5; POSITION: relative; PADDING-BOTTOM: 0px; PADDING-LEFT: 35px; PADDING-RIGHT: 35px; COLOR: #fff; PADDING-TOP: 0px
}
.gain-cont .light-fc {
	FONT-FAMILY: Tahoma, Geneva, sans-serif; COLOR: #fc8026
}
.gain-cont EM {
	FONT-FAMILY: Tahoma, Geneva, sans-serif; COLOR: #fc8026; FONT-SIZE: 20px
}
.gain-cont H2 {
	FONT-SIZE: 18px; FONT-WEIGHT: normal; PADDING-TOP: 15px
}
.gain-cont .f {
	LINE-HEIGHT: 50px; FONT-FAMILY: Tahoma, Geneva, sans-serif; HEIGHT: 50px; COLOR: #fc8026; FONT-SIZE: 34px; OVERFLOW: hidden
}
.gain-cont .f EM {
	FONT-SIZE: 40px
}
.gain-cont .reg {
	PADDING-BOTTOM: 5px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; HEIGHT: 46px; PADDING-TOP: 10px
}
.gain-cont .reg A {
	TEXT-ALIGN: center; LINE-HEIGHT: 46px; DISPLAY: block; BACKGROUND: #fc8026; HEIGHT: 46px; FONT-SIZE: 18px; TEXT-DECORATION: none
}
.gain-cont .tar {
	TEXT-ALIGN: right; PADDING-BOTTOM: 25px; MARGIN-TOP: 7px
}
.feat-area {
	POSITION: relative; MIN-WIDTH: 100%; HEIGHT: 330px; OVERFLOW: hidden
}
.bgsee {
	WIDTH: 100%; HEIGHT: 400px
}
.linehei {
	BACKGROUND-COLOR: #ccc; WIDTH: 100%; FLOAT: left; HEIGHT: 1px
}
#xingqing {
	BORDER-BOTTOM: #dcdcdc 1px solid; BORDER-LEFT: #dcdcdc 1px solid; BACKGROUND-COLOR: #fff; MARGIN: 10px auto 0px; WIDTH: 956px; HEIGHT: 100%; BORDER-TOP: #dcdcdc 1px solid; BORDER-RIGHT: #dcdcdc 1px solid
}
.listbai {
	WIDTH: 50px
}
.rec-items1 .items1 {
	BORDER-BOTTOM: #e9edef 1px solid; LINE-HEIGHT: 50px; PADDING-LEFT: 30px; HEIGHT: 50px; OVERFLOW: hidden
}
.rec-items1 LI {
	POSITION: relative; TEXT-ALIGN: left; WIDTH: 20%; DISPLAY: inline; FLOAT: left; HEIGHT: 50px
}
.rec-items1 {
	
}
.rec-items1 {
	WIDTH: 22%
}
.rec-items1 {
	
}
.rec-items1 {
	WIDTH: 16%
}
.rec-items1 {
	TEXT-ALIGN: center; WIDTH: 18%
}
.rec-items1 LI {
	POSITION: relative; LINE-HEIGHT: 16px; MARGIN: 18px auto 0px; WIDTH: 30px; DISPLAY: block; HEIGHT: 16px
}
.rec-items1 LI {
	POSITION: absolute; TOP: 6px; LEFT: -16px
}
.rec-items1 {
	BACKGROUND: #f0f5f7; FONT-SIZE: 13px
}
.rec-items1 {
	COLOR: #fc9b5e
}
.rec-items1 {
	MARGIN: 17px 0px 0px 3px; DISPLAY: inline; FLOAT: left
}
.boss {
	BORDER-BOTTOM: #d3d8dc 1px solid; BORDER-LEFT: #d3d8dc 1px solid; BORDER-RIGHT: #d3d8dc 1px solid
}
.blues {
	COLOR: #0e87d6
}
.xiang {
	BACKGROUND-IMAGE: url(../images/xiang.jpg); MARGIN: 35px; WIDTH: 437px; FLOAT: left; HEIGHT: 430px
}
.xiang_r {
	BACKGROUND-IMAGE: url(../images/xiang.jpg); MARGIN: 35px; WIDTH: 437px; FLOAT: right; HEIGHT: 430px
}
.xinag_w {
	MARGIN: 0px auto; WIDTH: 955px
}
.xing_bg_k {
	BACKGROUND-IMAGE: url(../images/xiang.jpg); WIDTH: 437px; HEIGHT: 430px
}
.kws {
	BACKGROUND-IMAGE: url(../images/kws.jpg); WIDTH: 437px; HEIGHT: 430px
}
.bouut {
	BACKGROUND-COLOR: #008ed6; MARGIN: 8px; HEIGHT: 25px
}
.mybtn {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 2em; OUTLINE-WIDTH: medium; WIDTH: 110px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #008ed6; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn:hover {
	BACKGROUND: #0279b5
}
.mybtn2 {
	TEXT-ALIGN: center; LINE-HEIGHT: 100%; OUTLINE-COLOR: invert; PADDING-LEFT: 0.9em; OUTLINE-WIDTH: medium; WIDTH: 110px; PADDING-RIGHT: 1em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BACKGROUND: #008ed6; HEIGHT: 30px; COLOR: #fefee9; FONT-SIZE: 14px; VERTICAL-ALIGN: text-bottom; CURSOR: pointer; TEXT-DECORATION: none; font-size-adjust: none; font-stretch: normal; background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn2:hover {
	BACKGROUND: #0279b5
}
.mybtn1 {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 2em; OUTLINE-WIDTH: medium; WIDTH: 110px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #06a852; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn1:hover {
	BACKGROUND: #059a4b
}
.kss {
	TEXT-DECORATION: underline
}
.gen-1 {
	LINE-HEIGHT: 110%
}
.licai-1 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 906px; HEIGHT: 389px; MARGIN-LEFT: auto; BORDER-TOP: medium none; MARGIN-RIGHT: auto; BORDER-RIGHT: medium none
}
.licai-1-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 437px; PADDING-RIGHT: 0px; HEIGHT: 389px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.licai-2 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 372px; HEIGHT: 389px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.licai-2-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 372px; PADDING-RIGHT: 0px; HEIGHT: 123px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: -1px
}
.gen-2 {
	MARGIN-TOP: 0px
}
.licai-2-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 18px; PADDING-LEFT: 0px; WIDTH: 372px; PADDING-RIGHT: 0px; BORDER-TOP: medium none; TOP: 123px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.licai-2-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 372px; PADDING-RIGHT: 0px; HEIGHT: 195px; BORDER-TOP: medium none; TOP: 182px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.licai-1-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 389px; PADDING-LEFT: 0px; WIDTH: 32px; PADDING-RIGHT: 0px; HEIGHT: 389px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 437px
}
.licai-1-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 437px; PADDING-RIGHT: 0px; HEIGHT: 389px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 469px
}
.licai-3 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 372px; HEIGHT: 389px; MARGIN-LEFT: auto; BORDER-TOP: medium none; MARGIN-RIGHT: auto; BORDER-RIGHT: medium none
}
.licai-4 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 911px; HEIGHT: 60px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.licai-4-1 {
	BACKGROUND-IMAGE: url(../css/images/yaos.jpg); BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 911px; PADDING-RIGHT: 0px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.licai-5 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; MARGIN-TOP: 20px; WIDTH: 889px; HEIGHT: 19px; MARGIN-LEFT: auto; BORDER-TOP: medium none; MARGIN-RIGHT: auto; BORDER-RIGHT: medium none
}
.licai-5-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 19px; PADDING-LEFT: 0px; WIDTH: 417px; PADDING-RIGHT: 0px; HEIGHT: 19px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.licai-5-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 19px; PADDING-LEFT: 0px; WIDTH: 472px; PADDING-RIGHT: 0px; HEIGHT: 19px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 417px
}
.line25 {
	LINE-HEIGHT: 25
}
.licai_list {
	MARGIN: 20px auto 0px; WIDTH: 900px
}
.licai_title {
	TEXT-ALIGN: left; WIDTH: 900px; HEIGHT: 45px
}
.main_kan_new {
	MARGIN: 0px auto; MIN-HEIGHT: 500px; WIDTH: 978px; margin-top:5px;
}
HTML {
	FILTER: expression(document.execCommand("BackgroundImageCache", false, true))
}
#Page {
	FONT-FAMILY: Verdana; FLOAT: left; HEIGHT: 50px; MARGIN-LEFT: 720px; PADDING-TOP: 20px
}
#Page A {
	BORDER-BOTTOM: #2991e6 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #2991e6 1px solid; LINE-HEIGHT: 20px; MARGIN: 10px 1px 0px; WIDTH: 26px; FLOAT: left; HEIGHT: 20px; COLOR: #2991e6; BORDER-TOP: #2991e6 1px solid; BORDER-RIGHT: #2991e6 1px solid; TEXT-DECORATION: none
}
#Page A:hover {
	BORDER-BOTTOM: #2991e6 1px solid; POSITION: relative; BORDER-LEFT: #2991e6 1px solid; PADDING-BOTTOM: 0px; LINE-HEIGHT: 40px; MARGIN: 0px -10px 0px -12px; PADDING-LEFT: 9px; WIDTH: 30px; PADDING-RIGHT: 9px; BACKGROUND: no-repeat left -10px; HEIGHT: 40px; COLOR: #000; FONT-SIZE: 18px; BORDER-TOP: #2991e6 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #2991e6 1px solid; PADDING-TOP: 0px
}
#Page SPAN {
	BORDER-BOTTOM: #2991e6 1px solid; BORDER-LEFT: #2991e6 1px solid; PADDING-BOTTOM: 0px; LINE-HEIGHT: 165%; MARGIN: 10px 1px 0px; PADDING-LEFT: 8px; PADDING-RIGHT: 8px; BACKGROUND: #00a8e8; FLOAT: left; COLOR: #fff; BORDER-TOP: #2991e6 1px solid; FONT-WEIGHT: bold; BORDER-RIGHT: #2991e6 1px solid; PADDING-TOP: 0px
}
.account {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; MIN-HEIGHT: 1200px; WIDTH: 978px; MAX-HEIGHT: 1500px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.account_left {
	MIN-HEIGHT: 1200px; WIDTH: 174px; FLOAT: left; MAX-HEIGHT: 1500px; BORDER-RIGHT: #e8ecef 1px solid
}
.account_right {
	WIDTH: 800px; FLOAT: right
}
.ran1 {
	COLOR: #349cd8
}
.gray1 {
	COLOR: #808080
}
.gray3 {
	FLOAT: left; COLOR: #808080; PADDING-TOP: 15px
}
.chen {
	COLOR: #f58708
}
.account_gay {
	COLOR: #8a949c
}
.account_gay1 {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #818080
}
.account_gau2 {
	COLOR: #676767
}
.account-1 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 160px; FLOAT: right; HEIGHT: 49px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-1-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 19px; PADDING-LEFT: 0px; WIDTH: 160px; PADDING-RIGHT: 0px; HEIGHT: 19px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-1-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 160px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 19px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-2 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 174px; FLOAT: right; HEIGHT: 40px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-2-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 174px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-3 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; MARGIN-TOP: 11px; WIDTH: 150px; FLOAT: right; HEIGHT: 18px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-3-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 18px; PADDING-LEFT: 0px; WIDTH: 31px; PADDING-RIGHT: 0px; HEIGHT: 18px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.gen-2 {
	VERTICAL-ALIGN: middle
}
.account-3-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 18px; PADDING-LEFT: 0px; WIDTH: 119px; PADDING-RIGHT: 0px; HEIGHT: 18px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 31px
}
.account-4-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 174px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-10 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 160px; FLOAT: right; HEIGHT: 45px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-10-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 45px; PADDING-LEFT: 0px; WIDTH: 160px; PADDING-RIGHT: 0px; HEIGHT: 45px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-11-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 174px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-19 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; MARGIN-TOP: 10px; WIDTH: 150px; FLOAT: right; HEIGHT: 19px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-19-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 19px; PADDING-LEFT: 0px; WIDTH: 31px; PADDING-RIGHT: 0px; HEIGHT: 19px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-19-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 19px; PADDING-LEFT: 0px; WIDTH: 119px; PADDING-RIGHT: 0px; HEIGHT: 19px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 31px
}
.account-20 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 774px; FLOAT: right; HEIGHT: 132px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-20-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 22px; PADDING-LEFT: 0px; WIDTH: 774px; PADDING-RIGHT: 0px; HEIGHT: 22px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-20-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 131px; PADDING-RIGHT: 0px; HEIGHT: 110px; BORDER-TOP: medium none; TOP: 22px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-21 {
	BORDER-BOTTOM: #ccc 1px solid; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: #ccc 1px solid; WIDTH: 105px; HEIGHT: 107px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.account-21-1 {
	POSITION: absolute; TEXT-ALIGN: center; LINE-HEIGHT: 105px; BACKGROUND-COLOR: #ffffff; WIDTH: 103px; HEIGHT: 105px; PADDING-TOP: 2px; LEFT: 1px
}
.account-20-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 643px; PADDING-RIGHT: 0px; HEIGHT: 110px; BORDER-TOP: medium none; TOP: 22px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 131px
}
.account-22 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 635px; HEIGHT: 69px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-22-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 0px; WIDTH: 635px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-22-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 58px; PADDING-RIGHT: 0px; HEIGHT: 39px; BORDER-TOP: medium none; TOP: 30px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-22-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 39px; PADDING-LEFT: 0px; WIDTH: 129px; PADDING-RIGHT: 0px; HEIGHT: 39px; BORDER-TOP: medium none; TOP: 30px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 58px
}
.account-22-4 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 39px; PADDING-LEFT: 0px; WIDTH: 84px; PADDING-RIGHT: 0px; HEIGHT: 39px; BORDER-TOP: medium none; TOP: 10px; RIGHT: 230px; BORDER-RIGHT: medium none; PADDING-TOP: 0px
}
.account-22-5 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 39px; PADDING-LEFT: 0px; WIDTH: 153px; PADDING-RIGHT: 0px; FLOAT: right; HEIGHT: 39px; BORDER-TOP: medium none; TOP: 10px; RIGHT: 110px; BORDER-RIGHT: medium none; PADDING-TOP: 0px
}
.account-22-6 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 101px; PADDING-RIGHT: 0px; FLOAT: right; HEIGHT: 39px; BORDER-TOP: medium none; TOP: 20px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 424px
}
.account-22-7 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 110px; PADDING-RIGHT: 0px; HEIGHT: 39px; BORDER-TOP: medium none; TOP: 20px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 525px
}
.account-23 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 635px; HEIGHT: 41px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-23-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 187px; PADDING-RIGHT: 0px; HEIGHT: 41px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-24 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 171px; HEIGHT: 40px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-24-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 40px; PADDING-LEFT: 0px; WIDTH: 41px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-24-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 40px; PADDING-LEFT: 0px; WIDTH: 41px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 41px
}
.account-24-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 40px; PADDING-LEFT: 0px; WIDTH: 41px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 82px
}
.account-24-4 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 40px; PADDING-LEFT: 0px; WIDTH: 48px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 123px
}
.account-23-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 41px; PADDING-LEFT: 0px; WIDTH: 448px; PADDING-RIGHT: 0px; HEIGHT: 41px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 187px
}
.account-25 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 774px; FLOAT: right; HEIGHT: 50px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-25-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 50px; PADDING-LEFT: 0px; WIDTH: 105px; PADDING-RIGHT: 0px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.gen-31 {
	COLOR: #349cd8; VERTICAL-ALIGN: middle; TEXT-DECORATION: underline
}
.account-25-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 50px; PADDING-LEFT: 0px; WIDTH: 26px; PADDING-RIGHT: 0px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 105px
}
.account-25-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 50px; PADDING-LEFT: 0px; WIDTH: 643px; PADDING-RIGHT: 0px; HEIGHT: 50px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 131px
}
.account-26 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 774px; FLOAT: right; HEIGHT: 110px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-26-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 774px; PADDING-RIGHT: 0px; HEIGHT: 110px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-27 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 732px; FLOAT: left; HEIGHT: 110px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-27-1 {
	BACKGROUND-IMAGE: url(../images/cico10.jpg); BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 732px; PADDING-RIGHT: 0px; HEIGHT: 110px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-28 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; MARGIN-TOP: 16px; WIDTH: 728px; HEIGHT: 77px; MARGIN-LEFT: auto; BORDER-TOP: medium none; MARGIN-RIGHT: auto; BORDER-RIGHT: medium none
}
.account-28-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 171px; PADDING-RIGHT: 0px; HEIGHT: 77px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-29 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 168px; HEIGHT: 77px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-29-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 18px; PADDING-LEFT: 0px; WIDTH: 168px; PADDING-RIGHT: 0px; HEIGHT: 18px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-29-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 40px; PADDING-LEFT: 0px; WIDTH: 168px; PADDING-RIGHT: 0px; HEIGHT: 40px; BORDER-TOP: medium none; TOP: 18px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-29-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 19px; PADDING-LEFT: 0px; WIDTH: 168px; PADDING-RIGHT: 0px; HEIGHT: 19px; BORDER-TOP: medium none; TOP: 58px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-28-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 194px; PADDING-RIGHT: 0px; HEIGHT: 77px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 171px
}
.account-28-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 196px; PADDING-RIGHT: 0px; HEIGHT: 77px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 364px
}
.account-28-4 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 168px; PADDING-RIGHT: 0px; HEIGHT: 77px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 560px
}
.account-33 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 774px; FLOAT: right; HEIGHT: 263px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-33-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 774px; PADDING-RIGHT: 0px; HEIGHT: 263px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-34 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 772px; HEIGHT: 263px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-34-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 772px; PADDING-RIGHT: 0px; HEIGHT: 263px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-35 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 732px; HEIGHT: 244px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-35-1 {
	BORDER-BOTTOM: #d3d8dc 1px solid; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: #d3d8dc 1px solid; PADDING-BOTTOM: 0px; LINE-HEIGHT: 242px; PADDING-LEFT: 0px; WIDTH: 730px; PADDING-RIGHT: 0px; HEIGHT: 242px; BORDER-TOP: #d3d8dc 1px solid; TOP: 0px; BORDER-RIGHT: #d3d8dc 1px solid; PADDING-TOP: 0px; LEFT: 0px
}
.account-36 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 774px; FLOAT: right; HEIGHT: 361px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-36-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 774px; PADDING-RIGHT: 0px; HEIGHT: 361px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-37 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 772px; HEIGHT: 361px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-37-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 772px; PADDING-RIGHT: 0px; HEIGHT: 361px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-38 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 733px; HEIGHT: 55px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-38-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 55px; PADDING-LEFT: 0px; WIDTH: 733px; PADDING-RIGHT: 0px; HEIGHT: 55px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-39 {
	BORDER-BOTTOM: #d3d8dc 1px solid; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: #d3d8dc 1px solid; WIDTH: 732px; HEIGHT: 306px; BORDER-TOP: #d3d8dc 1px solid; BORDER-RIGHT: #d3d8dc 1px solid
}
.account-rank {
	BORDER-BOTTOM: #d3d8dc 1px solid; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: #d3d8dc 1px solid; WIDTH: 460px; HEIGHT: 230px; BORDER-TOP: #d3d8dc 1px solid; BORDER-RIGHT: #d3d8dc 1px solid
}
.account-39-1 {
	BORDER-BOTTOM: #d3d8dc 1px solid; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: #d3d8dc 1px solid; PADDING-BOTTOM: 0px; LINE-HEIGHT: 304px; PADDING-LEFT: 0px; WIDTH: 730px; PADDING-RIGHT: 0px; HEIGHT: 304px; BORDER-TOP: #d3d8dc 1px solid; TOP: 0px; BORDER-RIGHT: #d3d8dc 1px solid; PADDING-TOP: 0px; LEFT: 0px
}
.account-40 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 774px; FLOAT: right; HEIGHT: 325px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-40-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 774px; PADDING-RIGHT: 0px; HEIGHT: 325px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-41 {
	BORDER-BOTTOM: medium none; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: medium none; WIDTH: 772px; HEIGHT: 325px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.account-41-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 615px; PADDING-RIGHT: 0px; HEIGHT: 55px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.gen-45 {
	MARGIN-TOP: 18px
}
.account-41-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 55px; PADDING-LEFT: 0px; WIDTH: 157px; PADDING-RIGHT: 0px; HEIGHT: 55px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 615px
}
.gen-46 {
	COLOR: #349cd8; VERTICAL-ALIGN: middle
}
.account-41-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 772px; PADDING-RIGHT: 0px; HEIGHT: 270px; BORDER-TOP: medium none; TOP: 55px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.account-42 {
	BORDER-BOTTOM: #d3d8dc 1px solid; POSITION: relative; TEXT-ALIGN: left; BORDER-LEFT: #d3d8dc 1px solid; BACKGROUND-COLOR: #dddddd; WIDTH: 732px; HEIGHT: 194px; BORDER-TOP: #d3d8dc 1px solid; BORDER-RIGHT: #d3d8dc 1px solid
}
.account-42-1 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 60px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 1px
}
.account-42-2 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 105px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 62px
}
.account-42-3 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 168px
}
.account-42-4 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 91px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 240px
}
.account-42-5 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 62px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 332px
}
.account-42-6 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 77px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 395px
}
.account-42-7 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 87px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 473px
}
.account-42-8 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 561px
}
.account-42-9 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 46px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 633px
}
.account-42-10 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 37px; BACKGROUND-COLOR: #f0f4f7; PADDING-LEFT: 0px; WIDTH: 51px; PADDING-RIGHT: 0px; HEIGHT: 37px; BORDER-TOP: medium none; TOP: 1px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 680px
}
.account-42-11 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 60px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 1px
}
.account-42-12 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 105px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 62px
}
.account-42-13 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 168px
}
.account-42-14 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 91px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 240px
}
.account-42-15 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 62px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 332px
}
.account-42-16 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 77px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 395px
}
.account-42-17 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 87px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 473px
}
.account-42-18 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 561px
}
.account-42-19 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 46px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 633px
}
.account-42-20 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 51px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 39px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 680px
}
.account-42-21 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 60px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 1px
}
.account-42-22 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 105px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 62px
}
.account-42-23 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 168px
}
.account-42-24 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 91px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 240px
}
.account-42-25 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 62px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 332px
}
.account-42-26 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 77px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 395px
}
.account-42-27 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 87px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 473px
}
.account-42-28 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 561px
}
.account-42-29 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 46px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 633px
}
.account-42-30 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 51px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 70px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 680px
}
.account-42-31 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 60px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 1px
}
.account-42-32 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 105px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 62px
}
.account-42-33 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 168px
}
.account-42-34 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 91px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 240px
}
.account-42-35 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 62px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 332px
}
.account-42-36 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 77px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 395px
}
.account-42-37 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 87px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 473px
}
.account-42-38 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 561px
}
.account-42-39 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 46px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 633px
}
.account-42-40 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 51px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 101px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 680px
}
.account-42-41 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 60px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 1px
}
.account-42-42 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 105px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 62px
}
.account-42-43 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 168px
}
.account-42-44 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 91px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 240px
}
.account-42-45 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 62px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 332px
}
.account-42-46 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 77px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 395px
}
.account-42-47 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 87px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 473px
}
.account-42-48 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 561px
}
.account-42-49 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 46px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 633px
}
.account-42-50 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #fbfcfd; PADDING-LEFT: 0px; WIDTH: 51px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 132px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 680px
}
.account-42-51 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 60px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 1px
}
.account-42-52 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 105px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 62px
}
.account-42-53 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 168px
}
.account-42-54 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 91px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 240px
}
.account-42-55 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 62px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 332px
}
.account-42-56 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 77px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 395px
}
.account-42-57 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 87px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 473px
}
.account-42-58 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 71px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 561px
}
.account-42-59 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 46px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 633px
}
.account-42-60 {
	BORDER-BOTTOM: medium none; POSITION: absolute; TEXT-ALIGN: center; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 0px; WIDTH: 51px; PADDING-RIGHT: 0px; HEIGHT: 30px; BORDER-TOP: medium none; TOP: 163px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 680px
}
.bh {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 35px; PADDING-LEFT: 5px; WIDTH: 235px; HEIGHT: 35px; COLOR: #bbb; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.bh_d {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; PADDING-LEFT: 5px; WIDTH: 50px; HEIGHT: 30px; COLOR: #cccccc; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.hei5 {
	COLOR: #66696b
}
.pptop {
	MARGIN-TOP: 10px; FLOAT: left
}
.tabtop-1 {
	BORDER-BOTTOM: medium none; TEXT-ALIGN: left; BORDER-LEFT: medium none; LINE-HEIGHT: 30px; WIDTH: 308px; HEIGHT: 30px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.tabtop-1-1 {
	BORDER-BOTTOM: medium none; TEXT-ALIGN: left; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 0px; WIDTH: 60px; PADDING-RIGHT: 0px; FLOAT: left; HEIGHT: 30px; BORDER-TOP: medium none; BORDER-RIGHT: medium none; PADDING-TOP: 0px
}
.tabtop-1-2 {
	BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 36px; PADDING-LEFT: 0px; WIDTH: 30px; PADDING-RIGHT: 0px; FLOAT: left; HEIGHT: 36px; BORDER-TOP: medium none; BORDER-RIGHT: medium none; PADDING-TOP: 0px
}
.gen-1 {
	VERTICAL-ALIGN: middle
}
.tabtop-1-4 {
	BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; LINE-HEIGHT: 36px; PADDING-LEFT: 0px; WIDTH: 100px; PADDING-RIGHT: 0px; FLOAT: left; HEIGHT: 36px; BORDER-TOP: medium none; BORDER-RIGHT: medium none; PADDING-TOP: 0px
}
.gen-3 {
	CLEAR: both
}
.tabtoptop {
	MARGIN-TOP: 7px
}
.toptab {
	MARGIN-TOP: 10px; FLOAT: left; HEIGHT: 10px
}
.title-items {
	POSITION: relative; HEIGHT: 70px; OVERFLOW: hidden
}
.title-items .toback {
	Z-INDEX: 5; POSITION: absolute; TEXT-ALIGN: center; LINE-HEIGHT: 30px; TEXT-INDENT: 10px; WIDTH: 60px; BACKGROUND: rgb(232,236,239); FLOAT: left; HEIGHT: 30px; COLOR: rgb(71,80,88); TOP: 20px; RIGHT: 0px
}
.title-items .toback .icons {
	POSITION: absolute; TOP: 10px; LEFT: 10px
}
.title-items .num {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 20px; MARGIN: 0px 3px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline-block; BACKGROUND: rgb(0,143,216); HEIGHT: 20px; COLOR: rgb(255,255,255); FONT-SIZE: 14px; PADDING-TOP: 0px
}
.title-items .line {
	POSITION: absolute; LINE-HEIGHT: 0; WIDTH: 100%; BACKGROUND: rgb(231,236,238); FLOAT: left; HEIGHT: 1px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 35px; LEFT: 0px
}
.title-items H2 {
	Z-INDEX: 3; POSITION: absolute; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; BACKGROUND: rgb(255,255,255); HEIGHT: 30px; COLOR: rgb(105,115,123); FONT-SIZE: 16px; TOP: 20px; PADDING-TOP: 0px; LEFT: 20px
}
.title-items .r {
	Z-INDEX: 3; POSITION: absolute; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; BACKGROUND: rgb(255,255,255); HEIGHT: 30px; COLOR: rgb(105,115,123); FONT-SIZE: 16px; TOP: 20px; PADDING-TOP: 0px; LEFT: 20px
}
.title-items .r {
	PADDING-RIGHT: 0px; COLOR: rgb(138,148,156); FONT-SIZE: 14px; RIGHT: 30px; LEFT: auto
}
.title-items .light-red {
	POSITION: relative; PADDING-LEFT: 12px; DISPLAY: inline-block
}
.title-items .arrow-red-down {
	POSITION: absolute; TOP: 9px; LEFT: 0px
}
.toptab1 {
	WIDTH: 100px; FLOAT: left; HEIGHT: 20px
}
.model-box {
	border-radius: 3px; -moz-border-radius: 3px; -khtml-border-radius: 3px; -webkit-border-radius: 3px
}
#main {
	MARGIN: 0px 20px; OVERFLOW: hidden
}
.trade-filter {
	PADDING-BOTTOM: 10px
}
.filter-items {
	LINE-HEIGHT: 30px; MARGIN: 0px 40px; ZOOM: 1; OVERFLOW: hidden; PADDING-TOP: 10px
}
.filter-items DT {
	WIDTH: 100px; DISPLAY: inline; FLOAT: left
}
.filter-items DD {
	HEIGHT: 30px; MARGIN-LEFT: 100px
}
.filter-items DD A {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 24px; MARGIN: 4px 10px 0px 0px; PADDING-LEFT: 10px; PADDING-RIGHT: 10px; DISPLAY: inline; FLOAT: left; HEIGHT: 24px; COLOR: rgb(71,80,88); PADDING-TOP: 0px; border-radius: 3px
}
.filter-items DD A:visited {
	COLOR: rgb(71,80,88)
}
.filter-items DD A:hover {
	BACKGROUND: rgb(0,143,216); COLOR: rgb(255,255,255); TEXT-DECORATION: none
}
.filter-items A.selected {
	BACKGROUND: rgb(0,143,216); COLOR: rgb(255,255,255); CURSOR: default
}
.filter-items A.selected:visited {
	BACKGROUND: rgb(0,143,216); COLOR: rgb(255,255,255); CURSOR: default
}
.msg-apk A.selected {
	BACKGROUND: rgb(0,143,216); COLOR: rgb(255,255,255); CURSOR: default
}
.msg-apk A.selected:visited {
	BACKGROUND: rgb(0,143,216); COLOR: rgb(255,255,255); CURSOR: default
}
.trade-table {
	ZOOM: 1; FONT-SIZE: 12px; OVERFLOW: hidden
}
.trade-table TABLE {
	TEXT-ALIGN: left; LINE-HEIGHT: 1.6; WIDTH: 100%
}
.trade-table TH {
	PADDING-BOTTOM: 15px; PADDING-LEFT: 43px; PADDING-RIGHT: 5px; PADDING-TOP: 15px
}
.trade-table TD {
	PADDING-BOTTOM: 15px; PADDING-LEFT: 43px; PADDING-RIGHT: 5px; PADDING-TOP: 15px
}
.trade-table TH {
	TEXT-ALIGN: left; BACKGROUND: rgb(232,236,239); FONT-SIZE: 13px; VERTICAL-ALIGN: middle
}
.trade-table TD {
	BORDER-BOTTOM: rgb(232,236,239) 1px solid; VERTICAL-ALIGN: middle
}
.trade-table .over {
	BACKGROUND: rgb(250,251,252)
}
.trade-table .double {
	BACKGROUND: rgb(250,251,252)
}
.trade-table .amount {
	POSITION: relative
}
.trade-table .amount .icons {
	MARGIN-LEFT: 5px
}
.model-box {
	BORDER-BOTTOM: rgb(211,216,220) 1px solid; POSITION: relative; BORDER-LEFT: rgb(211,216,220) 1px solid; ZOOM: 1; MARGIN-BOTTOM: 20px; OVERFLOW: hidden; BORDER-TOP: rgb(211,216,220) 1px solid; BORDER-RIGHT: rgb(211,216,220) 1px solid
}
.r {
	Z-INDEX: 3; POSITION: absolute; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; BACKGROUND: rgb(255,255,255); HEIGHT: 30px; COLOR: rgb(105,115,123); FONT-SIZE: 16px; TOP: 251px; PADDING-TOP: 0px; LEFT: 1073px
}
.query-input {
	PADDING-RIGHT: 20px; DISPLAY: inline; FLOAT: left; HEIGHT: 30px
}
.query-input LABEL {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline; FLOAT: left; PADDING-TOP: 0px
}
.query-input INPUT {
	PADDING-BOTTOM: 5px; LINE-HEIGHT: 18px; TEXT-INDENT: 10px; PADDING-LEFT: 25px; WIDTH: 90px; PADDING-RIGHT: 0px; DISPLAY: inline; FONT-FAMILY: Tahoma; BACKGROUND: url(img/common/ui-date.png) rgb(240,244,247) no-repeat 10px 9px; FLOAT: left; HEIGHT: 18px; OVERFLOW: hidden; CURSOR: default; PADDING-TOP: 6px; border-radius: 3px
}
.inputs {
	PADDING-BOTTOM: 5px; LINE-HEIGHT: 18px; TEXT-INDENT: 10px; PADDING-LEFT: 25px; WIDTH: 90px; PADDING-RIGHT: 0px; DISPLAY: inline; FONT-FAMILY: Tahoma; BACKGROUND: url(img/common/ui-date.png) rgb(240,244,247) no-repeat 10px 9px; HEIGHT: 18px; MARGIN-LEFT: 10px; OVERFLOW: hidden; CURSOR: default; PADDING-TOP: 6px; border-radius: 3px
}
.queryInputs {
	PADDING-BOTTOM: 3px; LINE-HEIGHT: 24px; MARGIN: 4px 10px 0px 0px; PADDING-LEFT: 10px; PADDING-RIGHT: 10px; DISPLAY: inline; BACKGROUND: rgb(0,143,216); HEIGHT: 24px; CURSOR: pointer; PADDING-TOP: 3px; border-radius: 3px
}
.cash-top {
	HEIGHT: 90px
}
.stats-items .items {
	POSITION: relative; PADDING-BOTTOM: 20px; PADDING-LEFT: 0px; WIDTH: 100%; PADDING-RIGHT: 0px; HEIGHT: 70px; MARGIN-LEFT: 3px; OVERFLOW: hidden; PADDING-TOP: 20px
}
.assets-group .items {
	BORDER-BOTTOM: rgb(171,176,181) 1px solid; POSITION: relative; WIDTH: 16%; DISPLAY: inline; FLOAT: left; HEIGHT: 100%
}
.package-table .items {
	BORDER-BOTTOM: rgb(233,237,239) 1px solid; BORDER-LEFT: rgb(255,255,255) 1px solid; BACKGROUND: rgb(255,255,255); HEIGHT: 59px; OVERFLOW: hidden; BORDER-TOP: rgb(255,255,255) 1px solid; BORDER-RIGHT: rgb(255,255,255) 1px solid
}
.yield-group .items {
	POSITION: relative; WIDTH: 33%; DISPLAY: inline; FLOAT: left; HEIGHT: 100%
}
.yield-group .items H2 {
	POSITION: absolute; TEXT-ALIGN: center; WIDTH: 100%; BOTTOM: -30px; FONT-WEIGHT: normal; LEFT: 0px
}
.invest-plan .items {
	BORDER-BOTTOM: rgb(233,237,239) 1px solid; BORDER-LEFT: rgb(255,255,255) 1px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 20px; PADDING-RIGHT: 10px; BACKGROUND: rgb(255,255,255); HEIGHT: 57px; OVERFLOW: hidden; BORDER-TOP: rgb(255,255,255) 1px solid; BORDER-RIGHT: rgb(255,255,255) 1px solid; PADDING-TOP: 0px
}
.invest-plan-entry .items {
	BORDER-BOTTOM: rgb(255,255,255) 0px solid; BORDER-LEFT: rgb(255,255,255) 0px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 10px; PADDING-RIGHT: 0px; BORDER-TOP: rgb(255,255,255) 0px solid; BORDER-RIGHT: rgb(255,255,255) 0px solid; PADDING-TOP: 0px
}
.invest-loan .items {
	Z-INDEX: 3; BORDER-BOTTOM: rgb(233,237,239) 1px solid; POSITION: relative; PADDING-LEFT: 35px; HEIGHT: 57px; FONT-SIZE: 12px; OVERFLOW: hidden
}
.parame-post .items {
	BORDER-BOTTOM: rgb(231,236,238) 1px solid; LINE-HEIGHT: 55px; PADDING-LEFT: 30px; HEIGHT: 55px; OVERFLOW: hidden
}
.plan-post .items {
	BORDER-BOTTOM: rgb(233,237,239) 1px solid; PADDING-LEFT: 35px; HEIGHT: 57px; FONT-SIZE: 12px; OVERFLOW: hidden
}
.loan-parame-post .items {
	BORDER-BOTTOM: rgb(233,237,239) 1px solid; LINE-HEIGHT: 59px; HEIGHT: 59px
}
.loan-post .items {
	BORDER-BOTTOM: rgb(233,237,239) 1px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 21px; PADDING-RIGHT: 0px; HEIGHT: 57px; FONT-SIZE: 12px; OVERFLOW: hidden; PADDING-TOP: 0px
}
.plan-deal-items .items {
	BORDER-BOTTOM: rgb(233,237,239) 1px solid; PADDING-LEFT: 20px; HEIGHT: 57px; OVERFLOW: hidden
}
.redeem-items .items {
	Z-INDEX: 2; BORDER-BOTTOM: rgb(233,237,239) 1px solid; POSITION: relative; PADDING-BOTTOM: 0px; LINE-HEIGHT: 57px; PADDING-LEFT: 10px; PADDING-RIGHT: 10px; HEIGHT: 57px; OVERFLOW: hidden; PADDING-TOP: 0px
}
.recharge-entry {
	TEXT-ALIGN: center; BORDER-LEFT: rgb(208,213,217) 1px solid; WIDTH: 25%; DISPLAY: inline; FLOAT: left; HEIGHT: 55px; MARGIN-LEFT: -2px; FONT-SIZE: 14px
}
.recharge-entry STRONG {
	FONT-FAMILY: Tahoma, Geneva, sans-serif; FONT-WEIGHT: normal
}
.recharge-entry INS {
	FONT-STYLE: normal; FONT-FAMILY: Microsoft YaHei, Helvetica, Tahoma; TEXT-DECORATION: none
}
.recharge-entry DT {
	POSITION: relative; LINE-HEIGHT: normal; FONT-SIZE: 20px
}
.recharge-entry DT STRONG {
	FONT-SIZE: 24px
}
.recharge-entry DD .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 6px; LEFT: auto
}
.recharge-entry DD {
	POSITION: relative; LINE-HEIGHT: 24px; HEIGHT: 24px; OVERFLOW: hidden
}
.recharge-entry DD .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 6px; LEFT: auto
}
.text-left {
	TEXT-ALIGN: left
}
.text-left DT {
	PADDING-LEFT: 20px
}
.text-left DD {
	PADDING-LEFT: 25px
}
.submit-rw {
	POSITION: absolute; MARGIN-TOP: -24px; HEIGHT: 50px; TOP: 50%; RIGHT: 30px
}
.submit-rw .icons {
	POSITION: absolute; MARGIN: -10px 0px 0px -32px; TOP: 50%; LEFT: 50%
}
.submit-rw A {
	POSITION: relative; PADDING-BOTTOM: 0px; LINE-HEIGHT: 50px; TEXT-INDENT: 55px; PADDING-LEFT: 0px; WIDTH: 125px; PADDING-RIGHT: 0px; HEIGHT: 50px; MARGIN-LEFT: 20px; FONT-SIZE: 16px; PADDING-TOP: 0px
}
.submit-rw .recharge {
	BACKGROUND: rgb(21,69,111)
}
.submit-rw .withdraw {
	BACKGROUND: rgb(24,177,96)
}
.submit-rw .withdraw .icons {
	MARGIN: -6px 0px 0px -30px
}
.redeem-entry .sum-btn .gbtn {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; MARGIN-TOP: 6px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline-block; HEIGHT: 30px; PADDING-TOP: 0px
}
.nextsubmit .gbtn {
	POSITION: relative; TEXT-ALIGN: center; PADDING-BOTTOM: 0px; LINE-HEIGHT: 50px; PADDING-LEFT: 20px; WIDTH: 100px; PADDING-RIGHT: 0px; HEIGHT: 50px; FONT-SIZE: 16px; PADDING-TOP: 0px
}
.nextsubmit .gbtn .icons {
	POSITION: absolute; TOP: 18px; LEFT: 18px
}
.form-opt .btn-submit .gbtn {
	TEXT-ALIGN: center; PADDING-BOTTOM: 0px; LINE-HEIGHT: 40px; PADDING-LEFT: 25px; PADDING-RIGHT: 25px; HEIGHT: 40px; COLOR: rgb(255,255,255); FONT-SIZE: 16px; PADDING-TOP: 0px
}
.submit-rw .recharge {
	BACKGROUND: rgb(0,142,216)
}
.balances .recharge {
	HEIGHT: 45px; PADDING-TOP: 5px
}
.balances .button-rmb {
	POSITION: absolute; MARGIN-LEFT: -30px; TOP: 5px; LEFT: 50%
}
.button-wit {
	WIDTH: 16px; BACKGROUND-POSITION: -54px -115px; HEIGHT: 13px
}
.button-rmb {
	WIDTH: 20px; BACKGROUND-POSITION: -41px -94px; HEIGHT: 20px
}
.gbtn {
	POSITION: relative; PADDING-BOTTOM: 3px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline-block; FONT-FAMILY: Microsoft YaHei; WHITE-SPACE: nowrap; BACKGROUND: rgb(0,168,232); COLOR: rgb(255,255,255); FONT-SIZE: 13px; OVERFLOW: hidden; CURSOR: pointer; PADDING-TOP: 3px
}
.icons {
	LINE-HEIGHT: 0; TEXT-INDENT: -9999px; WIDTH: 16px; DISPLAY: inline-block; BACKGROUND: url(../css/images/sprites.png) no-repeat; HEIGHT: 16px; FONT-SIZE: 0px; OVERFLOW: hidden
}
.baitis {
	COLOR: #ffffff
}
.WPA3-SELECT-PANEL {
	Z-INDEX: 2147483647; BORDER-BOTTOM: #d4d4d4 1px solid; BORDER-LEFT: #d4d4d4 1px solid; PADDING-BOTTOM: 0px; BACKGROUND-COLOR: #fff; MARGIN: 0px; PADDING-LEFT: 0px; WIDTH: 463px; PADDING-RIGHT: 0px; HEIGHT: 292px; BORDER-TOP: #d4d4d4 1px solid; BORDER-RIGHT: #d4d4d4 1px solid; PADDING-TOP: 0px; box-shadow: 0 0 15px #d4d4d4; border-radius: 5px
}
.WPA3-SELECT-PANEL * {
	CLIP: rect(auto auto auto auto); Z-INDEX: auto; BORDER-BOTTOM: 0px; POSITION: static; MIN-WIDTH: 0px; TEXT-ALIGN: left; FILTER: ; BORDER-LEFT: 0px; PADDING-BOTTOM: 0px; TEXT-TRANSFORM: none; LIST-STYLE-TYPE: none; TEXT-INDENT: 0px; MARGIN: 0px; OUTLINE-STYLE: none; OUTLINE-COLOR: invert; MIN-HEIGHT: 0px; PADDING-LEFT: 0px; OUTLINE-WIDTH: 0px; WIDTH: auto; BOTTOM: auto; TEXT-OVERFLOW: clip; PADDING-RIGHT: 0px; FONT: 12px/16px Microsoft YaHei, Simsun; DIRECTION: ltr; WORD-WRAP: normal; WHITE-SPACE: normal; BACKGROUND: none transparent scroll repeat 0% 0%; FLOAT: none; LETTER-SPACING: normal; HEIGHT: auto; VISIBILITY: visible; COLOR: #333; CLEAR: none; VERTICAL-ALIGN: baseline; OVERFLOW: visible; QUOTES: none; BORDER-TOP: 0px; TOP: auto; CURSOR: auto; RIGHT: auto; LIST-STYLE-IMAGE: none; BORDER-RIGHT: 0px; WORD-SPACING: normal; TEXT-DECORATION: none; PADDING-TOP: 0px; LEFT: auto; opacity: 1; box-shadow: none; text-shadow: none; border-radius: 0; -moz-border-radius: 0; -webkit-border-radius: 0; marks: none; page: auto; -o-set-link-source: none; size: auto; -webkit-box-shadow: none; -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -ms-border-radius: 0; -o-border-radius: 0; -webkit-opacity: 1; -moz-opacity: 1; -ms-opacity: 1; -o-opacity: 1; -webkit-outline: 0; -moz-outline: 0; -ms-outline: 0; -o-outline: 0; -webkit-text-size-adjust: none
}
.WPA3-SELECT-PANEL A {
	CURSOR: auto
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-TOP {
	HEIGHT: 25px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-CLOSE {
	WIDTH: 47px; DISPLAY: block; BACKGROUND: url(http://combo.b.qq.com/crm/wpa/release/3.3/wpa/views/SelectPanel-sprites.png) no-repeat; FLOAT: right; HEIGHT: 25px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-CLOSE:hover {
	BACKGROUND-POSITION: 0px -25px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-MAIN {
	PADDING-BOTTOM: 45px; PADDING-LEFT: 20px; PADDING-RIGHT: 20px; PADDING-TOP: 23px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-GUIDE {
	FONT-FAMILY: "Microsoft Yahei"; MARGIN-BOTTOM: 42px; FONT-SIZE: 16px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-SELECTS {
	MARGIN: 0px auto; WIDTH: 246px; HEIGHT: 111px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-CHAT {
	WIDTH: 88px; DISPLAY: block; BACKGROUND: url(http://combo.b.qq.com/crm/wpa/release/3.3/wpa/views/SelectPanel-sprites.png) no-repeat 0px -80px; FLOAT: right; HEIGHT: 111px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-CHAT:hover {
	BACKGROUND-POSITION: -88px -80px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-AIO-CHAT {
	FLOAT: left
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-QQ {
	MARGIN: 6px; WIDTH: 76px; DISPLAY: block; BACKGROUND: url(http://combo.b.qq.com/crm/wpa/release/3.3/wpa/views/SelectPanel-sprites.png) no-repeat -50px 0px; HEIGHT: 76px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-QQ-ANONY {
	BACKGROUND-POSITION: -130px 0px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-LABEL {
	TEXT-ALIGN: center; DISPLAY: block; COLOR: #00a2e6; PADDING-TOP: 10px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-BOTTOM {
	TEXT-ALIGN: right; PADDING-BOTTOM: 0px; PADDING-LEFT: 20px; PADDING-RIGHT: 20px; PADDING-TOP: 0px
}
.WPA3-SELECT-PANEL .WPA3-SELECT-PANEL-INSTALL {
	COLOR: #8e8e8e
}
.title-items {
	POSITION: relative; HEIGHT: 70px; OVERFLOW: hidden
}
.title-items .toback {
	Z-INDEX: 5; POSITION: absolute; TEXT-ALIGN: center; LINE-HEIGHT: 30px; TEXT-INDENT: 10px; WIDTH: 60px; BACKGROUND: #e8ecef; FLOAT: left; HEIGHT: 30px; COLOR: #475058; TOP: 20px; RIGHT: 0px
}
.title-items .toback .icons {
	POSITION: absolute; TOP: 10px; LEFT: 10px
}
.title-items .num {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 20px; MARGIN: 0px 3px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline-block; BACKGROUND: #15456f; HEIGHT: 20px; COLOR: #fff; FONT-SIZE: 14px; PADDING-TOP: 0px
}
.title-items .line {
	POSITION: absolute; LINE-HEIGHT: 0; WIDTH: 100%; BACKGROUND: #e7ecee; FLOAT: left; HEIGHT: 1px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 35px; LEFT: 0px
}
.title-items H2 {
	Z-INDEX: 3; POSITION: absolute; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; BACKGROUND: #fff; HEIGHT: 30px; COLOR: #69737b; FONT-SIZE: 16px; TOP: 20px; PADDING-TOP: 0px; LEFT: 20px
}
.title-items .r {
	Z-INDEX: 3; POSITION: absolute; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; BACKGROUND: #fff; HEIGHT: 30px; COLOR: #69737b; FONT-SIZE: 16px; TOP: 20px; PADDING-TOP: 0px; LEFT: 20px
}
.title-items .r {
	PADDING-RIGHT: 0px; COLOR: #8a949c; FONT-SIZE: 14px; RIGHT: 30px; LEFT: auto
}
.title-items .light-red {
	POSITION: relative; PADDING-LEFT: 12px; DISPLAY: inline-block
}
.title-items .arrow-red-down {
	POSITION: absolute; TOP: 9px; LEFT: 0px
}
.bankcards {
	ZOOM: 1; MARGIN-LEFT: -30px; OVERFLOW: hidden
}
.bank-card {
	POSITION: relative; WIDTH: 375px; DISPLAY: inline; FLOAT: left; HEIGHT: 230px; MARGIN-LEFT: 30px
}
.bank-card .head {
	POSITION: relative; BACKGROUND: #e7ecee; HEIGHT: 50px
}
.bank-card .foot {
	POSITION: relative; BACKGROUND: #e7ecee; HEIGHT: 50px
}
.bank-card .content {
	BACKGROUND: #fff; HEIGHT: 130px
}
.bank-card .content UL {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 20px; PADDING-RIGHT: 0px; ZOOM: 1; OVERFLOW: hidden; PADDING-TOP: 22px
}
.bank-card .content LI {
	LINE-HEIGHT: 30px; HEIGHT: 30px; OVERFLOW: hidden
}
.bank-card .content B {
	TEXT-ALIGN: right; WIDTH: 60px; DISPLAY: inline; FLOAT: left; MARGIN-RIGHT: 15px
}
.bank-card .content EM {
	FONT-FAMILY: Tahoma; FONT-SIZE: 24px
}
.bank-card .bank-logo {
	POSITION: absolute; LINE-HEIGHT: 30px; PADDING-LEFT: 30px; HEIGHT: 30px; FONT-SIZE: 14px; TOP: 10px; LEFT: 15px
}
.bank-card .bank-small {
	POSITION: absolute; TOP: 0px; LEFT: 0px
}
.bank-card .title {
	POSITION: relative; TEXT-ALIGN: center; PADDING-TOP: 15px
}
.bank-card .title .icons {
	POSITION: absolute; TOP: 18px; LEFT: 32%
}
.bank-status {
	POSITION: absolute; COLOR: #18b160; TOP: 15px; RIGHT: 30px
}
.bank-status .icons {
	MARGIN-RIGHT: 5px
}
.bank-card .foot .bank-action {
	POSITION: relative; FLOAT: left; PADDING-TOP: 13px; LEFT: 50%
}
.bank-card .foot A {
	POSITION: relative; PADDING-BOTTOM: 0px; LINE-HEIGHT: 26px; PADDING-LEFT: 25px; PADDING-RIGHT: 15px; DISPLAY: inline; WHITE-SPACE: nowrap; FLOAT: left; HEIGHT: 26px; OVERFLOW: hidden; MARGIN-RIGHT: 10px; PADDING-TOP: 0px; LEFT: -50%
}
.bank-card .foot .icons {
	POSITION: absolute; TOP: 10px; LEFT: 13px
}
.bank-card .bank-delete {
	BACKGROUND: #fff; COLOR: #8a949c
}
.bank-card .bank-delete:visited {
	COLOR: #8a949c
}
.bank-card .bank-bind {
	BACKGROUND: #15456f; COLOR: #fff
}
.bank-card .bank-bind:visited {
	COLOR: #fff
}
.bank-add-button {
	BORDER-BOTTOM: #d3d8dc 1px solid; TEXT-ALIGN: center; MARGIN: 49px 0px; HEIGHT: 130px; BORDER-TOP: #d3d8dc 1px solid
}
.bank-add-button A {
	POSITION: relative; MARGIN-TOP: 55px; DISPLAY: inline-block; COLOR: #349cd8; FONT-SIZE: 14px
}
.bank-add-button A:visited {
	COLOR: #349cd8
}
.bank-add-button .add-blue {
	POSITION: absolute; MARGIN-LEFT: -20px; TOP: 4px; LEFT: 0px
}
.bank-card-add {
	BACKGROUND: #fff; HEIGHT: auto; OVERFLOW: visible
}
.bank-card-opt {
	DISPLAY: none
}
.bank-card-opt .head {
	BORDER-BOTTOM: #d3d8dc 1px solid
}
.bank-card-opt .foot {
	BORDER-TOP: #d3d8dc 1px solid
}
.bank-card-opt .bank-items {
	PADDING-BOTTOM: 10px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 18px
}
.bank-card-opt .bank-items LI {
	POSITION: relative; HEIGHT: 48px; OVERFLOW: hidden
}
.bank-card-opt .bank-items LABEL {
	TEXT-ALIGN: right; LINE-HEIGHT: 33px; WIDTH: 80px; DISPLAY: inline; FLOAT: left; MARGIN-RIGHT: 5px
}
.bank-card-opt .bank-items INPUT {
	PADDING-BOTTOM: 6px; LINE-HEIGHT: 19px; PADDING-LEFT: 5px; WIDTH: 220px; PADDING-RIGHT: 5px; HEIGHT: 19px; COLOR: #999; PADDING-TOP: 6px
}
.bank-card-opt INPUT.my-name {
	BACKGROUND: #f0f5f7
}
.bank-card-opt .bank-select {
	POSITION: absolute; LINE-HEIGHT: 20px; PADDING-RIGHT: 10px; FLOAT: right; HEIGHT: 20px; COLOR: #349cd8; TOP: 5px; CURSOR: pointer; RIGHT: 53px
}
.bank-card-opt .bank-select .icons {
	POSITION: absolute; TOP: 8px; RIGHT: 0px
}
.bank-card-opt .Error {
	PADDING-LEFT: 85px; COLOR: #d35353
}
.assets-entry-tab {
	POSITION: relative; HEIGHT: 110px
}
.assets-entry-tab .arrow-eml-gray-down {
	DISPLAY: none
}
.assets-tab {
	WIDTH: 100%; MARGIN-BOTTOM: 0px; OVERFLOW: hidden
}
.assets-tab .stats-entry {
	CURSOR: pointer
}
.assets-tab .current {
	_border: 0
}
.assets-tab .current DT {
	MARGIN-TOP: -15px; BACKGROUND: #fff; CURSOR: pointer; PADDING-TOP: 18px; border-radius: 3px 3px 0 0
}
.assets-tab .current DD {
	BACKGROUND: #fff; HEIGHT: 63px; CURSOR: pointer; border-radius: 0 0 3px 3px
}
.assets-tab .current DT .yhelp {
	TOP: 22px
}
.assets-tab .current DD STRONG {
	COLOR: #349cd8
}
.assets-table {
	WIDTH: 100%; FONT-SIZE: 12px
}
.assets-table TH {
	BORDER-BOTTOM: #e7ecee 1px solid; TEXT-ALIGN: right; TEXT-INDENT: 30px; PADDING-RIGHT: 50px; HEIGHT: 40px; VERTICAL-ALIGN: middle
}
.assets-table TD {
	BORDER-BOTTOM: #e7ecee 1px solid; TEXT-ALIGN: right; TEXT-INDENT: 30px; PADDING-RIGHT: 50px; HEIGHT: 40px; VERTICAL-ALIGN: middle
}
.assets-table TH {
	BACKGROUND: #f0f4f7
}
.assets-table TD.txt {
	TEXT-ALIGN: left; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 20%; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
.assets-table .high TD {
	BACKGROUND: #f0f4f7
}
.assets-table TD.center {
	TEXT-ALIGN: center; PADDING-BOTTOM: 0px; TEXT-INDENT: 50px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
.assets-table .number {
	POSITION: relative; HEIGHT: 20px
}
.assets-table .num {
	POSITION: absolute; PADDING-BOTTOM: 0px; LINE-HEIGHT: 15px; TEXT-INDENT: 0px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline-block; FONT-FAMILY: Tahoma; BACKGROUND: #15456f; HEIGHT: 16px; COLOR: #fff; MARGIN-LEFT: 5px; FONT-SIZE: 12px; OVERFLOW: hidden; TOP: 2px; FONT-WEIGHT: normal; PADDING-TOP: 0px; LEFT: auto
}
.wei-yield {
	HEIGHT: 330px; OVERFLOW: hidden
}
.wei-yield .head {
	BACKGROUND: #f0f4f7; HEIGHT: 40px
}
.wei-yield .head H2 {
	TEXT-ALIGN: center; LINE-HEIGHT: 40px; WIDTH: 49.3%; DISPLAY: inline; FLOAT: left; HEIGHT: 40px
}
.yield-cpi {
	POSITION: relative; WIDTH: 52%; DISPLAY: inline; FLOAT: left; HEIGHT: 290px; COLOR: #8a949c; FONT-SIZE: 12px; OVERFLOW: hidden; BORDER-RIGHT: #e7ecee 1px solid
}
.yield-cpi H3 {
	TEXT-ALIGN: center; WIDTH: 100%; FONT-WEIGHT: normal; PADDING-TOP: 20px
}
.yield-cpi H3 EM {
	FONT-FAMILY: Tahoma; COLOR: #fc8936; FONT-SIZE: 16px; FONT-WEIGHT: bold
}
.yield-group {
	BORDER-BOTTOM: #e7ecee 1px solid; POSITION: absolute; WIDTH: 100%; BOTTOM: 40px; HEIGHT: 170px; LEFT: 0px
}
.yield-group .items {
	POSITION: relative; WIDTH: 33%; DISPLAY: inline; FLOAT: left; HEIGHT: 100%
}
.yield-group .pie {
	POSITION: absolute; WIDTH: 60%; BOTTOM: 0px; BACKGROUND: #a1acb4; LEFT: 20%
}
.yield-group .pie B {
	POSITION: absolute; TEXT-ALIGN: center; WIDTH: 100%; DISPLAY: block; TOP: -25px; LEFT: 0px
}
.yield-group .items H2 {
	POSITION: absolute; TEXT-ALIGN: center; WIDTH: 100%; BOTTOM: -30px; FONT-WEIGHT: normal; LEFT: 0px
}
.yield-group .icons {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 4px; LEFT: auto
}
.yield-per {
	MARGIN-TOP: 15px; WIDTH: 47%; FLOAT: right; HEIGHT: 270px; OVERFLOW: hidden
}
.invest-tabs {
	Z-INDEX: 5; POSITION: relative; HEIGHT: 50px
}
.invest-tabs UL {
	POSITION: absolute; BOTTOM: -1px; HEIGHT: 50px; LEFT: 0px
}
.invest-tabs LI {
	BORDER-BOTTOM: 0px; BORDER-LEFT: #e7ecee 1px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 40px; PADDING-RIGHT: 40px; DISPLAY: inline; BACKGROUND: #e7ecee; FLOAT: left; HEIGHT: 35px; FONT-SIZE: 16px; BORDER-TOP: #e7ecee 1px solid; CURSOR: pointer; MARGIN-RIGHT: 40px; BORDER-RIGHT: #e7ecee 1px solid; PADDING-TOP: 13px; border-radius: 5px 5px 0 0
}
.invest-tabs LI .r5 {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 16px; PADDING-LEFT: 6px; PADDING-RIGHT: 6px; DISPLAY: inline-block; BACKGROUND: #a1acb4; HEIGHT: 16px; COLOR: #fff; MARGIN-LEFT: 5px; FONT-SIZE: 12px; FONT-WEIGHT: normal; PADDING-TOP: 0px
}
.invest-tabs LI.current {
	BORDER-BOTTOM: 0px; BORDER-LEFT: #d0d5d9 1px solid; BACKGROUND: #fff; HEIGHT: 36px; COLOR: #349cd8; BORDER-TOP: #d0d5d9 1px solid; BORDER-RIGHT: #d0d5d9 1px solid
}
.invest-tabs LI.current .r5 {
	BACKGROUND: #15456f
}
.invest-plan {
	ZOOM: 1; BACKGROUND: #f0f4f7; FONT-SIZE: 12px; OVERFLOW: hidden
}
.invest-plan .items {
	BORDER-BOTTOM: #e9edef 1px solid; BORDER-LEFT: #fff 1px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 20px; PADDING-RIGHT: 10px; BACKGROUND: #fff; HEIGHT: 57px; OVERFLOW: hidden; BORDER-TOP: #fff 1px solid; BORDER-RIGHT: #fff 1px solid; PADDING-TOP: 0px
}
.invest-plan .title {
	FONT-SIZE: 13px
}
.invest-plan LI {
	LINE-HEIGHT: 57px; WIDTH: 16%; DISPLAY: inline; FLOAT: left; HEIGHT: 57px; OVERFLOW: hidden
}
.invest-plan LI.col_1 {
	TEXT-INDENT: 20px; WIDTH: 15%
}
.invest-plan LI.col_2 {
	TEXT-ALIGN: center; WIDTH: 10%
}
.invest-plan LI.col_3 {
	TEXT-ALIGN: right; WIDTH: 19%
}
.invest-plan LI.col_4 {
	TEXT-ALIGN: right; WIDTH: 23%
}
.invest-plan LI.col_5 {
	TEXT-ALIGN: center; TEXT-INDENT: 40px; WIDTH: 20%
}
.invest-plan LI.col_6 {
	TEXT-ALIGN: right; WIDTH: 10%
}
.invest-plan .links {
	POSITION: relative; PADDING-RIGHT: 17px; DISPLAY: inline-block
}
.invest-plan .links .icons {
	POSITION: absolute; TOP: 25px; RIGHT: 0px
}
.invest-plan .links:hover .arrow-gray-dotb {
	BACKGROUND-POSITION: -57px -149px
}
.invest-plan .links:hover .arrow-gray-dotr {
	BACKGROUND-POSITION: -49px -143px
}
.invest-plan .vouch {
	POSITION: relative; TEXT-ALIGN: left; TEXT-INDENT: 0px; PADDING-LEFT: 18px; DISPLAY: inline-block; COLOR: #18b160
}
.invest-plan .vouch .icons {
	POSITION: absolute; TOP: 20px; LEFT: 0px
}
.invest-plan .ltd {
	POSITION: relative; TEXT-ALIGN: left; TEXT-INDENT: 0px; PADDING-LEFT: 20px; DISPLAY: inline-block
}
.invest-plan .ltd .icons {
	POSITION: absolute; TOP: 20px; LEFT: 0px
}
.invest-plan-entry {
	BORDER-BOTTOM: #e8edef 1px solid; POSITION: relative; BORDER-LEFT: #e8edef 1px solid; MARGIN: 10px 9px; DISPLAY: none; MAX-HEIGHT: 285px; OVERFLOW: hidden; BORDER-TOP: #e8edef 1px solid; BORDER-RIGHT: #e8edef 1px solid; _overflow: visible
}
.invest-plan-entry .items {
	BORDER-BOTTOM: #fff 0px solid; BORDER-LEFT: #fff 0px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 10px; PADDING-RIGHT: 0px; BORDER-TOP: #fff 0px solid; BORDER-RIGHT: #fff 0px solid; PADDING-TOP: 0px
}
.invest-plan-entry .over {
	BACKGROUND: #eaf5fb
}
.invest-plan-entry LI.col_1 {
	COLOR: #349cd8
}
.invest-plan-entry .links .icons {
	TOP: 22px; RIGHT: 3px
}
.scrollbar {
	POSITION: relative; WIDTH: 10px; BACKGROUND: #ccc 0px 0px; FLOAT: right
}
.scrollbar .track {
	POSITION: relative; WIDTH: 10px; BACKGROUND: #eee; HEIGHT: 100%
}
.scrollbar .thumb {
	POSITION: absolute; WIDTH: 10px; BACKGROUND: #39f; HEIGHT: 20px; OVERFLOW: hidden; TOP: 0px; CURSOR: pointer; LEFT: -5px
}
.scrollbar .end {
	WIDTH: 10px; BACKGROUND: #f30; HEIGHT: 5px; OVERFLOW: hidden
}
.scrollbar .disable {
	DISPLAY: none
}
.scrollbar .noSelect {
	user-select: none; -o-user-select: none; -moz-user-select: none; -khtml-user-select: none; -webkit-user-select: none
}
.loan-deal {
	
}
.loan-deal LI.col_1 {
	WIDTH: 13%
}
.loan-deal LI.col_2 {
	WIDTH: 23%
}
.loan-deal LI.col_3 {
	WIDTH: 15%
}
.loan-deal LI.col_4 {
	WIDTH: 16%
}
.loan-deal LI.col_5 {
	WIDTH: 20%
}
.loan-deal LI.col_6 {
	WIDTH: 10%
}
.loan-deal .invest-plan-entry LI.col_2 {
	TEXT-ALIGN: center
}
.invest-loan {
	BACKGROUND: #fff; FONT-SIZE: 12px
}
.invest-loan .items {
	Z-INDEX: 3; BORDER-BOTTOM: #e9edef 1px solid; POSITION: relative; PADDING-LEFT: 35px; HEIGHT: 57px; FONT-SIZE: 12px; OVERFLOW: hidden
}
.invest-loan LI {
	POSITION: relative; WIDTH: 12%; DISPLAY: inline; FLOAT: left; HEIGHT: 47px; PADDING-TOP: 10px
}
.invest-loan LI.col_1 {
	TEXT-ALIGN: left; TEXT-INDENT: 0px; WIDTH: 20%
}
.invest-loan LI.col_2 {
	TEXT-ALIGN: left; WIDTH: 11%
}
.invest-loan LI.col_3 {
	TEXT-ALIGN: center; WIDTH: 12%; COLOR: #fc9b5e
}
.invest-loan LI.col_4 {
	TEXT-ALIGN: right; WIDTH: 10%
}
.invest-loan LI.col_5 {
	TEXT-ALIGN: right; WIDTH: 12%
}
.invest-loan LI.col_6 {
	TEXT-ALIGN: center; WIDTH: 12%
}
.invest-loan LI.col_7 {
	TEXT-ALIGN: left; WIDTH: 12%
}
.invest-loan LI.col_8 {
	TEXT-ALIGN: right; WIDTH: 7%
}
.invest-loan LI.col_7 A {
	COLOR: #475058
}
.invest-loan LI.col_7 A:visited {
	COLOR: #475058
}
.invest-loan LI.col_7 A:hover {
	COLOR: #349cd8
}
.invest-loan .tags {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 3px; PADDING-RIGHT: 0px; DISPLAY: inline; FLOAT: left; HEIGHT: 20px; PADDING-TOP: 13px
}
.invest-loan .tags .icons {
	LINE-HEIGHT: normal; WIDTH: 16px; DISPLAY: inline; FLOAT: left; HEIGHT: 16px; MARGIN-LEFT: 2px
}
.invest-loan .title {
	Z-INDEX: 5; POSITION: relative; FONT-SIZE: 13px; OVERFLOW: visible
}
.invest-loan .title A {
	POSITION: relative; PADDING-RIGHT: 12px; DISPLAY: inline-block; COLOR: #475058
}
.invest-loan .title .icons {
	POSITION: absolute; TOP: 24px; RIGHT: 0px
}
.invest-loan .title LI {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 57px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; HEIGHT: 57px; OVERFLOW: visible; PADDING-TOP: 0px
}
.invest-plan .title LI.col_3 {
	COLOR: #475058
}
.invest-loan .filter {
	BORDER-BOTTOM: 0px; POSITION: relative; BORDER-LEFT: #fff 1px solid; LINE-HEIGHT: 30px; MARGIN: 13px 10px 0px; HEIGHT: 30px; BORDER-TOP: #fff 1px solid; CURSOR: default; BORDER-RIGHT: #fff 1px solid; _margin: 16px 10px 0
}
.invest-loan .filter .icons {
	POSITION: static; MARGIN-LEFT: 3px; TOP: auto; RIGHT: auto; LEFT: auto
}
.invest-loan .filter-loan {
	BORDER-BOTTOM: #d2d7db 1px solid; POSITION: absolute; BORDER-LEFT: #d2d7db 1px solid; WIDTH: 100%; DISPLAY: none; BACKGROUND: #fff; BORDER-TOP: 0px; TOP: 30px; BORDER-RIGHT: #d2d7db 1px solid; LEFT: -1px
}
.invest-loan .filter-loan A {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 28px; PADDING-LEFT: 0px; WIDTH: 100%; PADDING-RIGHT: 0px; DISPLAY: block; HEIGHT: 28px; OVERFLOW: hidden; PADDING-TOP: 0px
}
.invest-loan .filter-loan A:hover {
	BACKGROUND: #a1acb4; COLOR: #fff
}
.invest-loan .filter-active {
	BORDER-BOTTOM: 0px; BORDER-LEFT: #d2d7db 1px solid; COLOR: #349cd8; BORDER-TOP: #d2d7db 1px solid; BORDER-RIGHT: #d2d7db 1px solid
}
.invest-loan .filter-active .icons {
	BACKGROUND-POSITION: -105px -80px
}
.invest-loan .links {
	POSITION: relative; PADDING-RIGHT: 17px
}
.invest-loan .links .icons {
	POSITION: absolute; TOP: 4px; RIGHT: 0px
}
.invest-loan .links:hover .arrow-gray-dotr {
	BACKGROUND-POSITION: -49px -143px
}
.invest-loan .vouch {
	POSITION: relative; PADDING-LEFT: 16px; COLOR: #18b160
}
.invest-loan .vouch .icons {
	POSITION: absolute; TOP: 1px; LEFT: 0px
}
.invest-loan .ltd {
	POSITION: relative; PADDING-LEFT: 20px
}
.invest-loan .ltd .icons {
	POSITION: absolute; TOP: 1px; LEFT: 0px
}
.plan-parame {
	BORDER-BOTTOM: #e7ecee 1px solid; POSITION: relative; BORDER-LEFT: #e7ecee 1px solid; HEIGHT: 498px; OVERFLOW: hidden; BORDER-TOP: #e7ecee 1px solid; BORDER-RIGHT: #e7ecee 1px solid
}
.plan-parame .head {
	BORDER-BOTTOM: #e7ecee 1px solid; BACKGROUND: #f0f4f7; HEIGHT: 49px; CLEAR: both; OVERFLOW: hidden
}
.plan-parame .head LI {
	POSITION: relative; LINE-HEIGHT: 49px; WIDTH: 44%; DISPLAY: inline; FLOAT: left
}
.plan-parame .head .fr {
	PADDING-RIGHT: 10px; FLOAT: right
}
.plan-parame .head .fl {
	PADDING-LEFT: 30px; FLOAT: left
}
.plan-parame .head LI.f {
	WIDTH: 54%; BORDER-RIGHT: #e7ecee 1px solid
}
.plan-parame .head LI.f .fr {
	PADDING-RIGHT: 30px
}
.plan-parame .head EM {
	COLOR: #fc8936
}
.plan-parame .head STRONG {
	FONT-SIZE: 16px
}
.parame-post {
	Z-INDEX: 5; POSITION: relative; WIDTH: 54%; DISPLAY: inline; FLOAT: left; HEIGHT: 450px; BORDER-RIGHT: #e7ecee 1px solid
}
.parame-post .items {
	BORDER-BOTTOM: #e7ecee 1px solid; LINE-HEIGHT: 55px; PADDING-LEFT: 30px; HEIGHT: 55px; OVERFLOW: hidden
}
.parame-post LI {
	DISPLAY: inline; FLOAT: left; HEIGHT: 55px
}
.parame-post LI.y_1 {
	WIDTH: 30%
}
.parame-per {
	Z-INDEX: 3; POSITION: absolute; WIDTH: 370px; FLOAT: right; HEIGHT: 400px; TOP: 30px; RIGHT: 0px; PADDING-TOP: 24px
}
.plan-items {
	
}
.plan-post .items {
	BORDER-BOTTOM: #e9edef 1px solid; PADDING-LEFT: 35px; HEIGHT: 57px; FONT-SIZE: 12px; OVERFLOW: hidden
}
.plan-post LI {
	TEXT-ALIGN: left; LINE-HEIGHT: 57px; WIDTH: 24%; DISPLAY: inline; FLOAT: left; HEIGHT: 57px; OVERFLOW: hidden
}
.plan-post LI.col_1 {
	TEXT-ALIGN: left; WIDTH: 24%
}
.plan-post LI.col_2 {
	WIDTH: 30%
}
.plan-post LI.col_3 {
	WIDTH: 25%
}
.plan-post LI.col_4 {
	WIDTH: 18%
}
.plan-post LI.col_3 A {
	COLOR: #475058
}
.plan-post LI.col_3 A:visited {
	COLOR: #475058
}
.plan-post LI.col_3 A:hover {
	COLOR: #349cd8
}
.plan-post .ltd {
	POSITION: relative; PADDING-LEFT: 20px; DISPLAY: inline-block
}
.plan-post .ltd .icons {
	POSITION: absolute; TOP: 22px; LEFT: 0px
}
.plan-post .title {
	BACKGROUND: #f0f4f7; HEIGHT: 42px; FONT-SIZE: 13px
}
.plan-post .title LI {
	LINE-HEIGHT: 42px; HEIGHT: 42px
}
.plan-stats {
	PADDING-BOTTOM: 20px; PADDING-LEFT: 20px; PADDING-RIGHT: 20px; MARGIN-BOTTOM: 30px; BACKGROUND: #f0f4f7; PADDING-TOP: 20px
}
.plan-stats EM {
	FONT-FAMILY: Tahoma; COLOR: #fc8936; FONT-SIZE: 14px
}
.loan-parame {
	MARGIN-BOTTOM: 0px
}
.loan-parame .head {
	PADDING-BOTTOM: 0px; LINE-HEIGHT: 50px; PADDING-LEFT: 20px; PADDING-RIGHT: 20px; BACKGROUND: #f0f4f7; HEIGHT: 50px; CLEAR: both; PADDING-TOP: 0px
}
.loan-parame .head H2 {
	FLOAT: left; FONT-SIZE: 14px
}
.loan-parame .head .r {
	FLOAT: right
}
.loan-parame .head .ltd {
	POSITION: relative; PADDING-LEFT: 30px; DISPLAY: inline-block
}
.loan-parame .head .ltd .icons {
	POSITION: absolute; TOP: 18px; LEFT: 10px
}
.loan-parame .loan-tip {
	PADDING-BOTTOM: 15px; PADDING-LEFT: 20px; PADDING-RIGHT: 20px; PADDING-TOP: 15px
}
.loan-parame .loan-tip EM {
	COLOR: #18b160
}
.loan-steps {
	MARGIN: 0px 10px 20px; HEIGHT: 40px; OVERFLOW: hidden
}
.loan-steps OL {
	MARGIN: 0px auto; WIDTH: 100%; HEIGHT: 40px; OVERFLOW: hidden
}
.loan-steps LI {
	POSITION: relative; LINE-HEIGHT: 40px; WIDTH: 20%; DISPLAY: inline; BACKGROUND: url(file:///D|/%E4%BA%BA%E4%BA%BA%E8%81%9A%E8%B4%A2%E4%BD%9C%E5%93%81/local/img/common/steps.png) #f0f4f7 no-repeat right -60px; FLOAT: left; HEIGHT: 40px
}
.loan-steps LI.step-1 {
	WIDTH: 25%
}
.loan-steps LI.step-2 {
	WIDTH: 23%
}
.loan-steps LI.step-3 {
	WIDTH: 19%
}
.loan-steps LI.step-4 {
	WIDTH: 17%
}
.loan-steps LI.step-5 {
	WIDTH: 15%; BACKGROUND: #f0f4f7; FLOAT: left
}
.loan-steps .txt {
	POSITION: relative; PADDING-LEFT: 28px; DISPLAY: inline; FLOAT: left
}
.loan-steps .dot20 {
	POSITION: absolute; TEXT-ALIGN: center; LINE-HEIGHT: 20px; TEXT-INDENT: 0px; FONT-FAMILY: Tahoma; FONT-SIZE: 14px; TOP: 10px; LEFT: 5px
}
.loan-steps .cm-green {
	POSITION: absolute; MARGIN-LEFT: 2px; TOP: 15px; LEFT: auto
}
.loan-parame-post {
	ZOOM: 1; OVERFLOW: hidden; BORDER-TOP: #e9edef 1px solid
}
.loan-parame-post .items {
	BORDER-BOTTOM: #e9edef 1px solid; LINE-HEIGHT: 59px; HEIGHT: 59px
}
.loan-parame-post LI {
	BORDER-LEFT: #e9edef 1px solid; WIDTH: 49%; DISPLAY: inline; FLOAT: left; HEIGHT: 59px; MARGIN-LEFT: -1px
}
.loan-parame-post .tit {
	PADDING-LEFT: 20px; WIDTH: 100px; DISPLAY: inline-block
}
.repay-pro {
	POSITION: relative; PADDING-BOTTOM: 0px; LINE-HEIGHT: 60px; PADDING-LEFT: 20px; PADDING-RIGHT: 20px; HEIGHT: 60px; PADDING-TOP: 0px
}
.repay-pro H2 {
	WIDTH: 100px; DISPLAY: inline; FLOAT: left
}
.repay-pro .repay-bar {
	LINE-HEIGHT: 0; MARGIN: 25px 10px 0px 0px; WIDTH: 190px; BACKGROUND: #e8ecef; FLOAT: left; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; border-radius: 15px
}
.repay-pro .repay-bar DIV {
	WIDTH: 0%; BACKGROUND: #15456f; FLOAT: left; HEIGHT: 10px; OVERFLOW: hidden; border-radius: 15px
}
.repay-pro .buy-number {
	POSITION: absolute; TEXT-ALIGN: center; LINE-HEIGHT: normal; WIDTH: 20px; DISPLAY: none; COLOR: #fc9b5e; TOP: 0px; LEFT: 125px
}
.repay-pro .buy-text {
	POSITION: absolute; TEXT-ALIGN: center; LINE-HEIGHT: normal; WIDTH: 60px; BOTTOM: 5px; DISPLAY: none; COLOR: #fc9b5e; LEFT: 105px
}
.repay-pro .ar_down {
	BORDER-BOTTOM-COLOR: transparent; BORDER-RIGHT-WIDTH: 5px; BORDER-TOP-COLOR: #fc9b5e; BOTTOM: -11px; BORDER-TOP-WIDTH: 5px; BORDER-BOTTOM-WIDTH: 5px; BORDER-RIGHT-COLOR: transparent; MARGIN-LEFT: -6px; BORDER-LEFT-COLOR: transparent; BORDER-LEFT-WIDTH: 5px
}
.loan-items {
	
}
.loan-post .items {
	BORDER-BOTTOM: #e9edef 1px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 21px; PADDING-RIGHT: 0px; HEIGHT: 57px; FONT-SIZE: 12px; OVERFLOW: hidden; PADDING-TOP: 0px
}
.loan-post LI {
	TEXT-ALIGN: left; LINE-HEIGHT: 57px; WIDTH: 19%; DISPLAY: inline; FLOAT: left; HEIGHT: 57px; OVERFLOW: hidden
}
.loan-post LI.col_1 {
	WIDTH: 20%
}
.loan-post LI.col_2 {
	WIDTH: 21%
}
.loan-post LI.col_3 {
	WIDTH: 21%
}
.loan-post LI.col_4 {
	WIDTH: 14%
}
.loan-post LI.col_5 {
	POSITION: relative; TEXT-ALIGN: center; WIDTH: 20%
}
.loan-post .title {
	BACKGROUND: #f0f4f7; HEIGHT: 42px; FONT-SIZE: 13px
}
.loan-post .title LI {
	LINE-HEIGHT: 42px; HEIGHT: 42px
}
.loan-post .fx {
	POSITION: relative; PADDING-RIGHT: 15px; DISPLAY: inline-block
}
.loan-post .fx .icons {
	POSITION: absolute; TOP: 14px; RIGHT: 0px
}
.loan-post LI.col_5 .icons {
	POSITION: absolute; MARGIN: -6px 0px 0px -7px; TOP: 50%; LEFT: 50%
}
.stats-items {
	BORDER-BOTTOM: 0px; BORDER-LEFT: 0px; WIDTH: 100%; BACKGROUND: #f0f4f7; HEIGHT: 110px; OVERFLOW: hidden; BORDER-TOP: 0px; BORDER-RIGHT: 0px
}
.stats-items .items {
	POSITION: relative; PADDING-BOTTOM: 20px; PADDING-LEFT: 0px; WIDTH: 100%; PADDING-RIGHT: 0px; HEIGHT: 70px; MARGIN-LEFT: 3px; OVERFLOW: hidden; PADDING-TOP: 20px
}
.stats-entry {
	TEXT-ALIGN: center; BORDER-LEFT: #d0d5d9 1px solid; WIDTH: 25%; DISPLAY: inline; FLOAT: left; HEIGHT: 70px; MARGIN-LEFT: -2px; FONT-SIZE: 14px
}
.stats-entry B {
	MARGIN-LEFT: -15px; FONT-WEIGHT: normal
}
.stats-entry EM {
	COLOR: #fc8936
}
.stats-entry INS {
	FONT-STYLE: normal; FONT-FAMILY: Microsoft YaHei, Helvetica, Tahoma; TEXT-DECORATION: none
}
.stats-entry .bk {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 3px; PADDING-RIGHT: 3px; BACKGROUND: #fc8936; COLOR: #fff; PADDING-TOP: 0px
}
.stats-entry DT {
	POSITION: relative; MARGIN: 0px 2px 0px 1px; HEIGHT: 20px; COLOR: #8a949c; PADDING-TOP: 3px
}
.stats-entry DT .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 7px; LEFT: auto
}
.stats-entry DD .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 7px; LEFT: auto
}
.stats-entry DD .yhelp {
	TOP: 5px
}
.stats-entry DD {
	MARGIN: 0px 2px 0px 1px; HEIGHT: 40px
}
.stats-entry DD STRONG {
	DISPLAY: block; FONT-FAMILY: Tahoma; HEIGHT: 30px; FONT-SIZE: 24px; OVERFLOW: hidden; FONT-WEIGHT: normal
}
.stats-entry DD P {
	POSITION: relative; LINE-HEIGHT: 22px; HEIGHT: 24px; OVERFLOW: hidden
}
.stats-items {
	BORDER-BOTTOM: 0px; BORDER-LEFT: 0px; WIDTH: 100%; BACKGROUND: #f0f4f7; HEIGHT: 110px; OVERFLOW: hidden; BORDER-TOP: 0px; BORDER-RIGHT: 0px
}
.stats-items .items {
	POSITION: relative; PADDING-BOTTOM: 20px; PADDING-LEFT: 0px; WIDTH: 100%; PADDING-RIGHT: 0px; HEIGHT: 70px; MARGIN-LEFT: 3px; OVERFLOW: hidden; PADDING-TOP: 20px
}
.stats-entry {
	TEXT-ALIGN: center; BORDER-LEFT: #d0d5d9 1px solid; WIDTH: 25%; DISPLAY: inline; FLOAT: left; HEIGHT: 70px; MARGIN-LEFT: -2px; FONT-SIZE: 14px
}
.stats-entry B {
	MARGIN-LEFT: -15px; FONT-WEIGHT: normal
}
.stats-entry EM {
	COLOR: #fc8936
}
.stats-entry INS {
	FONT-STYLE: normal; FONT-FAMILY: Microsoft YaHei, Helvetica, Tahoma; TEXT-DECORATION: none
}
.stats-entry .bk {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 3px; PADDING-RIGHT: 3px; BACKGROUND: #fc8936; COLOR: #fff; PADDING-TOP: 0px
}
.stats-entry DT {
	POSITION: relative; MARGIN: 0px 2px 0px 1px; HEIGHT: 20px; COLOR: #8a949c; PADDING-TOP: 3px
}
.stats-entry DT .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 7px; LEFT: auto
}
.stats-entry DD .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 7px; LEFT: auto
}
.stats-entry DD .yhelp {
	TOP: 5px
}
.stats-entry DD {
	MARGIN: 0px 2px 0px 1px; HEIGHT: 40px
}
.stats-entry DD STRONG {
	DISPLAY: block; FONT-FAMILY: Tahoma; HEIGHT: 30px; FONT-SIZE: 24px; OVERFLOW: hidden; FONT-WEIGHT: normal
}
.stats-entry DD P {
	POSITION: relative; LINE-HEIGHT: 22px; HEIGHT: 24px; OVERFLOW: hidden
}
.stats-items {
	BORDER-BOTTOM: 0px; BORDER-LEFT: 0px; WIDTH: 100%; BACKGROUND: #f0f4f7; HEIGHT: 110px; OVERFLOW: hidden; BORDER-TOP: 0px; BORDER-RIGHT: 0px
}
.stats-items .items {
	POSITION: relative; PADDING-BOTTOM: 20px; PADDING-LEFT: 0px; WIDTH: 100%; PADDING-RIGHT: 0px; HEIGHT: 70px; MARGIN-LEFT: 3px; OVERFLOW: hidden; PADDING-TOP: 20px
}
.stats-entry {
	TEXT-ALIGN: center; BORDER-LEFT: #d0d5d9 1px solid; WIDTH: 25%; DISPLAY: inline; FLOAT: left; HEIGHT: 70px; MARGIN-LEFT: -2px; FONT-SIZE: 14px
}
.stats-entry B {
	MARGIN-LEFT: -15px; FONT-WEIGHT: normal
}
.stats-entry EM {
	COLOR: #fc8936
}
.stats-entry INS {
	FONT-STYLE: normal; FONT-FAMILY: Microsoft YaHei, Helvetica, Tahoma; TEXT-DECORATION: none
}
.stats-entry .bk {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 3px; PADDING-RIGHT: 3px; BACKGROUND: #fc8936; COLOR: #fff; PADDING-TOP: 0px
}
.stats-entry DT {
	POSITION: relative; MARGIN: 0px 2px 0px 1px; HEIGHT: 20px; COLOR: #8a949c; PADDING-TOP: 3px
}
.stats-entry DT .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 7px; LEFT: auto
}
.stats-entry DD .yhelp {
	POSITION: absolute; MARGIN-LEFT: 3px; TOP: 7px; LEFT: auto
}
.stats-entry DD .yhelp {
	TOP: 5px
}
.stats-entry DD {
	MARGIN: 0px 2px 0px 1px; HEIGHT: 40px
}
.stats-entry DD STRONG {
	DISPLAY: block; FONT-FAMILY: Tahoma; HEIGHT: 30px; FONT-SIZE: 24px; OVERFLOW: hidden; FONT-WEIGHT: normal
}
.stats-entry DD P {
	POSITION: relative; LINE-HEIGHT: 22px; HEIGHT: 24px; OVERFLOW: hidden
}
.reg-title .line {
	POSITION: absolute; LINE-HEIGHT: 0; WIDTH: 100%; BACKGROUND: #e7ecee; FLOAT: left; HEIGHT: 1px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 35px; LEFT: 0px
}
.title-items .line {
	POSITION: absolute; LINE-HEIGHT: 0; WIDTH: 100%; BACKGROUND: #e7ecee; FLOAT: left; HEIGHT: 1px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 35px; LEFT: 0px
}
.assets-group .line {
	POSITION: absolute; LINE-HEIGHT: 0; WIDTH: 40%; BACKGROUND: #fff; HEIGHT: 101%; OVERFLOW: hidden; TOP: -1px; RIGHT: 0px
}
.assets-tab .stats-entry {
	CURSOR: pointer
}
.assets-tab .current {
	_border: 0
}
.assets-tab .current DT {
	MARGIN-TOP: -15px; BACKGROUND: #fff; CURSOR: pointer; PADDING-TOP: 18px; border-radius: 3px 3px 0 0
}
.assets-tab .current DD {
	BACKGROUND: #fff; HEIGHT: 63px; CURSOR: pointer; border-radius: 0 0 3px 3px
}
.assets-tab .current DT .yhelp {
	TOP: 22px
}
.assets-tab .current DD STRONG {
	COLOR: #349cd8
}
.model-box {
	BORDER-BOTTOM: #d3d8dc 1px solid; POSITION: relative; BORDER-LEFT: #d3d8dc 1px solid; ZOOM: 1; MARGIN-BOTTOM: 30px; OVERFLOW: hidden; BORDER-TOP: #d3d8dc 1px solid; BORDER-RIGHT: #d3d8dc 1px solid
}
.jTabstt {
	Z-INDEX: 5; POSITION: relative; HEIGHT: 50px
}
.jTabstt UL {
	POSITION: absolute; BOTTOM: -1px; HEIGHT: 50px; LEFT: 0px
}
.jTabstt LI {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 40px; PADDING-RIGHT: 40px; HEIGHT: 35px; PADDING-TOP: 13px
}
.page {
	TEXT-ALIGN: center; HEIGHT: 35px; COLOR: #666; FONT-SIZE: 12px; OVERFLOW: hidden
}
.page A {
	BORDER-BOTTOM: #dedede 1px solid; BORDER-LEFT: #dedede 1px solid; LINE-HEIGHT: 20px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline-block; MARGIN-BOTTOM: 10px; HEIGHT: 20px; COLOR: #666; BORDER-TOP: #dedede 1px solid; MARGIN-RIGHT: 5px; BORDER-RIGHT: #dedede 1px solid
}
.page A:hover {
	BORDER-BOTTOM: #148dd9 1px solid; BORDER-LEFT: #148dd9 1px solid; BACKGROUND: #148dd9; COLOR: #ffffff; BORDER-TOP: #148dd9 1px solid; BORDER-RIGHT: #148dd9 1px solid; TEXT-DECORATION: none
}
.page_input {
	BORDER-BOTTOM: #dedede 1px solid; BORDER-LEFT: #dedede 1px solid; WIDTH: 30px; HEIGHT: 24px; MARGIN-LEFT: 5px; BORDER-TOP: #dedede 1px solid; MARGIN-RIGHT: 5px; BORDER-RIGHT: #dedede 1px solid
}
.page_btn {
	BORDER-BOTTOM: #dedede 1px solid; BORDER-LEFT: #dedede 1px solid; PADDING-LEFT: 5px; WIDTH: 30px; PADDING-RIGHT: 5px; BACKGROUND: #f0f0f0; HEIGHT: 24px; COLOR: #666; MARGIN-LEFT: 5px; BORDER-TOP: #dedede 1px solid; CURSOR: pointer; MARGIN-RIGHT: 5px; BORDER-RIGHT: #dedede 1px solid
}
.pageDivClass .curPageColor {
	COLOR: #fff
}
.curPageColor {
	BACKGROUND-COLOR: #148dd9; COLOR: #ffffff
}
#curPageText {
	BORDER-BOTTOM: #dedede 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #dedede 1px solid; MARGIN-TOP: -3px; HEIGHT: 20px; BORDER-TOP: #dedede 1px solid; BORDER-RIGHT: #dedede 1px solid
}
.zhang {
	COLOR: #3a8dc9
}
.page2 {
	BORDER-BOTTOM: #dadada 1px solid; POSITION: relative; BORDER-LEFT: #dadada 1px solid; MARGIN: 0px auto; WIDTH: 753px; BACKGROUND: #eee; HEIGHT: 808px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.page_ul2 {
	BORDER-BOTTOM: #cbc7c7 1px solid; POSITION: absolute; WIDTH: 753px; DISPLAY: block; HEIGHT: 45px; TOP: 0px; LEFT: 0px; _height: 50px
}
.page_li5 {
	POSITION: absolute
}
.page_li6 {
	POSITION: absolute
}
.page_li7 {
	POSITION: absolute
}
.page_li5 {
	TOP: 0px; LEFT: 0px
}
.page_li6 {
	TOP: 0px; LEFT: 102px
}
.page_li7 {
	TOP: 0px; LEFT: 104px
}
.tip {
	BORDER-BOTTOM: #dedede 1px solid; BORDER-LEFT: #dedede 1px solid; MARGIN-TOP: 20px; FONT-FAMILY: Verdana, Geneva, sans-serif; BORDER-TOP: #dedede 1px solid; BORDER-RIGHT: #dedede 1px solid
}
.tip P {
	LINE-HEIGHT: 30px; PADDING-LEFT: 16px; BACKGROUND: #f1f1f1; HEIGHT: 30px
}
.tip PRE {
	
}
.tip2 {
	MARGIN-TOP: 16px; COLOR: #888; FONT-SIZE: 12px
}
.mF_tab {
	BORDER-BOTTOM: #dedede 1px solid; WIDTH: 742px; HEIGHT: 1000px
}
.mF_tab .btn {
	Z-INDEX: 2; POSITION: absolute; TOP: 0px; LEFT: 0px
}
.mF_tab .btn LI {
	BORDER-BOTTOM: 0px; TEXT-ALIGN: center; BORDER-LEFT: #dedede 1px solid; LINE-HEIGHT: 40px; WIDTH: 130px; FONT-FAMILY: "Microsoft Yahei"; BACKGROUND: #f1f1f1; FLOAT: left; HEIGHT: 40px; FONT-SIZE: 16px; BORDER-TOP: #dedede 1px solid; CURSOR: pointer; MARGIN-RIGHT: 5px; BORDER-RIGHT: #dedede 1px solid
}
.mF_tab .btn LI.current {
	LINE-HEIGHT: 41px; FONT-FAMILY: "Microsoft Yahei"; BACKGROUND: #fff; HEIGHT: 41px; FONT-SIZE: 16px
}
.mF_tab .cont {
	BORDER-BOTTOM: #dedede 1px solid; POSITION: absolute; BORDER-LEFT: #dedede 1px solid; LINE-HEIGHT: 25px; WIDTH: 742px; FONT-FAMILY: "Microsoft Yahei"; OVERFLOW: hidden; BORDER-TOP: #dedede 1px solid; TOP: 41px; BORDER-RIGHT: #dedede 1px solid; LEFT: 0px
}
.mF_tab .cont .swt {
	POSITION: absolute; TOP: 0px; LEFT: 0px
}
.mF_tab .cont .swt LI {
	FLOAT: left
}
.mF_tab .cont .swt LI P {
	PADDING-BOTTOM: 16px; PADDING-LEFT: 16px; PADDING-RIGHT: 16px; PADDING-TOP: 16px
}
.tips {
	BORDER-BOTTOM: #e2ebf2 0px solid;
	BORDER-LEFT: #e2ebf2 0px solid;
	PADDING-BOTTOM: 0px;
	MARGIN-TOP: 16px;
	PADDING-LEFT: 30px;
	PADDING-RIGHT: 10px;
	BORDER-TOP: #e2ebf2 0px solid;
	BORDER-RIGHT: #e2ebf2 0px solid;
	PADDING-TOP: 0px;
}
.inp188 {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; WIDTH: 188px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.bcbtn {
	BACKGROUND-IMAGE: url(../images/ut.jpg); TEXT-ALIGN: center; LINE-HEIGHT: 34px; WIDTH: 100px; PADDING-RIGHT: 10px; DISPLAY: inline-block; BACKGROUND-REPEAT: no-repeat; HEIGHT: 34px; COLOR: #136dad; FONT-SIZE: 14px
}
.txt420 {
	BORDER-BOTTOM: #ddd 1px solid; BORDER-LEFT: #ddd 1px solid; WIDTH: 420px; HEIGHT: 90px; BORDER-TOP: #ddd 1px solid; BORDER-RIGHT: #ddd 1px solid
}
.scbtn {
	BACKGROUND-IMAGE: url(../images/bosse.jpg); TEXT-ALIGN: center; LINE-HEIGHT: 24px; WIDTH: 78px; DISPLAY: inline-block; BACKGROUND-REPEAT: no-repeat; HEIGHT: 24px; COLOR: #666
}
.scbtn:hover {
	COLOR: #333
}
.pageDivClass {
	TEXT-ALIGN: center; WIDTH: 750px
}
.inp140 {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; WIDTH: 140px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.inp280 {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; WIDTH: 280px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.inp100x {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; WIDTH: 100px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.cc {
	MARGIN: 0px auto; margin-top:5px;
}
.ww {
	BORDER-BOTTOM: #ccc 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 25px; WIDTH: 50px; HEIGHT: 25px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.hong {
	COLOR: #f00
}
.into {
	WIDTH: 230px; DISPLAY: inline; FLOAT: left; COLOR: rgb(138,148,156)
}
.yuetuo {
	COLOR: #136dad; TEXT-DECORATION: underline
}
.per {
	LINE-HEIGHT: 0; MARGIN: 10px 0px 0px 20px; WIDTH: 60%; DISPLAY: inline; BACKGROUND: #e8ecef; FLOAT: left; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; border-radius: 15px; -moz-border-radius: 15px; -webkit-border-radius: 15px
}
.per_h {
	LINE-HEIGHT: 0; MARGIN: 10px 0px 0px 20px; WIDTH: 60%; DISPLAY: inline; BACKGROUND: #00a8e8; FLOAT: left; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; border-radius: 15px; -moz-border-radius: 15px; -webkit-border-radius: 15px
}
.pasfei {
	TEXT-ALIGN: center; LINE-HEIGHT: 28px; MARGIN: 10px 0px 0px 20px; WIDTH: 89px; DISPLAY: inline; BACKGROUND: #aeaeae; FLOAT: left; HEIGHT: 28px; COLOR: #fff; FONT-SIZE: 16px; OVERFLOW: hidden; border-radius: 15px; -moz-border-radius: 15px; -webkit-border-radius: 15px
}
.passjiang {
	TEXT-ALIGN: center; LINE-HEIGHT: 28px; MARGIN: 10px 0px 0px 20px; WIDTH: 89px; DISPLAY: inline; BACKGROUND: #00a8e8; FLOAT: left; HEIGHT: 28px; COLOR: #fff; FONT-SIZE: 16px; OVERFLOW: hidden; BEHAVIOR: url(ie-css3.htc); border-radius: 15px; -moz-border-radius: 15px; -webkit-border-radius: 15px
}
.passjiang A {
	DISPLAY: block
}
#wrapper A.deep_blue {
	COLOR: #224e73
}
#wrapper A.us_blue {
	COLOR: #349cd8
}
#wrapper:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; FONT-SIZE: 0px; OVERFLOW: hidden; CONTENT: "."
}
#wrap:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; FONT-SIZE: 0px; OVERFLOW: hidden; CONTENT: "."
}
.yol-top:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; FONT-SIZE: 0px; OVERFLOW: hidden; CONTENT: "."
}
.redeem-items:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; FONT-SIZE: 0px; OVERFLOW: hidden; CONTENT: "."
}
#mainsub:after {
	DISPLAY: block; HEIGHT: 0px; VISIBILITY: hidden; CLEAR: both; FONT-SIZE: 0px; OVERFLOW: hidden; CONTENT: "."
}
.title-sub {
	BORDER-BOTTOM: #e7ecee 1px solid; POSITION: relative; MARGIN: 0px 30px; HEIGHT: 40px; CLEAR: both
}
.content_post {
	POSITION: relative; WIDTH: 1000px; FLOAT: left; OVERFLOW: hidden
}
#sidesub {
	BORDER-LEFT: #e8ecef 1px solid; PADDING-BOTTOM: 0px; PADDING-LEFT: 30px; WIDTH: 250px; PADDING-RIGHT: 0px; FLOAT: right; MARGIN-RIGHT: 30px; PADDING-TOP: 30px
}
.static_nav {
	POSITION: relative; PADDING-BOTTOM: 30px; PADDING-LEFT: 0px; WIDTH: 208px; PADDING-RIGHT: 0px; DISPLAY: block; PADDING-TOP: 20px
}
.static_nav LI {
	FONT-SIZE: 14px; _height: 36px; _line-height: 36px
}
.static_nav LI A {
	BORDER-BOTTOM: #f0f5f7 1px solid; POSITION: relative; TEXT-ALIGN: center; LINE-HEIGHT: 36px; WIDTH: 208px; FLOAT: left; HEIGHT: 36px; COLOR: #224e73
}
.static_nav LI A:hover {
	COLOR: #349cd8; TEXT-DECORATION: none
}
.static_nav LI A.active {
	COLOR: #349cd8; TEXT-DECORATION: none
}
.static_nav .reg_btn {
	POSITION: relative; MARGIN-BOTTOM: 20px
}
.static_nav .reg_btn A {
	TEXT-ALIGN: center; PADDING-BOTTOM: 15px; MARGIN: 0px auto; PADDING-LEFT: 70px; PADDING-RIGHT: 70px; DISPLAY: block; BACKGROUND: #335c7d; COLOR: #fff; FONT-SIZE: 14px; FONT-WEIGHT: bold; PADDING-TOP: 15px; border-radius: 5px
}
.static_nav .reg_btn A:hover {
	COLOR: #fff
}
.t_icons {
	POSITION: absolute; LINE-HEIGHT: 0; TEXT-INDENT: -9999px; WIDTH: 10px; DISPLAY: inline-block; BACKGROUND: url(img/sx.png) no-repeat; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 14px; LEFT: 55px
}
.static_nav LI I {
	POSITION: absolute; LINE-HEIGHT: 0; TEXT-INDENT: -9999px; WIDTH: 10px; DISPLAY: inline-block; BACKGROUND: url(img/sx.png) no-repeat; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 14px; LEFT: 55px
}
.mech LI I {
	POSITION: absolute; LINE-HEIGHT: 0; TEXT-INDENT: -9999px; WIDTH: 10px; DISPLAY: inline-block; BACKGROUND: url(img/sx.png) no-repeat; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 14px; LEFT: 55px
}
.static_nav LI A:hover I {
	BACKGROUND-POSITION: 0px -11px
}
.static_nav .current I {
	BACKGROUND-POSITION: 0px -22px
}
.static_nav UL UL {
	POSITION: relative; FLOAT: left; MARGIN-LEFT: 40px; _margin-left: 20px
}
.static_nav LI LI {
	LINE-HEIGHT: 30px; FLOAT: left; HEIGHT: 30px; COLOR: #f00
}
.static_nav LI LI A {
	BORDER-BOTTOM: medium none; TEXT-ALIGN: left; BORDER-LEFT: medium none; LINE-HEIGHT: 30px; PADDING-LEFT: 50px; WIDTH: 110px; HEIGHT: 30px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.static_nav LI LI I.ico {
	WIDTH: 4px; BACKGROUND-POSITION: 0px -32px; HEIGHT: 4px; TOP: 14px; LEFT: 40px
}
.g_box .ico {
	WIDTH: 4px; BACKGROUND-POSITION: 0px -32px; HEIGHT: 4px; TOP: 14px; LEFT: 40px
}
.static_nav LI LI A:hover I {
	BACKGROUND-POSITION: -6px -32px
}
.nav_sub {
	POSITION: relative; PADDING-BOTTOM: 30px; PADDING-LEFT: 0px; WIDTH: 208px; PADDING-RIGHT: 0px; DISPLAY: block; PADDING-TOP: 20px
}
.nav_sub .reg_btn {
	POSITION: relative; MARGIN-BOTTOM: 20px
}
.nav_sub .reg_btn A {
	TEXT-ALIGN: center; PADDING-BOTTOM: 15px; MARGIN: 0px auto; PADDING-LEFT: 70px; PADDING-RIGHT: 70px; DISPLAY: block; BACKGROUND: #00a8e8; COLOR: #fff; FONT-SIZE: 14px; FONT-WEIGHT: bold; PADDING-TOP: 15px; border-radius: 5px
}
.nav_sub .reg_btn A:hover {
	COLOR: #fff
}
.nav_sub H3 I {
	POSITION: absolute; LINE-HEIGHT: 0; TEXT-INDENT: -9999px; WIDTH: 10px; DISPLAY: inline-block; BACKGROUND: url(img/sx.png) no-repeat; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 14px; LEFT: 55px
}
.nav_sub .current I {
	BACKGROUND-POSITION: 0px -22px
}
.nav_sub H3 {
	LINE-HEIGHT: 36px; HEIGHT: 36px; FONT-SIZE: 14px
}
.nav_sub .menu_herd {
	
}
.nav_sub H3 A {
	BORDER-BOTTOM: #f0f5f7 1px solid; POSITION: relative; TEXT-ALIGN: center; LINE-HEIGHT: 36px; WIDTH: 208px; FLOAT: left; HEIGHT: 36px; COLOR: #224e73
}
.menu_sub {
	DISPLAY: none
}
.menu_sub A {
	BORDER-BOTTOM: medium none; TEXT-ALIGN: left; BORDER-LEFT: medium none; LINE-HEIGHT: 30px; PADDING-LEFT: 50px; WIDTH: 110px; FLOAT: left; HEIGHT: 30px; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.menu_sub A I.ico {
	WIDTH: 4px; BACKGROUND-POSITION: 0px -32px; HEIGHT: 4px; TOP: 14px; LEFT: 40px
}
.m_lt_cont {
	TEXT-ALIGN: left; LINE-HEIGHT: 25px; FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 14px
}
.g_box {
	POSITION: relative; WIDTH: 630px
}
.g_box_f {
	TEXT-ALIGN: center; WIDTH: 630px; HEIGHT: 65px
}
.g_box_f1 {
	TEXT-ALIGN: center; WIDTH: 630px; HEIGHT: 45px; FONT-SIZE: 12px
}
.g_box LI {
	BORDER-BOTTOM: #e7ecee 1px solid; POSITION: relative; HEIGHT: 50px
}
.g_box LI A {
	LINE-HEIGHT: 50px; WIDTH: 420px; TEXT-OVERFLOW: ellipsis; DISPLAY: block; WHITE-SPACE: nowrap; FLOAT: left; MARGIN-LEFT: 20px; OVERFLOW: hidden; CURSOR: pointer
}
.g_box LI A:hover {
	COLOR: #349cd8; TEXT-DECORATION: underline
}
.g_box LI SPAN {
	Z-INDEX: 99; POSITION: absolute; TEXT-ALIGN: right; WIDTH: 120px; PADDING-RIGHT: 15px; BACKGROUND: #fff; COLOR: #a1acb4; OVERFLOW: hidden; TOP: 20px; RIGHT: 0px
}
.g_box .ico {
	FLOAT: left; TOP: 28px; LEFT: 5px
}
.g_box .ico_new {
	WIDTH: 26px; BACKGROUND-POSITION: 0px -70px; HEIGHT: 11px; MARGIN-LEFT: 10px; TOP: 25px; LEFT: auto
}
.m_lt_cont1 {
	MARGIN-LEFT: 10px; MARGIN-RIGHT: 10px
}
.bouut {
	BACKGROUND-COLOR: #008ed6; MARGIN: 8px; HEIGHT: 25px
}
.mybtn {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 2em; OUTLINE-WIDTH: medium; WIDTH: 110px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #008ed6; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn:hover {
	BACKGROUND: #0279b5
}
.mybtn_2 {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 1.7em; OUTLINE-WIDTH: medium; WIDTH: 130px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #008ed6; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn_2:hover {
	BACKGROUND: #0279b5
}
.mybtn2 {
	TEXT-ALIGN: center; LINE-HEIGHT: 100%; OUTLINE-COLOR: invert; PADDING-LEFT: 0.9em; OUTLINE-WIDTH: medium; WIDTH: 110px; PADDING-RIGHT: 1em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BACKGROUND: #008ed6; HEIGHT: 30px; COLOR: #fefee9; FONT-SIZE: 14px; VERTICAL-ALIGN: text-bottom; CURSOR: pointer; TEXT-DECORATION: none; font-size-adjust: none; font-stretch: normal; background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn2:hover {
	BACKGROUND: #0279b5
}
.mybtn1 {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 2em; OUTLINE-WIDTH: medium; WIDTH: 110px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #06a852; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn1:hover {
	BACKGROUND: #059a4b
}
.mybtn1_1 {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px 0px 1px; OUTLINE-COLOR: invert; PADDING-LEFT: 2em; OUTLINE-WIDTH: medium; WIDTH: 130px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #06a852; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn1_1:hover {
	BACKGROUND: #059a4b
}
.baitis {
	COLOR: #ffffff
}
.f24 {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 24px
}
.f28 {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 28px
}
.bh_y_new {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; PADDING-LEFT: 5px; WIDTH: 235px; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 35px; COLOR: #444; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid; border-radius: 5px
}
.bh_y_new2 {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; PADDING-LEFT: 5px; WIDTH: 235px; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 20px; COLOR: #444; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid; border-radius: 5px
}
.bh_100 {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 35px; PADDING-LEFT: 5px; WIDTH: 100px; HEIGHT: 35px; COLOR: #bbb; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid; border-radius: 5px
}
.bh {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 35px; PADDING-LEFT: 5px; WIDTH: 235px; HEIGHT: 35px; COLOR: #000000; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.bh_y {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 35px; PADDING-LEFT: 5px; WIDTH: 235px; HEIGHT: 35px; COLOR: #bbb; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid; border-radius: 5px
}
.lineone3 {
	BORDER-BOTTOM: #dadada 1px solid; BACKGROUND-COLOR: #fff; MARGIN-TOP: 1px; WIDTH: 383px; FLOAT: left; HEIGHT: 1px
}
.lineone5 {
	BORDER-BOTTOM: #dadada 1px solid; BACKGROUND-COLOR: #fff; MARGIN-TOP: 10px; WIDTH: 100%; FLOAT: left; HEIGHT: 1px
}
.line1 {
	LINE-HEIGHT: 25px; WIDTH: 256px; FLOAT: left; HEIGHT: 25px
}
.line_25 {
	LINE-HEIGHT: 22px
}
.gay {
	COLOR: #737272
}
.gay_line {
	BORDER-BOTTOM: #cccccc 1px dashed; LINE-HEIGHT: 35px; HEIGHT: 35px
}
.hei5 {
	COLOR: #66696b
}
.blues {
	COLOR: #0e87d6
}

.main_kuang_li3 {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; WIDTH: 958px; HEIGHT: 810px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.agree {
	MARGIN: 40px auto; WIDTH: 400px; COLOR: #3366cc; FONT-SIZE: 16px; FONT-WEIGHT: 800
}
.line25 {
	LINE-HEIGHT: 25
}
.main_kuang_li {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 0px auto; WIDTH: 958px; HEIGHT: 610px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.bh_d {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; PADDING-LEFT: 5px; WIDTH: 50px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.bh_d1 {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; PADDING-LEFT: 15px; WIDTH: 250px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.reg_bg {
	BACKGROUND-IMAGE: url(../images/bouuts_gay.png); WIDTH: 240px; HEIGHT: 50px
}
.help_box {
	BORDER-BOTTOM: #e7eaec 1px dashed; POSITION: relative; PADDING-BOTTOM: 20px; MARGIN: 20px 0px 0px 30px; WIDTH: 600px
}
.help_mg5 {
	MARGIN-BOTTOM: 50px
}
.help_box H5 {
	LINE-HEIGHT: 24px; FONT-SIZE: 14px; CURSOR: pointer
}
.help_box H5 B {
	POSITION: relative; MARGIN: -3px 0px 0px 30px; WIDTH: 560px; FLOAT: left
}
.help_box .hp_cont {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 30px; PADDING-RIGHT: 20px; DISPLAY: none; CLEAR: both; PADDING-TOP: 10px
}
.help_box .hp_cont P {
	LINE-HEIGHT: 26px
}
.help_box .hp_cont A {
	COLOR: #349cd8
}
.help_box TABLE {
	WIDTH: 500px; BORDER-COLLAPSE: collapse
}
.help_box TABLE TR {
	LINE-HEIGHT: 40px; HEIGHT: 40px
}
.help_box TABLE TD {
	BORDER-BOTTOM: #e7ecee 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #e7ecee 1px solid; BORDER-TOP: #e7ecee 1px solid; BORDER-RIGHT: #e7ecee 1px solid
}
.help_box TABLE TH {
	BORDER-BOTTOM: #e7ecee 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #e7ecee 1px solid; BORDER-TOP: #e7ecee 1px solid; BORDER-RIGHT: #e7ecee 1px solid
}
help_box {
	BORDER-BOTTOM: rgb(231,234,236) 1px dashed; POSITION: relative; PADDING-BOTTOM: 20px; MARGIN: 20px 0px 0px 30px; WIDTH: 600px
}
.help_mg5 {
	MARGIN-BOTTOM: 50px
}
.help_box .up {
	POSITION: relative; WIDTH: 16px; BACKGROUND: url(img/sx.png) no-repeat 0px -84px; HEIGHT: 16px
}
.help_box .down {
	POSITION: relative; WIDTH: 16px; BACKGROUND: url(img/sx.png) no-repeat 0px -103px; HEIGHT: 16px
}
.help_box H5 {
	LINE-HEIGHT: 24px; FONT-SIZE: 14px; CURSOR: pointer
}
.help_box H5 B {
	POSITION: relative; MARGIN: -3px 0px 0px 30px; WIDTH: 560px; FLOAT: left
}
.help_box .hp_cont {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 30px; PADDING-RIGHT: 20px; DISPLAY: none; CLEAR: both; PADDING-TOP: 10px
}
.help_box .hp_cont P {
	LINE-HEIGHT: 26px
}
.help_box .hp_cont A {
	COLOR: rgb(52,156,216)
}
.help_box TABLE {
	WIDTH: 500px; BORDER-COLLAPSE: collapse
}
.help_box TABLE TR {
	LINE-HEIGHT: 40px; HEIGHT: 40px
}
.help_box TABLE TD {
	BORDER-BOTTOM: rgb(231,236,238) 1px solid; TEXT-ALIGN: center; BORDER-LEFT: rgb(231,236,238) 1px solid; BORDER-TOP: rgb(231,236,238) 1px solid; BORDER-RIGHT: rgb(231,236,238) 1px solid
}
.help_box TABLE TH {
	BORDER-BOTTOM: rgb(231,236,238) 1px solid; TEXT-ALIGN: center; BORDER-LEFT: rgb(231,236,238) 1px solid; BORDER-TOP: rgb(231,236,238) 1px solid; BORDER-RIGHT: rgb(231,236,238) 1px solid
}
.zhangwst {
	PADDING-LEFT: 8px; COLOR: #444444; FONT-SIZE: 14px; border-radius: 15px; -moz-border-radius: 15; -webkit-border-radius: 15
}
.zhangwww {
	BORDER-BOTTOM: #cccccc 1px solid; BORDER-LEFT: #cccccc 1px solid; BACKGROUND-COLOR: #fff; BORDER-TOP: #cccccc 1px solid; BORDER-RIGHT: #cccccc 1px solid; PADDING-TOP: 15px
}
.inp70x {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; WIDTH: 70px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.lineone5_new {
	BORDER-BOTTOM: #dadada 1px dashed; BACKGROUND-COLOR: #fff; MARGIN-TOP: 10px; WIDTH: 100%; FLOAT: left; HEIGHT: 1px
}
.lineone5 {
	BORDER-BOTTOM: #dadada 1px solid; BACKGROUND-COLOR: #fff; MARGIN-TOP: 10px; WIDTH: 100%; FLOAT: left; HEIGHT: 1px
}
.line_35 {
	LINE-HEIGHT: 35px
}
.line_45 {
	LINE-HEIGHT: 45px
}
.line_45color {
	COLOR: #224e73
}
.mybtn_10 {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 1.7em; OUTLINE-WIDTH: medium; WIDTH: 150px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #008ed6; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn_10:hover {
	BACKGROUND: #0279b5
}
.hei1 {
	
}
.linetai1 {
	BORDER-BOTTOM: #cccccc 1px dashed; TEXT-ALIGN: right; WIDTH: 595px; MARGIN-LEFT: 65px
}
.timenew {
	TEXT-ALIGN: right; PADDING-BOTTOM: 2px; LINE-HEIGHT: 24px; WIDTH: 660px; PADDING-RIGHT: 10px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: right; HEIGHT: 40px; PADDING-TOP: 48px
}
.line_height {
	BORDER-BOTTOM: #ccc 1px dashed; LINE-HEIGHT: 40px; HEIGHT: 40px
}
.boar {
	BORDER-BOTTOM: #eee 1px solid; BORDER-LEFT: #eee 1px solid; MARGIN: 3px; BORDER-TOP: #eee 1px solid; BORDER-RIGHT: #eee 1px solid
}
.boarone {
	COLOR: #349cd8
}
.fred {
	COLOR: #ff0000
}
.form-style INPUT.error {
	BORDER-BOTTOM-COLOR: #ee4d3d; BORDER-TOP-COLOR: #ee4d3d; BORDER-RIGHT-COLOR: #ee4d3d; VERTICAL-ALIGN: middle; BORDER-LEFT-COLOR: #ee4d3d
}
.form-style SPAN.error {
	BORDER-BOTTOM: #ffbebd 1px solid; BORDER-LEFT: #ffbebd 1px solid; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 25px; PADDING-RIGHT: 10px; DISPLAY: inline-block; BACKGROUND: url(../images/error-icon.png) #fff1f1 no-repeat 5px center; HEIGHT: 30px; COLOR: #666666; MARGIN-LEFT: 5px; VERTICAL-ALIGN: middle; BORDER-TOP: #ffbebd 1px solid; BORDER-RIGHT: #ffbebd 1px solid; PADDING-TOP: 0px
}
.form-style SPAN.notices {
	BORDER-BOTTOM: #ffbebd 1px solid; BORDER-LEFT: #ffbebd 1px solid; PADDING-BOTTOM: 0px; LINE-HEIGHT: 30px; PADDING-LEFT: 25px; PADDING-RIGHT: 10px; DISPLAY: inline-block; BACKGROUND: url(../images/error-icon.png) #fff1f1 no-repeat 5px center; HEIGHT: 30px; COLOR: #666666; MARGIN-LEFT: 5px; VERTICAL-ALIGN: middle; BORDER-TOP: #ffbebd 1px solid; BORDER-RIGHT: #ffbebd 1px solid; PADDING-TOP: 0px
}
.form-style SPAN.notices {
	BORDER-BOTTOM-COLOR: #a4dbfe; BORDER-TOP-COLOR: #a4dbfe; BACKGROUND: url(../images/warn-icon.png) #e5f5ff no-repeat 5px center; BORDER-RIGHT-COLOR: #a4dbfe; VERTICAL-ALIGN: top; BORDER-LEFT-COLOR: #a4dbfe
}
.form-style SPAN.success {
	BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; WIDTH: 16px; DISPLAY: inline-block; BACKGROUND: url(../images/error-icon.png) no-repeat; HEIGHT: 16px; MARGIN-LEFT: 5px; VERTICAL-ALIGN: middle; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.inp70x {
	BORDER-BOTTOM: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; LINE-HEIGHT: 30px; WIDTH: 70px; HEIGHT: 30px; BORDER-TOP: #ccc 1px solid; BORDER-RIGHT: #ccc 1px solid
}
.lineone5_new {
	BORDER-BOTTOM: #dadada 1px dashed; BACKGROUND-COLOR: #fff; MARGIN-TOP: 10px; WIDTH: 100%; FLOAT: left; HEIGHT: 1px
}
.line_height {
	BORDER-BOTTOM: #ccc 1px dashed; LINE-HEIGHT: 40px; HEIGHT: 40px
}
.boar {
	BORDER-BOTTOM: #eee 1px solid; BORDER-LEFT: #eee 1px solid; MARGIN: 3px; BORDER-TOP: #eee 1px solid; BORDER-RIGHT: #eee 1px solid
}
.boarone {
	COLOR: #349cd8
}
.bkki {
	BORDER-BOTTOM: #dadada 1px solid;
	BORDER-LEFT: #dadada 1px solid;
	BACKGROUND-COLOR: #fff;
	WIDTH: 1162px;
	FLOAT: left;
	HEIGHT: 100%;
	BORDER-RIGHT: #dadada 1px solid;
}
.bkkifont {
	TEXT-ALIGN: left; FONT-FAMILY: "Microsoft Yahei"; COLOR: #636363
}
.lineone5_1 {
	BORDER-BOTTOM: #dadada 1px dashed; BACKGROUND-COLOR: #fff; MARGIN-TOP: 10px; WIDTH: 100%; FLOAT: left; HEIGHT: 1px
}
.line_28 {
	LINE-HEIGHT: 27px
}
.mybtn_20 {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 1.7em; OUTLINE-WIDTH: medium; WIDTH: 160px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #008ed6; HEIGHT: 50px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn_20:hover {
	BACKGROUND: #0279b5
}
.help_box1 {
	BORDER-BOTTOM: #e7eaec 1px dashed; POSITION: relative; PADDING-BOTTOM: 20px; MARGIN: 20px 0px 0px 30px; WIDTH: 860px; FONT-FAMILY: "??????"
}
.help_box1 .hp_cont {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 30px; PADDING-RIGHT: 20px; DISPLAY: none; CLEAR: both; PADDING-TOP: 10px
}
.help_box1 .hp_cont P {
	LINE-HEIGHT: 26px
}
.help_box1 .hp_cont A {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #349cd8
}
.help_box1 TABLE {
	WIDTH: 800px; BORDER-COLLAPSE: collapse
}
.help_box1 TABLE TR {
	LINE-HEIGHT: 40px; HEIGHT: 40px
}
.help_box1 TABLE TD {
	BORDER-BOTTOM: #e7ecee 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #e7ecee 1px solid; BORDER-TOP: #e7ecee 1px solid; BORDER-RIGHT: #e7ecee 1px solid
}
.help_box TABLE TH {
	BORDER-BOTTOM: #e7ecee 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #e7ecee 1px solid; BORDER-TOP: #e7ecee 1px solid; BORDER-RIGHT: #e7ecee 1px solid
}
help_box1 {
	BORDER-BOTTOM: rgb(231,234,236) 1px dashed; POSITION: relative; PADDING-BOTTOM: 20px; MARGIN: 20px 0px 0px 30px; WIDTH: 800px; FONT-FAMILY: "Microsoft Yahei"
}
.help_mg5 {
	MARGIN-BOTTOM: 50px
}
.help_box1 .up {
	POSITION: relative; WIDTH: 16px; BACKGROUND: url(img/sx.png) no-repeat 0px -84px; HEIGHT: 16px
}
.help_box1 .down {
	POSITION: relative; WIDTH: 16px; BACKGROUND: url(img/sx.png) no-repeat 0px -103px; HEIGHT: 16px
}
.help_box1 H5 {
	LINE-HEIGHT: 24px; FONT-SIZE: 14px; CURSOR: pointer
}
.help_box1 H5 B {
	POSITION: relative; MARGIN: -3px 0px 0px 30px; WIDTH: 860px; FLOAT: left
}
.help_box1 .hp_cont {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 30px; PADDING-RIGHT: 20px; DISPLAY: none; CLEAR: both; PADDING-TOP: 10px
}
.help_box1 .hp_cont P {
	LINE-HEIGHT: 26px
}
.help_box1 .hp_cont A {
	COLOR: rgb(52,156,216)
}
.help_box1 TABLE {
	WIDTH: 800px; BORDER-COLLAPSE: collapse
}
.help_box1 TABLE TR {
	LINE-HEIGHT: 40px; HEIGHT: 40px
}
.help_box1 TABLE TD {
	BORDER-BOTTOM: rgb(231,236,238) 1px solid; TEXT-ALIGN: center; BORDER-LEFT: rgb(231,236,238) 1px solid; FONT-FAMILY: "Microsoft Yahei"; BORDER-TOP: rgb(231,236,238) 1px solid; BORDER-RIGHT: rgb(231,236,238) 1px solid
}
.help_box1 TABLE TH {
	BORDER-BOTTOM: rgb(231,236,238) 1px solid; TEXT-ALIGN: center; BORDER-LEFT: rgb(231,236,238) 1px solid; FONT-FAMILY: "Microsoft Yahei"; BORDER-TOP: rgb(231,236,238) 1px solid; BORDER-RIGHT: rgb(231,236,238) 1px solid
}
.lhtgpm {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #fff; WIDTH: 890px; FLOAT: left; HEIGHT: 100%; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.lhtgpm_right {
	MARGIN-TOP: 15px; WIDTH: 298px; MARGIN-BOTTOM: 5px; HEIGHT: 100%; BORDER-RIGHT: #dadada 1px dashed
}
.lhtgpm_b {
	BORDER-BOTTOM: #eeeeee 1px solid; BORDER-LEFT: #eeeeee 1px solid; PADDING-BOTTOM: 3px; PADDING-LEFT: 3px; PADDING-RIGHT: 3px; BORDER-TOP: #eeeeee 1px solid; BORDER-RIGHT: #eeeeee 1px solid; PADDING-TOP: 3px
}
.main_kuang_li31 {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; WIDTH: 450px; HEIGHT: 510px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.daijing {
	TEXT-ALIGN: center; LINE-HEIGHT: 45px; BACKGROUND-COLOR: #b7b3b3; WIDTH: 45px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: left; HEIGHT: 45px; COLOR: #fff; FONT-SIZE: 30px; FONT-WEIGHT: bold; border-top-left-radius: 0.2em; border-top-right-radius: 0.2em; border-bottom-left-radius: 0.2em; border-bottom-right-radius: 0.2em
}
.yaoqingline {
	BORDER-LEFT: #dadada 1px dashed; WIDTH: 1px; FLOAT: left; HEIGHT: 160px
}
.TQ {
	WIDTH: 29px; BACKGROUND: url(../images/qqbb.jpg) no-repeat; FLOAT: left; HEIGHT: 29px; MARGIN-LEFT: 2px; CURSOR: pointer; MARGIN-RIGHT: 2px
}
.sina {
	WIDTH: 29px; BACKGROUND: url(../images/sinabb.jpg) no-repeat; FLOAT: left; HEIGHT: 29px; MARGIN-LEFT: 2px; CURSOR: pointer; MARGIN-RIGHT: 2px
}
.account-27-y {
	BACKGROUND-IMAGE: url(../images/cico10_1.jpg); BORDER-BOTTOM: medium none; POSITION: absolute; BORDER-LEFT: medium none; PADDING-BOTTOM: 0px; PADDING-LEFT: 0px; WIDTH: 732px; PADDING-RIGHT: 0px; HEIGHT: 110px; BORDER-TOP: medium none; TOP: 0px; BORDER-RIGHT: medium none; PADDING-TOP: 0px; LEFT: 0px
}
.mybtn_cong {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px 0px 1px; OUTLINE-COLOR: invert; PADDING-LEFT: 1em; OUTLINE-WIDTH: medium; WIDTH: 115px; PADDING-RIGHT: 2em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #008ed6; HEIGHT: 35px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn_cong:hover {
	BACKGROUND: #0279b5
}
.shufei A:link {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #fff; WIDTH: 937px; FLOAT: left; HEIGHT: 65px; BORDER-RIGHT: #dadada 0px solid
}
.shufei A:visited {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #fff; WIDTH: 937px; FLOAT: left; HEIGHT: 65px; BORDER-RIGHT: #dadada 0px solid
}
.shufei A:hover {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #f8f8f8; WIDTH: 937px; FLOAT: left; HEIGHT: 65px; BORDER-RIGHT: #dadada 0px solid
}
.shufei_1 A:link {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #fff; WIDTH: 957px; FLOAT: left; HEIGHT: 65px; BORDER-RIGHT: #dadada 0px solid
}
.shufei_1 A:visited {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #fff; WIDTH: 957px; FLOAT: left; HEIGHT: 65px; BORDER-RIGHT: #dadada 0px solid
}
.shufei_1 A:hover {
	BORDER-BOTTOM: #dadada 0px solid; BORDER-LEFT: #dadada 0px solid; BACKGROUND-COLOR: #f8f8f8; WIDTH: 957px; FLOAT: left; HEIGHT: 65px; BORDER-RIGHT: #dadada 0px solid
}
.pro_list_tt {
	BACKGROUND-IMAGE: url(../images/touzhitt.jpg); MARGIN-TOP: 5px; WIDTH: 979px; FLOAT: right; HEIGHT: 113px
}
.bgchange {
	WIDTH: 15px; BACKGROUND: url(../images/daoling_gao.png)
}
.bgchange:hover {
	BACKGROUND: url(../images/daoling.png)
}
.loan-prompt {
	POSITION: relative; MARGIN: 0px 30px; HEIGHT: 70px
}
.plans-items .sur {
	TEXT-ALIGN: left; PADDING-LEFT: 15px
}
.table-plan {
	PADDING-BOTTOM: 30px; ZOOM: 1; OVERFLOW: hidden
}
.table-plan TABLE {
	BORDER-BOTTOM: #d6dbde 1px solid; BORDER-LEFT: #d6dbde 1px solid; WIDTH: 100%; COLOR: #69737b; FONT-SIZE: 12px; BORDER-TOP: #d6dbde 1px solid; BORDER-RIGHT: #d6dbde 1px solid
}
.table-plan TD {
	BORDER-BOTTOM: #e8ecef 1px solid; PADDING-BOTTOM: 15px; LINE-HEIGHT: 1.8; PADDING-LEFT: 20px; PADDING-RIGHT: 20px; VERTICAL-ALIGN: text-top; PADDING-TOP: 15px
}
.table-plan .th {
	TEXT-ALIGN: right; WIDTH: 104px; BACKGROUND: #f0f4f7; BORDER-RIGHT: #e8ecef 1px solid
}
.table-plan .vouch {
	POSITION: relative; PADDING-LEFT: 18px; COLOR: #18b160; FONT-SIZE: 14px; FONT-WEIGHT: bold
}
.table-plan .vouch .icons {
	POSITION: absolute; TOP: 4px; LEFT: 0px
}
.plans-sum {
	POSITION: relative; PADDING-BOTTOM: 40px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; MARGIN-BOTTOM: 20px; HEIGHT: 48px; COLOR: #8a949c; PADDING-TOP: 40px
}
.plans-sum UL {
	POSITION: relative; FLOAT: left; HEIGHT: 48px; LEFT: 50%
}
.plans-sum LI {
	POSITION: relative; BORDER-LEFT: #d0d5d9 1px solid; PADDING-BOTTOM: 0px; LINE-HEIGHT: normal; PADDING-LEFT: 90px; PADDING-RIGHT: 30px; DISPLAY: inline; BACKGROUND: url(../../../local/img/common/info_dot.png) no-repeat 30px 0px; FLOAT: left; HEIGHT: 48px; COLOR: #475058; OVERFLOW: hidden; PADDING-TOP: 0px; LEFT: -50%
}
.plans-sum LI.col_1 {
	BORDER-BOTTOM: 0px; BORDER-LEFT: 0px; BACKGROUND-POSITION: 30px 0px; BORDER-TOP: 0px; BORDER-RIGHT: 0px
}
.plans-sum LI.col_2 {
	BACKGROUND-POSITION: 30px -53px
}
.plans-sum LI.col_3 {
	BACKGROUND-POSITION: 30px -106px
}
.plans-sum LI.col_4 {
	BACKGROUND-POSITION: 30px -159px
}
.plans-sum EM {
	DISPLAY: block; FONT-FAMILY: Tahoma, Geneva, sans-serif; COLOR: #69737b; FONT-SIZE: 24px
}
.foobar {
	Z-INDEX: 997; POSITION: fixed; TEXT-ALIGN: center; LINE-HEIGHT: 25px; WIDTH: 100%; BOTTOM: 0px; HEIGHT: 25px; COLOR: #fff; FONT-SIZE: 12px; OVERFLOW: hidden; BORDER-TOP: #325675 1px solid; LEFT: 0px; _position: absolute; _bottom: auto
}
.foobar A {
	COLOR: #fff
}
.foobar A:visited {
	COLOR: #fff
}
.foobar A:hover {
	COLOR: #fc8936
}
.foobar .bg {
	Z-INDEX: 3; POSITION: absolute; FILTER: alpha(opacity=85); WIDTH: 100%; BACKGROUND: #224e73; HEIGHT: 100%; BORDER-TOP: #6d8aa3 1px solid; TOP: 0px; LEFT: 0px; opacity: .85
}
.foobar SPAN {
	Z-INDEX: 5; POSITION: relative; PADDING-LEFT: 20px; DISPLAY: inline-block; _margin-top: 2px
}
.foobar .ver-green-down {
	POSITION: absolute; TOP: 5px; LEFT: 0px
}
.foobar .more {
	POSITION: relative; PADDING-BOTTOM: 0px; PADDING-LEFT: 10px; PADDING-RIGHT: 10px; PADDING-TOP: 0px
}
.foobar .arrow-white-dotr {
	POSITION: absolute; TOP: 4px; RIGHT: 2px; _top: 7px
}
.yterm .button {
	TEXT-ALIGN: center; PADDING-BOTTOM: 40px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; HEIGHT: 40px; PADDING-TOP: 20px
}
.time-limit {
	WIDTH: 100%; HEIGHT: 80px; PADDING-TOP: 5px
}
.time-limit .per {
	POSITION: relative; TEXT-ALIGN: right; WIDTH: 50%; FONT-FAMILY: Tahoma; FLOAT: left; HEIGHT: 80px; FONT-SIZE: 18px
}
.time-limit .time {
	POSITION: relative; TEXT-ALIGN: left; WIDTH: 49.33%; FLOAT: left; HEIGHT: 55px; FONT-SIZE: 18px; FONT-WEIGHT: normal; PADDING-TOP: 25px
}
.time-limit B {
	POSITION: absolute; DISPLAY: block; FONT-FAMILY: "????"; FONT-SIZE: 14px; TOP: 60px; FONT-WEIGHT: normal; LEFT: 25px
}
.time-limit .per B {
	RIGHT: 15px; FONT-WEIGHT: normal; LEFT: auto
}
.time-limit .light-blue {
	PADDING-RIGHT: 15px; DISPLAY: block; FONT-FAMILY: Microsoft YaHei; HEIGHT: 60px; COLOR: #15456f; OVERFLOW: hidden; FONT-WEIGHT: bold
}
.time-limit .light-blue EM {
	FONT-SIZE: 40px
}
.time-limit .x {
	PADDING-RIGHT: 15px
}
.plans-items .rate {
	POSITION: absolute; TEXT-ALIGN: center; LINE-HEIGHT: normal; WIDTH: 100%; BOTTOM: 157px; HEIGHT: 20px; FONT-SIZE: 12px; VERTICAL-ALIGN: top; LEFT: 0px
}
.plans-items .rate B {
	PADDING-LEFT: 18px; FONT-SIZE: 14px; FONT-WEIGHT: normal
}
.plans-items .rate .icons {
	POSITION: absolute; TOP: 1px; MARGIN-RIGHT: 3px; LEFT: 30px
}
.plans-items .button {
	POSITION: absolute; TEXT-ALIGN: center; WIDTH: 100%; BOTTOM: 0px; BACKGROUND: #f0f5f7; HEIGHT: 139px; BORDER-TOP: #d9dee2 1px solid; LEFT: 0px; _bottom: -1px
}
.plans-items .button A {
	LINE-HEIGHT: 24px; MARGIN-TOP: 18px; WIDTH: 60px; HEIGHT: 24px; FONT-SIZE: 14px
}
.progre {
	POSITION: relative; PADDING-BOTTOM: 10px; PADDING-LEFT: 15px; PADDING-RIGHT: 15px; HEIGHT: 20px; PADDING-TOP: 20px
}
.progre .per {
	POSITION: absolute; LINE-HEIGHT: 0; WIDTH: 40%; DISPLAY: inline; BACKGROUND: #fff; FLOAT: left; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; TOP: 25px; LEFT: 96px; border-radius: 15px
}
.progre .per DIV {
	WIDTH: 0%; BACKGROUND: #18b160; FLOAT: left; HEIGHT: 10px; border-radius: 15px
}
.plans-items .sur {
	TEXT-ALIGN: left; PADDING-LEFT: 15px
}
.dingtaobao {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; MARGIN: 0px auto; WIDTH: 956px; BACKGROUND: #fff; FLOAT: left; HEIGHT: 480px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.dingtaobaonew {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; MARGIN: 0px auto; WIDTH: 940px; BACKGROUND: #fff; FLOAT: left; HEIGHT: 406px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.dingtaobaonew10 {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; MARGIN: 0px auto; WIDTH: 956px; BACKGROUND: #fff; FLOAT: left; HEIGHT: 353px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.dingtaobaonew1 {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; MARGIN: 0px auto; WIDTH: 940px; BACKGROUND: #fff; FLOAT: left; HEIGHT: 150px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.dingtaobaonew2 {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; MARGIN: 0px auto; WIDTH: 940px; BACKGROUND: #fff; FLOAT: left; HEIGHT: 680px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.dingcenter {
	MARGIN: 0px auto
}
.dingfont {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 16px; TEXT-DECORATION: underline
}
.dingline {
	BORDER-BOTTOM: #e0e0e0 1px solid
}
.dingline1 {
	BORDER-BOTTOM: #e0e0e0 1px dashed
}
.dingkuang {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; MARGIN: 10px auto 0px; WIDTH: 910px; HEIGHT: 100px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.dingkuang:link {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 10px auto 0px; WIDTH: 910px; HEIGHT: 100px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.dingkuang:hover {
	BORDER-BOTTOM: #e0e0e0 1px solid; BORDER-LEFT: #e0e0e0 1px solid; BACKGROUND-COLOR: #eeeeee; MARGIN: 10px auto 0px; WIDTH: 910px; HEIGHT: 100px; BORDER-TOP: #e0e0e0 1px solid; BORDER-RIGHT: #e0e0e0 1px solid
}
.f35 {
	FONT-SIZE: 26px
}
.f36 {
	FONT-SIZE: 24px
}
.mybtn_2t {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.3em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN-TOP: 0px; OUTLINE-COLOR: invert; PADDING-LEFT: 1em; OUTLINE-WIDTH: medium; WIDTH: 65px; PADDING-RIGHT: 1em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; MARGIN-BOTTOM: 0px; BACKGROUND: #008ed6; HEIGHT: 18px; COLOR: #ffffff; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.4em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn_2t:hover {
	BACKGROUND: #0279b5
}
.peru {
	LINE-HEIGHT: 0; MARGIN: 5px 0px 0px 1px; WIDTH: 95%; DISPLAY: inline; BACKGROUND: #e8ecef; FLOAT: left; HEIGHT: 10px; FONT-SIZE: 0px; OVERFLOW: hidden; border-radius: 15px; -moz-border-radius: 15px; -webkit-border-radius: 15px
}
.peru DIV {
	WIDTH: 0%; BACKGROUND: #00a8e8; FLOAT: left; HEIGHT: 10px; border-radius: 15px; -moz-border-radius: 15px; -webkit-border-radius: 15px
}
.ding_line_1 {
	BORDER-LEFT: #e0e0e0 1px dashed
}
.ding_right_line {
	BORDER-LEFT: #e0e0e0 1px dashed
}
.gaoliang {
	BORDER-BOTTOM: #e8e8e8 1px solid; WIDTH: 900px; HEIGHT: 50px
}
.gaoliang:hover {
	BORDER-BOTTOM: #e8e8e8 1px solid; BACKGROUND: #eeeeee
}
.zhicolor {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #666666
}
.zhiline {
	BORDER-RIGHT: #cccccc 1px dashed
}
.zhiorg {
	COLOR: #ff6600
}
.f13 {
	FONT-SIZE: 13px
}
.zhibottom {
	HEIGHT: 1px; BORDER-TOP: #cccccc 1px dashed
}
.zhitop {
	BORDER-BOTTOM: #cccccc 1px dashed; HEIGHT: 1px
}
.btns {
	Z-INDEX: 2; POSITION: absolute; TOP: 0px; LEFT: 0px
}
.btns LI {
	BORDER-BOTTOM: 0px; TEXT-ALIGN: center; BORDER-LEFT: #dedede 1px solid; LINE-HEIGHT: 40px; WIDTH: 180px; FONT-FAMILY: "Microsoft Yahei"; BACKGROUND: #f1f1f1; FLOAT: left; HEIGHT: 40px; FONT-SIZE: 16px; BORDER-TOP: #dedede 1px solid; CURSOR: pointer; MARGIN-RIGHT: 7px; BORDER-RIGHT: #dedede 1px solid
}
.btns LI.current {
	LINE-HEIGHT: 41px; FONT-FAMILY: "Microsoft Yahei"; BACKGROUND: #fff; HEIGHT: 41px; FONT-SIZE: 16px
}
.pageone {
	TEXT-ALIGN: center; MARGIN-TOP: 15px; FONT-FAMILY: "Microsoft Yahei"; HEIGHT: 30px; COLOR: #666; FONT-SIZE: 14px; OVERFLOW: hidden
}
.pageone A {
	BORDER-BOTTOM: #dedede 1px solid; BORDER-LEFT: #dedede 1px solid; LINE-HEIGHT: 20px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; DISPLAY: inline-block; MARGIN-BOTTOM: 10px; HEIGHT: 20px; COLOR: #666; BORDER-TOP: #dedede 1px solid; MARGIN-RIGHT: 5px; BORDER-RIGHT: #dedede 1px solid
}
.pageone A:hover {
	BORDER-BOTTOM: #148dd9 1px solid; BORDER-LEFT: #148dd9 1px solid; BACKGROUND-COLOR: #148dd9; COLOR: #ffffff; BORDER-TOP: #148dd9 1px solid; BORDER-RIGHT: #148dd9 1px solid; TEXT-DECORATION: none
}
.pageone_input {
	BORDER-BOTTOM: #dedede 1px solid; BORDER-LEFT: #dedede 1px solid; WIDTH: 30px; HEIGHT: 24px; MARGIN-LEFT: 5px; BORDER-TOP: #dedede 1px solid; MARGIN-RIGHT: 5px; BORDER-RIGHT: #dedede 1px solid
}
.pageone_btn {
	BORDER-BOTTOM: #dedede 1px solid; BORDER-LEFT: #dedede 1px solid; PADDING-LEFT: 5px; WIDTH: 30px; PADDING-RIGHT: 5px; BACKGROUND: #f0f0f0; HEIGHT: 24px; COLOR: #666; MARGIN-LEFT: 5px; BORDER-TOP: #dedede 1px solid; CURSOR: pointer; MARGIN-RIGHT: 5px; BORDER-RIGHT: #dedede 1px solid
}
.pageDivClass .curPageColor {
	COLOR: #ffffff
}
.curPageColor {
	BACKGROUND: #148dd9; COLOR: #ffffff
}
#curPageText {
	BORDER-BOTTOM: #dedede 1px solid; TEXT-ALIGN: center; BORDER-LEFT: #dedede 1px solid; MARGIN-TOP: -3px; HEIGHT: 20px; BORDER-TOP: #dedede 1px solid; BORDER-RIGHT: #dedede 1px solid
}
.zhib {
	BACKGROUND-COLOR: #008fd8; WIDTH: 70px; FLOAT: left; HEIGHT: 25px; COLOR: #fff; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px
}
.zhib_1 {
	LINE-HEIGHT: 25px; BACKGROUND-COLOR: #008fd8; WIDTH: 48px; FLOAT: left; HEIGHT: 25px; COLOR: #fff; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px
}
.f15_1 {
	FONT-SIZE: 15px
}
.f15_2 {
	LINE-HEIGHT: 17px; FLOAT: left
}
.zhib_new {
	TEXT-ALIGN: center; LINE-HEIGHT: 40px; BACKGROUND-COLOR: #008fd8; WIDTH: 185px; FLOAT: left; HEIGHT: 40px; COLOR: #fff; FONT-SIZE: 22px; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px
}
.contw {
	BORDER-BOTTOM: #dedede 1px solid; POSITION: absolute; BORDER-LEFT: #dedede 1px solid; LINE-HEIGHT: 25px; MARGIN: 0px auto; WIDTH: 956px; FONT-FAMILY: "Microsoft Yahei"; OVERFLOW: hidden; BORDER-TOP: #dedede 1px solid; TOP: 41px; BORDER-RIGHT: #dedede 1px solid; LEFT: 0px
}
.contw .swt {
	POSITION: absolute; TOP: 0px; LEFT: 0px
}
.contw .swt LI {
	FLOAT: left
}
.contw .swt LI P {
	PADDING-BOTTOM: 16px; PADDING-LEFT: 16px; PADDING-RIGHT: 16px; PADDING-TOP: 16px
}
.zhibai {
	COLOR: #ffffff
}
.zhigay {
	COLOR: #545353
}
.zhibiao {
	BORDER-BOTTOM: 0px; BORDER-LEFT: 0px; BACKGROUND-COLOR: #e8ecef; FONT-FAMILY: "Microsoft Yahei"; BORDER-TOP: 0px; BORDER-RIGHT: 0px
}
.zhaoline {
	BORDER-BOTTOM: #e7e7e7 1px solid; WIDTH: 100%; HEIGHT: 1px
}
.zhaobouut {
	TEXT-DECORATION: underline
}
.zhangus {
	COLOR: #3a8dc9
}
.ding_right_se {
	BORDER-LEFT: #e7e7e7 1px solid
}
.ding_s_x {
	MARGIN-TOP: 15px; MARGIN-BOTTOM: 15px
}
.zhib_10 {
	LINE-HEIGHT: 20px; BACKGROUND-COLOR: #008fd8; WIDTH: 38px; FLOAT: left; HEIGHT: 20px; COLOR: #fff; MARGIN-LEFT: 10px; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px
}
.libiao {
	BACKGROUND-COLOR: #1b9bdc; WIDTH: 110px; FLOAT: left; HEIGHT: 22px; COLOR: #fff; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px; -webkit-box-shadow: 2px 2px 2px black; -moz-box-shadow: 2px 2px 2px black
}
.zhaoline1 {
	BORDER-BOTTOM: #e7e7e7 1px solid; WIDTH: 60px; HEIGHT: 1px
}
.newsoute_line {
	BORDER-BOTTOM: #e7e7e7 1px dashed
}
.f30 {
	COLOR: #ff0000; FONT-SIZE: 38px
}
.newsou_bin {
	BORDER-BOTTOM: #e7e7e7 1px solid; BORDER-LEFT: #e7e7e7 1px solid; WIDTH: 250px; HEIGHT: 25px; BORDER-TOP: #e7e7e7 1px solid; BORDER-RIGHT: #e7e7e7 1px solid
}
.new_bouutse {
	BORDER-BOTTOM: #dadada 1px solid
}
.new_bou_m {
	PADDING-LEFT: 15px
}
.gayzhen {
	COLOR: #a6a4a4
}
.lineone5_tw {
	BORDER-BOTTOM: #dadada 1px dashed; BACKGROUND-COLOR: #fff; MARGIN-TOP: 10px; WIDTH: 100%; FLOAT: left
}
.newsou A:link {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #3a8dc9; TEXT-DECORATION: none
}
.newsou A:visited {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #3a8dc9; TEXT-DECORATION: none
}
.newsou A:active {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #3a8dc9; TEXT-DECORATION: none
}
.newsou A:hover {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #fc9141; TEXT-DECORATION: none
}
.seofy {
	TEXT-ALIGN: center; PADDING-BOTTOM: 20px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; HEIGHT: 36px; PADDING-TOP: 10px
}
.seofy EM {
	LINE-HEIGHT: 36px; DISPLAY: inline-block; HEIGHT: 36px
}
.seofy SPAN {
	LINE-HEIGHT: 36px; DISPLAY: inline-block; HEIGHT: 36px
}
.seofy A {
	LINE-HEIGHT: 36px; DISPLAY: inline-block; HEIGHT: 36px
}
.seofy SPAN {
	BORDER-BOTTOM: rgb(211,211,211) 1px solid; TEXT-ALIGN: center; BORDER-LEFT: rgb(211,211,211) 1px solid; MARGIN: 0px 2px; WIDTH: 40px; VERTICAL-ALIGN: middle; BORDER-TOP: rgb(211,211,211) 1px solid; BORDER-RIGHT: rgb(211,211,211) 1px solid; TEXT-DECORATION: none; border-radius: 3px
}
.seofy A {
	BORDER-BOTTOM: rgb(211,211,211) 1px solid; TEXT-ALIGN: center; BORDER-LEFT: rgb(211,211,211) 1px solid; MARGIN: 0px 2px; WIDTH: 40px; VERTICAL-ALIGN: middle; BORDER-TOP: rgb(211,211,211) 1px solid; BORDER-RIGHT: rgb(211,211,211) 1px solid; TEXT-DECORATION: none; border-radius: 3px
}
.seofy .active {
	BORDER-BOTTOM: rgb(252,145,65) 1px solid; BORDER-LEFT: rgb(252,145,65) 1px solid; BACKGROUND: rgb(252,145,65); COLOR: rgb(255,255,255); BORDER-TOP: rgb(252,145,65) 1px solid; BORDER-RIGHT: rgb(252,145,65) 1px solid
}
.seofy A:hover {
	BORDER-BOTTOM: rgb(252,145,65) 1px solid; BORDER-LEFT: rgb(252,145,65) 1px solid; BACKGROUND: rgb(252,145,65); COLOR: rgb(255,255,255); BORDER-TOP: rgb(252,145,65) 1px solid; BORDER-RIGHT: rgb(252,145,65) 1px solid
}
.sort11 UL {
	HEIGHT: auto; OVERFLOW: hidden
}
.sort11 UL LI {
	BORDER-BOTTOM: rgb(211,211,211) 1px solid; BORDER-LEFT: rgb(211,211,211) 1px solid; MARGIN: 0px 10px 10px 0px; WIDTH: 172px; FLOAT: left; HEIGHT: 180px; BORDER-TOP: rgb(211,211,211) 1px solid; BORDER-RIGHT: rgb(211,211,211) 1px solid
}
.sort11 UL LI A {
	WIDTH: 172px; DISPLAY: block; HEIGHT: 180px
}
.sort11 UL LI A:hover {
	BACKGROUND-COLOR: rgb(248,248,248)
}
.sort11 UL LI A .top {
	TEXT-ALIGN: center; DISPLAY: block; HEIGHT: 50%
}
.sort11 UL LI A .foot {
	PADDING-BOTTOM: 0px; PADDING-LEFT: 10px; PADDING-RIGHT: 10px; DISPLAY: block; HEIGHT: 50%; PADDING-TOP: 0px
}
.black2 UL {
	TEXT-ALIGN: center; WIDTH: 100%; MARGIN-LEFT: 20px
}
.black2 UL LI {
	FLOAT: left; MARGIN-LEFT: 3px
}
.newsks {
	PADDING-BOTTOM: 5px; BACKGROUND-COLOR: #ededed; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; MARGIN-BOTTOM: 6px; FLOAT: left; MARGIN-RIGHT: 6px; PADDING-TOP: 5px; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px; -webkit-box-shadow: 2px 2px 2px black; -moz-box-shadow: 2px 2px 2px black
}
.newsks UL LI {
	LINE-HEIGHT: 24px
}
.news_ks {
	BACKGROUND-COLOR: #ffffff; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; HEIGHT: 20px; MARGIN-LEFT: 5px; border-radius: 2px; -moz-border-radius: 2px; -webkit-border-radius: 2px; -webkit-box-shadow: 2px 2px 2px black; -moz-box-shadow: 2px 2px 2px black
}
.renrenbiank {
	LINE-HEIGHT: 40px; BACKGROUND-COLOR: #ffffff; FLOAT: left
}
.renrenbiank UL LI {
	BORDER-BOTTOM: #dadada 1px solid; TEXT-ALIGN: center; LINE-HEIGHT: 40px; LIST-STYLE-TYPE: none; WIDTH: 138px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: left; HEIGHT: 40px; LIST-STYLE-IMAGE: none; BORDER-RIGHT: #dadada 1px solid
}
.renrenbiank UL LI A:hover {
	BORDER-BOTTOM: #dadada 1px solid; TEXT-ALIGN: center; LINE-HEIGHT: 40px; BACKGROUND-COLOR: #cccccc; LIST-STYLE-TYPE: none; WIDTH: 138px; FONT-FAMILY: "Microsoft Yahei"; FLOAT: left; HEIGHT: 40px; COLOR: #ffffff; LIST-STYLE-IMAGE: none; BORDER-RIGHT: #dadada 1px solid
}
.mybtn_2t_ok {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.3em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN-TOP: 0px; OUTLINE-COLOR: invert; PADDING-LEFT: 1em; OUTLINE-WIDTH: medium; WIDTH: 65px; PADDING-RIGHT: 1em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; MARGIN-BOTTOM: 0px; BACKGROUND: #aeaeae; HEIGHT: 18px; COLOR: #ffffff; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.4em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn_2w {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.3em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN-TOP: 0px; OUTLINE-COLOR: invert; PADDING-LEFT: 1em; OUTLINE-WIDTH: medium; WIDTH: 72px; PADDING-RIGHT: 1em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; MARGIN-BOTTOM: 0px; BACKGROUND: #fc9141; HEIGHT: 18px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.4em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtn_2w:hover {
	BACKGROUND: #0279b5
}
.newsou_binrr {
	BORDER-BOTTOM: #e7e7e7 1px solid; BORDER-LEFT: #e7e7e7 1px solid; WIDTH: 350px; HEIGHT: 35px; BORDER-TOP: #e7e7e7 1px solid; BORDER-RIGHT: #e7e7e7 1px solid
}
.nnsy {
	COLOR: #666666
}
.newsou_binrr {
	BORDER-BOTTOM: #e7e7e7 1px solid; BORDER-LEFT: #e7e7e7 1px solid; WIDTH: 350px; HEIGHT: 35px; BORDER-TOP: #e7e7e7 1px solid; BORDER-RIGHT: #e7e7e7 1px solid
}
.newsou_binrr1 {
	BORDER-BOTTOM: #e7e7e7 2px solid; BORDER-LEFT: #e7e7e7 2px solid; WIDTH: 450px; HEIGHT: 35px; BORDER-TOP: #e7e7e7 2px solid; BORDER-RIGHT: #e7e7e7 2px solid
}
.nnsy {
	COLOR: #666666
}
.baidsf {
	COLOR: #999999
}
.grenn {
	COLOR: #ff6600
}
.newtvli {
	BORDER-BOTTOM: #dadada 1px solid; BORDER-LEFT: #dadada 1px solid; BACKGROUND-COLOR: #ffffff; MARGIN: 13px auto 0px; WIDTH: 958px; HEIGHT: 910px; BORDER-TOP: #dadada 1px solid; BORDER-RIGHT: #dadada 1px solid
}
.cnew {
	FONT-FAMILY: "Microsoft Yahei"; COLOR: #c00000
}
.cnew_line {
	LINE-HEIGHT: 35px; FONT-FAMILY: "Microsoft Yahei"; COLOR: #c00000; FONT-SIZE: 16px; TEXT-DECORATION: underline
}
.line_29 {
	LINE-HEIGHT: 29px
}
.top_table {
	BORDER-BOTTOM: #dedede 0px solid; WIDTH: 684px; HEIGHT: 45px
}
.top_table UL {
	WIDTH: 684px
}
.top_table UL LI {
	BACKGROUND-IMAGE: url(images/renren_2.png); TEXT-ALIGN: center; LINE-HEIGHT: 45px; LIST-STYLE-TYPE: none; WIDTH: 100px; FLOAT: left; HEIGHT: 45px; LIST-STYLE-IMAGE: none; MARGIN-RIGHT: 5px
}
.top_table_hight {
	BACKGROUND-IMAGE: url(images/renren_1.png); TEXT-ALIGN: center; LINE-HEIGHT: 45px; LIST-STYLE-TYPE: none; WIDTH: 100px; FLOAT: left; HEIGHT: 45px; LIST-STYLE-IMAGE: none; MARGIN-RIGHT: 5px
}
.dingtoo {
	COLOR: #515151
}
.gaynew {
	COLOR: #6f6f6f
}
.f32_1 {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 34px
}
.f30_1 {
	FONT-FAMILY: "Microsoft Yahei"; FONT-SIZE: 30px
}
.f30_t {
	TEXT-DECORATION: underline
}
.mybtnwt {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 2px; OUTLINE-COLOR: invert; PADDING-LEFT: 0.5em; OUTLINE-WIDTH: medium; WIDTH: 90px; PADDING-RIGHT: 0.5em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #008ed6; HEIGHT: 40px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtnwt:hover {
	BACKGROUND: #0279b5
}
.mybtnwt1 {
	BORDER-BOTTOM-STYLE: solid; TEXT-ALIGN: center; PADDING-BOTTOM: 0.55em; LINE-HEIGHT: 100%; BORDER-RIGHT-STYLE: solid; MARGIN: 0px 1px; OUTLINE-COLOR: invert; PADDING-LEFT: 0.5em; OUTLINE-WIDTH: medium; WIDTH: 95px; PADDING-RIGHT: 0.5em; ZOOM: 1; DISPLAY: inline-block; FONT-FAMILY: Microsoft Yahei; BORDER-TOP-STYLE: solid; BACKGROUND: #06a852; HEIGHT: 45px; COLOR: #fefee9; FONT-SIZE: 16px; VERTICAL-ALIGN: text-bottom; BORDER-LEFT-STYLE: solid; CURSOR: pointer; TEXT-DECORATION: none; PADDING-TOP: 0.5em; font-size-adjust: none; font-stretch: normal; border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; border-bottom-left-radius: 0.5em; border-bottom-right-radius: 0.5em; box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2); text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.3); background-size: auto; background-origin: padding-box; background-clip: padding-box
}
.mybtnwt1:hover {
	FONT-FAMILY: "Microsoft Yahei"; BACKGROUND: #048440
}

/*软件下载页面*/
.body-container.down-bg{
background:url(../images/9195421.jpg) no-repeat;
background-size: 100% 100%;
padding-top:141px;
padding-bottom: 85px;
width: 100%;

}
.body-container.down-bg .down-left-img{
float: left;
}
.body-container.down-bg .down-right{
padding-left: 40px;
float: left;
color: #fff;
}
.body-container.down-bg .down-right .tit{
	font-size: 44px;
	padding-left: 28px;
	font-weight: bold;
	padding-bottom: 20px;
}
.body-container.down-bg .down-right  .brief{
	font-size: 26px;
}
.body-container.down-bg .down-btn{
	padding: 30px 0px 0px 28px;
}
.body-container.down-bg .down-btn .button{
border-radius: 7px;
width: 270px;
height: 77px;
line-height: 77px;
background:#fff;
text-align: center;
display:block;
font-size: 24px;
margin-bottom:38px;
border:0px;
}
.body-container.down-bg .down-btn a{
border-radius: 7px;
width: 300px;
height: 62px;
line-height: 62px;
background:#fff;
text-align: center;
display:block;
font-size: 24px;
margin-bottom:38px;
border:0px;
}
.body-container.down-bg .down-btn .win-mac-icon{
	width:50px;
	height:50px;
	display: block;
	float: left;
	position: relative;
	top: 5px;
	left: 22px;
	background:url(../images/win-mac-icon.png) no-repeat;
}
.body-container.down-bg .down-btn .win-mac-icon.mac-icon{
	background-position: -60px 1px;
}
.body-container.down-bg .down-btn .and-icon{
	background-position: -122px 1px;
}
/*软件下载页面 end*/