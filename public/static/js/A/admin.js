

var isSub = false;
function checkSub(){
	if(isSub){
		layer.msg('不要重复提交表单！',{icon:2});
		return false;
	}
	isSub = true;
	return true;
}



function infoCard(uid){
    if( !uid ){
        return false;
    }

    layer.open({
        type: 2,
        title: '会员信息',
        maxmin: false,
        shadeClose: false,
        area : ['580px' , '380px'],
        content: '../members/infoCard?uid='+uid,
        end: function(){
            // location.reload();
        }
    });
}



function IEVersion() {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串  
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器  
    var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器  
    var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
    if(isIE) {
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        if(fIEVersion == 7) {
            return 7;
        } else if(fIEVersion == 8) {
            return 8;
        } else if(fIEVersion == 9) {
            return 9;
        } else if(fIEVersion == 10) {
            return 10;
        } else {
            return 6;//IE版本<=7
        }   
    } else if(isEdge) {
        return 'edge';//edge
    } else if(isIE11) {
        return 11; //IE11  
    }else{
        return false;//不是ie浏览器
    }
}