function login(){
    // var uname = $('input[name=uname]').val();
    // var pass = $('input[name=pass]').val();
    // var vcode = $('input[name=vcode]').val();
    var uname = $('#user_name').val();
    var pass = $('#password').val();
    var vcode = $('#vcodeyc').val();
    /*var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(uname)) ){
        return layer.msg( '手机号码有误，请重新输入',{icon:2,anim:6} );
    }*/

    if( uname.length<2 || uname.length>16 ){
        return layer.msg( '用户名不得小于2位或者大于16位',{icon:2,anim:6} );
    }
    if( pass.length<6 || pass.length>16 ){
        return layer.msg( '密码不得小于6位或者大于16位',{icon:2,anim:6} );
    }
   if( !vcode ){
        return layer.msg( '图形验证码不得为空',{icon:2,anim:6} );
    }

    var la_load = layer.load(0,{
            shade: [0.1,'#000']
        });
    $.ajax({
        url: loginUrl,
        type: "post",
        dataType: "json",
        data: {uname:uname,vcode:vcode,pass:pass},
        success: function(d,e,x){  
            if(d.status==1){
                layer.msg( d.message,{icon:1} );
                var t1 = setTimeout(function(){
                        // layer.close(la_load);
                        window.location.href = member_index;//页面刷新
                    },1500);
            }else{
                ent = true;
                $('.jym').trigger('click');
                layer.close(la_load);
                return layer.msg( d.message,{icon:2} );
            }
        }
    });
}

function reg(from){
    var uname = $('input[name=uname]').val();
    var vcode = $('input[name=vcode]').val();
    var pass = $('input[name=pass]').val();
    var pass2 = $('input[name=pass2]').val();
    var recommend = $('input[name=recommend]').val();

    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(uname)) ){
        return layer.msg( '手机号码有误，请重新输入',{icon:2,anim:6} );
    }
    if( !vcode ){
        return layer.msg( '短信验证码不能为空',{icon:2,anim:6} );
    }
    if( pass.length<6 || pass.length>16 ){
        return layer.msg( '密码不得小于6位或者大于16位',{icon:2,anim:6} );
    }
    if( from!='index' ){
        if( pass != pass2 ){
            return layer.msg( '两次密码输入不一致',{icon:2,anim:6} );
        }
    }
    if( !$("input[name=agree]").is(":checked") ){
        return layer.msg( '请阅读并同意配资协议',{icon:2,anim:6} );
    }

    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url: regUrl,
        type: "post",
        dataType: "json",
        data: {uname:uname,vcode:vcode,pass:pass,recommend:recommend},
        success: function(d,e,x){  
            if(d.status==1){
                layer.msg( d.message,{icon:1} );
                var t1 = setTimeout(function(){
                        // layer.close(la_load);
                        window.location.href = member_index;//页面刷新
                    },1500);
            }else{
                layer.close(la_load);
                return layer.msg( d.message,{icon:2} );
            }
        }
    });
}



$('#sendSMS').on('click', function(event) {
    var phone = $('input[name=uname]').val();
    var reg = /^1[3456789]\d{9}$/;
    if( !(reg.test(phone)) ){  
        return layer.msg( '手机号码有误，请重新输入',{icon:2,anim:6} );
    } 
    // console.log(phone);
    var la_load = layer.load(0,{
            shade: [0.2,'#000']
        });
    $.ajax({
        url: smsUrl,
        type: "post",
        dataType: "json",
        data: {phone:phone,source:'register'},
        beforeSend:function(){
            txtObj.text('验证码发送中').attr("disabled",true).addClass('btn-disabled');
        },
        success: function(d,e,x){  
            layer.close(la_load);
            if(d.status==1){
                myInterval();
                return layer.msg( d.message,{icon:1} );
            }else{
                //启用按钮
                txtObj.text('重新发送').removeAttr("disabled").removeClass('btn-disabled');
                return layer.msg( d.message,{icon:5} );
            }
        }
    });
});
var txtObj = $('#sendSMS');//文字DOM Obj
var InterValObj; //timer变量，控制时间 
var count = 90; //间隔函数，1秒执行 
var curCount;//当前剩余秒数
function myInterval() {
    curCount = count;
    //设置button效果，开始计时
    txtObj.text(curCount + "秒后重新发送"); 
    InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次 
} 
function SetRemainTime() {
    if (curCount == 0) {         
        window.clearInterval(InterValObj);//停止计时器 
        //启用按钮
        txtObj.removeAttr("disabled").removeClass('btn-disabled');
        txtObj.text("重新发送"); 
    }else{
        curCount--; 
        txtObj.text(curCount + "秒后重新发送"); 
    } 
} 