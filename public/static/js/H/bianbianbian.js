$(function(){
	$.fn.countTo = function (options) {
		options = options || {};
		
		return $(this).each(function () {
			// set options for current element
			var settings = $.extend({}, $.fn.countTo.defaults, {
				from:            $(this).data('from'),
				to:              $(this).data('to'),
				speed:           $(this).data('speed'),
				refreshInterval: $(this).data('refresh-interval'),
				decimals:        $(this).data('decimals')
			}, options);
			
			// how many times to update the value, and how much to increment the value on each update
			var loops = Math.ceil(settings.speed / settings.refreshInterval),
				increment = (settings.to - settings.from) / loops;
				
			// references & variables that will change with each update
			var self = this,
				$self = $(this),
				loopCount = 0,
				value = settings.from,
				data = $self.data('countTo') || {};
			
			$self.data('countTo', data);
			
			// if an existing interval can be found, clear it first
			if (data.interval) {
				clearInterval(data.interval);
			}
			data.interval = setInterval(updateTimer, settings.refreshInterval);
			
			// initialize the element with the starting value
			render(value);
			
			function updateTimer() {
				value += increment;
				loopCount++;
				
				render(value);
				
				if (typeof(settings.onUpdate) == 'function') {
					settings.onUpdate.call(self, value);
				}
				
				if (loopCount >= loops) {
					// remove the interval
					$self.removeData('countTo');
					clearInterval(data.interval);
					value = settings.to;
					
					if (typeof(settings.onComplete) == 'function') {
						settings.onComplete.call(self, value);
					}
				}
			}
			
			function render(value) {
				var formattedValue = settings.formatter.call(self, value, settings);
				$self.html(formattedValue);
			}
		});
	};
	
	$.fn.countTo.defaults = {
		from: 200,               // the number the element should start at
		to: 0,                 // the number the element should end at
		speed: 1000,           // how long it should take to count between the target numbers
		refreshInterval: 1,  // how often the element should be updated
		decimals: 0,           // the number of decimal places to show
		formatter: formatter,  // handler for formatting the value before rendering
		onUpdate: null,        // callback method for every time the element is updated
		onComplete: null       // callback method for when the element finishes updating
	};
	
	function formatter(value, settings) {
		return value.toFixed(settings.decimals);
	}



  // custom formatting example
  $('#count-number1').data('countToOptions', {
	formatter: function (value, options) {
	  return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');

	}
  });
   $('#count-number2').data('countToOptions', {
	formatter: function (value, options) {
	  return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');

	}
  });
    $('#count-number3').data('countToOptions', {
	formatter: function (value, options) {
	  return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');

	}
  });
      $('#count-number4').data('countToOptions', {
	formatter: function (value, options) {
	  return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');

	}
  });
        $('#count-number5').data('countToOptions', {
	formatter: function (value, options) {
	  return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');

	}
  });
  
  // start all the timers
  $('.timer').each(count);  
  
  function count(options) {
	var $this = $(this);
	options = $.extend({}, options || {}, $this.data('countToOptions') || {});
	$this.countTo(options);
  }
})




$(function(){
	var stime = $('#itmer').data('stimeb');
	countDown(stime);
})
function countDown(stime){
	var timer=null;
	timer=setInterval(function(){
		var now = Date.parse(new Date())/1000;
		var start = stime;
		times = now-start;
		var year=0, day=0,
		hour=0,
		minute=0,
		second=0;//时间默认值
		if(times > 0){
			year = Math.floor(times / (60 * 60 * 24 * 365));
	        day = Math.floor(times / (60 * 60 * 24)) - (year * 365) ;
	        hour = Math.floor(times / (60 * 60)) - (year * 365 * 24) - (day * 24);
	        minute = Math.floor(times / 60) - (year * 365 * 24 * 60) - (day * 24 * 60) - (hour * 60);
	        second = Math.floor(times) - (year * 365 * 24 * 60 * 60) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
	    }
	    var html = ''; 
	    if(year > 0 ){
	        html += '<span>'+year+'</span><label>年</label>';
	    }
	    day = day+'';
	    if(day.length==1){
	        day = '00'+day;
	    }
	    if(day.length==2){
	        day = '0'+day;
	    }
	    for(var i=0;i<day.length;i++){
	        html += '<span>'+day.substr(i,1)+'</span>';
	    }
	    html += '<label>天</label>';
	    hour = hour+'';
	    if(hour.length==1){
	        hour = '0'+hour;
	    }
	    for(var i=0;i<hour.length;i++){
	        html += '<span>'+hour.substr(i,1)+'</span>';
	    }
	    html += '<label>时</label>';
	    minute = minute+'';
	    if(minute.length==1){
	        minute = '0'+minute;
	    }
	    for(var i=0;i<minute.length;i++){
	        html += '<span>'+minute.substr(i,1)+'</span>';
	    }
	    html += '<label>分</label>';
	    second = second+'';
	    if(second.length==1){
	        second = '0'+second;
	    }
	    for(var i=0;i<second.length;i++){
	        html += '<span>'+second.substr(i,1)+'</span>';
	    }
	    html += '<label>秒</label>';
	    $("#itmer").html(html);
    },1000);
}
