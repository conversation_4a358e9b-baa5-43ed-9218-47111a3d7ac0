/* 新手任务 */
.contain {
    background: url(./bg0.jpg) repeat;
}
.bannerxsrw {
    height: 409px;
    background: url(./banner02.png) no-repeat center;
}
.task {
    width: 1080px;
    margin: 0 auto;
    border: 8px solid #ee878f;
    border-radius: 10px;
    background: #ffffff80;
    padding: 60px 60px 0;
}
ul, li {
    list-style-type: none;
}
.task .down {
    width: 60px;
    height: 60px;
    background: url(./jt_down.png) no-repeat center;
    background-size: contain;
    margin: 10px 0px 10px 920px;
}
.task .down2 {
    width: 60px;
    height: 60px;
    background: url(./jt_down.png) no-repeat center;
    background-size: contain;
    margin: 10px 0px 10px 118px;
}
.task li {
    display: block;
    width: 28%;
    float: left;
    text-align: center;
}
.task .right {
    width: 60px;
    height: 60px;
    background: url(./jt_right.png) no-repeat center;
    background-size: contain;
    margin: 86px auto;
}
.task .left {
    width: 60px;
    height: 60px;
    background: url(./jt_left.png) no-repeat center;
    background-size: contain;
    margin: 86px auto;
}
.task .item {
    background: #fff;
    border-radius: 5px;
    padding: 40px 0;
    position: relative;
}
.task .item .wc {
    position: absolute;
    right: 0;
    top: 0;
    width: 141px;
    height: 111px;
    background: url(./over.png) no-repeat;
    background-size: contain;
}
.task li .title {
    color: #ff8b19;
    font-size: 24px;
}
.task li .desc {
    color: #bcbcbc;
    font-size: 14px;
    margin: 10px 0px;
}
.task li .money {
    color: #ff8b19;
    font-size: 18px;
    margin-bottom: 20px;
}
.task li.done .button a {
    display: block;
    background: #ccc;
    border-radius: 4px;
    color: #fff;
    font-size: 18px;
    text-align: center;
    line-height: 40px;
    width: 70%;
    margin: 0px auto;
}
.task li .button a {
    display: block;
    background: #ff5064;
    border-radius: 4px;
    color: #fff;
    font-size: 18px;
    text-align: center;
    line-height: 40px;
    width: 70%;
    margin: 0px auto;
}
/* 新手任务 end */