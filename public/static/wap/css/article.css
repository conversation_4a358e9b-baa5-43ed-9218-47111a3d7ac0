:root {
    --primary-color: #3B7AFE;
}

body {
    background: #f5f5f5;
}

.head_text {
    padding-top: 5px;
    font-size: 12px;
    line-height: 20px;
}

.wap_photo {
    height: 160px;
    background: #5b0f0c;
    background-image: url(../images/bg.png);
    background-size: 100%;
    background-repeat: no-repeat;
    text-align: center;
}

.wap_photo img {
    margin-top: 30px;
}

.wap_photo p {
    color: #fff;
    font-size: 14px;
    line-height: 16px;
    margin-top: 10px;
    padding: 0
}

.wap_photo .img_photo {
    width: 80px;
    height: 80px;
    float: left;
    overflow: hidden;
    margin: 13px 13px 0 13px;
}

.wap_photo .img_photo img {
    border-radius: 160px;
}

.qiandao {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 50px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    border-radius: 4px;
    background: #fff0c9;
    color: #ef6886;
}

.height10 {
    height: 10px;
}

.user_box {
    width: 100%
}

.user_box ul {
    width: 100%
}

.user_box ul li {
    position: relative;
    height: 40px;
    border-bottom: 1px solid #eee;
    background: #fff
}

.user_box ul li:last-child {
    border-bottom: 0;
}

.user_box ul .icon {
    position: absolute;
    width: 40px;
    height: 40px;
    left: 0;
    top: 0;
    text-align: center;
}

.user_box ul .icon img {
    width: 16px;
    margin-top: 12px
}

.user_box ul .title {
    padding-left: 40px;
    line-height: 40px;
    color: #666;
    font-size: 14px
}

.user_box ul .more {
    position: absolute;
    width: 20px;
    height: 40px;
    line-height: 40px;
    right: 8px;
    top: 0;
    background-image: url('../images/right.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 8px
}

.weui-media-box__title {
    font-size: 14px;
}

.weui-media-box {
    border-bottom: 1px dashed #eee;
    margin: 10px 15px;
    padding: 0px 0px 10px;
}

.weui-media-box:before {
    border-top: none;
}

.article {
    line-height: 26px;
    padding: 15px;
    font-size: 14px;
}

.article h1 {
    text-align: center;
    font-size: 16px;
}

.article .time {
    text-align: center;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}










/*关于我们*/
.new-h {
    /*background: url(/Style/H/images/one/bg.png) no-repeat;*/
    background: linear-gradient(180.67deg, #6AB2FE 0.57%, #3977FE 99.43%);
    height: 150px;
    background-size: 100%;
    text-align: center;
}

.new-h p {
    color: #fff;
    font-size: 14px;
    margin-top: 5%;
    font-weight: bold;
}

.new-c {
    width: 100%;
    /*border: 1px solid black;*/
}

.new-c .newblock {
    background: #fff;
    padding: 0 10px;
    line-height: 40px;
    color: #666;
    font-size: 14px;
    margin-bottom: 1px;
}

.new-c .newblock .ileft {
    width: 20px;
    height: 20px;
    background: url(../img/a1_icon.png) no-repeat;
    display: inline-block;
    background-size: 16px;
    /*background-position: 0px 5px;*/
    margin-right: 3px;
    position: relative;
    top: 7px;
}

.new-c .newblock.jianjie .ileft,
.new-c .newblock.gshj .ileft {
    background: url(../img/a1_icon.png) no-repeat;
}

.new-c .newblock.zzry .ileft {
    background: url(../img/a2_icon.png) no-repeat;
}

.new-c .newblock.waplxwm .ileft {
    background: url(../img/a3_icon.png) no-repeat;
}

.new-c .newblock.gshq .ileft {
    background: url(../img/a4_icon.png) no-repeat;
}

.new-c .newblock.pzbk .ileft {
    background: url(../img/a5_icon.png) no-repeat;
}

.new-c .newblock.wapxszn .ileft {
    background: url(../img/a6_icon.png) no-repeat;
}

.new-c .newblock.wapcpxz .ileft {
    background: url(../img/a7_icon.png) no-repeat;
}

.new-c .newblock.wapcjwt .ileft {
    background: url(../img/a8_icon.png) no-repeat;
}

.new-c .newblock.wapaylxjj .ileft {
    background: url(../img/a9_icon.png) no-repeat;
}

.new-c .newblock.wapmftyhd .ileft {
    background: url(../img/a10_icon.png) no-repeat;
}

.new-c .newblock.zcxy .ileft {
    background: url(../img/a11_icon.png) no-repeat;
}

.new-c .newblock .iright {
    width: 10px;
    height: 40px;
    display: inline-block;
    background: url(../img/right.png) no-repeat;
    background-position: 0px 11px;
    float: right;
}

.new-c .newblock a {
    color: #666;
}

.mt08 {
    margin-top: 8px !important;
    ;
}

.article-list li {
    font-size: 14px;
    line-height: 40px;
    border-bottom: 1px dashed #eee;
}

.detial .head a {
    color: #777;
}

.detial .head {
    color: #999;
    font-size: 15px;
    padding-bottom: 5px;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;
}