:root {
    --primary-color: #3B7AFE;
}

/*banner*/
.swiper-container img {
    width: 100%;
    display: block;
}

.swiper-container-banner {
    -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    -o-box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 4px rgba(0, 0, 0, .2);
    zoom: 1;
}

/*notice*/
.notice {
    width: 100%;
    height: 34px;
    font-size: 14px;
}

.notice {
    margin: 0 auto;
    height: 35px;
    line-height: 35px;
}

.notice_slider a {
    color: #ff774f;
}

.notice_slider span {
    margin: 0 8px;
}

.notice .notice-icon {
    width: 35px;
    line-height: 33px;
}

.notice .notice-icon img {
    margin-left: 8px;
}

.project-con {
    background: var(--primary-color);
}

.project-con .item-wrapper {
    text-align: center;
}

.project-con .weui-flex__item {
    width: 20%;
    padding: 10px 0;
}

.project-con img {
    vertical-align: middle;
    width: 68%;
}

.project-con p {
    color: #FFFFFF;
    font-size: 14px;
    margin: 5px 0px !important;
    line-height: 14px;
}

.ggmain {
    float: left;
    width: 87%;
    height: 35px;
    overflow: hidden;
}

.ggmain li a {
    width: 71%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ggmain li span {
    width: 28%;
    text-align: right;
}

/*products*/
.products {
    background-color: #fff;
    padding: 20px 0 10px;
    margin-top: 10px;
}

.weui-navbar {
    border-radius: 3px;
    border: 1px solid var(--primary-color);
    width: 88%;
    margin: 0 auto;
    background-color: #fff;
    z-index: 1;
}

.weui-navbar__item:first-child {
    border-left: 0;
}

.weui-navbar__item {
    padding: 10px 0;
    font-size: 14px;
    display: table-cell;
    overflow: hidden;
    -webkit-transition: background-color .1s ease-out;
    transition: background-color .1s ease-out;
    text-align: center;
    text-overflow: ellipsis;
    color: var(--primary-color);
    border-left: 1px solid var(--primary-color);
    border-right: 0;
}

.weui-navbar__item.weui-bar__item_on {
    color: #fff;
    background-color: var(--primary-color);
}

.weui-navbar:after {
    border-bottom: 0;
}

.weui-navbar__item:after {
    border-right: 0;
}

.mod-product-item {
    padding: 0;
}

.mod-product-item .item {
    position: relative;
    overflow: hidden;
    margin: 10px auto;
    width: 88%;
    border: 1px solid var(--primary-color);
    border-radius: 5px;
    font-size: 14px;
    padding: 20px 0;
}

.mod-product-item .item-top {
    display: flex;
    padding: 0 6%;
    font-size: 15px;
}

.mod-product-item .item-top span {
    color: #ff595b;
}

.mod-product-item .item-top .top-l {
    text-align: left;
    flex: 1;
}

.mod-product-item .item-top .top-r {
    text-align: right;
    flex: 1;
}

.mod-product-item .item .mod {
    background-color: var(--primary-color);
    width: 88%;
    height: 1px;
    margin: 10px auto;
}

.mod-product-item .item .item-bot {
    padding-bottom: 10px;
    font-size: 13px;
    color: #8f8f94;
    padding: 0 6%;
    line-height: 21px;
    text-align: center;
}

.mod-product-item .item .item-bot span {
    color: #ff595b;
}

.mod-product-item .item-btn {
    width: 50%;
    margin: 5px auto;
}

.weui-btn_primary {
    background-color: var(--primary-color);
}

.weui-tab__content .weui-btn {
    border-radius: 20px;
}

.weui-btn {
    font-size: 16px;
    font-weight: normal;
    width: 205px;
}

.weui-tab__panel .tj {
    position: absolute;
    right: -10px;
    top: 0px;
    z-index: 2;
    padding: 6px 10px 0;
    color: #fff;
    font-size: 11px;
    font-style: normal;
    transform: rotate(45deg)
}

.weui-tab__panel .tj:before {
    content: '';
    background: var(--primary-color);
    /*transform:skew(45deg,0);*/
    height: 120%;
    width: 150%;
    position: absolute;
    top: -5px;
    left: -25%;
    z-index: -1;
}



/**数据*/
.stock-data {
    background-color: #fff;
    margin-top: 10px;
}

.stock-data .hd {
    font-size: 17px;
    padding: 0 15px;
    line-height: 35px;
    border-bottom: 1px solid #EDEDED;
}

.stock-data .hd .icon-prefixer {
    display: inline-block;
    width: 15px;
    height: 20px;
    background: url(../img/fixer.png) no-repeat 0px 0px;
    -webkit-background-size: 100% 100%;
    background-size: 100% 100%;
    position: relative;
    top: 3px;
}

.stock-data .flex-item-wrapper {
    padding: 10px 0;
}

.stock-number {
    font-size: 18px;
}

.stock-data__item {
    text-align: center;
}

.stock-data__item:first-child {
    border-right: 1px solid #E2E2E2;
}

.stock-data__item:last-child {
    border-left: 1px solid #E2E2E2;
}

.stock-data__item .title {
    color: #525252;
}

.stock-data__item .meta>span {
    display: inline-block;
    padding: 0;
    font-size: 14px;
}