/* default style */

:root {
	--primary-color: #3B7AFE;
}

body {
	position: relative;
	max-width: 430px;
	min-width: 330px;
	margin: 0px auto;
}

html,
body {
	min-height: 100%;
}

body {
	color: #333;
	font-size: 14px;
	background: #f5f5f5;
	font-family: arial, microsoft yahei, verdana, simsun;
	width: 100%;
}

div,
p,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
th,
td {
	margin: 0;
	padding: 0;
}

i,
cite,
em,
var,
address,
dfn {
	font-style: normal;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

ol,
ul,
li {
	list-style: none;
}

.mtb10 {
	margin: 10px 0;
}

.m15 {
	margin: 15px !important;
}

.mt0 {
	margin-top: 0 !important;
}

.mt5 {
	margin-top: 5px;
}

.mt10 {
	margin-top: 10px;
}

.mt20 {
	margin-top: 20px !important;
}

.mt30 {
	margin-top: 30px;
}

.mt40 {
	margin-top: 40px;
}

.padding15 {
	padding: 15px
}

.padding20 {
	padding: 20px
}

.p10 {
	padding: 10px !important;
}

.m_a_none {
	margin: 0px !important;
}

/* vue cloak */
[v-cloak] {
	display: none;
}

/* override weui css style */
.weui-btn+.weui-btn {
	margin: 0;
}

.weui-tab {
	height: auto;
}

.weui-btn_primary {
	background-color: var(--primary-color);
}

.weui-btn_primary:not(.weui-btn_disabled):active {
	background-color: var(--primary-color);
}

.weui-btn_warn {
	background-color: #FB7C34;
}

.weui-tabbar__fixed {
	position: fixed;
}

.weui-navbar__relative {
	position: relative;
}

.weui-toptips_normal {
	background-color: rgba(0, 0, 0, .6);
}

.weui-cell {
	font-size: 14px;
}

.weui-vcode-btn {
	font-size: 12px;
}

.weui-btn_plain-primary {
	color: #fe7b20;
	border-color: #fe7b20;
}

.weui-input {
	height: 30px;
}


.page-with-footbar {
	padding-bottom: 65px;
}

.page-box {
	padding: 0px 20px;
}

.default-width {
	width: 100%;
	text-align: center
}

.weui-tabbar__icon {
	width: 22px;
	height: 22px;
}

a {
	color: #333;
}

a:link {
	text-decoration: none;
}

/*utils*/
.clearfix::before,
.clearfix::after {
	content: "";
	display: table;
}

.clearfix::after {
	clear: both;
}

.wrapper {
	padding: 0 15px;
}

.block-btn-wrapper {
	padding: 15px 20px;
}


/*常用数字字体大小*/
.number28 {
	font-size: 28px;
}

.number22 {
	font-size: 22px;
}

.number20 {
	font-size: 20px;
}

.number18 {
	font-size: 18px;
}

.number-red {
	color: #ff1920;
}

.number-green {
	color: #20821C;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: 100%;
	font-weight: normal;
}

/*字体颜色*/
.primary {
	color: var(--primary-color);
}

.red {
	color: red;
}

.c-red {
	color: #F00;
	margin-right: 5px;
}

.white {
	color: white;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.text-center {
	text-align: center;
}

.text-center {
	text-align: center;
}

fieldset,
img {
	border: none;
	vertical-align: middle;
}

/*dialog*/
.weui-dialog {
	width: 80%;
	max-width: 300px;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}


/*header*/
.header {
	position: absolute;
	background: var(--primary-color);
	top: 0;
	left: 0;
	height: 50px;
	width: 100%;
	z-index: 999;
	text-align: center
}

.header-height {
	height: 50px;
	width: 100%;
}

.header img {
	height: 38px;
	margin-top: 7px;
}

.header a {}

.header .login {
	position: absolute;
	left: 10px;
	top: 15px;
}

.header .register {
	position: absolute;
	right: 10px;
	top: 15px;
}

/*footer*/
.weui-tabbar {
	width: 100%;
}

.footer {
	bottom: 0;
	background: white;
}

.footer a {
	border-right: 1px solid #eee;
}

.footer a:last-child {
	border-right: none;
}

.footer .weui-tabbar__label {
	color: #b9b9b9;
	font-size: 12px;
}

.footer .weui-tabbar__item {
	padding: 8px 0 2px;
}

.footer p.active {
	color: #ff595b;
}

#app {
	padding-top: 40px;
}

/*nav*/
.v-header .v-header-left .left-arrow {
	position: absolute;
	width: 30px;
	height: 30px;
	line-height: 30px;
	top: -9px;
	left: -5px;
	padding-left: 12px;
}

.v-header .v-header-left .left-arrow:before {
	content: "";
	position: absolute;
	width: 10px;
	height: 10px;
	border: 1px solid #fff;
	border-width: 1px 0 0 1px;
	-webkit-transform: rotate(315deg);
	transform: rotate(315deg);
	top: 9px;
	left: 0;
}

.v-header .v-header-left {
	left: 18px;
}

.v-header .v-header-left,
.v-header .v-header-right {
	position: absolute;
	top: 14px;
	display: block;
	font-size: 14px;
	line-height: 21px;
	color: #fff;
}

.v-header {
	/*position: relative; box-sizing: border-box;background-color: #f53d52;*/
	box-sizing: border-box;
	background-color: var(--primary-color);
	width: 100%;
	z-index: 100;
	position: fixed;
	left: 50%;
	top: 0;
	max-width: 430px;
	min-width: 330px;
	margin: 0px auto;
	transform: translateX(-50%);
}

.v-header .v-header-title,
.v-header h1 {
	margin: 0 88px;
	line-height: 40px;
	text-align: center;
	height: 40px;
	font-size: 16px;
	font-weight: 300;
	width: auto;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: #fff;
}

.v-header .v-header-right .right-arrow {
	position: absolute;
	width: 120px;
	height: 30px;
	line-height: 30px;
	top: -9px;
	right: -5px;
	text-align: right;
	font-size: 14px;
}

.v-header .v-header-right .right-arrow a {
	color: #fff;
}

.v-header .v-header-right {
	right: 18px;
}




/*底部导航*/
.wap_footer {
	background: #fff;
	width: 100%;
	line-height: 50px;
	text-align: center;
	height: 50px;
	border-top: 1px solid #eee;
	z-index: 10;
	position: fixed;
	bottom: 0;
	left: 50%;
	max-width: 430px;
	min-width: 330px;
	margin: 0px auto;
	transform: translateX(-50%);
}

.wap_footer li {
	position: relative;
	width: 20%;
	float: left;
	display: block;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	/* Firefox */
	-webkit-box-sizing: border-box;
	/* Safari */
}

.wap_footer li a {
	display: block;
	color: #b9b9b9;
	font-size: 12px;
	padding-top: 12px;
	background-position: center 5px;
	background-size: 24px;
	background-repeat: no-repeat
}

.wap_footer .active a {
	color: var(--primary-color)
}

.wap_footer .home {
	background-image: url(../img/f_home.png);
}

.wap_footer .active .home {
	background-image: url(../img/f_home_a.png);
}

.wap_footer .user {
	background-image: url(../img/f_user.png);
}

.wap_footer .active .user {
	background-image: url(../img/f_user_a.png);
}

.wap_footer .stock {
	background-image: url(../img/f_stock.png);
}

.wap_footer .active .stock {
	background-image: url(../img/f_stock_a.png);
}

.wap_footer .kefu {
	background-image: url(../img/f_kefu.png);
}

.wap_footer .active .kefu {
	background-image: url(../img/f_kefu_a.png);
}

.wap_footer .trading {
	background-image: url(../img/stockup.png);
}

.wap_footer .active .trading {
	background-image: url(../img/stockup_a.png);
}

.wap_footer .red_d {
	min-width: 14px;
	text-align: center;
	line-height: 14px;
	display: inline-block;
	position: absolute;
	right: 21px;
	top: 0;
	background: red;
	color: #fff;
	border-radius: 17px;
	padding: 5px 7px;
	font-size: 14px;
	transform: scale(.7);
	font-family: Tahoma !important;
}


.emptydata {
	background-image: url(../img/empty-data.png);
	background-repeat: no-repeat;
	background-position: center 20px;
	background-size: 48px;
	padding: 80px;
}

.emptydata p {
	display: block;
	text-align: center;
	font-size: 18px;
	color: #8D8D8D;
}





/*********分页********/
.pager {
	padding: 20px 0;
	text-align: center;
	background: #fff;
}

.pager li {
	display: inline-block;
	cursor: pointer;
	margin: 0 1%;
	border: 1px solid #dfdfdf;
	background: #f3f3f3;
	color: #ccc;
	line-height: 25px;
	height: 25px;
	width: 25%;
	text-align: center;
}

.pager li.nowpage {
	color: #06a8fb;
}

.pager li:hover {
	background: #fff0e1;
	border: 1px solid #d90101;
	color: #d90000;
}

.pager li.disabled {
	border: 1px solid #dfdfdf;
	background: #f3f3f3;
	color: #ccc
}

.pager li.select {
	padding: 0
}

.pager li select {
	width: auto;
	padding: 3px 10px;
	border: 0;
	background: #f3f3f3;
	color: #428bca;
	font-size: 14px;
	margin-top: -4px;
	text-align: center
}

.pager li select option {
	text-align: center
}

.pager li.info {
	border-style: none;
	background: none;
	margin-left: 5px;
	padding: 4px 0px;
	color: #666;
}

.pager li.danye select {
	width: 70%;
}



/*交易软件下载*/
.down_hd {
	text-align: center;
	padding-top: 30px;
}

.down_hd .app_down_rwm img {
	width: 135px;
}

.down_hd .inputBox,
.app_hd .inputBox {
	width: 86%;
	height: 40px;
	line-height: 40px;
	margin: 30px auto;
	border-radius: 3px;
	border: 1px solid var(--primary-color);
}

.down_hd .inputBox input,
.app_hd .inputBox input {
	border: none;
	background: #E5EDFF;
	font-size: 14px;
	color: var(--primary-color);
	width: 60%;
	float: left;
	height: 100%;
	padding-left: 8px;
	border-right: 1px solid var(--primary-color);
	border-radius: 3px 0 0 3px;
}

.down_hd .inputBox button,
.app_hd .inputBox button {
	border: none;
	background: none;
	width: 35%;
	height: 40px;
	font-size: 14px;
	float: right;
	font-weight: 600;
	cursor: pointer;
	color: var(--primary-color);
	text-align: center;
}

.down_hd p {
	font-size: 13px;
	color: #960098;
	line-height: 22px;
}

.app_hd {
	padding-top: 30px;
}

.app_hd h2 {
	font-size: 30px;
	color: var(--primary-color);
	text-align: center;
	padding-bottom: 10px;
	font-weight: bolder;
}

.app_hd p {
	font-size: 13px;
	color: #333333;
	line-height: 28px;
	text-align: left;
	padding: 0 20px;
}

.app_hd .adown_btn {
	width: 86%;
	height: 40px;
	line-height: 40px;
	background: var(--primary-color);
	border-radius: 3px;
	color: #fff;
	font-size: 16px;
	text-align: center;
	border: 0px;
	display: block;
	margin: 10px auto;
	font-weight: bold;
}

.app_hd .app_down_rwm p {
	text-align: center;
}

.app_hd .app_down_rwm img {
	width: 135px;
}


/*kefu*/
.am-share {
	font-size: 14px;
	border-radius: 0;
	bottom: 0;
	left: 0;
	position: fixed;
	text-align: center;
	-webkit-transform: translateY(100%);
	-ms-transform: translateY(100%);
	transform: translateY(100%);
	-webkit-transition: -webkit-transform 300ms;
	transition: transform 300ms;
	width: 100%;
	z-index: 1110;
}

.am-modal-active {
	transform: translateY(0px);
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	transform: translateY(0)
}

.am-modal-out {
	z-index: 1109;
	-webkit-transform: translateY(100%);
	-ms-transform: translateY(100%);
	transform: translateY(100%)
}

.am-share-title {
	background-color: #f8f8f8;
	border-bottom: 1px solid #fff;
	border-top-left-radius: 2px;
	border-top-right-radius: 2px;
	color: #555;
	font-weight: 400;
	margin: 0 10px;
	padding: 10px 0 0;
	text-align: center;
}

.am-share-title::after {
	border-bottom: 1px solid #dfdfdf;
	content: "";
	display: block;
	height: 0;
	margin-top: 10px;
	width: 100%;
}

.am-share-footer {
	margin: 0 25% 0 50%;
}

.am-share-footer .share_btn {
	margin-bottom: 50px;
	color: #555;
	display: block;
	width: 100%;
	background-color: #e6e6e6;
	border: 0px solid #e6e6e6;
	border-radius: 0;
	cursor: pointer;
	font-size: 16px;
	font-weight: 400;
	line-height: 50px;
	padding: 0;
	text-align: center;
	transition: background-color 300ms ease-out 0s, border-color 300ms ease-out 0s;
	vertical-align: middle;
	white-space: nowrap;
	font-family: "微软雅黑";
}

.am-share-sns {
	background-color: #f8f8f8;
	margin: 0 25% 0 50%;
	height: auto;
	zoom: 1;
	overflow: auto;
}

.am-share-sns li {
	display: block;
	float: none;
	height: auto;
	width: 100%;
}

.am-share-sns a {
	color: #555;
	display: block;
	text-decoration: none;
}

.wap_footer .am-share-sns li a {
	padding-top: 0;
}

.am-share-sns span {
	display: block;
}

.am-share-sns li i {
	background-position: center 50%;
	background-repeat: no-repeat;
	background-size: 36px 36px;
	background-color: #ccc;
	color: #fff;
	display: inline-block;
	font-size: 18px;
	height: 36px;
	line-height: 36px;
	margin-bottom: 5px;
	width: 36px;
}

.am-share-sns .share-icon-weibo {
	background-image: url();
}

.sharebg {
	background-color: rgba(0, 0, 0, 0.6);
	bottom: 0;
	height: 100%;
	left: 0;
	opacity: 0;
	position: fixed;
	right: 0;
	top: 0;
	width: 100%;
	z-index: 1108;
	display: none;
}

.sharebg-active {
	opacity: 1;
	display: block;
}

.input:focus-visible,
select:focus-visible {
	outline: 1px solid var(--primary-color);
	border-radius: 3px;
}

input,
textarea {
	caret-color: #ADADAD;
}

select {
	color: #333333;
}