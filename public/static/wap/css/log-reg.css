:root {
    --primary-color: #3B7AFE;
}

.l-header {
    text-align: center;
    position: relative;
    padding-top: 8%;
    padding-bottom: 10%;
    box-sizing: border-box;
}

.l-header img {
    max-width: 70%;
}

.wecell_login {
    padding: 0px 30px;
}

.wecell_login .weui-cell__bd {
    border: 1px solid #e2e2e2;
    padding: 4px 0px;
    border-radius: 4px;
    background: #FFF;
    width: 100%;
    text-indent: 0;
    font-size: 14px;
    line-height: normal;
}

.wecell_login .weui-cell:before {
    height: 0;
    border-top: none;
}

.wecell_login .weui-cell {
    padding: 0px 0 10px;
}

.weui-cell__bd .s1,
.weui-cell__bd .s2,
.weui-cell__bd .s3,
.weui-cell__bd .s4,
.weui-cell__bd .s5 {
    background: #fff url(../img/bg02.png) 10px center no-repeat;
    background-size: 18px;
    text-indent: 40px;
}

.weui-cell__bd .s2 {
    background: #fff url(../img/bg03.png) 10px center no-repeat;
    background-size: 18px;
}

.weui-cell__bd .s3 {
    background: #fff url(../img/bg13.png) 10px center no-repeat;
    background-size: 18px;
}

.weui-cell__bd .s4 {
    background: #fff url(../img/bg12.png) 10px center no-repeat;
    background-size: 18px;
}

.weui-cell__bd .s5 {
    background: #fff url(../img/bg20.png) 10px center no-repeat;
    background-size: 18px;
}

.log-botton {
    margin-top: 25px;
}

.wecell_login .weui-cells__title a {
    font-size: 12px;
    color: #7b7b7b;
}

.wecell_login .weui-btn_primary {
    background-color: var(--primary-color);
    width: 100%;
    font-weight: normal;
    font-size: 16px;
    height: 40px;
}

.wecell_reg {}

button.weui-vcode-btn {
    cursor: pointer;
    display: inline-block;
    background: var(--primary-color);
    /*width: 100%;*/
    width: 108px;
    text-align: center;
    border-radius: 3px;
    height: 40px;
    line-height: 40px;
    color: #fff;
    margin-left: 0;
}

.wecell_login .weui-cell_vcode .weui-cell__bd {
    margin-right: 5px;
}