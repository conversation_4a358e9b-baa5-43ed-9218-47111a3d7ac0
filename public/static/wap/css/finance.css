:root {
    --primary-color: #3B7AFE;
}

body {
    background-color: #fff;
}

.peizi_nav li {
    list-style: none;
    float: left;
    height: 35px;
    line-height: 35px;
    text-align: center;
    color: var(--primary-color);
    border: 1px solid currentColor;
    width: 21%;
    font-size: 14px;
    margin: 10px 2%;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    /* Firefox */
    -webkit-box-sizing: border-box;
    /* Safari */
}

.peizi_nav li a {
    color: var(--primary-color)
}

.peizi_nav li.active {
    background: var(--primary-color)
}

.peizi_nav li.active a {
    color: #fff
}

.wap_ddw_l {
    line-height: 100%;
    font-size: 16px;
    line-height: 50px;
    color: #555;
}

#div_risk li {
    display: block;
    float: left;
    width: 32%;
    margin: 5px 0.6%;
    border: 1px solid #ddd;
    padding: 10px 0;
    text-align: center;
    color: #999;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 5px
}

#div_risk li.active {
    background: var(--primary-color);
    color: #fff;
    border: 1px solid var(--primary-color) !important;
}

#div_risk li strong {
    line-height: 40px;
    font-size: 36px
}

#div_risk li p {
    line-height: 18px;
    font-size: 12px
}

input.money {
    height: 30px;
    line-height: 30px;
    width: 98%;
    text-align: center;
    font-size: 16px;
    border-radius: 3px;
    border: 1px solid #ccc;
}

#div_trade_time ul {
    display: block;
    border: 1px solid var(--primary-color);
    border-radius: 5px;
    margin: 0 20px;
}

#div_trade_time li {
    display: block;
    float: left;
    width: 50%;
    text-align: center;
    line-height: 34px;
    color: var(--primary-color);
    font-size: 14px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#div_trade_time li.active {
    background: var(--primary-color);
    color: #fff
}

#div_trade_time li:nth-child(2) {
    border-left: 1px solid currentColor;
}

.mselect {
    width: 95px;
    height: 26px;
    font-size: 14px;
}

.mselect option {
    width: 80px;
}

.sure {
    font-size: 14px;
    border: 1px solid #E8E8E8;
}

.sure .title {
    width: 32%;
    border-bottom: 1px solid #e4e4e4;
    border-right: 1px solid #e4e4e4;
    color: #676767;
    font-size: 14px;
    padding: 5px 5px 5px 0;
    text-align: right
}

.sure td {
    line-height: 25px;
    padding-left: 15px;
    border-bottom: 1px solid #e4e4e4;
    line-height: 36px;
}

.sure em {
    color: var(--primary-color);
    font-size: 20px;
    float: left;
}

.btn,
.btn:hover {
    display: inline-block;
    text-decoration: none;
    font-family: VTahoma, Arial, "Microsoft YaHei";
    /*background: #ec2400;*/
}

.btn-l {
    width: 270px;
    height: 42px;
    line-height: 42px;
    font-size: 16px;
}

.btn {
    background: var(--primary-color);
    text-align: center;
    vertical-align: middle;
    color: #FFFFFF;
    border: 0 none;
    cursor: pointer;
    outline: none;
    border-radius: 3px;
    transition: 0.3s background ease;
}

#textBox {
    width: 90%;
    margin: 0 auto;
}

#textBox dt,
#textBox dd {
    width: 100%;
    height: 40px;
    display: inline-block;
    line-height: 40px;
    margin-top: 20px;
    font-size: 18px;
    border-radius: 5px;
    background: #eeeeee;
    position: relative;
}

#textBox dd {
    background-position: 0 -80px;
    /*text-indent: 150px;*/
    text-align: center;
    color: #666;
}

#textBox dt {
    border-radius: 5px;
    background: var(--primary-color);
    text-align: center;
    color: #fff;
}

#textBox .font1 {
    color: #ee2c00
}

#textBox dd span {
    font-size: 14px;
    color: #888888;
}

#textBox dd b {
    font-size: 18px;
    padding: 0 5px;
}

.tryText {
    display: block;
    text-align: center;
    line-height: 30px;
    background: #fff;
    padding-bottom: 50px;
    margin-top: 20px;
}

.tryText .btn {
    width: 80%;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    margin-top: 20px;
    text-align: center;
    display: inline-block;
    border-radius: 4px;
    color: #fff !important;
    cursor: pointer;
    text-decoration: none !important;
}

.tryText .btnBg1 {
    background: #f53d52;
}

.tryText p {
    color: #666;
    font-size: 14px;
}

#textBox dt em {
    border-top: 15px solid var(--primary-color);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
}

#textBox dt em,
#textBox dd em {
    position: absolute;
    bottom: -15px;
    left: 49%;
    width: 0;
    height: 0;
}

#textBox dd em {
    border-top: 15px solid #eeeeee;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
}