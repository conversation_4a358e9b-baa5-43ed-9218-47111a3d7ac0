// util js file include validate , time format etc...
var Util = {
    Validate : {
        phone : function (num) {
            var reg = /^(13|14|15|16|17|18|19)[0-9]{9}$/;
            return reg.test(parseInt(num));
        },
        email : function(email){
            var reg = /^[\w-\.]+@([\w-]+\.)+[a-z]{2,3}/g;
            return reg.test(parseInt(email));
        }
    }
};

// 如果用戶是返回上頁，則刷新頁面，避免某些狀態未更新
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        location.reload();
    }
});

$(function(){
    //回退按钮
    $(".v-header").on("click",".v-header-left",function (e) {
        window.history.back(-1);
    })
})