/*!
 * WeUI v2.0.0 (https://github.com/weui/weui)
 * Copyright 2019 Tencent, Inc.
 * Licensed under the MIT license
 */
html{
  -ms-text-size-adjust:100%;
  -webkit-text-size-adjust:100%;
}
body{
  line-height:1.6;
  font-family:-apple-system-font, "Helvetica Neue", sans-serif;
}
*{
  margin:0;
  padding:0;
}
a img{
  border:0;
}
a{
  text-decoration:none;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
input,
textarea{
  caret-color:#07C160;
}
@font-face{
  font-weight:normal;
  font-style:normal;
  font-family:"weui";
  src:url('data:application/octet-stream;base64,AAEAAAALAIAAAwAwR1NVQrD+s+0AAAE4AAAAQk9TLzJAKEx8AAABfAAAAFZjbWFw65cFHQAAAhwAAAJQZ2x5Zp+UEEcAAASUAAAIvGhlYWQUqc7xAAAA4AAAADZoaGVhB/YD+wAAALwAAAAkaG10eEJoAAAAAAHUAAAASGxvY2EUxhJeAAAEbAAAACZtYXhwASEAQwAAARgAAAAgbmFtZeNcHtgAAA1QAAAB5nBvc3T6OoZLAAAPOAAAAOYAAQAAA+gAAABaA+gAAAAAA7MAAQAAAAAAAAAAAAAAAAAAABIAAQAAAAEAAMCU2KdfDzz1AAsD6AAAAADY7EUUAAAAANjsRRQAAAAAA7MD5AAAAAgAAgAAAAAAAAABAAAAEgA3AAUAAAAAAAIAAAAKAAoAAAD/AAAAAAAAAAEAAAAKAB4ALAABREZMVAAIAAQAAAAAAAAAAQAAAAFsaWdhAAgAAAABAAAAAQAEAAQAAAABAAgAAQAGAAAAAQAAAAAAAQOwAZAABQAIAnoCvAAAAIwCegK8AAAB4AAxAQIAAAIABQMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUGZFZABA6gHqEQPoAAAAWgPoAAAAAAABAAAAAAAAAAAAAAPoAAAD6AAAA+gAAAPoAAAD6AAAA+gAAAPoAAAD6AAAA+gAAAPoAAAD6AAAA+gAAAPoAAAD6AAAA+gAAAPoAAAD6AAAAAAABQAAAAMAAAAsAAAABAAAAXQAAQAAAAAAbgADAAEAAAAsAAMACgAAAXQABABCAAAABAAEAAEAAOoR//8AAOoB//8AAAABAAQAAAABAAIAAwAEAAUABgAHAAgACQAKAAsADAANAA4ADwAQABEAAAEGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAANwAAAAAAAAAEQAA6gEAAOoBAAAAAQAA6gIAAOoCAAAAAgAA6gMAAOoDAAAAAwAA6gQAAOoEAAAABAAA6gUAAOoFAAAABQAA6gYAAOoGAAAABgAA6gcAAOoHAAAABwAA6ggAAOoIAAAACAAA6gkAAOoJAAAACQAA6goAAOoKAAAACgAA6gsAAOoLAAAACwAA6gwAAOoMAAAADAAA6g0AAOoNAAAADQAA6g4AAOoOAAAADgAA6g8AAOoPAAAADwAA6hAAAOoQAAAAEAAA6hEAAOoRAAAAEQAAAAAARACKAMQBEgFgAZIB4gH6AioCeAK0AwwDZAOiA9wEEAReAAAAAgAAAAADlQOVABQAKQAAJSInJicmNDc2NzYyFxYXFhQHBgcGJzI3Njc2NCcmJyYiBwYHBhQXFhcWAfRxYV83OTk3X2HiYV83OTk3X2FxZFVTMTIyMVNVyFVTMTIyMVNVUzk3X2HiYV83OTk3X2HiYV83OTIyMVNVyFVTMTIyMVNVyFVTMTIAAAIAAAAAA7MDswAXAC0AAAEiBwYHBhUUFxYXFjMyNzY3NjU0JyYnJhMHBiIvASY2OwERNDY7ATIWFREzMhYB7nZnZDs9PTtkZ3Z8amY7Pj47Zmkhdg4oDnYODRddCwcmBwtdFw0Dsz47Zmp8dmdkOz09O2Rndn1pZjs+/fCaEhKaEhoBFwgLCwj+6RoAAwAAAAADlQOVABQAGAAhAAAlIicmJyY0NzY3NjIXFhcWFAcGBwYDETMRJzI2NCYiBhQWAfRxYV83OTk3X2HiYV83OTk3X2GQPh8RGRkiGRlTOTdfYeJhXzc5OTdfYeJhXzc5AfT+3QEjKhgjGBgjGAAAAAACAAAAAAOxA+QAFwAsAAABBgcGDwERFBcWFxYXNjc2NzY1EScmJyYTAQYvASY/ATYyHwEWNjclNjIfARYB9WlsP3A3Rz5sXmxsXW09SDdwQGuP/tUEBIoDAxIBBQFxAQUCARICBQERBAPjFyASJBL+rI51ZUg/HBw/SGV1jgFUEiQSIP66/tkDA48EBBkCAVYCAQHlAQIQBAAAAAADAAAAAAOxA+QAFwAmAC8AAAEGBwYPAREUFxYXFhc2NzY3NjURJyYnJgczMhYVAxQGKwEiJwM0NhMiJjQ2MhYUBgH1aWtAcDdHPmxebGxdbT1IN3BAa4M0BAYMAwImBQELBh4PFhYeFRUD5BggEiQS/q2PdWRJPh0dPklkdY8BUxIkEiD4BgT+xgIDBQE6BAb+QBUfFRUfFQAAAAACAAAAAAOVA5UAFAAaAAAlIicmJyY0NzY3NjIXFhcWFAcGBwYDJwcXAScB9HFhXzc5OTdfYeJhXzc5OTdfYaJzLJ8BFi1TOTdfYeJhXzc5OTdfYeJhXzc5AUhzLJ8BFSwAAAAAAwAAAAADlQOVABQAKQAvAAAlIicmJyY0NzY3NjIXFhcWFAcGBwYnMjc2NzY0JyYnJiIHBgcGFBcWFxYTNxcBJzcB9HFhXzc5OTdfYeJhXzc5OTdfYXFkVVMxMjIxU1XIVVMxMjIxU1Uz8iT+6p8jUzk3X2HiYV83OTk3X2HiYV83OTIyMVNVyFVTMTIyMVNVyFVTMTIBBPIj/uufJAAAAAEAAAAAA5kDGAAHAAAlATcXARcBBgGF/vg7zgHYOv3vAcsBCTvPAdg7/e4BAAAAAAIAAAAAA5UDlQAFABoAAAE1IxUXNwMiJyYnJjQ3Njc2MhcWFxYUBwYHBgITPrEsvnFhXzc5OTdfYeJhXzc5OTdfYQIO4PqxLP7kOTdfYeJhXzc5OTdfYeJhXzc5AAAAAAMAAAAAA5UDlQAFABoALwAAARcHJzUzAyInJicmNDc2NzYyFxYXFhQHBgcGJzI3Njc2NCcmJyYiBwYHBhQXFhcWAg2iI7EyGXFhXzc5OTdfYeJhXzc5OTdfYXFkVVMxMjIxU1XIVVMxMjIxU1UCCaIksfr9ZTk3X2HiYV83OTk3X2HiYV83OTIyMVNVyFVTMTIyMVNVyFVTMTIAAAMAAAAAA5UDlQAUABgAIQAAJSInJicmNDc2NzYyFxYXFhQHBgcGAxMzEwMyNjQmIg4BFgH0cWFfNzk5N19h4mFfNzk5N19hkQU2BSAQFRUgFQEWUzk3X2HiYV83OTk3X2HiYV83OQKV/sQBPP43Fh8VFR8WAAAAAAQAAAAAA5UDlQAUACkALQA2AAAlIicmJyY0NzY3NjIXFhcWFAcGBwYnMjc2NzY0JyYnJiIHBgcGFBcWFxYTMxEjEyImNDYyFhQGAfRxYV83OTk3X2HiYV83OTk3X2FxZFVTMTIyMVNVyFVTMTIyMVNVSzIyGREZGSIZGVM5N19h4mFfNzk5N19h4mFfNzkyMjFTVchVUzEyMjFTVchVUzEyAcL+3QFNGCMYGCMYAAAAAwAAAAADlQOVABQAKQA1AAAlIicmJyY0NzY3NjIXFhcWFAcGBwYnMjc2NzY0JyYnJiIHBgcGFBcWFxYTFwcnByc3JzcXNxcB9HFhXzc5OTdfYeJhXzc5OTdfYXFkVVMxMjIxU1XIVVMxMjIxU1WHgiOCgiOCgiOCgiNTOTdfYeJhXzc5OTdfYeJhXzc5MjIxU1XIVVMxMjIxU1XIVVMxMgFvgiOCgiOCgiOCgiMAAAACAAAAAANUA0IAGAAlAAABFwcnDgEjIicmJyY0NzY3NjIXFhcWFRQGJzQuASIOARQeATI+AQKoqyOsJ180T0RCJycnJ0JEn0RCJiglDUFvg29BQW+Db0EBYKwjrCAjKCZCRJ9EQicnJydCRE82YZdBb0FBb4NvQUFvAAAAAgAAAAADlQOVAAsAIAAAATcnBycHFwcXNxc3AyInJicmNDc2NzYyFxYXFhQHBgcGAiB9LH19LH19LH19LKlxYV83OTk3X2HiYV83OTk3X2EB9H0sfX0sfX0sfX0s/tw5N19h4mFfNzk5N19h4mFfNzkAAAACAAAAAAOVA5UAFAAcAAAlIicmJyY0NzY3NjIXFhcWFAcGBwYDJzcnBwYfAQH0cWFfNzk5N19h4mFfNzk5N19hHoqKK7UBAbVTOTdfYeJhXzc5OTdfYeJhXzc5ARKPjy27AQG6AAAAAAUAAAAAA1cDbAAJAB0AJwArAC8AAAETHgEzITI2NxMzAw4BIyEiJicDIzU0NjMhMhYdASUyFh0BIzU0NjMHMxMjEzMDIwEaIgETDQEuDRMBIjIiAjAh/tIhMAIiVgwJApoJDP7xCQzQDAkVMhUyiTIVMgLd/cgOEhIOAjj9xSEuLiECOx4IDAwIHo4MCR0dCQz6/okBd/6JAAAAAAAAEADGAAEAAAAAAAEABAAAAAEAAAAAAAIABwAEAAEAAAAAAAMABAALAAEAAAAAAAQABAAPAAEAAAAAAAUACwATAAEAAAAAAAYABAAeAAEAAAAAAAoAKwAiAAEAAAAAAAsAEwBNAAMAAQQJAAEACABgAAMAAQQJAAIADgBoAAMAAQQJAAMACAB2AAMAAQQJAAQACAB+AAMAAQQJAAUAFgCGAAMAAQQJAAYACACcAAMAAQQJAAoAVgCkAAMAAQQJAAsAJgD6d2V1aVJlZ3VsYXJ3ZXVpd2V1aVZlcnNpb24gMS4wd2V1aUdlbmVyYXRlZCBieSBzdmcydHRmIGZyb20gRm9udGVsbG8gcHJvamVjdC5odHRwOi8vZm9udGVsbG8uY29tAHcAZQB1AGkAUgBlAGcAdQBsAGEAcgB3AGUAdQBpAHcAZQB1AGkAVgBlAHIAcwBpAG8AbgAgADEALgAwAHcAZQB1AGkARwBlAG4AZQByAGEAdABlAGQAIABiAHkAIABzAHYAZwAyAHQAdABmACAAZgByAG8AbQAgAEYAbwBuAHQAZQBsAGwAbwAgAHAAcgBvAGoAZQBjAHQALgBoAHQAdABwADoALwAvAGYAbwBuAHQAZQBsAGwAbwAuAGMAbwBtAAAAAgAAAAAAAAAKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASAQIBAwEEAQUBBgEHAQgBCQEKAQsBDAENAQ4BDwEQAREBEgETAAZjaXJjbGUIZG93bmxvYWQEaW5mbwxzYWZlLXN1Y2Nlc3MJc2FmZS13YXJuB3N1Y2Nlc3MOc3VjY2Vzcy1jaXJjbGURc3VjY2Vzcy1uby1jaXJjbGUHd2FpdGluZw53YWl0aW5nLWNpcmNsZQR3YXJuC2luZm8tY2lyY2xlBmNhbmNlbAZzZWFyY2gFY2xlYXIEYmFjawZkZWxldGUAAAAA') format('truetype');
}
[class^="weui-icon-"],
[class*=" weui-icon-"]{
  display:inline-block;
  vertical-align:middle;
  font:normal normal normal 14px/1 "weui";
  font-size:inherit;
  text-rendering:auto;
  -webkit-font-smoothing:antialiased;
}
[class^="weui-icon-"]:before,
[class*=" weui-icon-"]:before{
  display:inline-block;
  margin-left:.2em;
  margin-right:.2em;
}
.weui-icon-circle:before{
  content:"\EA01";
}
.weui-icon-download:before{
  content:"\EA02";
}
.weui-icon-info:before{
  content:"\EA03";
}
.weui-icon-safe-success:before{
  content:"\EA04";
}
.weui-icon-safe-warn:before{
  content:"\EA05";
}
.weui-icon-success:before{
  content:"\EA06";
}
.weui-icon-success-circle:before{
  content:"\EA07";
}
.weui-icon-success-no-circle:before{
  content:"\EA08";
}
.weui-icon-waiting:before{
  content:"\EA09";
}
.weui-icon-waiting-circle:before{
  content:"\EA0A";
}
.weui-icon-warn:before{
  content:"\EA0B";
}
.weui-icon-info-circle:before{
  content:"\EA0C";
}
.weui-icon-cancel:before{
  content:"\EA0D";
}
.weui-icon-search:before{
  content:"\EA0E";
}
.weui-icon-clear:before{
  content:"\EA0F";
}
.weui-icon-back:before{
  content:"\EA10";
}
.weui-icon-delete:before{
  content:"\EA11";
}
[class^="weui-icon_"]:before,
[class*=" weui-icon_"]:before{
  margin:0;
}
.weui-icon-success{
  font-size:23px;
  color:#07C160;
}
.weui-icon-waiting{
  font-size:23px;
  color:#10AEFF;
}
.weui-icon-warn{
  font-size:23px;
  color:#FA5151;
}
.weui-icon-info{
  font-size:23px;
  color:#10AEFF;
}
.weui-icon-success-circle{
  font-size:23px;
  color:#07C160;
}
.weui-icon-success-no-circle{
  font-size:23px;
  color:#07C160;
}
.weui-icon-waiting-circle{
  font-size:23px;
  color:#10AEFF;
}
.weui-icon-circle{
  font-size:23px;
  color:#C9C9C9;
}
.weui-icon-download{
  font-size:23px;
  color:#07C160;
}
.weui-icon-info-circle{
  font-size:23px;
  color:#07C160;
}
.weui-icon-safe-success{
  color:#07C160;
}
.weui-icon-safe-warn{
  color:#FFBE00;
}
.weui-icon-cancel{
  color:#FA5151;
  font-size:22px;
}
.weui-icon-search{
  color:rgba(0, 0, 0, 0.5);
  font-size:18px;
}
.weui-icon-search:before{
  margin-right:0;
}
.weui-icon-clear{
  color:#B2B2B2;
  font-size:14px;
}
.weui-icon-delete.weui-icon_gallery-delete{
  color:#FFFFFF;
  font-size:22px;
}
.weui-icon_msg{
  font-size:64px;
}
.weui-icon_msg.weui-icon-warn{
  color:#FA5151;
}
.weui-icon_msg-primary{
  font-size:64px;
}
.weui-icon_msg-primary.weui-icon-warn{
  color:rgba(0, 0, 0, 0.3);
}
.weui-btn{
  position:relative;
  display:block;
  width:184px;
  margin-left:auto;
  margin-right:auto;
  padding:8px 24px;
  box-sizing:border-box;
  font-weight:700;
  font-size:17px;
  text-align:center;
  text-decoration:none;
  color:#FFFFFF;
  line-height:1.41176471;
  border-radius:4px;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  overflow:hidden;
}
.weui-btn_block{
  width:auto;
}
.weui-btn_inline{
  display:inline-block;
}
.weui-btn_default{
  color:#06AE56;
  background-color:#F2F2F2;
}
.weui-btn_default:not(.weui-btn_disabled):visited{
  color:#06AE56;
}
.weui-btn_default:not(.weui-btn_disabled):active{
  color:#06AE56;
  background-color:#D9D9D9;
}
.weui-btn_primary{
  background-color:#07C160;
}
.weui-btn_primary:not(.weui-btn_disabled):visited{
  color:#FFFFFF;
}
.weui-btn_primary:not(.weui-btn_disabled):active{
  color:#FFFFFF;
  background-color:#06AD56;
}
.weui-btn_warn{
  color:#FA5151;
  background-color:#F2F2F2;
}
.weui-btn_warn:not(.weui-btn_disabled):visited{
  color:#FA5151;
}
.weui-btn_warn:not(.weui-btn_disabled):active{
  color:#FA5151;
  background-color:#D9D9D9;
}
.weui-btn_disabled{
  color:rgba(0, 0, 0, 0.18);
  background-color:#fafafa;
}
.weui-btn_loading .weui-loading{
  margin:-0.2em 0.34em 0 0;
}
.weui-btn_loading.weui-btn_primary{
  color:#FFFFFF;
}
.weui-btn_loading.weui-btn_default{
  background-color:#D9D9D9;
}
.weui-btn_loading.weui-btn_primary{
  background-color:#06AD56;
}
.weui-btn_loading.weui-btn_warn{
  background-color:#D9D9D9;
}
.weui-btn_plain-primary{
  color:#07C160;
  border:1px solid #1aad19;
}
.weui-btn_plain-primary:not(.weui-btn_plain-disabled):active{
  color:#06ae56;
  border-color:#179c16;
  background-color:rgba(0, 0, 0, 0.1);
}
.weui-btn_plain-primary:after{
  border-width:0;
}
.weui-btn_plain-default{
  color:#353535;
  border:1px solid #353535;
}
.weui-btn_plain-default:not(.weui-btn_plain-disabled):active{
  color:#323232;
  border-color:#323232;
  background-color:rgba(0, 0, 0, 0.1);
}
.weui-btn_plain-default:after{
  border-width:0;
}
.weui-btn_plain-disabled{
  color:rgba(0, 0, 0, 0.2);
  border-color:rgba(0, 0, 0, 0.2);
}
.weui-btn_cell{
  position:relative;
  display:block;
  margin-left:auto;
  margin-right:auto;
  box-sizing:border-box;
  font-weight:700;
  font-size:17px;
  text-align:center;
  text-decoration:none;
  color:#FFFFFF;
  line-height:1.41176471;
  padding:16px;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  overflow:hidden;
  background-color:#FFFFFF;
}
.weui-btn_cell + .weui-btn_cell{
  margin-top:16px;
}
.weui-btn_cell:active{
  background-color:#ECECEC;
}
.weui-btn_cell__icon{
  display:inline-block;
  vertical-align:middle;
  width:24px;
  height:24px;
  margin:-0.2em 0.34em 0 0;
}
.weui-btn_cell-default{
  color:rgba(0, 0, 0, 0.9);
}
.weui-btn_cell-primary{
  color:#576B95;
}
.weui-btn_cell-warn{
  color:#FA5151;
}
button.weui-btn,
input.weui-btn{
  border-width:0;
  outline:0;
  -webkit-appearance:none;
}
button.weui-btn:focus,
input.weui-btn:focus{
  outline:0;
}
button.weui-btn_inline,
input.weui-btn_inline,
button.weui-btn_mini,
input.weui-btn_mini{
  width:auto;
}
button.weui-btn_plain-primary,
input.weui-btn_plain-primary,
button.weui-btn_plain-default,
input.weui-btn_plain-default{
  border-width:1px;
  background-color:transparent;
}
.weui-btn_mini{
  display:inline-block;
  width:auto;
  padding:0 0.75em;
  line-height:2;
  font-size:16px;
}
.weui-btn + .weui-btn{
  margin-top:16px;
}
.weui-btn.weui-btn_inline + .weui-btn.weui-btn_inline{
  margin-top:auto;
  margin-left:16px;
}
.weui-btn-area{
  margin:48px 16px 8px;
}
.weui-btn-area_inline{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-btn-area_inline .weui-btn{
  margin-top:auto;
  margin-right:16px;
  width:100%;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-btn-area_inline .weui-btn:last-child{
  margin-right:0;
}
.weui-cells{
  margin-top:8px;
  background-color:#FFFFFF;
  line-height:1.41176471;
  font-size:17px;
  overflow:hidden;
  position:relative;
}
.weui-cells:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  z-index:2;
}
.weui-cells:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  z-index:2;
}
.weui-cells__title{
  margin-top:16px;
  margin-bottom:3px;
  padding-left:16px;
  padding-right:16px;
  color:rgba(0, 0, 0, 0.5);
  font-size:14px;
  line-height:1.4;
}
.weui-cells__title + .weui-cells{
  margin-top:0;
}
.weui-cells__tips{
  margin-top:.3em;
  color:rgba(0, 0, 0, 0.5);
  padding-left:16px;
  padding-right:16px;
  font-size:14px;
}
.weui-cell{
  padding:16px;
  position:relative;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-cell:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:16px;
  z-index:2;
}
.weui-cell:first-child:before{
  display:none;
}
.weui-cell_primary{
  -webkit-box-align:start;
  -webkit-align-items:flex-start;
          align-items:flex-start;
}
.weui-cell__bd{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-cell__ft{
  text-align:right;
  color:rgba(0, 0, 0, 0.5);
}
.weui-cell_swiped{
  display:block;
  padding:0;
}
.weui-cell_swiped > .weui-cell__bd{
  position:relative;
  z-index:1;
  background-color:#FFFFFF;
}
.weui-cell_swiped > .weui-cell__ft{
  position:absolute;
  right:0;
  top:0;
  bottom:0;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  color:#FFFFFF;
}
.weui-swiped-btn{
  display:block;
  padding:16px 1em;
  line-height:1.41176471;
  color:inherit;
}
.weui-swiped-btn_default{
  background-color:#EDEDED;
}
.weui-swiped-btn_warn{
  background-color:#FA5151;
}
.weui-cell_access{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  color:inherit;
}
.weui-cell_access:active{
  background-color:#ECECEC;
}
.weui-cell_access .weui-cell__ft{
  padding-right:16px;
  position:relative;
}
.weui-cell_access .weui-cell__ft:after{
  content:" ";
  display:inline-block;
  height:8px;
  width:8px;
  border-width:2px 2px 0 0;
  border-color:#B2B2B2;
  border-style:solid;
  -webkit-transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
          transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position:relative;
  top:-2px;
  position:absolute;
  top:50%;
  margin-top:-5px;
  right:0;
}
.weui-cell_link{
  color:#576B95;
  font-size:17px;
}
.weui-cell_link:first-child:before{
  display:block;
}
.weui-check__label{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-check__label:active{
  background-color:#ECECEC;
}
.weui-check{
  position:absolute;
  left:-9999em;
}
.weui-cells_radio .weui-cell__ft{
  padding-left:16px;
}
.weui-cells_radio .weui-check + .weui-icon-checked{
  min-width:16px;
}
.weui-cells_radio .weui-check + .weui-icon-checked:before{
  margin:0;
}
.weui-cells_radio .weui-check:checked + .weui-icon-checked:before{
  display:block;
  content:'\EA08';
  color:#07C160;
  font-size:16px;
}
.weui-cells_checkbox .weui-check__label:before{
  left:55px;
}
.weui-cells_checkbox .weui-cell__hd{
  padding-right:16px;
}
.weui-cells_checkbox .weui-icon-checked:before{
  content:'\EA01';
  color:rgba(0, 0, 0, 0.3);
  font-size:23px;
  display:block;
  margin:0;
}
.weui-cells_checkbox .weui-check:checked + .weui-icon-checked:before{
  content:'\EA06';
  color:#07C160;
}
.weui-label{
  display:block;
  width:105px;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-input{
  width:100%;
  border:0;
  outline:0;
  -webkit-appearance:none;
  background-color:transparent;
  font-size:inherit;
  color:inherit;
  height:1.41176471em;
  line-height:1.41176471;
}
.weui-input::-webkit-outer-spin-button,
.weui-input::-webkit-inner-spin-button{
  -webkit-appearance:none;
  margin:0;
}
.weui-textarea{
  display:block;
  border:0;
  resize:none;
  width:100%;
  color:inherit;
  font-size:1em;
  line-height:inherit;
  outline:0;
}
.weui-textarea-counter{
  color:rgba(0, 0, 0, 0.3);
  text-align:right;
}
.weui-cell_warn .weui-textarea-counter{
  color:#FA5151;
}
.weui-toptips{
  display:none;
  position:fixed;
  -webkit-transform:translateZ(0);
          transform:translateZ(0);
  top:0;
  left:0;
  right:0;
  padding:5px;
  font-size:14px;
  text-align:center;
  color:#FFF;
  z-index:5000;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-toptips_warn{
  background-color:#FA5151;
}
.weui-cells_form .weui-cell__ft{
  font-size:0;
}
.weui-cells_form .weui-icon-warn{
  display:none;
}
.weui-cells_form input,
.weui-cells_form textarea,
.weui-cells_form label[for]{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-cell_warn{
  color:#FA5151;
}
.weui-cell_warn .weui-icon-warn{
  display:inline-block;
}
.weui-form-preview{
  position:relative;
  background-color:#FFFFFF;
}
.weui-form-preview:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-form-preview:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-form-preview__hd{
  position:relative;
  padding:16px;
  text-align:right;
  line-height:2.5em;
}
.weui-form-preview__hd:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:16px;
}
.weui-form-preview__hd .weui-form-preview__value{
  font-style:normal;
  font-size:1.6em;
}
.weui-form-preview__bd{
  padding:16px;
  font-size:.9em;
  text-align:right;
  color:rgba(0, 0, 0, 0.5);
  line-height:2;
}
.weui-form-preview__ft{
  position:relative;
  line-height:50px;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-form-preview__ft:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-form-preview__item{
  overflow:hidden;
}
.weui-form-preview__label{
  float:left;
  margin-right:1em;
  min-width:4em;
  color:rgba(0, 0, 0, 0.5);
  text-align:justify;
  text-align-last:justify;
}
.weui-form-preview__value{
  display:block;
  overflow:hidden;
  word-break:normal;
  word-wrap:break-word;
}
.weui-form-preview__btn{
  position:relative;
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  color:#576B95;
  text-align:center;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
button.weui-form-preview__btn{
  background-color:transparent;
  border:0;
  outline:0;
  line-height:inherit;
  font-size:inherit;
}
.weui-form-preview__btn:active{
  background-color:#ECECEC;
}
.weui-form-preview__btn:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-form-preview__btn:first-child:after{
  display:none;
}
.weui-form-preview__btn_default{
  color:rgba(0, 0, 0, 0.9);
}
.weui-form-preview__btn_primary{
  color:#576B95;
}
.weui-cell_select{
  padding:0;
}
.weui-cell_select .weui-select{
  padding-right:30px;
}
.weui-cell_select .weui-cell__bd:after{
  content:" ";
  display:inline-block;
  height:8px;
  width:8px;
  border-width:2px 2px 0 0;
  border-color:#B2B2B2;
  border-style:solid;
  -webkit-transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
          transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position:relative;
  top:-2px;
  position:absolute;
  top:50%;
  right:16px;
  margin-top:-5px;
}
.weui-select{
  -webkit-appearance:none;
  border:0;
  outline:0;
  background-color:transparent;
  width:100%;
  font-size:inherit;
  height:56px;
  line-height:56px;
  position:relative;
  z-index:1;
  padding-left:16px;
}
.weui-cell_select-before{
  padding-right:16px;
}
.weui-cell_select-before .weui-select{
  width:105px;
  box-sizing:border-box;
}
.weui-cell_select-before .weui-cell__hd{
  position:relative;
}
.weui-cell_select-before .weui-cell__hd:after{
  content:" ";
  position:absolute;
  right:0;
  top:0;
  width:1px;
  bottom:0;
  border-right:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:100% 0;
          transform-origin:100% 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-cell_select-before .weui-cell__hd:before{
  content:" ";
  display:inline-block;
  height:8px;
  width:8px;
  border-width:2px 2px 0 0;
  border-color:#B2B2B2;
  border-style:solid;
  -webkit-transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
          transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position:relative;
  top:-2px;
  position:absolute;
  top:50%;
  right:16px;
  margin-top:-5px;
}
.weui-cell_select-before .weui-cell__bd{
  padding-left:16px;
}
.weui-cell_select-before .weui-cell__bd:after{
  display:none;
}
.weui-cell_select-after{
  padding-left:16px;
}
.weui-cell_select-after .weui-select{
  padding-left:0;
}
.weui-cell_vcode{
  padding-top:0;
  padding-right:0;
  padding-bottom:0;
}
.weui-vcode-img{
  margin-left:5px;
  height:56px;
  vertical-align:middle;
}
.weui-vcode-btn{
  display:inline-block;
  height:56px;
  margin-left:5px;
  padding:0 0.6em 0 0.7em;
  line-height:56px;
  vertical-align:middle;
  font-size:17px;
  color:#576B95;
  position:relative;
}
.weui-vcode-btn:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
button.weui-vcode-btn{
  background-color:transparent;
  border:0;
  outline:0;
}
.weui-vcode-btn:active{
  color:#767676;
}
.weui-gallery{
  display:none;
  position:fixed;
  top:0;
  right:0;
  bottom:0;
  left:0;
  background-color:#000000;
  z-index:1000;
}
.weui-gallery__img{
  position:absolute;
  top:constant(safe-area-inset-top);
  top:env(safe-area-inset-top);
  right:constant(safe-area-inset-right);
  right:env(safe-area-inset-right);
  bottom:calc(60px + constant(safe-area-inset-bottom));
  bottom:calc(60px + env(safe-area-inset-bottom));
  left:constant(safe-area-inset-left);
  left:env(safe-area-inset-left);
  background:center center no-repeat;
  background-size:contain;
}
.weui-gallery__opr{
  position:absolute;
  right:constant(safe-area-inset-right);
  right:env(safe-area-inset-right);
  bottom:constant(safe-area-inset-bottom);
  bottom:env(safe-area-inset-bottom);
  left:constant(safe-area-inset-left);
  left:env(safe-area-inset-left);
  background-color:#0D0D0D;
  color:#FFFFFF;
  line-height:60px;
  text-align:center;
}
.weui-gallery__del{
  display:block;
}
.weui-cell_switch{
  padding-top:12px;
  padding-bottom:12px;
}
.weui-switch{
  -webkit-appearance:none;
          appearance:none;
}
.weui-switch,
.weui-switch-cp__box{
  position:relative;
  width:52px;
  height:32px;
  border:1px solid rgba(0, 0, 0, 0.1);
  outline:0;
  border-radius:16px;
  box-sizing:border-box;
  background-color:rgba(0, 0, 0, 0.1);
  -webkit-transition:background-color 0.1s, border 0.1s;
  transition:background-color 0.1s, border 0.1s;
}
.weui-switch:before,
.weui-switch-cp__box:before{
  content:" ";
  position:absolute;
  top:0;
  left:0;
  width:50px;
  height:30px;
  border-radius:15px;
  background-color:#FDFDFD;
  -webkit-transition:-webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
  transition:-webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
  transition:transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
  transition:transform 0.35s cubic-bezier(0.45, 1, 0.4, 1), -webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
}
.weui-switch:after,
.weui-switch-cp__box:after{
  content:" ";
  position:absolute;
  top:0;
  left:0;
  width:30px;
  height:30px;
  border-radius:15px;
  background-color:#FFFFFF;
  box-shadow:0 1px 3px rgba(0, 0, 0, 0.4);
  -webkit-transition:-webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  transition:-webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  transition:transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  transition:transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35), -webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
}
.weui-switch:checked,
.weui-switch-cp__input:checked ~ .weui-switch-cp__box{
  border-color:#07C160;
  background-color:#07C160;
}
.weui-switch:checked:before,
.weui-switch-cp__input:checked ~ .weui-switch-cp__box:before{
  -webkit-transform:scale(0);
          transform:scale(0);
}
.weui-switch:checked:after,
.weui-switch-cp__input:checked ~ .weui-switch-cp__box:after{
  -webkit-transform:translateX(20px);
          transform:translateX(20px);
}
.weui-switch-cp__input{
  position:absolute;
  left:-9999px;
}
.weui-switch-cp__box{
  display:block;
}
.weui-uploader__hd{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  padding-bottom:16px;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-uploader__title{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-uploader__info{
  color:rgba(0, 0, 0, 0.3);
}
.weui-uploader__bd{
  margin-bottom:-8px;
  margin-right:-8px;
  overflow:hidden;
}
.weui-uploader__files{
  list-style:none;
}
.weui-uploader__file{
  float:left;
  margin-right:8px;
  margin-bottom:8px;
  width:96px;
  height:96px;
  background:no-repeat center center;
  background-size:cover;
}
.weui-uploader__file_status{
  position:relative;
}
.weui-uploader__file_status:before{
  content:" ";
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  background-color:rgba(0, 0, 0, 0.5);
}
.weui-uploader__file_status .weui-uploader__file-content{
  display:block;
}
.weui-uploader__file-content{
  display:none;
  position:absolute;
  top:50%;
  left:50%;
  -webkit-transform:translate(-50%, -50%);
          transform:translate(-50%, -50%);
  color:#FFFFFF;
}
.weui-uploader__file-content .weui-icon-warn{
  display:inline-block;
}
.weui-uploader__input-box{
  float:left;
  position:relative;
  margin-right:8px;
  margin-bottom:8px;
  width:96px;
  height:96px;
  box-sizing:border-box;
  background-color:#EDEDED;
}
.weui-uploader__input-box:before,
.weui-uploader__input-box:after{
  content:" ";
  position:absolute;
  top:50%;
  left:50%;
  -webkit-transform:translate(-50%, -50%);
          transform:translate(-50%, -50%);
  background-color:#A3A3A3;
}
.weui-uploader__input-box:before{
  width:2px;
  height:32px;
}
.weui-uploader__input-box:after{
  width:32px;
  height:2px;
}
.weui-uploader__input-box:active{
  border-color:#8b8b8b;
}
.weui-uploader__input-box:active:before,
.weui-uploader__input-box:active:after{
  background-color:#8b8b8b;
}
.weui-uploader__input{
  position:absolute;
  z-index:1;
  top:0;
  left:0;
  width:100%;
  height:100%;
  opacity:0;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-msg{
  padding-top:48px;
  padding:calc(48px + constant(safe-area-inset-top)) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left);
  padding:calc(48px + env(safe-area-inset-top)) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  text-align:center;
  line-height:1.4;
  min-height:100%;
  box-sizing:border-box;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-orient:vertical;
  -webkit-box-direction:normal;
  -webkit-flex-direction:column;
          flex-direction:column;
  background-color:#FFFFFF;
}
.weui-msg a:not(.weui-btn){
  color:#576B95;
  display:inline-block;
  vertical-align:baseline;
}
.weui-msg__icon-area{
  margin-bottom:32px;
}
.weui-msg__text-area{
  margin-bottom:32px;
  padding:0 32px;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  line-height:1.6;
}
.weui-msg__text-area:first-child{
  padding-top:96px;
}
.weui-msg__title{
  margin-bottom:16px;
  font-weight:700;
  font-size:22px;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-msg__desc{
  font-size:17px;
  color:rgba(0, 0, 0, 0.9);
  word-wrap:break-word;
  word-break:break-all;
  margin-bottom:16px;
}
.weui-msg__desc-primary{
  font-size:14px;
  color:rgba(0, 0, 0, 0.5);
  word-wrap:break-word;
  word-break:break-all;
  margin-bottom:16px;
}
.weui-msg__opr-area{
  margin-bottom:16px;
}
.weui-msg__opr-area .weui-btn-area{
  margin:0;
}
.weui-msg__opr-area .weui-btn + .weui-btn{
  margin-bottom:16px;
}
.weui-msg__opr-area:last-child{
  margin-bottom:96px;
}
.weui-msg__opr-area + .weui-msg__extra-area{
  margin-top:48px;
}
.weui-msg__tips-area{
  margin-bottom:16px;
  padding:0 40px;
}
.weui-msg__opr-area + .weui-msg__tips-area{
  margin-bottom:48px;
}
.weui-msg__tips-area:last-child{
  margin-bottom:64px;
}
.weui-msg__tips{
  font-size:12px;
  color:rgba(0, 0, 0, 0.5);
}
.weui-msg__extra-area{
  margin-bottom:24px;
  font-size:12px;
  color:rgba(0, 0, 0, 0.5);
}
.weui-msg__extra-area a{
  color:#576B95;
}
.weui-article{
  padding:24px 16px;
  padding:24px calc(16px + constant(safe-area-inset-right)) calc(24px + constant(safe-area-inset-bottom)) calc(16px + constant(safe-area-inset-left));
  padding:24px calc(16px + env(safe-area-inset-right)) calc(24px + env(safe-area-inset-bottom)) calc(16px + env(safe-area-inset-left));
  font-size:17px;
  color:rgba(0, 0, 0, 0.9);
}
.weui-article section{
  margin-bottom:1.5em;
}
.weui-article h1{
  font-size:22px;
  font-weight:700;
  margin-bottom:.9em;
  line-height:1.4;
}
.weui-article h2{
  font-size:17px;
  font-weight:700;
  margin-bottom:.34em;
  line-height:1.4;
}
.weui-article h3{
  font-weight:700;
  font-size:15px;
  margin-bottom:.34em;
  line-height:1.4;
}
.weui-article *{
  max-width:100%;
  box-sizing:border-box;
  word-wrap:break-word;
}
.weui-article p{
  margin:0 0 .8em;
}
.weui-tabbar{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  position:relative;
  z-index:500;
  background-color:#F7F7F7;
}
.weui-tabbar:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-tabbar__item{
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  padding:8px 0;
  padding-bottom:calc(8px + constant(safe-area-inset-bottom));
  padding-bottom:calc(8px + env(safe-area-inset-bottom));
  font-size:0;
  color:rgba(0, 0, 0, 0.5);
  text-align:center;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-tabbar__item:first-child{
  padding-left:constant(safe-area-inset-left);
  padding-left:env(safe-area-inset-left);
}
.weui-tabbar__item:last-child{
  padding-right:constant(safe-area-inset-right);
  padding-right:env(safe-area-inset-right);
}
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon > i,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label{
  color:#07C160;
}
.weui-tabbar__icon{
  display:inline-block;
  width:28px;
  height:28px;
  margin-bottom:2px;
}
i.weui-tabbar__icon,
.weui-tabbar__icon > i{
  font-size:24px;
  color:rgba(0, 0, 0, 0.5);
}
.weui-tabbar__icon img{
  width:100%;
  height:100%;
}
.weui-tabbar__label{
  color:rgba(0, 0, 0, 0.9);
  font-size:10px;
  line-height:1.4;
}
.weui-navbar{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  position:relative;
  z-index:500;
  background-color:#FFFFFF;
  padding-top:constant(safe-area-inset-top);
  padding-top:env(safe-area-inset-top);
}
.weui-navbar:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-navbar + .weui-tab__panel{
  padding-bottom:constant(safe-area-inset-bottom);
  padding-bottom:env(safe-area-inset-bottom);
}
.weui-navbar__item{
  position:relative;
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  padding:16px 0;
  padding-top:calc(16px + constant(safe-area-inset-top));
  padding-top:calc(16px + env(safe-area-inset-top));
  text-align:center;
  font-size:17px;
  line-height:1.41176471;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-navbar__item:active{
  background-color:#ECECEC;
}
.weui-navbar__item.weui-bar__item_on{
  background-color:#ECECEC;
}
.weui-navbar__item:after{
  content:" ";
  position:absolute;
  right:0;
  top:0;
  width:1px;
  bottom:0;
  border-right:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:100% 0;
          transform-origin:100% 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-navbar__item:first-child{
  padding-left:constant(safe-area-inset-left);
  padding-left:env(safe-area-inset-left);
}
.weui-navbar__item:last-child{
  padding-right:constant(safe-area-inset-right);
  padding-right:env(safe-area-inset-right);
}
.weui-navbar__item:last-child:after{
  display:none;
}
.weui-tab{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  height:100%;
  box-sizing:border-box;
  -webkit-box-orient:vertical;
  -webkit-box-direction:normal;
  -webkit-flex-direction:column;
          flex-direction:column;
}
.weui-tab__panel{
  box-sizing:border-box;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
}
.weui-tab__content{
  display:none;
}
.weui-progress{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-progress__bar{
  background-color:#EDEDED;
  height:3px;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-progress__inner-bar{
  width:0;
  height:100%;
  background-color:#07C160;
}
.weui-progress__opr{
  display:block;
  margin-left:15px;
  font-size:0;
}
.weui-panel{
  background-color:#FFFFFF;
  margin-top:10px;
  position:relative;
  overflow:hidden;
}
.weui-panel:first-child{
  margin-top:0;
}
.weui-panel:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-panel:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-panel__hd{
  padding:16px 16px 13px;
  color:rgba(0, 0, 0, 0.9);
  font-size:15px;
  font-weight:700;
  position:relative;
}
.weui-panel__hd:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:15px;
}
.weui-media-box{
  padding:16px;
  position:relative;
}
.weui-media-box:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:16px;
}
.weui-media-box:first-child:before{
  display:none;
}
a.weui-media-box{
  color:#000000;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
a.weui-media-box:active{
  background-color:#ECECEC;
}
.weui-media-box__title{
  font-weight:400;
  font-size:17px;
  line-height:1.4;
  color:rgba(0, 0, 0, 0.9);
  width:auto;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  word-wrap:normal;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-media-box__desc{
  color:rgba(0, 0, 0, 0.3);
  font-size:14px;
  line-height:1.4;
  padding-top:4px;
  overflow:hidden;
  text-overflow:ellipsis;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:2;
}
.weui-media-box__info{
  margin-top:16px;
  padding-bottom:4px;
  font-size:13px;
  color:#CECECE;
  line-height:1em;
  list-style:none;
  overflow:hidden;
}
.weui-media-box__info__meta{
  float:left;
  padding-right:1em;
}
.weui-media-box__info__meta_extra{
  padding-left:1em;
  border-left:1px solid #CECECE;
}
.weui-media-box_appmsg{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-media-box_appmsg .weui-media-box__hd{
  margin-right:16px;
  width:60px;
  height:60px;
  line-height:60px;
  text-align:center;
}
.weui-media-box_appmsg .weui-media-box__thumb{
  width:100%;
  max-height:100%;
  vertical-align:top;
}
.weui-media-box_appmsg .weui-media-box__bd{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  min-width:0;
}
.weui-media-box_small-appmsg{
  padding:0;
}
.weui-media-box_small-appmsg .weui-cells{
  margin-top:0;
}
.weui-media-box_small-appmsg .weui-cells:before{
  display:none;
}
.weui-grids{
  position:relative;
  overflow:hidden;
}
.weui-grids:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-grids:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-grid{
  position:relative;
  float:left;
  padding:20px 10px;
  width:33.33333333%;
  box-sizing:border-box;
}
.weui-grid:before{
  content:" ";
  position:absolute;
  right:0;
  top:0;
  width:1px;
  bottom:0;
  border-right:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:100% 0;
          transform-origin:100% 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-grid:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-grid:active{
  background-color:#ECECEC;
}
.weui-grid__icon{
  width:28px;
  height:28px;
  margin:0 auto;
}
.weui-grid__icon img{
  display:block;
  width:100%;
  height:100%;
}
.weui-grid__icon + .weui-grid__label{
  margin-top:4px;
}
.weui-grid__label{
  display:block;
  text-align:center;
  color:rgba(0, 0, 0, 0.9);
  font-size:14px;
  white-space:nowrap;
  text-overflow:ellipsis;
  overflow:hidden;
}
.weui-footer{
  color:rgba(0, 0, 0, 0.3);
  font-size:14px;
  line-height:1.4;
  text-align:center;
}
.weui-footer a{
  color:#576B95;
}
.weui-footer_fixed-bottom{
  position:fixed;
  bottom:16px;
  left:0;
  right:0;
}
.weui-footer__links{
  font-size:0;
}
.weui-footer__link{
  display:inline-block;
  vertical-align:top;
  margin:0 8px;
  position:relative;
  font-size:14px;
}
.weui-footer__link:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid #C7C7C7;
  color:#C7C7C7;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
  left:-8px;
  top:.36em;
  bottom:.36em;
}
.weui-footer__link:first-child:before{
  display:none;
}
.weui-footer__text{
  padding:0 16px;
  font-size:12px;
}
.weui-flex{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-flex__item{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-dialog{
  position:fixed;
  z-index:5000;
  top:50%;
  left:16px;
  right:16px;
  -webkit-transform:translate(0, -50%);
          transform:translate(0, -50%);
  background-color:#FFFFFF;
  text-align:center;
  border-radius:3px;
  overflow:hidden;
}
.weui-dialog__hd{
  padding:32px 24px 16px;
}
.weui-dialog__title{
  font-weight:700;
  font-size:17px;
  line-height:1.4;
}
.weui-dialog__bd{
  padding:0 24px 32px;
  min-height:40px;
  font-size:17px;
  line-height:1.4;
  word-wrap:break-word;
  word-break:break-all;
  color:rgba(0, 0, 0, 0.5);
}
.weui-dialog__bd:first-child{
  padding:32px 24px;
  color:rgba(0, 0, 0, 0.9);
}
.weui-dialog__ft{
  position:relative;
  line-height:64px;
  font-size:17px;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-dialog__ft:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-dialog__btn{
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  color:#576B95;
  font-weight:700;
  text-decoration:none;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  position:relative;
}
.weui-dialog__btn:active{
  background-color:#ECECEC;
}
.weui-dialog__btn:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-dialog__btn:first-child:after{
  display:none;
}
.weui-dialog__btn_default{
  color:rgba(0, 0, 0, 0.9);
}
.weui-skin_android .weui-dialog{
  text-align:left;
  box-shadow:0 6px 30px 0 rgba(0, 0, 0, 0.1);
}
.weui-skin_android .weui-dialog__title{
  font-size:22px;
  line-height:1.4;
}
.weui-skin_android .weui-dialog__hd{
  text-align:left;
}
.weui-skin_android .weui-dialog__bd{
  color:rgba(0, 0, 0, 0.5);
  font-size:17px;
  text-align:left;
}
.weui-skin_android .weui-dialog__bd:first-child{
  color:rgba(0, 0, 0, 0.9);
}
.weui-skin_android .weui-dialog__ft{
  display:block;
  text-align:right;
  line-height:40px;
  padding:0 24px 16px;
}
.weui-skin_android .weui-dialog__ft:after{
  display:none;
}
.weui-skin_android .weui-dialog__btn{
  display:inline-block;
  vertical-align:top;
  padding:0 .8em;
}
.weui-skin_android .weui-dialog__btn:after{
  display:none;
}
.weui-skin_android .weui-dialog__btn:last-child{
  margin-right:-0.8em;
}
.weui-skin_android .weui-dialog__btn_default{
  color:rgba(0, 0, 0, 0.9);
}
@media screen and (min-width: 1024px){
  .weui-dialog{
    width:35%;
  }
}
.weui-toast{
  position:fixed;
  z-index:5000;
  width:7.6em;
  min-height:7.6em;
  top:180px;
  left:50%;
  margin-left:-3.8em;
  background:rgba(17, 17, 17, 0.7);
  text-align:center;
  border-radius:5px;
  color:#FFFFFF;
}
.weui-icon_toast{
  margin:24px 0 0;
  display:block;
}
.weui-icon_toast.weui-icon-success-no-circle:before{
  color:#FFFFFF;
  font-size:55px;
}
.weui-icon_toast.weui-loading{
  margin:32px 0 0;
  width:38px;
  height:38px;
  vertical-align:baseline;
}
.weui-toast__content{
  margin:0 0 16px;
  font-size:14px;
}
.weui-mask{
  position:fixed;
  z-index:1000;
  top:0;
  right:0;
  left:0;
  bottom:0;
  background:rgba(0, 0, 0, 0.6);
}
.weui-mask_transparent{
  position:fixed;
  z-index:1000;
  top:0;
  right:0;
  left:0;
  bottom:0;
}
.weui-actionsheet{
  position:fixed;
  left:0;
  bottom:0;
  -webkit-transform:translate(0, 100%);
          transform:translate(0, 100%);
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  z-index:5000;
  width:100%;
  background-color:#EDEDED;
  -webkit-transition:-webkit-transform .3s;
  transition:-webkit-transform .3s;
  transition:transform .3s;
  transition:transform .3s, -webkit-transform .3s;
}
.weui-actionsheet__title{
  position:relative;
  height:56px;
  padding:0 24px;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-pack:center;
  -webkit-justify-content:center;
          justify-content:center;
  -webkit-box-orient:vertical;
  -webkit-box-direction:normal;
  -webkit-flex-direction:column;
          flex-direction:column;
  text-align:center;
  font-size:14px;
  color:rgba(0, 0, 0, 0.5);
  line-height:1.4;
  background:#FFFFFF;
}
.weui-actionsheet__title:before{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-actionsheet__title .weui-actionsheet__title-text{
  overflow:hidden;
  text-overflow:ellipsis;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:2;
}
.weui-actionsheet__menu{
  background-color:#FFFFFF;
}
.weui-actionsheet__action{
  margin-top:6px;
  background-color:#FFFFFF;
  padding-bottom:constant(safe-area-inset-bottom);
  padding-bottom:env(safe-area-inset-bottom);
}
.weui-actionsheet__cell{
  position:relative;
  padding:16px;
  text-align:center;
  font-size:17px;
  line-height:1.41176471;
}
.weui-actionsheet__cell:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-actionsheet__cell:active{
  background-color:#ECECEC;
}
.weui-actionsheet__cell:first-child:before{
  display:none;
}
.weui-skin_android .weui-actionsheet{
  position:fixed;
  left:50%;
  top:50%;
  bottom:auto;
  -webkit-transform:translate(-50%, -50%);
          transform:translate(-50%, -50%);
  width:274px;
  box-sizing:border-box;
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  background:transparent;
  -webkit-transition:-webkit-transform .3s;
  transition:-webkit-transform .3s;
  transition:transform .3s;
  transition:transform .3s, -webkit-transform .3s;
}
.weui-skin_android .weui-actionsheet__action{
  display:none;
}
.weui-skin_android .weui-actionsheet__menu{
  border-radius:2px;
  box-shadow:0 6px 30px 0 rgba(0, 0, 0, 0.1);
}
.weui-skin_android .weui-actionsheet__cell{
  padding:16px;
  font-size:17px;
  line-height:1.41176471;
  color:rgba(0, 0, 0, 0.9);
  text-align:left;
}
.weui-skin_android .weui-actionsheet__cell:first-child{
  border-top-left-radius:2px;
  border-top-right-radius:2px;
}
.weui-skin_android .weui-actionsheet__cell:last-child{
  border-bottom-left-radius:2px;
  border-bottom-right-radius:2px;
}
.weui-actionsheet_toggle{
  -webkit-transform:translate(0, 0);
          transform:translate(0, 0);
}
.weui-loadmore{
  width:65%;
  margin:1.5em auto;
  line-height:1.6em;
  font-size:14px;
  text-align:center;
}
.weui-loadmore__tips{
  display:inline-block;
  vertical-align:middle;
  color:rgba(0, 0, 0, 0.9);
}
.weui-loadmore_line{
  border-top:1px solid rgba(0, 0, 0, 0.1);
  margin-top:2.4em;
}
.weui-loadmore_line .weui-loadmore__tips{
  position:relative;
  top:-0.9em;
  padding:0 .55em;
  background-color:#FFFFFF;
  color:rgba(0, 0, 0, 0.5);
}
.weui-loadmore_dot .weui-loadmore__tips{
  padding:0 .16em;
}
.weui-loadmore_dot .weui-loadmore__tips:before{
  content:" ";
  width:4px;
  height:4px;
  border-radius:50%;
  background-color:rgba(0, 0, 0, 0.1);
  display:inline-block;
  position:relative;
  vertical-align:0;
  top:-0.16em;
}
.weui-badge{
  display:inline-block;
  padding:.15em .4em;
  min-width:8px;
  border-radius:18px;
  background-color:#FA5151;
  color:#FFFFFF;
  line-height:1.2;
  text-align:center;
  font-size:12px;
  vertical-align:middle;
}
.weui-badge_dot{
  padding:.4em;
  min-width:0;
}
.weui-search-bar{
  position:relative;
  padding:8px;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  box-sizing:border-box;
  background-color:#EDEDED;
  -webkit-text-size-adjust:100%;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-search-bar.weui-search-bar_focusing .weui-search-bar__cancel-btn{
  display:block;
}
.weui-search-bar.weui-search-bar_focusing .weui-search-bar__label{
  display:none;
}
.weui-search-bar__form{
  position:relative;
  -webkit-box-flex:1;
  -webkit-flex:auto;
          flex:auto;
  background-color:#FFFFFF;
  border-radius:4px;
}
.weui-search-bar__box{
  position:relative;
  padding-left:32px;
  padding-right:32px;
  height:100%;
  width:100%;
  box-sizing:border-box;
  z-index:1;
}
.weui-search-bar__box .weui-search-bar__input{
  padding:8px 0;
  width:100%;
  height:1.14285714em;
  border:0;
  font-size:14px;
  line-height:1.14285714em;
  box-sizing:content-box;
  background:transparent;
  caret-color:#07C160;
}
.weui-search-bar__box .weui-search-bar__input:focus{
  outline:none;
}
.weui-search-bar__box .weui-icon-search{
  position:absolute;
  top:50%;
  left:8px;
  margin-top:-14px;
  line-height:28px;
}
.weui-search-bar__box .weui-icon-clear{
  position:absolute;
  top:50%;
  right:0;
  margin-top:-14px;
  padding:0 8px;
  line-height:28px;
}
.weui-search-bar__label{
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  z-index:2;
  font-size:0;
  border-radius:4px;
  line-height:32px;
  text-align:center;
  color:rgba(0, 0, 0, 0.5);
  background:#FFFFFF;
}
.weui-search-bar__label span{
  display:inline-block;
  font-size:14px;
  vertical-align:middle;
}
.weui-search-bar__label .weui-icon-search{
  margin-right:4px;
}
.weui-search-bar__cancel-btn{
  display:none;
  margin-left:8px;
  line-height:28px;
  color:#576B95;
  white-space:nowrap;
}
.weui-search-bar__input:not(:valid) ~ .weui-icon-clear{
  display:none;
}
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration{
  display:none;
}
.weui-picker{
  position:fixed;
  width:100%;
  left:0;
  bottom:0;
  z-index:5000;
  background-color:#fff;
  padding-bottom:constant(safe-area-inset-bottom);
  padding-bottom:env(safe-area-inset-bottom);
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  -webkit-transform:translate(0, 100%);
          transform:translate(0, 100%);
  -webkit-transition:-webkit-transform .3s;
  transition:-webkit-transform .3s;
  transition:transform .3s;
  transition:transform .3s, -webkit-transform .3s;
}
.weui-picker__hd{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  padding:16px;
  padding:16px calc(16px + constant(safe-area-inset-right)) 16px calc(16px + constant(safe-area-inset-left));
  padding:16px calc(16px + env(safe-area-inset-right)) 16px calc(16px + env(safe-area-inset-left));
  position:relative;
  text-align:center;
  font-size:17px;
  line-height:1.4;
}
.weui-picker__hd:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-picker__action{
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  color:#576B95;
}
.weui-picker__action:first-child{
  text-align:left;
  color:rgba(0, 0, 0, 0.9);
}
.weui-picker__action:last-child{
  text-align:right;
}
.weui-picker__bd{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  position:relative;
  background-color:#fff;
  height:240px;
  overflow:hidden;
}
.weui-picker__group{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  position:relative;
  height:100%;
}
.weui-picker__group:first-child .weui-picker__item{
  padding-left:constant(safe-area-inset-left);
  padding-left:env(safe-area-inset-left);
}
.weui-picker__group:last-child .weui-picker__item{
  padding-right:constant(safe-area-inset-right);
  padding-right:env(safe-area-inset-right);
}
.weui-picker__mask{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%;
  margin:0 auto;
  z-index:3;
  background:-webkit-linear-gradient(top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)), -webkit-linear-gradient(bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
  background:linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)), linear-gradient(0deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
  background-position:top, bottom;
  background-size:100% 102px;
  background-repeat:no-repeat;
  -webkit-transform:translateZ(0);
          transform:translateZ(0);
}
.weui-picker__indicator{
  width:100%;
  height:34px;
  position:absolute;
  left:0;
  top:102px;
  z-index:3;
}
.weui-picker__indicator:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-picker__indicator:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid rgba(0, 0, 0, 0.1);
  color:rgba(0, 0, 0, 0.1);
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-picker__content{
  position:absolute;
  top:0;
  left:0;
  width:100%;
}
.weui-picker__item{
  height:34px;
  line-height:34px;
  text-align:center;
  color:rgba(0, 0, 0, 0.9);
  text-overflow:ellipsis;
  white-space:nowrap;
  overflow:hidden;
}
.weui-picker__item_disabled{
  color:rgba(0, 0, 0, 0.5);
}
@-webkit-keyframes slideUp{
  from{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
  to{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
}
@keyframes slideUp{
  from{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
  to{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
}
.weui-animate-slide-up{
  -webkit-animation:slideUp ease .3s forwards;
          animation:slideUp ease .3s forwards;
}
@-webkit-keyframes slideDown{
  from{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
  to{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
}
@keyframes slideDown{
  from{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
  to{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
}
.weui-animate-slide-down{
  -webkit-animation:slideDown ease .3s forwards;
          animation:slideDown ease .3s forwards;
}
@-webkit-keyframes fadeIn{
  from{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
@keyframes fadeIn{
  from{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
.weui-animate-fade-in{
  -webkit-animation:fadeIn ease .3s forwards;
          animation:fadeIn ease .3s forwards;
}
@-webkit-keyframes fadeOut{
  from{
    opacity:1;
  }
  to{
    opacity:0;
  }
}
@keyframes fadeOut{
  from{
    opacity:1;
  }
  to{
    opacity:0;
  }
}
.weui-animate-fade-out{
  -webkit-animation:fadeOut ease .3s forwards;
          animation:fadeOut ease .3s forwards;
}
.weui-agree{
  display:block;
  padding:.5em 15px;
  font-size:13px;
}
.weui-agree a{
  color:#576B95;
}
.weui-agree__text{
  color:rgba(0, 0, 0, 0.5);
}
.weui-agree__checkbox{
  -webkit-appearance:none;
          appearance:none;
  outline:0;
  font-size:0;
  border:1px solid rgba(0, 0, 0, 0.3);
  background-color:#FFFFFF;
  border-radius:3px;
  width:13px;
  height:13px;
  position:relative;
  vertical-align:0;
  top:2px;
}
.weui-agree__checkbox:checked:before{
  font-family:"weui";
  font-style:normal;
  font-weight:normal;
  font-variant:normal;
  text-transform:none;
  text-align:center;
  speak:none;
  display:inline-block;
  vertical-align:middle;
  text-decoration:inherit;
  content:"\EA08";
  color:#09BB07;
  font-size:13px;
  position:absolute;
  top:50%;
  left:50%;
  -webkit-transform:translate(-50%, -48%) scale(0.73);
          transform:translate(-50%, -48%) scale(0.73);
}
.weui-agree__checkbox:disabled{
  background-color:#E1E1E1;
}
.weui-agree__checkbox:disabled:before{
  color:#ADADAD;
}
.weui-loading{
  width:20px;
  height:20px;
  display:inline-block;
  vertical-align:middle;
  -webkit-animation:weuiLoading 1s steps(12, end) infinite;
          animation:weuiLoading 1s steps(12, end) infinite;
  background:transparent url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E9E9E9' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23989697' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%239B999A' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23A3A1A2' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23ABA9AA' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23B2B2B2' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23BAB8B9' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23C2C0C1' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23CBCBCB' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23D2D2D2' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23DADADA' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E2E2E2' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E") no-repeat;
  background-size:100%;
}
.weui-loading.weui-loading_transparent,
.weui-btn_loading.weui-btn_primary .weui-loading{
  background-image:url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect xmlns='http://www.w3.org/2000/svg' width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.56)' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.5)' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.43)' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.38)' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.32)' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.28)' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.25)' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.2)' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.17)' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.14)' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.1)' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.03)' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E");
}
@-webkit-keyframes weuiLoading{
  0%{
    -webkit-transform:rotate3d(0, 0, 1, 0deg);
            transform:rotate3d(0, 0, 1, 0deg);
  }
  100%{
    -webkit-transform:rotate3d(0, 0, 1, 360deg);
            transform:rotate3d(0, 0, 1, 360deg);
  }
}
@keyframes weuiLoading{
  0%{
    -webkit-transform:rotate3d(0, 0, 1, 0deg);
            transform:rotate3d(0, 0, 1, 0deg);
  }
  100%{
    -webkit-transform:rotate3d(0, 0, 1, 360deg);
            transform:rotate3d(0, 0, 1, 360deg);
  }
}
.weui-slider{
  padding:15px 18px;
  -webkit-user-select:none;
          user-select:none;
}
.weui-slider__inner{
  position:relative;
  height:2px;
  background-color:rgba(0, 0, 0, 0.1);
}
.weui-slider__track{
  height:2px;
  background-color:#07C160;
  width:0;
}
.weui-slider__handler{
  position:absolute;
  left:0;
  top:50%;
  width:28px;
  height:28px;
  margin-left:-14px;
  margin-top:-14px;
  border-radius:50%;
  background-color:#FFFFFF;
  box-shadow:0 0 4px rgba(0, 0, 0, 0.2);
}
.weui-slider-box{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-slider-box .weui-slider{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-slider-box__value{
  margin-left:.5em;
  min-width:24px;
  color:rgba(0, 0, 0, 0.5);
  text-align:center;
  font-size:14px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
