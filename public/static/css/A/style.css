
.clearfix:after {
    content: "";
    display: table;
    clear:both;
}

#topMenus .active a{
	color: #fff;
}
#topMenus .active:after{
	content:"";
	display:block;
	width: 100%;
    height: 5px;
    background: #5FB878;
    position: absolute;
    bottom: 0;
}
#leftMenus .active:after{
	content:"";
	display:block;
	background-color: #009688;
	position: absolute;
    left: 0;
    width: 5px;
    height: 45px;
}
.ft15{
	font-size: 16px !important;
}
.ft16{
	font-size: 16px !important;
}
.ft16a a{
	font-size: 16px !important;
}
.w333px{
    width: 333px !important;
}
.w440px{
    width: 440px !important;
}
.w600px{
	width: 600px !important;
}
.h200px{
    height: 200px !important;
}
.h230px{
    height: 230px !important;
}
.mt15{
    margin-top: 15px;
}
.label2-title{
	width: 169px;
    text-align: center;
    padding: 9px 0 0;
}
.table-center th , .table-center td{
	text-align: center !important;
}
.layui-nav2{
	margin: 0 2px;
	margin-bottom: 15px;
}
.layui-body{
	bottom: 0!important;
}
.extend-inline-text{
	font-size: 10px;
    line-height: 20px;
    padding: 0 !important;
    width: 279px;
}
.fl{
	float: left;
}
.fr{
    float: right;
}

.btn-disabled,.btn-disabled:hover{
    cursor: not-allowed !important;
}

.disabled:hover {
    cursor: not-allowed !important;
}

.form-border{
    min-height: 450px;
    border: 1px solid #e6e6e6;
    border-top: none;
}
.layui-table{
    margin-top: 0;
}


.pager2{
    margin-top: 20px;
}
.pagination{
	text-align: center;
}
.pagination li.disabled:hover{
	color: #d2d2d2!important;
    cursor: not-allowed!important;
}
.pagination li.active span{
	color: #fff;
    background: #009688;
    border: 1px solid #009688;
}
.pagination li{
	display: inline-block;
}
.pagination li a,.pagination li span{
    vertical-align: middle;
    padding: 8px 16px;
    /*height: 28px;
    line-height: 28px;*/
    margin: 0 -1px 0px -1px;
    background-color: #fff;
    color: #000;
    font-size: 14px;
    border: 1px solid #e2e2e2;
}


.empty_data{
    text-align: center;
}
.empty_data:hover{
    background: #fff !important;
}
.empty_data td{
    color: #999999;
    padding: 100px 0;
    font-size: 20px;
}
.empty_data td i{
    display: inline-block;
    width: 40px;
    height: 30px;
    background: url(/static/img/A/empty.png) no-repeat;
    position: relative;
    top: 9px;
}
.layui-nav-img{
    width: 35px !important;
    height: 35px !important;
}
.infocard{
    color: #090996 !important;
}