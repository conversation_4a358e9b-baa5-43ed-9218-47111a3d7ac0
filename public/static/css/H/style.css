body, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, form, fieldset, input, button, textarea, p, th, td {
    margin: 0;
    padding: 0;
}
a{
    color: #333333;
    text-decoration: none;
}
,a:hover{
    text-decoration: none;
}
li {
    list-style: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
img{
    border: none;
    vertical-align: middle;
}
.mt25 {
    margin-top: 25px !important;
}

.wrapper {
    width: 1200px;
    margin-left: auto;
    margin-right: auto;
}
.clearfix:after {
    content: "";
    display: table;
    clear:both;
}
.fl{
    float: left;
}
.fr{
    float: right;
}
.white {
    color: white;
}
.red {
    color: red;
}
.mr0{
    margin-right: 0 !important;
}
body {
    background-color: #f5f6f7;
    min-width: 1200px;
    font-family: "Microsoft YaHei";
    font-size: 14px;
}
.header{
    min-width: 1200px;
    width: 100%;
	height: auto;
    background: #fff;
    margin: 0 auto;
    box-shadow: 0px 2px 5px #ddd;
    font-size: 14px;
}
.header .top{
    width: 100%;
	height: auto;
    line-height: 35px;
}
.header .top2{
    width: 100%;
    height: 90px;
    clear: both;
}
.header .top2 .logo{
	float: left;
    margin-top: 10px;
}
.header .top2 .nav{
    float: right;
    position: relative;
}
.header .top2 .nav li.nav1{
    float: left;
    font-size: 16px;
    position: relative;
    margin: 0 16px;
    margin-top: 17px;
}
.header .top2 .nav .nav1 a.item{
    color: #3E3E3E;
    line-height: 60px;
    
}
.header .top2 .nav .nav1:hover a.item{
     color: #F65C54;
}
.header .top2 .active .active a.item{color: #F65C54;}
.header .top2 .nav .nav1 .item-111::before {
    content: "";
    display: block;
    width: 27px;
    height: 20px;
    position: absolute;
    top: -5px;
    right: 11px;
    background: url(../../img/H/extend/hot.gif) no-repeat;
}
.header .top2 .nav .nav1 .item-118::before {
    content: "";
    display: block;
    width: 27px;
    height: 20px;
    position: absolute;
    top:0px;
    right: 11px;
    background: url(../../img/H/extend/new.gif) no-repeat;
}
.header .top2 .nav .nav2{
    width: 100px;
    background-color: #fff;
    text-align: center;
    position: absolute;
    top: 50px;
    left: -25px;
    padding: 2px 10px 10px;
    z-index: 9;
    box-shadow: 1px 1px 8px 0px #ccc;
    border-top: 3px solid #cf2d36;
    display: none;
}
.header .top2 .nav .nav2 a{
    color: #000;
    display: inline-block;
    line-height: 40px;
    width: 100%;
    font-size: 16px;
    border-bottom: 1px solid #f1f1f1;
}
.header .t1{
    width: 700px;
    position: relative;
}
.header .t2 a{
    color: #000;
    margin-left: 10px;
}
.banner {
    min-width: 1200px;
    position: relative;
    height: 350px;
    top: 0px;
}
.banner-slide, .banner-slide .bd ul, .banner-slide .bd li {
    width: 100%;
    height: 100%;
}
.banner-slide {
    position: absolute;
    left: 0;
    top: 0;
}
.banner-slide .hd {
    position: absolute;
    bottom: 19px;
    z-index: 2;
    width: 100%;
}
.banner-slide .hd ul {
    text-align: center;
}
.banner-slide .hd li {
    display: inline-block;
    cursor: pointer;
    margin: 0 5px;
    height: 14px;
    width: 14px;
    background: #fff;
    border-radius: 50%;
    opacity: 0.6;
    background-color: #fff;
}
.banner-slide .hd li.on {
    opacity: 1;
    height: 16px;
    width: 16px;
    position: relative;
    top: -2px;
}
.banner-slide a {
    display: block;
    width: 100%;
    height: 100%;
}
.banner-slide .bd li {
    position: absolute;
    left: 0;
    top: 0;
}
.banner .left,.banner .right{
    width: 20px;
    height: 35px;
    display: block;
    position: absolute;
    background: url(../../img/H/slider-arrow.png) no-repeat;
    top: 49%;
    left: 2%;
    background-position: -175px 0;
}
.banner .right{
    left: auto;
    right: 2%;
    background-position: -60px 0;
}

.footer{
	width: 100%;
	height: 200px;
	background-color: #000;
}
.btn-disabled,.btn-disabled:hover{
    cursor: not-allowed !important;
}

/* 分页 */
.pager2{
    margin-top: 25px;
}
.pagination{
    text-align: center;
}
.pagination li.disabled:hover{
    color: #d2d2d2!important;
    cursor: not-allowed!important;
}
.pagination li.active span{
    color: #fff;
    background: #cf2d36;
    border: 1px solid #cf2d36;
}
.pagination li{
    display: inline-block;
}
.pagination li a,.pagination li span{
    vertical-align: middle;
    padding: 8px 16px;
    /*height: 28px;
    line-height: 28px;*/
    margin: 0 -1px 0px -1px;
    background-color: #fff;
    color: #000;
    font-size: 14px;
    border: 1px solid #e2e2e2;
}
/* 分页 */