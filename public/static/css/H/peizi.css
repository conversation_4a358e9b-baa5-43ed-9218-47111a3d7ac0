em {
    font-style: normal;
    font-weight: normal;
}
input, button, textarea, select {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
}

.peizi_img .imgfl{float:left;margin-right:10px;}

/* 选择配资方案 */
.ipt{border:1px solid #ddd;background-color:#fff;padding:0 6px;color:#999}
ul.chooseUlLi li{float:left;margin:8px;position:relative;height:100px;width:140px}
ul.chooseUlLi li a{height:100px;width:142px;display:table-cell;vertical-align:middle;border:2px solid #ddd;padding:0;text-align:center;font-size:16px;color:#999;border-radius:5px;position:absolute;top:-15px}
ul.chooseUlLi li a strong{font-size:40px;font-family:Arial;line-height:58px}
ul.chooseUlLi li a p{display:block}
ul.chooseUlLi li a p span{font-size:16px;font-weight:bolder}
ul.chooseUlLi li i{width:33px;height:34px;position:absolute;bottom:0;right:0;display:none;background:url(../../img/H/dagou.png) no-repeat}
ul.chooseUlLi li a:hover{height:100px;color:#ff5256;top:-15px;border:2px solid #ff5256}
ul.chooseUlLi li a:hover p{display:block}
ul.chooseUlLi li a.active{
	height: 100px;
    color: #ff5256;
    top: -15px;
    border: 2px solid #ff5256;
    color: #fff;
    background: #ff6f06;
}
ul.chooseUlLi li a.active i,ul.chooseUlLi li a.active p{display:block}
ul.chooseUlLi li a .yxi{display:block;font-size:14px}

.table td,.table th{
    text-align:center;
    border-bottom:1px solid #eee;
    padding:8px;
    line-height:26px;
    font-size: 14px;
}
.tableborder td,.tableborder th{border:1px solid #ddd}
.tabletree th{text-align:right}
.tabletree td{text-align:left}
.tablemony td,.tablemony th{padding:12px 6px}
.tablemony th{    color: #555;
    background-color: #ffe9e9;
    font-size: 14px;
    font-weight: normal;
    padding-right: 15px;}
.tablemony td{color:#555}
.tablemony .colorfe5911{color:#fe5911;font-size:16px}

.listgz{padding-bottom:20px}
.listgz p{padding-top:20px;text-align:center;font-size:16px;color:#333}
.btnorg{background-color:var(--primary-color)}
.btnorg-sizebig{font-size:18px;width:165px;height:48px;line-height:48px}
.btnorgd:active{background-color:#fbc083}
.radius5px{border-radius:5px;-webkit-border-radius:5px;-moz-border-radius:5px}

.newallt .btn{border:none;display:inline-block;text-align:center;cursor:pointer;font-family:microsoft yahei;color:#fff;overflow:visible}

.margin-left15{margin-left:15px}
.ctrlexp dt{font-size:16px;font-weight:700;color:#333;padding-bottom:10px}
.ctrlexp dd{line-height:28px;color:#666}
/* 选择配资方案 end */

/*.peizi_bg{ background-color: #f1f1f1;}*/
.peizi_img{width:1090px;margin:0 auto;}

.isstartBtn.active{
    background: #ff6f06 !important;
    color: #fff !important;
    border: 1px solid #ff6f06 !important;
}


.open_border ul li .btn-s-disable { 
    padding: 10px 0 10px;width:110px;line-height:16px; font-size:16px; color:#7A7A7A;background-color:#999999;
    cursor:default;
}
.open_border ul li .btn-s-disable:hover{color:#777; background-color:#999999;}



/*new*/
/*order*/
.confirm-apply{
    width: 740px;
    background-color: #fff;
    border-radius: 6px;
    margin: 0 auto;
}
.confirm-apply .title {
    font-size: 18px;
    line-height: 50px;
    text-align: center;
    color: #5D5D5D;
}
.confirm-apply .meta {
    background-color: #F9F9F9;
}

.confirm-apply .meta .item {
    border-bottom: 1px solid #ECECEC;
    height: 51px;
    line-height: 51px;
    padding: 0 20px;
    color: #666666;
    overflow: hidden;
}

.confirm-apply .meta .item .left {
    width: 125px;
    float: left;
}

.confirm-apply .meta .item .middle {
    float: left;
    width: 112px;
    text-align: right;
    padding-right: 30px;
}

.confirm-apply .meta .item .right {
    overflow: hidden;
    color: #909090;
}

.confirm-apply .meta .item .right a.fr {
    float: right;
    color: #b28e60;
}

.confirm-apply .meta .item:nth-child(4) {
    border: none;
}

.confirm-apply .meta .item:last-child {
    background: #fff;
    border: none;
}

.confirm-apply .text-red{
    color: #ff6e07;
}

.confirm-apply .operate-group {
    margin-top: 5px;
    padding: 20px 0;
    border-top: 1px dashed #C6C6C6;
    text-align: center;
}

.confirm-apply .operate-group .btn {
    display: inline-block;
    padding: 0;
    width: 150px;
    font-size: 18px;
    line-height: 45px;
    border-radius: 5px;
    margin: 0 15px;
}

.confirm-apply .operate-group a{
    text-decoration: none;
}

.confirm-apply .operate-group .btn-cancel {
    background-color: #D9D9D9;
    color: #ffffff;
}
.confirm-apply .operate-group .btn-primary {
    background-color: var(--primary-color);
    color: #ffffff;
}
/*order end*/
/*new end*/


/*免费*/
#experienceBox {
    padding: 12px 30px;
}
#textBox {
    width: 686px;
    margin: 0 auto;
}
#textBox dt, #textBox dd {
    width: 100%;
    height: 80px;
    display: inline-block;
    line-height: 66px;
    margin-top: 25px;
    font-size: 26px;
    background: url(../../img/H/experienceText.gif) no-repeat;
}
#textBox dd {
    background-position: 0 -80px;
    /*text-indent: 150px;*/
    text-align: center;
    color: #666;
}
#textBox dt {
    background-position: 0 0;
    text-align: center;
    color: #fff;
}

#textBox dd b {
    font-size: 30px;
    padding: 0 5px;
}
.ruleText {
    display: block;
    text-align: center;
    line-height: 30px;
    margin-top: 20px;
    padding-bottom: 50px;
}
.ruleText .btn {
    width: 200px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    margin-top: 20px;

    text-align: center;
    display: inline-block;
    border-radius: 4px;
    color: #fff !important;
    cursor: pointer;
    text-decoration: none !important;
}
.ruleText .btnBg1 {
    background: #e43925;
}


.pz_banner {
    position: relative
}
 .pz_banner .banner_img {
    overflow: hidden
}
 .pz_banner .banner_img p {
    width: 100%;
    /*height: 290px;*/
    position: relative;
    display: inline-block
}
 .pz_banner .banner_img p img {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%)
}