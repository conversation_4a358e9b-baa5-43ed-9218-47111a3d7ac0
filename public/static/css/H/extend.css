

.header .t1 .footgz {
    position: absolute;
    display: inline-block;
    top: 0;
    right: 0;
}
.header .t1 .saoma {
    margin-left: 20px;
    padding-left: 20px;
}
.header .t1 .saoma.wx {
    background: url(../../img/H/icon-wx.png) no-repeat 0px -4px;
}
.header .t1 .saoma.gz {
    background: url(../../img/H/icon-wx.png) no-repeat 0px -4px;
}
.header .t1 .saoma.dh {
    background: url(../../img/H/icon-wx.png) no-repeat 0px -4px;
}
.header .t1 .saoma.wx:hover {
    background: url(../../img/H/icon-wx.png) no-repeat 0px -36px;
}
.header .t1 .saoma.dh:hover {
    background: url(../../img/H/icon-wx.png) no-repeat 0px -36px;
}
.header .t1 .saoma.gz:hover {
    background: url(../../img/H/icon-wx.png) no-repeat 0px -36px;
}
.header .t1 .imgcode {
    position: absolute;
    z-index: 99;
    padding: 12px 10px 1px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 10px 0px #ccc;
    display: none;
}
.header .t1 .imgcode img{
    width: 100px;
}
.header .t1 .imgcode p.tex{
    line-height: 27px;
    height: 27px;
    font-size: 13px;
    text-align: center;
}
.header .t1 .imgcode i{
    width: 0;
    height: 0;
    border-width: 0 15px 10px;
    border-style: solid;
    border-color: transparent transparent #fff;
    margin: 40px auto;
    position: absolute;
}
.header .t1 .imgcode.code1{
    right: 240px;
    top: 35px;
    padding-bottom: 10px;
}
.header .t1 .imgcode.code2{
    right: 108px;
    top: 35px;
    padding-bottom: 10px;
}
.header .t1 .imgcode.code3{
    right: 5px;
    top: 35px;
    padding-bottom: 10px;
}
.header .t1 .imgcode.code3 i{
    top: -47px;
    right: 41px;
}
.header .t1 .imgcode.code1 i{
    top: -47px;
    right: 44px;
}
.header .t1 .imgcode.code2 i{
    top: -47px;
    right: 42px;
}



.header .top2 .zhanghu {
    float: right;
    width: 150px;
    border:1px solid #C3C3C3;
    border-radius: 5px;
    height: 40px;
    margin-left: 30px;
    margin-top: 25px;
    position: relative;
    cursor: pointer;
}
.header .top2 .zhanghu .i1 {
    display: block;
    width: 18px;
    height: 18px;
    background: url(../../img/H/zhanghu.png) no-repeat 0 0;
    position: absolute;
    top: 12px;
    left: 15px;
}
.header .top2 .zhanghu span {
    font-size: 16px;
    line-height: 40px;
    margin-left: 40px;
    font-weight: 600;
    color: #3E3E3E;
}
.header .top2 .zhanghu .i2 {
    display: block;
    width: 18px;
    height: 18px;
    background: url(../../img/H/you.png) no-repeat 0 0;
    position: absolute;
    top: 12px;
    right: 15px;
}
.header .zh-y {
    width: 182px;
    height: 235px;
    background: #fff;
    position: absolute;
    right: 0;
    top: 65px;
    z-index: 20;
    border-radius: 5px;
    box-shadow: 1px 3px 5px 0px #ccc;
    display: none;
}
.header .zh-y .members {
    height: 70px;
    line-height: 71px;
    border-bottom: 1px solid #ECECFB;
}
.header .zh-y .members img {
    width: 40px;
    border-radius: 50%;
    float: left;
    margin-top: 18px;
    margin-left: 10px;
}
.header .zh-y .members a {
    float: left;
    color: #3583E9;
    margin-left: 10px;
}
.header .zh-y .zh-list {
    line-height: 37px;
    margin-top: 3px;
    color: #333;
}
.header .zh-y .zh-list li{
    text-align: left;
    float: none;
    width: auto;
}
.header .zh-y .zh-list li a{
    font-size: 15px;
    display: inline;
    line-height: 37px;
    font-weight: normal;
}
.header .zh-y .zh-list .xxzx i {
    background: url(../../img/H/icon-new.png) no-repeat;
    display: inline-block;
    width: 38px;
    height: 15px;
    float: right;
    position: relative;
    top: 14px;
}
/*首页登录注册框*/
.loginreg,.afterloginreg{
    background-color: rgba(0,0,0,.3);
    border-radius: 5px;
    overflow: hidden;
    width: 300px;
    height: 351px;
    float: right;
    border: 1px solid #C3C3C3;
    z-index: 5;
    position: absolute;
    top: 150px;
    right: 0;
}
.loginreg .login_reg_title {
    line-height: 40px;
    font-size: 16px;
    position: relative;
}
.loginreg .login_reg_title a{
    color: #fff;
    text-align: center;
    float: left;
    width: 50%;
    box-sizing: border-box;
    border-bottom: 1px solid #88909b;
    background: rgba(121,133,139,.6);
    cursor: pointer;
}
.loginreg .login_reg_title a.on {
    border-bottom: none;
    background: none;
}
.loginreg .form {
    width: 100%;
    margin: 60px auto 25px;
    height: auto;
    text-align: center;
    overflow: hidden;
}
.loginreg .form ul li {
    padding-bottom: 15px;
    font-size: 12px;
    height: auto;
    position: relative;
    /* overflow: hidden; */
}
.loginreg .form ul li .yzm{
    position: absolute;
    right: 28px;
    top: 0;
    width: 121px;
    height: 35px;
    border: 1px solid #ccc;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    cursor: pointer;
}

.loginreg .form ul li input[type=text],
.loginreg .form ul li input[type=password]{
    padding: 0px 5px;
    width: 210px;
    height: 35px;
    color: #666;
    border: 1px solid #ddd;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    padding-left: 30px;
    font-size: 12px;
    outline: none;
}
.loginreg .form .login_text{
    background: #fff url(../../img/H/user.png) no-repeat 8px 7px;
}
.loginreg .form .login_pass {
    background: #fff url(../../img/H/pwd.png) no-repeat 8px 8px;
}
.loginreg .form .login_yzm {
    background: #fff url(../../img/H/yanzhengma.png) no-repeat 8px 8px;
}
.loginreg .form .login_tuijian {
    background: #fff url(../../img/H/tuijian.png) no-repeat 7px 7px;
}
.loginreg .form .fleft {
    display: block;
    float: left;
}
.loginreg .form .fright {
    display: block;
    float: right;
}
.loginreg .form #j-submit-btn{
    width: 247px;
    height: 35px;
    padding: 0;
    background-color: red;
    border: none;
    color: #fff;
    border-radius: 5px;
}
.loginreg #sendSMS {
    color: #fff;
    background-color: #0096fd;
    border-radius: 3px;
    width: 121px;
    line-height: 37px;
    text-align: center;
    float: left;
    margin-left: 10px;
    right: 27px;
    top: 0;
    position: absolute;
    border: none;
    cursor: pointer;
}




.zjbz {
    border: 1px solid #e0e0e0;
    background: #fff;
    /*position: absolute;
    top: -62px;
    left: 50%;
    margin-left: -600px;*/
    margin-top: -55px;
}
.zjbz a {
    display: block;
    float: left;
    width: 25%;
    box-sizing: border-box;
    padding: 25px 0;
    height: 146px;
    text-align: center;
    color: #666;
    transition: .3s;
    -moz-transition: .3s;
    -webkit-transition: .3s;
}
.zjbz a dl {
    border-right: 1px solid #eeeeee;
    line-height: 24px;
    font-size: 14px;
}

.zjbz a:hover {
    border-bottom: 4px solid red;
    color: red;
}

.icons2 {
    display: inline-block;
    background: url(../../img/H/icons2.png);
}
.zjbz .icons2 {
    width: 54px;
    height: 50px;
    position: relative;
    transition: .3s;
    -moz-transition: .3s;
    -webkit-transition: .3s;
}
.zjbz a dl i.i1 {
    background-position: -170px -290px;
}
.zjbz a:hover dl i.i1 {
    background-position: -170px -340px;
}
.zjbz a:hover dl i.i2 {
    background-position: -224px -340px;
}
.zjbz a dl i.i2 {
    background-position: -224px -290px;
}
.zjbz a:hover dl i.i3 {
    background-position: -278px -340px;
}
.zjbz a dl i.i3 {
    background-position: -278px -290px;
}
.zjbz a:hover dl i.i4 {
    background-position: -332px -340px;
}
.zjbz a dl i.i4 {
    background-position: -332px -290px;
}


.pb4 {
    padding: 0px;
    height: 360px;
    background: #fff;
    overflow: hidden;
   
}
.index_mod4 {
    overflow: hidden; 
    border:1px solid #f1f1f1;
}

.index_mod4 .mod_right {
    background: url(../../img/H/extend/img-market.jpg) no-repeat 0px 0px;
    width: 300px;
    height: 358px;
    float: left;
    position: relative;
}

.index_mod4 .mod_right .right-tit{
    font-size: 32px;
    height: 35px;
    line-height: 35px;
    width: 100%;
    margin: 76px 0px;
    color:#fff;
    text-align: center;
}
.index_mod4 .mod_right .s_menu {
    position: absolute;
    top: 165px;
    left: 91px;
}

.index_mod4 .mod_right .s_menu li {
    margin-bottom: 10px
}

.index_mod4 .mod_right .s_menu li a {
    display: block;
    height: 38px;
    line-height: 38px;
    border-style: solid;
    border-color: #fff;
    border-width: 1px;
    background-color: rgba(255, 255, 255, .1);
    font-size: 18px;
    color: #fff;
    -webkit-transition: all .1s ease-in-out;
    -moz-transition: all .1s ease-in-out;
    -ms-transition: all .1s ease-in-out;
    transition: all .1s ease-in-out;
    text-decoration: none;
    cursor: pointer;
    padding-left: 20px;
    padding-right: 20px;
    width: 73px;
}

.index_mod4 .mod_right .s_menu li.current a {
    color: #fc8819;
    background: #fff
}

.index_mod4 .mod_left {
    height: 357px;
    float: left;
}

.index_mod4 .mod_left .hq_con {
    width: 575px;
    border-right: solid 1px #eeeeee;
    height: 357px;
    float: left;
    overflow: hidden;
}

.index_mod4 .mod_left .hq_sv {
    width: 317px;
    float: left;
}

.index_mod4 .mod_left .hq_con h1 {
    font-size: 36px;
    margin-top: 30px;
    margin-left: 30px;
    margin-bottom: 30px;
}

.index_mod4 .mod_left .hq_img {
    margin-left: 20px;
}

.index_mod4 .mod_left .hq_txt {
    margin: 16px 0 13px 56px;
}

.index_mod4 .mod_left .hq_txt a {
    background: #d5e7f6;
    font-size: 14px;
    margin-right: 10px;
    padding: 5px 28px 5px 28px;
    color: #4a4949;
    text-decoration: none;
}

.index_mod4 .mod_left .hq_txt a.cur {
    font-size: 14px;
    background-color: #02A1ED;
    color: #fff;
}

.index_mod4 .mod_left .hq_st .hq_a1 {
    padding-left: 80px;
    border-bottom: solid 1px #e4e4e4;
    padding-top: 20px;
    overflow: hidden;
    padding-bottom: 20px;
}

.index_mod4 .mod_left .hq_st .hq_a1 li {
    float: left;
    font-size: 18px;
    line-height: 36px;
}

.index_mod4 .mod_left .hq_st .hq_a1 li.sv {
    float: left;
    font-size: 24px;
}

.index_mod4 .mod_left .hq_st .hq_a1 li.ico {
    width: 35px;
    height: 36px;
    background-image: url(../../img/H/extend/index_tr_up.jpg);
    background-repeat: no-repeat;
    background-position: center center;
}

.index_mod4 .mod_left .hq_st .hq_a1 li.dw {
    width: 35px;
    height: 36px;
    background-image: url(../../img/H/extend/index_tr_down.jpg);
    background-repeat: no-repeat;
    background-position: center center;
}

.index_mod4 .mod_left .hq_st .hq_a1 li.icon-right {
    font-size: 12px;
    margin-right: 15px;
    height: 16px;
    line-height: 16px;
    width: 70px;
}
.index_mod4 .mod_left .hq_st .hq_a1.up li, .index_mod4 .mod_left .hq_st .hq_a1.up font{
    color: #FF3646;
}
.index_mod4 .mod_left .hq_st .hq_a1.down li, .index_mod4 .mod_left .hq_st .hq_a1.down font{
    color: #237C02;
}
.index_mod4 .mod_left .hq_st .gupiao {
    padding: 0px 0px 0px 19px;
    margin-top: 10px;
    border-top: solid 1px #e4e4e4;
    float: left;
    width: 100%;
}
.index_mod4 .mod_left .hq_st .gupiao table{
    width: 50%;
    float: left;
}
.index_mod4 .mod_left .hq_st .hq_aq1_xq {
    font-size: 12px;
    line-height: 28px;
    width: 95%;
    padding: 0 0 0 9px;
    float: left;
}

.index_mod4 .mod_left .hq_st .gupiao .title {
    height: 35px;
    line-height: 35px;
}

.index_mod4 .mod_left .hq_st .gupiao table tr td {
    height: 28px;
    line-height: 28px;
    font-size: 12px !important;
}

.index_mod4 .mod_left .hq_st .hq_aq1_xq p span {
    width: 50%;
    display: block;
    float: left;
    overflow: hidden;
    padding: 0px;
    margin: 0px;
}

.index_mod4 .mod_left .hq_st .hq_aq1_xq p span .xq-color {
    text-align: right;
    width: 40%;
    display: inline-block;
}
.index_mod4 .mod_left .hq_st .hq_aq1_xq.up font{
    color: #FF3646;
    font-size: 12px !important;
}
.index_mod4 .mod_left .hq_st .hq_aq1_xq.down font {
    color: #237C02;
}


.hhhhh .ptgg {
    width: 1200px;
    margin: 0 auto;
    height: 54px;
    line-height: 54px;
    background: #fff;
    border-radius: 27px;
    box-shadow: 4px 6px 18px 0px rgba(219,234,249,0.39);
    font-size: 14px;
    text-align: center;
    box-shadow: 3px 5px 6px 0px #ccc;
}
.hhhhh .ptgg .title {
    margin-left: 20px;
    float: left;
    color: #161924;
    font-size: 16px;
}
.hhhhh .ptgg .title span {
    color: #FC1539;
}
.hhhhh .ptgg .ggName {
    width: 303px;
}
.hhhhh .ptgg .marqueeA{
    color: #333;
    display: inline-block;
    margin-right: 100px;
}
.hhhhh .ptgg .fgx {
    width: 20px;
    float: left;
    color: #ddd;
}
.hhhhh .ptgg .basemore{
    float: right;
    margin-right: 15px;
}











.articleBox {
    width: 1200px;
    padding: 10px 20px 0;
    background: #f5f6f7;
    border-radius: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    overflow: hidden;
    margin-top: 15px;
    font-size: 14px;
}
.articleBox .left {
    float: left;
}
.articleBox .right {
    float: right;
}
.articleBox .news li a:hover,.articleBox .topic a:hover{
    color: #F65C54;
}
.articleBox .newsBox {
    width: 590px;
    min-height: 420px;
    box-shadow: 0 0 10px 0px #ccc;
    margin-bottom: 10px;
    margin-right: 10px;
    border-radius:15px
}
.articleBox .title {
    border-bottom: 1px #f0f0f0 solid;
    padding: 10px;
    line-height: 20px;
    font-size:18px;
    font-weight:600
}
.articleBox .title a {
    font-size: 14px;
    /*font-family: SimSun;*/
    line-height: 27px;
}
.title p {
    margin: 0 0 10px;
}
.articleBox .topNews {
    padding: 10px;
    overflow: hidden;
}
.articleBox .topNews .topic {
    width: 347px;
    line-height: 30px;
    height: 30px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.articleBox .topNews .content {
    width: 320px;
    font-size: 14px;
    color: #999;
}
.articleBox .clear {
    clear: both;
    font-size: 0px;
    line-height: 0px;
    height: 0px;
    overflow: hidden;
}
.articleBox .news {
    padding: 0 10px;
    padding-top: 0px;
    padding-right: 10px;
    padding-bottom: 0px;
    padding-left: 10px;
}
.articleBox ol, .articleBox ul {
    margin-top: 0;
    margin-bottom: 15px;
}
.articleBox .news li {
    line-height: 30px;
    border-bottom: 1px #ccc dotted;
    padding: 0 15px;
}
.pic img{
    box-shadow: 2px 2px 15px #635959;
    border-radius: 5px;
    height: 100px;
    width: 200px;
}






.partner_box {
    width: 100%;
    height: 300px;
    margin-top: 15px;
    background: url(../../img/H/index-friend.jpg) no-repeat center center;
}
.item_nav_common {
    padding: 60px 0 20px 0;
}
.item_nav_common .item_nav_tit {
    position: relative;
    width: 350px;
    height: 3px;
    background: #f0f0f0;
    margin: 0 auto 20px;
}
.item_nav_common .item_nav_tit h4 {
    position: absolute;
    left: 75px;
    top: -20px;
    background: #fff;
    color: #666666;
    font-size: 28px;
    font-weight: bolder;
    width: 200px;
    height: 40px;
    text-align: center;
    line-height: 40px;
}
.item_nav_common .item_nav_tit h4.f6f6f6 {
    background: #f6f6f6;
}
.item_nav_common p {
    color: #999999;
    font-size: 18px;
    line-height: 32px;
    text-align: center;
}
.partner_nav_box {
    position: relative;
    width: 1200px;
    margin: 0 auto;
    padding-bottom: 80px;
}
.partner_nav_box .commonB {
    position: absolute;
    width: 36px;
    height: 54px;
    top: 8px;
}
.partner_nav_box .commonB img {
    display: block;
    margin: 12px auto 0;
    cursor: pointer;
}
.partner_nav_box .leftB {
    left: 0;
}
.partner_nav_box .rightB {
    right: 0;
}
.partner_nav_box .commonB:hover {
    background: #e5e5e5;
}
#marquee1 {
    width: 1119px;
    height: 80px;
    overflow: hidden;
    margin: 0 auto;
}
#marquee1 ul li {
    float: left;
    width: 215px;
    height: 80px;
    padding: 0 5px;
}
#marquee1 ul li img {
    display: block;
    width: 200px;
    margin: 0 auto;
}









/*弹窗，txt_indexs文字模式,chz_indexs图片模式*/
.modal-mask{
    background: rgba(0,0,0,.5);
    position: fixed;
    z-index: 99998;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
}
.modalmain{
    z-index: 99999;
    border-radius: 15px;
    position: fixed;
    margin-left: -400px;
    left: 50%;
    top:15%;
    background-color: #fff;
}

.txt_indexs .modalmain{
    width: 520px;
    min-height: 400px;
    overflow: hidden;
}

.modalmain .modal-title{
    text-align: center;
    height: 39px;
    line-height: 39px;
    width: 100%;
    background-color: #0350DE;
    color: #fff;
    font-weight: bold;
    font-size: 16px;
}
.txt_indexs .modalmain .modal-cont{
    padding: 20px 20px 0;
}

.chz_indexs .modalmain .modal-cont img{
    width: 800px;
    height: 500px;   
}
.chz_indexs .modalmain .modal-bottom,.txt_indexs .modalmain .modal-bottom{
    text-align: center;
    position: absolute;
    bottom: 0;
    height: 40px;
    line-height: 40px;
    border-top: 1px solid #0350DE;
    width: 100%;
    font-size: 14px;
    cursor: pointer;
}
.chz_indexs .close{
    position: absolute;
    right: -30px;
    top: -18px;
    cursor: pointer;
}










.g-footer {
    min-width: 1200px;
    padding: 25px 0;
    font-size: 12px;
    color: #D8D8D8;
    background: #111111;
}
.g-footer-section {
    float: left;
    width: 180px;
}
.g-footer-title {
    font-size: 14px;
    margin: 14px 0;
}
.g-footer-list {
    margin: 15px 0;
}
.g-footer-link {
    padding: 5px 0;
    color: #fff;
}
.g-footer-section.wide {
    width: 300px;
}
.g-footer-section p {
    /* margin: 14px 0; */
    font-size: 17px;
    line-height: 20px;
    margin-bottom: 6px;
}
.g-footer-section .ftel i{
    display: inline-block;
    width: 60px;
    height: 60px;
    background: url(../../img/H/ftel.png) no-repeat -6px 0px;
}
.g-footer-section .ftel span{
    font-size: 25px;
    font-weight: bold;
    position: relative;
    top: -22px;
}
.g-footer-wx-qrcode img{
    width: 125px;
}
.g-footer-wx-qrcode .fwxtxt{
    text-align: center;
    font-size: 14px;
    margin-top: 5px;
    color: #F6F6F6;
}
.g-footer-safeguard {
    margin-top: 13px;
    padding: 6px 0;
    /*border-top: 1px solid #525252;*/
    border-bottom: 1px solid #525252;
}
.g-footer-safeguard .info:first-child {
    margin-right: 18px;
}
.g-footer-safeguard .info {
    float: left;
    margin-top: 7px;
}
.g-footer-safeguard .info .box {
    font-size: 12px;
    color: #D8D8D8;
    line-height: 18px;
}
.g-footer-copyright {
    padding-top: 27px;
    padding-bottom: 17px;
    font-size: 12px;
    line-height: 1;
}
.g-footer-safeguard .frfir{
    float: right;
}
.g-footer-safeguard .frfir a{
    width: 96px;
    background: url(../../img/H/163451.png) no-repeat;
    height: 37px;
    /* border: 1px solid red; */
    /*float: right;*/
    display: inline-block;
    background-size: 566px 82px;
    margin-left: 6px;
}
.g-footer-safeguard .frfir a:nth-child(1):hover{
    background-position: 0px -45px;
}
.g-footer-safeguard .frfir a:nth-child(2){
    background-position: -113px 0px;
}
.g-footer-safeguard .frfir a:nth-child(2):hover{
    background-position: -113px -45px;
}
.g-footer-safeguard .frfir a:nth-child(3){
    background-position: -227px 0px;
}
.g-footer-safeguard .frfir a:nth-child(3):hover{
    background-position: -227px -45px;
}
.g-footer-safeguard .frfir a:nth-child(4){
    background-position: -340px 0px;
}
.g-footer-safeguard .frfir a:nth-child(4):hover{
    background-position: -340px -45px;
}
.g-footer-safeguard .frfir a:nth-child(5){
    background-position: -453px 0px;
}
.g-footer-safeguard .frfir a:nth-child(5):hover{
    background-position: -453px -45px;
}















/*右侧浮动*/
.float {
    top: 25%;
    width: 60px;
    min-height: 153px;
    position: fixed;
    right: 3px;
    z-index: 999;
}
.float ul li {
    width: 60px;
    height: 66px;
    border-bottom: 0px solid #e6e6e6;
    padding: 0;
    margin-bottom: 4px;
    position: relative;
}

.float ul li a {
    position: absolute;
    right: 1px;
    top: 0px;
    transition: all 0.3s;
    color: #fff;
    line-height: 66px;
    overflow: hidden;
    font-size: 14px;
    background-position: center 6px;
    /* text-indent: 70px; */
}
.float ul li a .ke_tit {
    position: absolute;
    top: 42px;
    left: 0px;
    width: 60px;
    text-align: center;
    text-indent: 0;
    line-height: 0.9;
}
.float ul li a .ke_info {
    display: block;
    position: absolute;
    left: 60px;
    height: 66px;
    background: #fff;
    color: #FF465A;
    padding-right: 15px;
    padding-left: 10px;
    width: 131px;
    height: 66px;
    /* background: rgba(255,255,255,0.3); */
    box-shadow: 0px 2px 4px 0px rgba(255,70,90,0.3);
    font-size: 15px;
}
.flo_1 {
    width: 60px;
    height: 66px;
    display: block;
    background: #999 url(../../img/H/extend/float_5.png) no-repeat;
}
.flo_2 {
    width: 60px;
    height: 66px;
    display: block;
    background: #999;
}
.flo_2 .ke_tit {
    background: url(../../img/H/extend/dh.png) 10px 0 no-repeat;
    padding-top: 35px;
    top: 6px!important;
}
.flo_3 {
    width: 60px;
    height: 66px;
    display: block;
    background: #999 url(../../img/H/extend/wx.png) no-repeat;
}
.flo_4 {
    width: 60px;
    height: 66px;
    display: block;
    background: #999 url(../../img/H/extend/float_8.png) no-repeat;
}
.flo_w {
    width: 60px;
    height: 66px;
    display: block;
    background: #999 url(../../img/H/extend/app.png) no-repeat;
}
.flo_j {
    width: 60px;
    height: 66px;
    display: block;
    background: #999 url(../../img/H/extend/jy.png) no-repeat;
}
.flqrc{
    display: none;
    position: absolute;
    padding: 7px 7px;
    background: #fff;
    box-shadow: 1px 1px 10px 0px #ccc;
}
.flqrc img{ 
    width: 100px;
}
.flqrc.wxqrc{
    top: 210px;
    right: 63px;
}

.flqrc.appqrc{
    top: 280px;
    right: 63px;
}
.flqrc.jappqrc{
    top: 350px;
    right: 63px;
}
.flo_qq {
    width: 60px;
    height: 66px;
    display: block;
    background: #999 url(../../img/H/extend/qq.png) no-repeat;
}
.flqrc.qqqrc{
    width: 170px;
    text-align: center;
    top: 140px;
    right: 61px;
}

/*header*/
.red1{color: #f77e76;}
.header .top .tel-num {
    float: left;
    vertical-align: 5px;
    /*height: 34px;*/
}
.header .top .tel-num span {
    display: inline-block;
    height: 100%;
    font-size: 12px;
}
.header .top{
    height: 40px;
    line-height: 40px;
    display: block;
    color: #666666;
    border-bottom: 1px solid #F0F0F0;
    background: #ffffff;
}
.header .top .tel-num span {
    display: inline-block;
    height: 100%;
    font-size: 12px;
}
.header .top .quick-link {
    float: right;
    color: #dbdbdb;
    font-size: 12px;

}

.header .top .quick-link a {
    font-size: 12px;
    display: inline-block;
    margin-left: 30px;
    color: #666666;
}

.header .top .quick-link a:hover {
    color: #F65C54;
}
.header-reg {
    height: 25px;
    line-height: 25px;
    border: 1px solid #FF5D5B;
    border-radius: 5px;
    padding: 0px 10px;
}

/*banner-login*/
.banner .float-box {
    position: absolute;
    z-index: 2;
    right: 0;
    top: 30px;
    background-color: #ffffff;
    width: 240px;
    height: 260px;
    padding: 30px 30px 0 30px;
    text-align: center;
    border-radius: 15px;
}

.banner .float-box .text {
    font-size: 14px;
    color: #333333;
    line-height: 2;
}

.banner .float-box .text-large {
    font-size: 26px;
    font-weight: 700;
    line-height: 2.2;
    color: #172a88;
}

.banner .float-box .operate {
    margin: 25px 0;
}

.banner .float-box .operate .btn {
    font-size: 16px;
    line-height: 2;
}

.banner .float-box .extra-text a {
    color: #172a88;
}

.banner .float-box .operate .btn {
    color: #fff;
    display: inline-block;
    background-color: #172a88;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    border: none;
    width: 216px;
    font-weight: 400;
    padding: 6px 12px;
    cursor: pointer;
    border-radius: 5px;
}
.banner .float-box .operate .btn:hover{
    background-color: #967850;
}

/*notice*/
.notice {
    height: 50px;
    line-height: 50px;
    padding: 0px;
    background: #f5f6f7;
    border-bottom: 1px solid #ddd;
    overflow: hidden;
}

.noticei {height: 50px;line-height: 50px;overflow: hidden;}
.notice .notice-tit {
    color: #959595;
    display: block;
    float: left;

    font-size:14px;
    height: 50px;
    line-height: 50px;
    width: 85px; 
    padding-left: 10px; 
}
.notice .notice-tit span{
    color: #e3e5e4;
}
.notice-left {
    width: 950px;
    overflow: hidden;
    position: relative;
    float: left;
}
.notice-left .bd {
    width: 100%;
}
.notice-left .infoList li {
    height: 50px;
    line-height: 52px;
    font-size: 14px; text-align: right;
    width:100%;
    float: left; 
}
.notice-left .infoList li a{
    color:#959595;float: left
}
.notice-left .infoList li a:hover{
    color: red;
}
.notice-left .infoList li span{
    font-size: 14px;
    color: #959595;
}
.notice .notice-more {
    color: #959595;
    float: right;
    font-size: 14px;margin-top:-3px;
    
    border-radius: 16px;
    height: 25px;
    line-height: 25px;
    margin-top:11px;
    margin-right: 20px;
    text-align: center;
    padding:0px 9px;
}
.notice .notice-more a {
    color: #959595; 
}
.notice .notice-more a:hover {
    color: red;
}
.noticei i{
    background: url(../../img/H/extend/top-gg.png) no-repeat right center;
    width: 25px;
    height: 100%;
    display: inline-block;
    float: left;
}
/*bianbian*/
.index_white_bg{background: white;width: 100%;}
.fleft {
    display: block;
    float: left;
}
.fright {
    display: block;
    float: right;
}
.bian-title{width: 200px;text-align: left;}
.pb01 {
    background: #fff;  
    float: left;   
    width: 1200px;
    border-bottom: 1px solid #dddddd;
}
.pb01   li {
    width: 240px;
    float: left;
    
    padding:10px 0;
    height: 74px;
    overflow: hidden; 
    text-align: center;
 
 }

#i-two{
    font-size: 12px;
    color:#666;
    margin-top: 7px;
    margin-left: 5px;
}

.pb01  li:last-child {
    background: none;
}

.pb01 li i {
    width: 16px;
    height: 16px;
    display: block;
    margin-left: 12px;
    margin-top: 30px;
    margin-right: 9px; 
    background:url(../../img/H/extend/dw_Cbg.png) no-repeat;
    background-position: 0 -329px;
}

.pb01  li span {
    font-size: 18px;
    color: #172a88;
    padding-top: 3px;
    float: left;
    margin-left: -1px;
    font-weight: bold;
}

.pb01  li strong {
    display: block;
    font: 16px/18px "Microsoft YaHei", tahoma, arial;
    color: #777;
    font-weight: 600;
   
    padding-top: 20px;
}

.pb01  li strong span {
    font-size: 14px;
    color: #666;
    font-weight: 300;
    padding-top: 2px;
    margin-top: -5px;
    display: block;
    width: 100%;
}
 
.pb01  li span span {
    font-size: 14px;
    float: right;
    margin-left: 2px;
    margin-top: -2px;
    color:#666;
}

/*5items*/
.newbee {
    padding: 30px 0;
    background-color: #fff;
    margin-bottom: 25px;
}

.newbee .item {
    width: 23.1%;
    float: left;
    padding-left: 20px;
    cursor:pointer;
}

.newbee .item .figure {
    float: left;
    margin-right: 15px;
}

.newbee .item .item-body {
    overflow: hidden;
}


.newbee .item:hover .item-body div {
    color:#172a88;
}

.newbee .item .item-body .title {
    font-size: 18px;
    color: #333333;
    line-height: 2;
}

.newbee .item .item-body .text {
    color: #999999;
}

.newbee .item:last-child .item-body {
    border: none;
}

.newbee .figure {
    height: auto;
    overflow: hidden
}

.newbee .item:hover img{
    -webkit-transform: rotateY(360deg);
    -moz-transform: rotateY(360deg);
    -ms-transform: rotateY(360deg);
    -o-transform: rotateY(360deg);
    transform: rotateY(360deg)
}

.newbee .item img {
    width: 60px;
    height: 60px;
    margin-right: 24px;
    background-position: -122px -137px;
    -webkit-transition: all .4s linear;
    -moz-transition: all .4s linear;
    -ms-transition: all .4s linear;
    -o-transition: all .4s linear;
    transition: all .4s linear
}
.index-tezheng {
    background-color: #FFF;
}

.index-tezheng .partOne {
    background-color: #FFF;
    padding: 20px 0;
}

.index-tezheng .partOne .wrap {
    overflow: visible
}

.index-tezheng .partOne .list {
    font-size: 0;
    -webkit-text-size-adjust: none
}

.index-tezheng .partOne .partItem {
    width: 20%;
    height: 190px;
    display: inline-block;
    text-align: center;
    font-size: 18px;
    color: #333333;
    padding-top: 10px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    /* font-weight: bold; */
}

 .index-tezheng .partOne .partItem:hover {
    box-shadow: 0 0 10px rgba(0,0,0,.2);
}

.index-tezheng .partOne .partItem .icon {
    display: inline-block;
    width: 100px;
    height: 100px;
    background: url(../../img/H/extend/dw_Cbg.png) no-repeat;
}

.index-tezheng .partOne .partItem .little {
    font-size: 14px;
    color: #999999;
    padding-top: 10px;
    font-weight: normal;
}

.index-tezheng .partOne .moreLine .icon {
    background-position: -198px -58px;
}

.index-tezheng .partOne .safe .icon {
    background-position: -93px -58px;
}

.index-tezheng .partOne .deal .icon {
    background-position: -304px -58px;
}

.index-tezheng .partOne .short .icon {
    background-position: 14px -58px;
}
.index-tezheng .partOne .zyfu .icon {
    background-position: -411px -58px;
}
/*stock*/
.plan{ margin: -20px auto;height: auto;overflow: hidden;padding: 20px 0;}
.plan .plan-right {width: 100%;}
.plan .plan-right .text{padding: 60px 0 0 90px;background: none;float: left;}
.plan .plan-right .text p{font-size: 16px;}
.plan .plan-right .text p:nth-child(1){font-size: 22px;}
.plan .plan-right .text p:nth-child(3) span{font-size: 20px;color: #ff6666;border-bottom: 2px solid #ff6666;padding-bottom: 5px;margin-right: 5px}
.plan .plan-right .phone{float: left;margin-top: 55px;margin-right: 130px;margin-left: 6px;}
.plan .plan-right .code{float: left;margin-top: 60px;text-align: center;}
.plan .plan-right .code img{margin-bottom: 36px;}
.plan .plan-right .code .btn{width: 135px;display: block;border-radius: 0;height: 35px;line-height: 35px;margin-bottom: 17px;position: relative;text-align: left;text-indent: 44px;font-size: 12px;}
.plan .plan-right .code .btn::before{ content: ''; display: block; width: 18px; height: 24px; background-repeat: no-repeat; position: absolute; left: 13px; top: 4px;}
.plan .plan-right .code .btn:nth-child(2)::before{ background-position: -57px 0}

.plan .plan-left {  float: left; width: 180px; height: 401px; position: relative; padding: 10px 20px;
background: #ff6789; 
background: -webkit-linear-gradient(-45deg, rgb(255, 159, 137), #ff4c89); 
background: -linear-gradient(-45deg, rgb(255, 159, 137), #ff4c89);
}

.plan .plan-left h2 {padding: 20px 0; text-align: center;font-size: 24px; border-bottom: 1px solid #ffffff;color: #fff;}
.plan .plan-left ul { padding-top: 10px;}
.plan .plan-left li { font-size: 15px; color: #fff; line-height: 30px;text-align: center;}
.plan .plan-left p {  text-align: center; margin-top: 20px;}

.plan .plan-right .plan-right-list {
   float: left;
    width: 343px;
    text-align: center;
    
    margin-right: 82px;    
}
.plan .plan-right .plan-right-list:last-child{ margin-right: 0;}


.plan .btn1 {
    background-color: #f6a665;
    border-bottom: 4px solid #f18b38;
}
.plan .btn2 {
    background-color: #7edaf2;
    border-bottom: 4px solid #40c9ec;
}
.plan .btn3 {
    background-color: #899be7;
    border-bottom: 4px solid #6d84e1;
}
.plan .btn1:hover {
    background-color: #ffbb84;
    border-bottom-color: #f6a665;
    text-decoration: none;
}
.plan .btn2:hover {
    background-color: #6ed7f2;
    border-bottom-color: #54c2de;
    text-decoration: none;
}
.plan .btn3:hover {
    background-color: #acbcff;
    border-bottom-color: #899be7;
    text-decoration: none;
}
.plan .project {
    width: 350px;
    margin: 0 auto;
    background: #FFF;    
}
.plan .project:hover{box-shadow:0 0px 20px rgba(0, 0, 0, 0.25);}

.plan .project-title {
    width: 100%;
    height: 68px;
    color: #666666;
    border-bottom: 1px dotted #ccc;;
}
.plan .project .project1,.plan .project .project2,.plan .project .project3 {
    background: #ffffff;
}

.plan .project-title strong {
    display: block;    
    font-size: 20px;
    font-weight: 100;
    line-height: 72px;

   }
.plan .project-title span {
    display: block;
    font-size: 14px;
    color: #D0D0D0;
    padding-top: 5px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}
.plan .project .project-intro {
    padding: 20px 50px 50px 50px;

    font-size: 16px;
    line-height: 30px;
    color: #999;
}
.plan .project .project-intro .line-x {
    width: 100%;
    height: 1px;
    border-top: 1px dotted #ccc;
    margin-top: 10px;
    padding-bottom: 10px;
}
.bule {
    color: #01a7ff;
}
.yello {
    color: #ffae00;
}
.fs40 {
    font-size: 28px;
}

.chengse {
    color: #ff0b00;
}
.mb30 {margin-bottom: 30px !important;}
.right-btn-b,.right-btn-y,.right-btn-r {
    border-radius: 4px;
    padding: 12px 74px;
    text-align: center;
    margin-top: 7px;
    width: 210px;  line-height: 30px; border-radius: 3px; 
}
.right-btn-b{ border: 1px solid #0198ff; color: #0198ff;}
.right-btn-y{ border: 1px solid #eea915; color: #eea915;}
.right-btn-r{ border: 1px solid #ff6332; color: #ff6332;}

.right-btn-b:hover{ border: 1px solid #0198ff; color: #0198ff;}
.right-btn-y:hover{ border: 1px solid #eea915; color: #eea915;}
.right-btn-r:hover{ border: 1px solid #ff6332; color: #ff6332;}

.in-icon01,.in-icon02 ,.in-icon03 {width: 255px;
    text-align: center;
    height: 63px;
    line-height: 63px;
    color: #fff;
    font-size: 22px;   
    display: block; margin: 0 auto;  overflow: hidden;   background: url(../../img/H/extend/commons1.png) no-repeat;
    background-repeat: no-repeat;}
.in-icon01 { background-position: -2px -13px}
.in-icon02 { background-position: -337px -13px;}
.in-icon03 { background-position: -664px -14px;}
.tl40{ line-height: 40px;}
.t-l {
    text-align: left !important;
}
.fs14 {
    font-size: 14px;
}
.right {
    float: right;
}

/**/
.plan-right .pr-left{
    width: 300px;
    height: 100%;
    float: left;
    /*border: 1px solid;*/
}
.plan-right .pr-left .prl-block{
    width: 83%;
    /*border: 1px solid;*/
    margin: 0 auto;
    height: 100%;
}
.plan-right .pr-left .prl-block .prl-title{
    font-size: 21px;
    height: 52px;
    line-height: 52px;
    border-bottom: 1px solid;
}
.plan-right .pr-left .prl-block .prl-li{
    height: 310px;
    overflow: hidden;
    /*border: 1px solid red;*/
}
.plan-right .pr-left .prl-block ul li{
    font-size: 16px;
    padding: 9px 0px;
}
.plan-right .pr-left .prl-block ul li .li-l{
    float: left;
}
.plan-right .pr-left .prl-block ul li .li-r{
    float: right;
}
.plan3 .app-d{
    width: 300px;
    height: 100%;
    float: left;
    /*border: 1px solid;*/
    margin-left: 150px;
}
.plan3 .app-d .jxrjxz{
    height: 52px;
    line-height: 52px;
    text-align: center;
    font-size: 21px;
}
.plan3 .app-d .jxrjp{
    width: 70%;
    margin: 0 auto;
    text-align: center;
    line-height: 22px;
    margin-top: 25px;
    font-size: 15px;
}
.plan3 .app-d .jxrjlogo{
    width: 100px;
    margin: 0 auto;
    margin-top: 25px;
}
.plan3 .app-d .jxrjlogo img{
   width: 100%;
}
.plan3 .app-d .jxrjpc{
    width: 135px;
    margin: 0 auto;
    margin-top: 35px;
}
.plan3 .app-d .jxrjpc button{
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 10px;
    background: #CB0000;
    color: #fff;
    cursor: pointer;
}
.plan3 .plan-left {
    background: url() no-repeat;
    float: left;
    width: 171px;
    position: relative;
    padding: 10px 20px;
}
.plan3 .plan-left {
    height: 345px;
}
.plan3 .plan-left h2 {
    padding: 20px 0;
    text-align: center;
    font-size: 24px;
    border-bottom: 1px solid rgba(255,255,255, .4);
    color: #fff;
    text-shadow: 0 2px 3px #ff9900;
}
.plan3 .plan-right {
    float: left;
    background-color: #fff;
    width: 989px;
    height: 364px;
    color: #666666;
}
.plan3 .plan-right .text {
    padding: 60px 0 0 95px;
    background: none;
    float: left;
}
.plan3 .plan-right .text p:nth-child(1) {
    font-size: 22px;
}
.plan3 .plan-right .text p {
    font-size: 16px;
}
.plan3 .plan-right .phone {
    float: left;
    margin-top: 17px;
    margin-right: 130px;
    margin-left: 6px;
}
.plan3 .plan-right .code {
    float: left;
    margin-top: 50px;
    text-align: center;
    margin-left: 63px;
}
.plan3 .plan-right .code img {
    margin-bottom: 36px;
}
.plan3 .plan-right .code .btn {
    width: 172px;
    display: block;
    background: #000000;
    color: #fff;
    border-radius: 0;
    height: 40px;
    line-height: 40px;
    border-radius: 3px;
    margin-bottom: 17px;
    position: relative;
    text-indent: 44px;
    font-size: 12px;
}
.plan3 .plan-right .code .btn:nth-child(2) {
    background: rgba(42, 171, 0, 0.97);
}
.plan3 .plan-right .code .btn::before {
    content: '';
    display: block;
    width: 18px;
    height: 24px;
    background: url();
    background-repeat: no-repeat;
    position: absolute;
    left: 38px;
    top: 10px;
    background-size: 47px;
}
.plan3 .plan-right .code .btn:nth-child(2)::before {
    background-position: -22px 0;
}

/**/
.pb6 {
    height: 350px;
    overflow: hidden;
    margin-bottom: 25px;
    background: #fff;
    border: 1px solid #eeeeee;
}

.pb6 .pb6-r .gonggaoBox .dd p span.active {
    color: #CB0000;
}
.pb6 .pb6-r .gonggaoBox .hd{
    float: right;
}
.pb6 .pb6-r .gonggaoBox .bd{
    float: right;
}

.pb6 .pb6-r .gonggaoBox #hd_two{
    border-right: 1px solid #fff;
}
.pb6 .pb6-r .gonggaoBox #bd_two{
    border-left: 1px solid #fff;
    padding: 25px 0px 0 30px;
}

.pb6 .pb6-r .gonggaoBox .dd{
    width: 450px;
    height: 348px;
    border-left: 1px solid #f4f3f4;
    box-sizing: border-box;
}
.pb6 .pb6-r .gonggaoBox .dd p{
    width: 97%;
    height: 74px;
    border-bottom: 1px solid #f5f5f5;
    margin-left: 0px;
}
.pb6 .pb6-r .gonggaoBox .dd .dd_line{
    width: 100%;
    height: 80px;
    border-bottom: 1px solid #eee;
    background: #fff;
}
.pb6 .pb6-r .gonggaoBox .dd p span{
    float: left;
    padding: 0 20px 0 45px;
    padding-top: 30px;
    font-size: 20px;
    color: #333333;
    
    background-repeat: no-repeat;
    background-position: 23px 31px;
    background-size: 26px;
    cursor: pointer;
}
.pb6 .pb6-r .gonggaoBox .dd p a{
    float: right;
    padding-left: 10px;
    padding-right: 50px;
    padding-top: 33px;
    color: #fe7b20;
    font-size: 12px;
}
.pb6 .pb6-r .gonggaoBox .dd p a:hover{
    color: red;
}
.pb6 .pb6-r .gonggaoBox .dd>span{
    float: left;
    width: 390px;
    line-height: 37px;
    margin-top: 5px;
    padding-left: 30px;
    height: 260px;
}

.pb6 .pb6-r .gonggaoBox .dd>span > li{
    padding-left: 0;
 }

.pb6 .pb6-r .gonggaoBox .dd>span > li a{
   color: #666666;
 }
.pb6 .pb6-r .gonggaoBox .dd>span > li a{color:#666;transition: all 1s ease 0s;border-radius:3px;}
.pb6 .pb6-r .gonggaoBox .dd>span > li:hover a{padding:2px 8px;color:red;transition: all 0.3s ease 0s;box-shadow:2px 2px 2px 3px #d1d1d1;}

.date{
    float: right;
    color: #999999;
}
.down_load{
        float: left;
    width: 374px;
    height: 348px;
    border-right: 0;
    text-align: center;
    border-left: 1px solid #eee;
}

.down_load .load_top{
    float: left;
    width: 100%;
    padding-top: 29px;
    /* background: url('../img/load_top.png') center 28px no-repeat; */
}
.down_load .load_top span{
    font-size: 18px;
    /* color: #fff; */
}
.down_load .load_top span.text-red{
    font-size: 14px;
}
.down_load .load_top p{
    font-size: 14px;
    color: #666666;
    /* width: 190px; */
    /* margin-left: 52px; */
    margin-top: 20px;
    line-height: 25px;
}
.down_load .load_top i {
    float: left;
    background: url(../../img/H/extend/20180704181002.png) no-repeat -30px -15px;
    height: 106px;
    width: 145px;
    margin-top: 6px;
    margin-left: 137px;
}
.down_load.index-tuigg .load_top i {
    background: url(../../img/H/extend/20180704181033.png) no-repeat -41px -13px;
    margin-left: 131px;
}
.down_load  .load_top .btns-botton {
    width: 100%;
    display: block;
    float: left;
    margin-top: 0px;
    text-align: center;
}
.down_load  .load_top .btns-botton  .grab-btn{
    box-sizing: border-box;
    display:block;
    overflow:hidden;
    position:relative;
    margin:0px auto 0;
    border:1px solid #d99c00;
    border-radius:5px;
    -webkit-border-radius:5px;
    -moz-border-radius:5px;
    -ms-border-radius:5px;
    -o-border-radius:5px;
    display: block;
    width: 206px;
    height:40px;
    font-size:16px;
    line-height:40px;
    text-align:center;
    color:#bb9767;
    transition:color .2s ease-in-out;
    cursor:pointer;
    behavior: url(../../img/H/extend/ie-css3.htc); /* 通知IE浏览器调用脚本作用于'box'类 */
    z-index:9999;
}
.down_load  .load_top .btns-botton  .grab-btn em, .down_load  .load_top .btns-botton  .grab-btn i{display:block;overflow:hidden;position:absolute;top:0;left:0;width:100%;height:100%;font-style:normal;-webkit-transition:all .3s;-moz-transition:all .3s;transition:all .3s}
.down_load  .load_top .btns-botton  .grab-btn em{height:0;background: #bb9767;-webkit-transform:translateY(-50%) rotate(45deg);-moz-transform:translateY(-50%) rotate(45deg);transform:translateY(-50%) rotate(45deg);}
.down_load  .load_top .btns-botton  .grab-btn:hover em{height:230px}
.down_load  .load_top .btns-botton  .grab-btn:hover,.down_load  .load_top .btns-botton  .grab-btn:hover i{color:#fff}
.down_load  .load_top .btns-botton  .grab-btn.btn-gray{border-color:#ddd;color:#fff;background-color:#ddd}
.down_load  .load_top .btns-botton  .grab-btn.btn-gray:hover{background-color:#ddd}
.down_load  .load_top .btns-botton  .grab-btn.exch-btn{width:122px;margin:0}
.down_load  .load_top .btns-botton  .grab-btn.exch-btn:hover em{width:210px}

.down_load .load_top a {
    float: left;
    margin-top: 127px;
    color: #fb5474;
    margin-left: -205px;
    border: 1px solid #fb5474;
    padding: 11px 71px;
    border-radius: 4px;
    font-size: 16px;
}

/*友情链接*/
.pb7 {
    height: 417px;
    overflow: hidden;
    margin-bottom: 20px;
    background: #fff;
   
}
.pb7 .pb7-bottom .floor-hd {
    margin-top: 25px;
}
.pb7 .pb7-bottom .floor-hd h3 {
    float: left;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
    padding-left: 60px;
}
.yqlj {
    min-height: 250px;
    overflow: hidden;
    background: #fff;
    border: 1px solid #eeeeee;
    width: 1200px;
    margin: 0 auto 25px;
}

.linkM {
    min-height: 50px;
    display: block;
    padding: 0;
    position: relative;
    z-index: 0;
    margin-top: -1px;
    margin-bottom: 15px;
}

.linkM div {
    display: inline-block;
    width: 100%;
    padding-left: 30px;
}
.linkM div a{
   
    text-align: center;
    line-height: 70px;
    float: left;
    display: block; 
    margin-left: 29px;
    margin-bottom: 20px;
    margin-top: 30px;
}
.linkM div a:hover{
    box-shadow:0 0px 30px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 0px 30px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow:0 0px 30px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0px 30px rgba(0, 0, 0, 0.1); 
}
.linkM div a img{width: 193px;}
.linkPic img {
   
    float: left;

    text-align: center;
}
/*foot*/
.footer-module {
    clear: both;
    background: #ddd;
    color: #646364;
    border-top: 1px solid #eee;
    padding-bottom: 25px;
}
.footer-module .related-link {
    float: left;
    width: 410px;
   
    box-sizing: border-box;
    overflow: hidden;
    margin-top: 36px;
}
.footer-module .related-link div {
    float: left;
    font-size: 14px;
    text-align: center;
    color: #888;
    line-height: 32px;
    margin-right: 46px;
}
.footer-module .related-link div a{
   color: #646364;
   font-size:14px;
   height: 35px;line-height: 35px;display: block;
}
.footer-module .related-link div .first-row {
    font-size: 18px;
        color: #646364;
        font-weight: bold;
        text-align: left;
    
}
.footer-module .qr-code-module {
    float: left;
    width: 170px;
    box-sizing: border-box;
    overflow: hidden;
    margin-top: 36px;
    margin-left: 18px;
}
.footer-module .qr-code-module .qr-code-item {
    float: left;
}
.footer-module .qr-code-module .qr-code-item img {
    width: 120px;height: 120px;
   
}
.footer-module .qr-code-module .qr-code-item p {
    font-size: 14px;
    line-height: 33px;
    color: #646364;
    text-align: center;
}
.footer-module .service-num-module {
    float: right;
    text-align: right;
    width: 220px;
    margin-top: 36px;
    line-height: 20px;
    font-size: 14px;
    color:#646364;
}
.footer-module .service-num-module p{text-align: center;line-height: 25px;}
.footer-module .service-num-module img{
    height: 57px;
}
.footer-module .service-num-module .dianhua {
    font-size: 30px;
    color: #646364;
    padding: 10px 0;
    font-weight: bold;
}

.copyRight {height:auto; width: 100%; padding: 10px 0 0;background: #2C2C2C;overflow: hidden; }
.copyRight .text { width:40%; float: left; line-height: 35px; color:#cccccc;font-size:12px;text-align: left;margin-bottom: 10px;}
.copyRight .text p {height:23px;line-height: 23px;  margin-right: 20px; overflow:hidden;}
.copyRight .auth{
  text-align: right;
  width: 60%;float: left;
}
.copyRight .auth img{
  margin-right: 5px;width:109px;height:40px;
}
.copyRight .text2 { width:100%;height:50px;line-height: 50px; color:#cccccc;font-size:18px;text-align: center;}

.copyRight .text2 span {
    display: inline-block;
    margin-right: 20px;
}

.copyRight a{
    color: #545454;
        position: relative;
    /*top: -18px;*/
}
.copyRight span a{top: 2px;}