body{
    font-family: "Microsoft YaHei";
    color: #333333;
}
a,a:hover{
    text-decoration: none;
}
table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    text-align: center;
}

.agreement{
    width: 1024px;
    height: auto;
    min-height: 500px;
    margin: 0 auto;
    border: 1px solid #dcdcdc;
}
.lending_top_info{
    width: 1000px;
    height: auto;
    margin-left: 10px;
}
.lending_logo{
    width: 1000px;
    height: 100px;
    border-bottom: 2px solid #EEEEEE;
}
.lenging_left_logo{
    width: auto;
    height: 100px;
    float: left;
    padding: 0 5px;
    border-bottom: 2px solid #17a3d8;
}
.lenging_left_logo a{
    display: inline-block;
    margin-top: 16px;
}
.lending_ageeement_tit {
    width: 960px;
    height: 45px;
    line-height: 35px;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin: 0 auto;
    margin-top: 30px;
    border-bottom: 1px solid #EEEEEE;
}
.ageeement_content {
    width: 980px;
    height: auto;
    margin: 0 auto;
    margin-top: 25px;
    font-size: 14px;
    font-weight: bold;
    overflow: auto;
    font-family: Arial, Helve<PERSON>, sans-serif;
}
.ageeement_content p {
    line-height: 26px;
    font-size: 14px;
    font-weight: normal;
    margin: 0px;
}
.u-info{
    border-collapse: collapse;
    width: 100%;
    margin-top: 20px;
    margin-bottom: 10px;
}
.u-info td{
    border: 1px solid #333333;
    padding: 7px;
    color: #333333;
    font-weight: normal;
    font-size: 14px;
}
.ageeement_content strong,.font_lendig {
    font-size: 14px;
    font-weight: bold;
    font-family: Arial, Helvetica, sans-serif;
    margin-bottom: 10px;
    margin: 10px 0;
    display: block;
}
.htz-left {
    float: left;
}
.htz-right {
    float: right;
}
.htz img {
    float: left;
    margin-top: -80px;
    margin-left: 110px;
}


.zcxy .title{
    text-align: center;
    font-size: 24px;
    font-family: SimHei;
}