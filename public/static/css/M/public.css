@charset "utf-8";
/* CSS Document */
a{ color:#333;}
i,em{ font-style:normal}
img{border:none;}
div,ul,li,p,dl,dt,dd,h1,h2,h3,h4{ margin:0; padding:0;list-style:none; font-weight:normal;font-family:"微软雅黑";}
table,tr,td{ margin:0; padding:0}
input,textarea,select,label,button{font-family:"微软雅黑"; color:#333;outline:none;}
p{word-break:break-all;}
body a:active, body a:hover {
    text-decoration: none;
}
strong, b {
    font-weight: bold;
}
table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    text-align: center;
}

.btn {
    text-align: center;
    display: inline-block;
    border-radius: 4px;
    color: #fff !important;
    cursor: pointer;
    text-decoration: none !important;
}
input.btn, button.btn {
    width: 150px;
    height: 50px;
}
input, select, button, textarea {
    outline: none;
    border: 0;
}
.fl{float:left;}
.fr{float:right;}
.hide{display:none;}
.block{display:block;}
.clearfix:before,
.clearfix:after
{content: " ";display: table;clear: both;*zoom:1}
