﻿@charset "UTF-8";

*, *:before, *:after{padding:0;margin:0}
h1, h2, h3, h4, h5, h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit;
}
img { border: 0 none; vertical-align: middle; }
ul, li { list-style-type: none; }
input, select, button, textarea{ outline:none;border: 0 }
button { cursor: pointer; }
i, em, cite {font-style: normal; }



.wrap {
	margin: 0 auto;
	width: 1180px;
	text-align: left;
}

.extend-bg01,.extend-bg02,.extend-bg03,.extend-bg04,.extend-bg05,.extend-bg07,.extend-bg08,.extend-bg09,.extend-bg10,.extend-bg11 {
	min-width: 1180px;
}

.extend-bg01 {
	height: 130px;
	background: url(img/bg-01.jpg) center top no-repeat;
}

.extend-bg02 {
	height: 121px;
	background: url(img/bg-02.jpg) center top no-repeat;
}

.extend-bg03 {
	height: 141px;
	background: url(img/bg-03.jpg) center top no-repeat;
}

.extend-bg04 {
	height: 147px;
	background: url(img/bg-04.jpg) center top no-repeat;
}

.extend-bg05 {
	height: 244px;
	background: url(img/bg-05.jpg) center top no-repeat;
}

.extend-bg06 {
	height: 315px;
	min-width: 1180px;
	background: url(img/bg-06.jpg) center top no-repeat;
}

.extend-bg07 {
	height: 182px;
	background: url(img/bg-07.jpg) center top no-repeat
}

.extend-bg08 {
	height: 243px;
	background: url(img/bg-05.jpg) center top no-repeat;
}

.extend-bg09 {
	height: 402px;
	background: url(img/bg-09.jpg) center top no-repeat;
}

.extend-bg10 {
	height: 207px;
	background: url(img/bg-10.jpg) center top no-repeat;
}

.extend-bg11 {
	height: 200px;
	background: url(img/bg-11.jpg) center top no-repeat;
}

.btn-position {
	padding-top: 22px;
	text-align: center;
}

.word-request {
	padding-top: 95px;
	padding-left: 225px;
	color: #333;
	font-style: italic;
	font-size: 24px;
	line-height: 34px;
}

.word-request h1 {
	padding-bottom: 8px;
	color: #d9311f;
	font-weight: 700;
	font-size: 28px;
}

.word-request h1 span {
	display: inline-block;
	margin: 0 5px;
	width: 36px;
	height: 36px;
	border-radius: 36px;
	background: #d9311f;
	color: #f3da19;
	text-align: center;
	font-style: normal;
}

.word-white,.word-yellow1 {
	padding-left: 42px;
	font-weight: 700;
	font-style: italic;
}

.word-request span {
	color: #d9311f;
}

.word-white {
	padding-top: 18px;
	color: #fff;
	font-size: 22px;
}

.word-yellow1 {
	margin-top: 165px;
	color: #ff9;
	font-size: 24px;
}

.word-yellow2,.word-yellow3 {
	margin-top: 8px;
	color: #ff9;
	font-style: italic;
}

.word-yellow2 {
	padding-left: 42px;
	font-size: 21px;
}

.word-yellow3 {
	padding-left: 325px;
	font-weight: 700;
	font-size: 24px;
}

.btn-position2 {
	padding-top: 35px;
	text-align: center;
}
.btn-position2 .btn_img{
	display: inline-block;
	background: url(img/btn.png)no-repeat;
	width: 284px;
	height: 111px;
}
.word-request.request2 {
	padding-top: 58px;
}

.word-white2 {
	margin: 20px 40px;
	padding-top: 15px;
	border-top: 1px solid #cf760e;
	color: #fff;
	font-size: 18px;
	line-height: 25px;
}

.small-yellow {
	font-weight: 400;
	font-size: 20px;
}

.t_f_txt{ margin-top: 126px; }
.t_f_txt ul li{ font-size: 15px;font-weight: bold;color: #555; }