<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class InitDatabase extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $sql = file_get_contents(__DIR__ . '/init.sql');

        // 執行 SQL 查詢
        $this->execute($sql);
    }

    public function down(): void
    {
        $this->table('area')->drop()->save();
        $this->table('zh_acl')->drop()->save();
        $this->table('zh_ad')->drop()->save();
        $this->table('zh_admin_log')->drop()->save();
        $this->table('zh_article')->drop()->save();
        $this->table('zh_category')->drop()->save();
        $this->table('zh_extend_data')->drop()->save();
        $this->table('zh_friend')->drop()->save();
        $this->table('zh_future_global')->drop()->save();
        $this->table('zh_future_rateset')->drop()->save();
        $this->table('zh_global')->drop()->save();
        $this->table('zh_hetong')->drop()->save();
        $this->table('zh_inner_msg')->drop()->save();
        $this->table('zh_kefu')->drop()->save();
        $this->table('zh_manage')->drop()->save();
        $this->table('zh_member_bank')->drop()->save();
        $this->table('zh_member_info')->drop()->save();
        $this->table('zh_member_login')->drop()->save();
        $this->table('zh_member_money')->drop()->save();
        $this->table('zh_member_moneylog')->drop()->save();
        $this->table('zh_member_qd')->drop()->save();
        $this->table('zh_member_recharge')->drop()->save();
        $this->table('zh_member_withdraw')->drop()->save();
        $this->table('zh_members')->drop()->save();
        $this->table('zh_navigation')->drop()->save();
        $this->table('zh_realname_set')->drop()->save();
        $this->table('zh_recharge_set')->drop()->save();
        $this->table('zh_send_log')->drop()->save();
        $this->table('zh_slide')->drop()->save();
        $this->table('zh_sms_set')->drop()->save();
        $this->table('zh_stock_account')->drop()->save();
        $this->table('zh_stock_addfinancing')->drop()->save();
        $this->table('zh_stock_addmoney')->drop()->save();
        $this->table('zh_stock_borrow')->drop()->save();
        $this->table('zh_stock_drawprofit')->drop()->save();
        $this->table('zh_stock_global')->drop()->save();
        $this->table('zh_stock_rateset')->drop()->save();
        $this->table('zh_stock_renewal')->drop()->save();
        $this->table('zh_stock_stopfinancing')->drop()->save();
        $this->table('zh_tip')->drop()->save();
        $this->table('zh_transfer')->drop()->save();
        $this->table('zh_verifyip')->drop()->save();
        $this->table('zh_xsrw')->drop()->save();
    }
}
