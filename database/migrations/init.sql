-- MySQL dump 10.13  Distrib 8.0.27, for macos11 (arm64)
--
-- Host: 127.0.0.1    Database: peizi
-- ------------------------------------------------------
-- Server version	8.4.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `area`
--

DROP TABLE IF EXISTS `area`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `area` (
  `id` smallint unsigned NOT NULL AUTO_INCREMENT,
  `reid` smallint unsigned NOT NULL DEFAULT '0',
  `name` varchar(120) NOT NULL DEFAULT '',
  `sort_order` smallint unsigned NOT NULL DEFAULT '0',
  `is_open` tinyint unsigned NOT NULL DEFAULT '0',
  `domain` varchar(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`reid`,`sort_order`),
  KEY `is_open` (`is_open`,`domain`,`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=3414 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_acl`
--

DROP TABLE IF EXISTS `zh_acl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_acl` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(1024) NOT NULL DEFAULT '',
  `value` text CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `remark` varchar(1024) NOT NULL DEFAULT '',
  `add_time` int NOT NULL DEFAULT '0',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_ad`
--

DROP TABLE IF EXISTS `zh_ad`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_ad` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(1024) NOT NULL DEFAULT '',
  `content` text NOT NULL,
  `hide` tinyint NOT NULL DEFAULT '1',
  `add_time` int NOT NULL DEFAULT '0',
  `ad_type` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_admin_log`
--

DROP TABLE IF EXISTS `zh_admin_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_admin_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `admin_name` varchar(255) NOT NULL DEFAULT '',
  `deal_ip` varchar(255) NOT NULL DEFAULT '',
  `add_time` int NOT NULL DEFAULT '0',
  `deal_info` varchar(10240) NOT NULL DEFAULT '',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30924 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_article`
--

DROP TABLE IF EXISTS `zh_article`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_article` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '',
  `info` varchar(10240) NOT NULL DEFAULT '' COMMENT '文章简介',
  `keyword` varchar(10240) NOT NULL DEFAULT '' COMMENT '文章关键字',
  `content` text NOT NULL,
  `writer` varchar(255) NOT NULL DEFAULT '' COMMENT '作者',
  `parent_id` int unsigned NOT NULL DEFAULT '0',
  `is_hide` tinyint NOT NULL DEFAULT '1',
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  `art_click` int unsigned NOT NULL DEFAULT '0',
  `add_time` int unsigned NOT NULL DEFAULT '0',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `pid` (`parent_id`) USING BTREE,
  KEY `addtime` (`add_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2878 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_category`
--

DROP TABLE IF EXISTS `zh_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_category` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(40) NOT NULL,
  `content` text NOT NULL,
  `sort_order` int NOT NULL DEFAULT '0',
  `parent_id` int NOT NULL DEFAULT '0',
  `type_set` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1：单页面、2：列表页',
  `type_nid` varchar(255) NOT NULL DEFAULT '',
  `is_hidden` int unsigned NOT NULL DEFAULT '1' COMMENT '1：显示、2：隐藏',
  `create_time` datetime NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `pid` (`parent_id`),
  KEY `type_nid` (`type_nid`)
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_extend_data`
--

DROP TABLE IF EXISTS `zh_extend_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_extend_data` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(1024) NOT NULL DEFAULT '',
  `content` varchar(10240) NOT NULL DEFAULT '',
  `field1` varchar(1024) NOT NULL DEFAULT '',
  `field2` varchar(1024) NOT NULL DEFAULT '',
  `field3` varchar(1024) NOT NULL DEFAULT '',
  `field4` varchar(1024) NOT NULL DEFAULT '',
  `field5` varchar(1024) NOT NULL DEFAULT '',
  `type` varchar(255) NOT NULL DEFAULT '',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_friend`
--

DROP TABLE IF EXISTS `zh_friend`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_friend` (
  `id` int NOT NULL AUTO_INCREMENT,
  `link_txt` varchar(50) NOT NULL DEFAULT '',
  `link_href` varchar(500) NOT NULL DEFAULT '',
  `link_img` varchar(100) NOT NULL DEFAULT '',
  `link_order` int NOT NULL DEFAULT '0',
  `link_type` int NOT NULL DEFAULT '0',
  `is_show` int NOT NULL DEFAULT '1',
  `f_type` tinyint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_future_global`
--

DROP TABLE IF EXISTS `zh_future_global`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_future_global` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(11) NOT NULL DEFAULT '',
  `text` text NOT NULL,
  `name` varchar(100) NOT NULL DEFAULT '',
  `tip` varchar(255) NOT NULL DEFAULT '',
  `code` varchar(50) NOT NULL DEFAULT '',
  `order_sn` int NOT NULL DEFAULT '0',
  `is_hide` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_future_rateset`
--

DROP TABLE IF EXISTS `zh_future_rateset`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_future_rateset` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint NOT NULL,
  `info` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_global`
--

DROP TABLE IF EXISTS `zh_global`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_global` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(10) NOT NULL DEFAULT '',
  `text` text NOT NULL,
  `name` varchar(50) NOT NULL DEFAULT '',
  `tip` varchar(200) NOT NULL DEFAULT '',
  `order_sn` int NOT NULL DEFAULT '0',
  `code` varchar(30) NOT NULL DEFAULT '',
  `is_sys` tinyint unsigned NOT NULL DEFAULT '1',
  `is_show` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否显示 0：不显示 1：显示',
  `must` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_hetong`
--

DROP TABLE IF EXISTS `zh_hetong`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_hetong` (
  `id` int NOT NULL AUTO_INCREMENT,
  `hetong_img` varchar(500) NOT NULL DEFAULT '',
  `thumb_hetong_img` varchar(500) NOT NULL DEFAULT '' COMMENT '合同缩略图url',
  `add_time` int NOT NULL,
  `deal_user` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '公司名称',
  `dizhi` varchar(200) NOT NULL DEFAULT '' COMMENT '公司地址',
  `tel` varchar(50) NOT NULL DEFAULT '' COMMENT '公司电话',
  `content` text NOT NULL,
  `category` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1股票合同 2期货合同 3注册协议合同',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_inner_msg`
--

DROP TABLE IF EXISTS `zh_inner_msg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_inner_msg` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0',
  `title` varchar(1024) NOT NULL DEFAULT '',
  `msg` text NOT NULL,
  `send_time` int unsigned NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`,`id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=49039 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_kefu`
--

DROP TABLE IF EXISTS `zh_kefu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_kefu` (
  `id` int NOT NULL AUTO_INCREMENT,
  `number` varchar(50) NOT NULL DEFAULT '',
  `title` varchar(1024) NOT NULL DEFAULT '',
  `order` int NOT NULL DEFAULT '0',
  `is_show` int NOT NULL DEFAULT '1' COMMENT '1显示 2隐藏',
  `type` int NOT NULL COMMENT '1：qq号；2：客服电话',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_manage`
--

DROP TABLE IF EXISTS `zh_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_manage` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_name` varchar(120) NOT NULL DEFAULT '',
  `user_pass` varchar(120) NOT NULL DEFAULT '',
  `u_group_id` int NOT NULL DEFAULT '0' COMMENT '1超级管理员',
  `real_name` varchar(120) NOT NULL DEFAULT '',
  `last_log_time` int NOT NULL DEFAULT '0',
  `last_log_ip` varchar(120) NOT NULL DEFAULT '',
  `is_ban` int NOT NULL DEFAULT '0' COMMENT '0正常 1禁用',
  `limit_ip` varchar(50) DEFAULT NULL,
  `qq` varchar(20) NOT NULL DEFAULT '',
  `phone` varchar(20) NOT NULL COMMENT '客服电话',
  `content` varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
  `add_time` int NOT NULL COMMENT '添加时间',
  `create_time` datetime DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `codeStr` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_bank`
--

DROP TABLE IF EXISTS `zh_member_bank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_bank` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL COMMENT '会员ID',
  `bank_name` varchar(255) NOT NULL DEFAULT '' COMMENT '银行',
  `bank_code` varchar(255) NOT NULL DEFAULT '' COMMENT '银行简称',
  `bank_address` varchar(255) NOT NULL DEFAULT '' COMMENT '开户行',
  `number` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号',
  `province` varchar(255) NOT NULL DEFAULT '' COMMENT '开户所在省',
  `city` varchar(255) NOT NULL DEFAULT '' COMMENT '开户所在市',
  `add_time` int unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `add_ip` varchar(16) NOT NULL DEFAULT '',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=1077 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_info`
--

DROP TABLE IF EXISTS `zh_member_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0',
  `phone` varchar(32) NOT NULL DEFAULT '',
  `phone_status` tinyint NOT NULL DEFAULT '0',
  `real_name` varchar(255) NOT NULL DEFAULT '',
  `id_status` tinyint NOT NULL DEFAULT '0',
  `id_card` varchar(20) NOT NULL DEFAULT '',
  `sex` varchar(25) NOT NULL DEFAULT '',
  `province` varchar(255) NOT NULL DEFAULT '',
  `head_img` varchar(255) NOT NULL DEFAULT '' COMMENT '上传头像',
  `card_type` int NOT NULL DEFAULT '0' COMMENT '证件类型',
  `education` varchar(50) NOT NULL DEFAULT '' COMMENT '最高学历',
  `marry` varchar(20) NOT NULL DEFAULT '' COMMENT '婚姻状态',
  `school` varchar(200) NOT NULL DEFAULT '' COMMENT '毕业院校',
  `origin_place` varchar(255) NOT NULL DEFAULT '' COMMENT '籍贯地址',
  `address` varchar(255) NOT NULL DEFAULT '' COMMENT '居住地址',
  `card_img` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证正面图片',
  `card_back_img` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证反面图片',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=1579 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_login`
--

DROP TABLE IF EXISTS `zh_member_login`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_login` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0',
  `ip` varchar(32) NOT NULL DEFAULT '',
  `domain` varchar(1024) NOT NULL DEFAULT '',
  `add_time` int unsigned NOT NULL DEFAULT '0',
  `is_success` tinyint NOT NULL DEFAULT '0',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=35994 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_money`
--

DROP TABLE IF EXISTS `zh_member_money`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_money` (
  `uid` int unsigned NOT NULL,
  `account_money` bigint NOT NULL DEFAULT '0' COMMENT '充值资金存放池_可用余额（单位：分）',
  `back_money` bigint NOT NULL DEFAULT '0' COMMENT '回款资金存放池_可用余额（单位：分）',
  `interest_money` bigint NOT NULL DEFAULT '0' COMMENT '（单位：分）',
  `money_freeze` bigint NOT NULL DEFAULT '0' COMMENT '冻结金额（单位：分）',
  `money_collect` bigint NOT NULL DEFAULT '0' COMMENT '待收金额（单位：分）',
  `deposit_money` bigint NOT NULL DEFAULT '0',
  `borrow_vouch_limit` bigint NOT NULL DEFAULT '0',
  `borrow_vouch_cuse` bigint NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`uid`),
  KEY `uid` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_moneylog`
--

DROP TABLE IF EXISTS `zh_member_moneylog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_moneylog` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0',
  `user_name` varchar(255) NOT NULL DEFAULT '',
  `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '具体类型在函数里面查看',
  `affect_money` bigint NOT NULL DEFAULT '0' COMMENT '影响金额',
  `account_money` bigint NOT NULL DEFAULT '0' COMMENT '充值资金存放池_可用余额',
  `interest_money` bigint NOT NULL DEFAULT '0',
  `freeze_money` bigint NOT NULL DEFAULT '0' COMMENT '冻结金额',
  `info` varchar(1024) NOT NULL DEFAULT '',
  `back_money` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '回款资金存放池_可用余额',
  `collect_money` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '待收金额',
  `add_time` int unsigned NOT NULL DEFAULT '0',
  `add_ip` varchar(32) NOT NULL DEFAULT '',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `type` (`type`),
  KEY `affect_money` (`affect_money`)
) ENGINE=InnoDB AUTO_INCREMENT=103433 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_qd`
--

DROP TABLE IF EXISTS `zh_member_qd`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_qd` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0',
  `ip` varchar(32) NOT NULL DEFAULT '',
  `status` tinyint NOT NULL DEFAULT '0',
  `money` bigint NOT NULL DEFAULT '0',
  `add_time` int unsigned NOT NULL DEFAULT '0',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`,`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25263 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_recharge`
--

DROP TABLE IF EXISTS `zh_member_recharge`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_recharge` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL DEFAULT '0',
  `user_name` varchar(255) NOT NULL DEFAULT '',
  `money` bigint NOT NULL DEFAULT '0',
  `fee` bigint NOT NULL DEFAULT '0',
  `way` varchar(32) NOT NULL DEFAULT '',
  `status` tinyint NOT NULL DEFAULT '0',
  `add_time` int unsigned NOT NULL,
  `add_ip` varchar(16) NOT NULL DEFAULT '',
  `pay_account` varchar(100) NOT NULL DEFAULT '',
  `tran_id` varchar(255) NOT NULL DEFAULT '',
  `off_bank` varchar(255) NOT NULL DEFAULT '',
  `off_way` varchar(255) NOT NULL DEFAULT '',
  `deal_time` int NOT NULL DEFAULT '0' COMMENT '后台处理时间',
  `deal_admin` varchar(255) NOT NULL DEFAULT '',
  `deal_ip` varchar(255) NOT NULL DEFAULT '',
  `deal_info` varchar(10240) NOT NULL DEFAULT '',
  `wap` int(1) unsigned zerofill NOT NULL DEFAULT '0',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`) USING BTREE,
  KEY `atime` (`add_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6342 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_member_withdraw`
--

DROP TABLE IF EXISTS `zh_member_withdraw`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_member_withdraw` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int unsigned NOT NULL,
  `user_name` varchar(255) NOT NULL DEFAULT '',
  `money` bigint NOT NULL DEFAULT '0',
  `bank_id` int NOT NULL DEFAULT '0' COMMENT '关联银行卡id',
  `add_time` int unsigned NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '1success  -1fail',
  `fee` bigint NOT NULL DEFAULT '0',
  `account` bigint NOT NULL DEFAULT '0' COMMENT '实际到账金额',
  `add_ip` varchar(16) NOT NULL DEFAULT '',
  `deal_time` int unsigned NOT NULL DEFAULT '0',
  `deal_user` varchar(50) NOT NULL DEFAULT '',
  `deal_info` varchar(10240) NOT NULL DEFAULT '',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3911 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_members`
--

DROP TABLE IF EXISTS `zh_members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_members` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_name` varchar(255) NOT NULL DEFAULT '',
  `user_pass` varchar(255) NOT NULL DEFAULT '',
  `user_type` tinyint unsigned NOT NULL DEFAULT '1',
  `pin_pass` char(255) NOT NULL DEFAULT '',
  `reg_time` int unsigned NOT NULL DEFAULT '0',
  `reg_ip` varchar(15) NOT NULL DEFAULT '',
  `reg_domain` varchar(255) NOT NULL DEFAULT '',
  `recommend_id` int unsigned NOT NULL DEFAULT '0',
  `is_ban` int NOT NULL DEFAULT '0' COMMENT '是否冻结0：否； 1：是',
  `last_log_ip` varchar(32) NOT NULL DEFAULT '',
  `last_log_time` int NOT NULL DEFAULT '0',
  `is_auto_renewal` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启自动续期',
  `czpz_bl` varchar(255) NOT NULL DEFAULT '' COMMENT '充值|配资 返佣比例',
  `dl_yuming` varchar(1024) NOT NULL DEFAULT '' COMMENT '代理域名',
  `temp_phone` varchar(255) NOT NULL DEFAULT '',
  `is_blacklist` tinyint(1) NOT NULL DEFAULT '0' COMMENT '黑名单 0的正常 1 加入黑名单',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_name` (`user_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1585 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_navigation`
--

DROP TABLE IF EXISTS `zh_navigation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_navigation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '',
  `url` varchar(255) NOT NULL DEFAULT '',
  `sort_order` int NOT NULL DEFAULT '0',
  `parent_id` int NOT NULL DEFAULT '0',
  `is_hidden` int unsigned NOT NULL DEFAULT '1' COMMENT '1显示 2隐藏',
  `add_time` int unsigned NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_realname_set`
--

DROP TABLE IF EXISTS `zh_realname_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_realname_set` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '实名认证表',
  `dizhi` varchar(1024) NOT NULL DEFAULT '',
  `mima` varchar(1024) NOT NULL DEFAULT '',
  `key` varchar(1024) NOT NULL,
  `xingming` varchar(1024) NOT NULL DEFAULT '',
  `zhengjian` varchar(1024) NOT NULL DEFAULT '',
  `status` tinyint unsigned NOT NULL DEFAULT '0',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_recharge_set`
--

DROP TABLE IF EXISTS `zh_recharge_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_recharge_set` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `payee` varchar(1024) NOT NULL DEFAULT '' COMMENT '收款人名称',
  `bank_name` varchar(1024) NOT NULL DEFAULT '' COMMENT '银行名称',
  `open_bank` varchar(1024) NOT NULL DEFAULT '' COMMENT '开户行',
  `number` varchar(1024) NOT NULL DEFAULT '' COMMENT '卡号',
  `qrcode` varchar(1024) NOT NULL DEFAULT '' COMMENT '图片、二维码',
  `way` varchar(255) NOT NULL DEFAULT '' COMMENT '支付方式',
  `content` text NOT NULL COMMENT '备注、说明',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0hide 1show',
  `create_time` datetime NOT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_send_log`
--

DROP TABLE IF EXISTS `zh_send_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_send_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(32) NOT NULL DEFAULT '',
  `phone` varchar(32) NOT NULL DEFAULT '',
  `send_time` int NOT NULL,
  `ip` varchar(255) NOT NULL DEFAULT '',
  `is_success` int NOT NULL DEFAULT '0',
  `is_use` int NOT NULL DEFAULT '0',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `code` (`send_time`,`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1985 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_slide`
--

DROP TABLE IF EXISTS `zh_slide`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_slide` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `link` varchar(1024) NOT NULL DEFAULT '',
  `order` int NOT NULL DEFAULT '0',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1、pc  2、wap',
  `image` varchar(1024) DEFAULT '',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_sms_set`
--

DROP TABLE IF EXISTS `zh_sms_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_sms_set` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user` varchar(255) NOT NULL DEFAULT '',
  `password` varchar(255) NOT NULL DEFAULT '',
  `sign` varchar(255) NOT NULL DEFAULT '',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_account`
--

DROP TABLE IF EXISTS `zh_stock_account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_account` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL,
  `borrow_id` int NOT NULL COMMENT '关联zh_stock_borrow表主键ID',
  `name` varchar(255) NOT NULL DEFAULT '',
  `password` varchar(255) NOT NULL DEFAULT '',
  `remark` varchar(10240) NOT NULL DEFAULT '',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '使用状态 1：未使用    2：已使用 3已终止',
  `add_time` int NOT NULL DEFAULT '0',
  `category` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1: 股票子账号  2:期货子账户',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `borrow_id` (`borrow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2987 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_addfinancing`
--

DROP TABLE IF EXISTS `zh_stock_addfinancing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_addfinancing` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0',
  `borrow_id` int NOT NULL DEFAULT '0',
  `money` bigint NOT NULL DEFAULT '0',
  `borrow_fee` bigint NOT NULL DEFAULT '0',
  `dikou_interest` bigint NOT NULL DEFAULT '0' COMMENT '管理费抵扣金额',
  `last_deposit_money` bigint NOT NULL DEFAULT '0',
  `last_borrow_money` bigint NOT NULL DEFAULT '0',
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `add_time` int NOT NULL DEFAULT '0',
  `verify_time` int NOT NULL DEFAULT '0',
  `category` tinyint(1) unsigned zerofill NOT NULL DEFAULT '1' COMMENT '1:股票 2:期货',
  `content` text NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `borrow_id` (`borrow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1960 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_addmoney`
--

DROP TABLE IF EXISTS `zh_stock_addmoney`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_addmoney` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `borrow_id` int NOT NULL DEFAULT '0',
  `category` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1: 股票 2:期货',
  `uid` int NOT NULL,
  `money` bigint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `add_time` int NOT NULL DEFAULT '0',
  `verify_time` int unsigned NOT NULL DEFAULT '0',
  `content` varchar(10240) NOT NULL DEFAULT '',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `borrow_id` (`borrow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2388 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_borrow`
--

DROP TABLE IF EXISTS `zh_stock_borrow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_borrow` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0',
  `order_id` bigint NOT NULL DEFAULT '0' COMMENT '配资单号',
  `category` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '产品类型  1股票 2期货',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '1: 按天 2:按月 3: 免息 4:免费体验 8:vip',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0：待审核 2：使用中  3：已结束   -1：未通过',
  `deposit_money` bigint NOT NULL DEFAULT '0' COMMENT '保证金',
  `multiple` int NOT NULL DEFAULT '0',
  `borrow_duration` int NOT NULL DEFAULT '0',
  `borrow_money` bigint NOT NULL DEFAULT '0' COMMENT '配资可用金额',
  `borrow_interest` bigint NOT NULL DEFAULT '0',
  `dikou_interest` bigint NOT NULL DEFAULT '0' COMMENT '管理费抵扣金额',
  `repayment_type` tinyint NOT NULL DEFAULT '0',
  `loss_warn` bigint NOT NULL DEFAULT '0' COMMENT '预警线',
  `loss_close` bigint NOT NULL DEFAULT '0' COMMENT '止损线',
  `position` int NOT NULL DEFAULT '0' COMMENT '仓位限制',
  `rate` decimal(10,2) NOT NULL DEFAULT '0.00',
  `borrow_fee` bigint NOT NULL DEFAULT '0',
  `sort_order` tinyint NOT NULL DEFAULT '0',
  `total` tinyint NOT NULL DEFAULT '0',
  `add_time` int NOT NULL DEFAULT '0',
  `end_time` int NOT NULL DEFAULT '0',
  `verify_time` int NOT NULL DEFAULT '0',
  `content` varchar(10240) NOT NULL DEFAULT '' COMMENT '备注信息',
  `home_user` varchar(255) NOT NULL DEFAULT '' COMMENT '客户端交易账号',
  `home_pws` varchar(255) NOT NULL DEFAULT '' COMMENT '客户端交易密码',
  `stock_usable_money` bigint NOT NULL DEFAULT '0' COMMENT '股票可操作资金',
  `stock_money` bigint NOT NULL DEFAULT '0' COMMENT '股票卖出价',
  `return_money` bigint NOT NULL DEFAULT '0' COMMENT '股票盈亏金额',
  `return_rate` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '盈亏利率',
  `stock_addfinancing` bigint NOT NULL DEFAULT '0' COMMENT '扩大配资累计金额',
  `stock_addmoney` bigint NOT NULL DEFAULT '0' COMMENT '追加保证金累计金额',
  `stock_drawprofit` bigint NOT NULL DEFAULT '0' COMMENT '申请提取盈利累计金额',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3328 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_drawprofit`
--

DROP TABLE IF EXISTS `zh_stock_drawprofit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_drawprofit` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0',
  `borrow_id` int NOT NULL,
  `money` bigint NOT NULL DEFAULT '0',
  `borrow_money` bigint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `add_time` int NOT NULL,
  `verify_time` int NOT NULL,
  `content` varchar(10240) NOT NULL DEFAULT '' COMMENT '备注信息',
  `category` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '产品类型 1:股票 2:期货',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `borrow_id` (`borrow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2655 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_global`
--

DROP TABLE IF EXISTS `zh_stock_global`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_global` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(11) NOT NULL DEFAULT '',
  `text` text NOT NULL,
  `name` varchar(100) NOT NULL DEFAULT '',
  `tip` varchar(255) NOT NULL DEFAULT '',
  `code` varchar(50) NOT NULL DEFAULT '',
  `order_sn` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_rateset`
--

DROP TABLE IF EXISTS `zh_stock_rateset`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_rateset` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint NOT NULL,
  `info` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_renewal`
--

DROP TABLE IF EXISTS `zh_stock_renewal`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_renewal` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0',
  `borrow_id` int NOT NULL DEFAULT '0',
  `borrow_fee` bigint NOT NULL DEFAULT '0',
  `dikou_interest` bigint NOT NULL DEFAULT '0' COMMENT '管理费抵扣金额',
  `borrow_duration` int NOT NULL COMMENT '延期时间:单位  按周转成了天数 ~~!',
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `add_time` int NOT NULL DEFAULT '0',
  `verify_time` int NOT NULL DEFAULT '0',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '续期类型0 手动，1 自动',
  `category` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '产品类型 1:股票 2:期货',
  `content` text NOT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `borrow_id` (`borrow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4297 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_stock_stopfinancing`
--

DROP TABLE IF EXISTS `zh_stock_stopfinancing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_stock_stopfinancing` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0',
  `borrow_id` int NOT NULL DEFAULT '0',
  `borrow_fee` bigint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `add_time` int NOT NULL DEFAULT '0',
  `verify_time` int NOT NULL DEFAULT '0',
  `category` int unsigned NOT NULL DEFAULT '1' COMMENT '产品分类 1:股票 2: 期货',
  `content` text NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `borrow_id` (`borrow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3071 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_tip`
--

DROP TABLE IF EXISTS `zh_tip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_tip` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0' COMMENT '用户id',
  `money` bigint NOT NULL DEFAULT '0' COMMENT '提现金额',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '是否提醒1 已提 0未提',
  `type` tinyint NOT NULL DEFAULT '0',
  `info` varchar(10240) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25645 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_transfer`
--

DROP TABLE IF EXISTS `zh_transfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_transfer` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0',
  `user_name` varchar(255) NOT NULL DEFAULT '',
  `admin_name` varchar(255) NOT NULL DEFAULT '',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '1转至账户余额 2赠送管理费',
  `money` bigint NOT NULL DEFAULT '0',
  `interest_money` bigint NOT NULL DEFAULT '0' COMMENT '当前管理费粤',
  `account_money` bigint NOT NULL DEFAULT '0' COMMENT '当前账户余额',
  `content` varchar(10240) NOT NULL DEFAULT '',
  `add_time` int NOT NULL DEFAULT '0',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=1676 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_verifyip`
--

DROP TABLE IF EXISTS `zh_verifyip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_verifyip` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) NOT NULL DEFAULT '',
  `admin_name` varchar(255) NOT NULL DEFAULT '',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `zh_xsrw`
--

DROP TABLE IF EXISTS `zh_xsrw`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zh_xsrw` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` int NOT NULL DEFAULT '0',
  `rw_id` tinyint NOT NULL DEFAULT '0',
  `add_time` int NOT NULL DEFAULT '0',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16517 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'peizi'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-07-15 13:01:19
