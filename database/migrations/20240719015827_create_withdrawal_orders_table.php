<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class CreateWithdrawalOrdersTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->table('withdrawal_orders', ['comment' => '提領訂單'])
            ->addColumn('member_id', 'integer', ['null' => false, 'comment' => '用戶ID',])
            ->addColumn('member_withdraw_id', 'integer', ['null' => false, 'comment' => '提領申請ID',])
            ->addColumn('order_no', 'string', ['null' => false, 'comment' => '訂單編號',])
            ->addColumn('amount', 'integer', ['null' => false, 'comment' => '提領金額',])
            ->addColumn('status', 'string', ['length' => 20, 'null' => false, 'comment' => '狀態',])
            ->addColumn('transaction_no', 'string', ['null' => true, 'comment' => '三方交易編號'])
            ->addColumn('account_name', 'string', ['comment' => '帳戶名稱'])
            ->addColumn('account_number', 'string', ['comment' => '帳戶號碼'])
            ->addColumn('bank_name', 'string', ['comment' => '銀行名稱'])
            ->addColumn('bank_branch', 'string', ['null' => true, 'comment' => '分行名稱'])
            ->addColumn('payment_provider', 'string', ['null' => true, 'comment' => '支付提供商'])
            ->addColumn('payment_method', 'string', ['null' => true, 'comment' => '支付方式'])
            ->addTimestamps()
            ->create();
    }
}
