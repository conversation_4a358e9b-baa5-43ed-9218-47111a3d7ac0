<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class CreateRechargeOrdersTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table('zh_recharge_orders');
        $table->addColumn('user_id', 'integer')
            ->addColumn('order_no', 'string', ['length' => 20])
            ->addColumn('transaction_no', 'string', ['null' => true, 'default' => null])
            ->addColumn('amount', 'decimal', ['precision' => 10, 'scale' => 2])
            ->addColumn('status', 'string', ['length' => 20])
            ->addColumn('payment_provider', 'string')
            ->addColumn('payment_method', 'string', ['null' => true, 'default' => null])
            ->addColumn('paid_at', 'timestamp', ['null' => true, 'default' => null])
            ->addColumn('created_at', 'timestamp',
                ['comment' => '資料建立時間', 'default' => 'CURRENT_TIMESTAMP', 'update' => ''])
            ->addColumn('updated_at', 'timestamp',
                ['comment' => '資料更新時間', 'null' => true, 'default' => null, 'update' => 'CURRENT_TIMESTAMP'])
            ->create();
    }
}
