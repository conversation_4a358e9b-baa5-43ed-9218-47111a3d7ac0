<?php

// 加载环境变量配置文件
if (is_file('.env')) {
    $env = parse_ini_file('.env', true);

    foreach ($env as $key => $val) {
        $name = strtoupper($key);

        if (is_array($val)) {
            foreach ($val as $k => $v) {
                $item = $name . '_' . strtoupper($k);
                $_ENV[$item] = $v;
            }
        } else {
            $_ENV[$name] = $val;
        }
    }
}

return
[
    'paths' => [
        'migrations' => 'database/migrations',
        'seeds' => 'database/seeds'
    ],
    'environments' => [
        'default_migration_table' => 'phinxlog',
        'default_environment' => 'development',
        'production' => [
            'adapter' => 'mysql',
            'host' => $_ENV['DATABASE_HOSTNAME'],
            'name' => $_ENV['DATABASE_DATABASE'],
            'user' => $_ENV['DATABASE_USERNAME'],
            'pass' => $_ENV['DATABASE_PASSWORD'],
            'port' => $_ENV['DATABASE_HOSTPORT'],
            'charset' => 'utf8',
            'table_prefix' => 'zh_',
        ],
        'development' => [
            'adapter' => 'mysql',
            'host' => $_ENV['DATABASE_HOSTNAME'],
            'name' => $_ENV['DATABASE_DATABASE'],
            'user' => $_ENV['DATABASE_USERNAME'],
            'pass' => $_ENV['DATABASE_PASSWORD'],
            'port' => $_ENV['DATABASE_HOSTPORT'],
            'charset' => 'utf8',
            'table_prefix' => 'zh_',
        ],
        'testing' => [
            'adapter' => 'mysql',
            'host' => $_ENV['DATABASE_HOSTNAME'],
            'name' => $_ENV['DATABASE_DATABASE'],
            'user' => $_ENV['DATABASE_USERNAME'],
            'pass' => $_ENV['DATABASE_PASSWORD'],
            'port' => $_ENV['DATABASE_HOSTPORT'],
            'charset' => 'utf8',
            'table_prefix' => 'zh_',
        ]
    ],
    'version_order' => 'creation'
];
