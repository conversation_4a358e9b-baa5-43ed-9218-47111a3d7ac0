<?php
namespace NeteaseSms;

use think\Log;

class NeteaseSmsSender
{
    private $secretId;
    private $secretKey;
    private $businessId;
    private $apiUrl;
    private $version;
    private $paramType;

    public function __construct()
    {
        $config = config('netease_sms_config');

        if (!$config) { 
            Log::error('[NeteaseSmsSender] 构造函数 - 加载 netease_sms_config 失败.');
            throw new \Exception('网易云信短信服务配置加载失败');
        }

        if (empty($config['secretId']) || empty($config['secretKey']) || empty($config['businessId'])) {
            Log::error('[NeteaseSmsSender] 构造函数 - 缺少必要的配置字段: secretId, secretKey, 或 businessId.');
            throw new \Exception('网易云信短信服务配置不完整 (secretId, secretKey, businessId 必须存在)');
        }
        $this->secretId = $config['secretId'];
        $this->secretKey = $config['secretKey'];
        $this->businessId = $config['businessId'];
        $this->apiUrl = $config['apiUrl'] ?? 'https://sms.dun.163.com/v2/sendsms';
        $this->version = $config['version'] ?? 'v2';
        $this->paramType = $config['paramType'] ?? 'json';
    }

    public function sendWithParam($mobile, $templateId, array $params)
    {
        $timestamp = round(microtime(true) * 1000); // 时间戳
        $nonce = uniqid(); // 随机数
        $paramsJson = json_encode($params);

        $signParams = [
            'secretId' => $this->secretId,
            'businessId' => $this->businessId,
            'version' => $this->version,
            'timestamp' => $timestamp,
            'nonce' => $nonce,
            'templateId' => $templateId,
            'mobile' => $mobile,
            'paramType' => $this->paramType,
            'params' => $paramsJson, // 模板参数
        ];

        ksort($signParams);

        $stringToSign = "";
        foreach ($signParams as $key => $value) {
            $stringToSign .= $key . (is_array($value) || is_object($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : (string)$value);
        }

        $stringToSign .= $this->secretKey;

        $signature = md5($stringToSign);

        $postDataArray = [
            'secretId' => $this->secretId,
            'businessId' => $this->businessId,
            'version' => $this->version,
            'timestamp' => $timestamp,
            'nonce' => $nonce,
            'signature' => $signature,
            'templateId' => $templateId,
            'mobile' => $mobile,
            'paramType' => $this->paramType,
            'params' => $paramsJson, // 模板参数
        ];

        $postData = http_build_query($postDataArray);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded;charset=utf-8'
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            Log::error('[NeteaseSmsSender] sendWithParam - cURL 错误: ' . $curlError);
            return ['code' => -1, 'msg' => 'cURL请求失败: ' . $curlError, 'data' => null];
        }

        $result = json_decode($response, true);
        if (is_null($result)) {
            Log::error('[NeteaseSmsSender] sendWithParam - API 响应 JSON 解码失败. 原始响应: ' . $response);
            return ['code' => -2, 'msg' => 'API响应解析失败', 'data' => $response];
        }

        // 记录 API 的原始响应 (只在非200时记录为error，或可调整为恒常记录为info)
        if ($result['code'] != 200) {
            Log::error('[NeteaseSmsSender] sendWithParam - API 错误响应: ' . $response);
        } else {
            // 成功情况下，可选择性记录更简洁的日志或不记录
        }

        return $result;
    }
}
